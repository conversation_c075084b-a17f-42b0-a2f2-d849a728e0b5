import sys
from sympy.external import import_module
matchpy = import_module("matchpy")

if not matchpy:
    #bin/test will not execute any tests now
    disabled = True

if sys.version_info[:2] < (3, 6):
    disabled = True

from sympy.integrals.rubi.utility_function import (
        sympy_op_factory, Int, Sum, Set, With, Module, Scan, MapAnd, FalseQ,
        ZeroQ, NegativeQ, NonzeroQ, FreeQ, NFreeQ, List, Log, PositiveQ,
        PositiveIntegerQ, NegativeIntegerQ, IntegerQ, IntegersQ,
        ComplexNumberQ, PureComplexNumberQ, RealNumericQ, PositiveOrZeroQ,
        NegativeOrZeroQ, FractionOrNegativeQ, NegQ, Equal, Unequal, IntPart,
        FracPart, RationalQ, ProductQ, SumQ, NonsumQ, Subst, First, Rest,
        SqrtNumberQ, SqrtNumberSumQ, LinearQ, Sqrt, ArcCosh, Coefficient,
        Denominator, Hypergeometric2F1, Not, Simplify, FractionalPart,
        IntegerPart, AppellF1, EllipticPi, EllipticE, EllipticF, ArcTan,
        ArcCot, ArcCoth, ArcTanh, ArcSin, ArcSinh, ArcCos, ArcCsc, ArcSec,
        ArcCsch, ArcSech, Sinh, Tanh, Cosh, Sech, Csch, Coth, LessEqual, Less,
        Greater, GreaterEqual, FractionQ, IntLinearcQ, Expand, IndependentQ,
        PowerQ, IntegerPowerQ, PositiveIntegerPowerQ, FractionalPowerQ, AtomQ,
        ExpQ, LogQ, Head, MemberQ, TrigQ, SinQ, CosQ, TanQ, CotQ, SecQ, CscQ,
        Sin, Cos, Tan, Cot, Sec, Csc, HyperbolicQ, SinhQ, CoshQ, TanhQ, CothQ,
        SechQ, CschQ, InverseTrigQ, SinCosQ, SinhCoshQ, LeafCount, Numerator,
        NumberQ, NumericQ, Length, ListQ, Im, Re, InverseHyperbolicQ,
        InverseFunctionQ, TrigHyperbolicFreeQ, InverseFunctionFreeQ, RealQ,
        EqQ, FractionalPowerFreeQ, ComplexFreeQ, PolynomialQ, FactorSquareFree,
        PowerOfLinearQ, Exponent, QuadraticQ, LinearPairQ, BinomialParts,
        TrinomialParts, PolyQ, EvenQ, OddQ, PerfectSquareQ, NiceSqrtAuxQ,
        NiceSqrtQ, Together, PosAux, PosQ, CoefficientList, ReplaceAll,
        ExpandLinearProduct, GCD, ContentFactor, NumericFactor,
        NonnumericFactors, MakeAssocList, GensymSubst, KernelSubst,
        ExpandExpression, Apart, SmartApart, MatchQ,
        PolynomialQuotientRemainder, FreeFactors, NonfreeFactors,
        RemoveContentAux, RemoveContent, FreeTerms, NonfreeTerms,
        ExpandAlgebraicFunction, CollectReciprocals, ExpandCleanup,
        AlgebraicFunctionQ, Coeff, LeadTerm, RemainingTerms, LeadFactor,
        RemainingFactors, LeadBase, LeadDegree, Numer, Denom, hypergeom, Expon,
        MergeMonomials, PolynomialDivide, BinomialQ, TrinomialQ,
        GeneralizedBinomialQ, GeneralizedTrinomialQ, FactorSquareFreeList,
        PerfectPowerTest, SquareFreeFactorTest, RationalFunctionQ,
        RationalFunctionFactors, NonrationalFunctionFactors, Reverse,
        RationalFunctionExponents, RationalFunctionExpand, ExpandIntegrand,
        SimplerQ, SimplerSqrtQ, SumSimplerQ, BinomialDegree, TrinomialDegree,
        CancelCommonFactors, SimplerIntegrandQ, GeneralizedBinomialDegree,
        GeneralizedBinomialParts, GeneralizedTrinomialDegree,
        GeneralizedTrinomialParts, MonomialQ, MonomialSumQ,
        MinimumMonomialExponent, MonomialExponent, LinearMatchQ,
        PowerOfLinearMatchQ, QuadraticMatchQ, CubicMatchQ, BinomialMatchQ,
        TrinomialMatchQ, GeneralizedBinomialMatchQ, GeneralizedTrinomialMatchQ,
        QuotientOfLinearsMatchQ, PolynomialTermQ, PolynomialTerms,
        NonpolynomialTerms, PseudoBinomialParts, NormalizePseudoBinomial,
        PseudoBinomialPairQ, PseudoBinomialQ, PolynomialGCD, PolyGCD,
        AlgebraicFunctionFactors, NonalgebraicFunctionFactors,
        QuotientOfLinearsP, QuotientOfLinearsParts, QuotientOfLinearsQ,
        Flatten, Sort, AbsurdNumberQ, AbsurdNumberFactors,
        NonabsurdNumberFactors, SumSimplerAuxQ, Prepend, Drop,
        CombineExponents, FactorInteger, FactorAbsurdNumber,
        SubstForInverseFunction, SubstForFractionalPower,
        SubstForFractionalPowerOfQuotientOfLinears,
        FractionalPowerOfQuotientOfLinears, SubstForFractionalPowerQ,
        SubstForFractionalPowerAuxQ, FractionalPowerOfSquareQ,
        FractionalPowerSubexpressionQ, Apply, FactorNumericGcd,
        MergeableFactorQ, MergeFactor, MergeFactors, TrigSimplifyQ,
        TrigSimplify, TrigSimplifyRecur, Order, FactorOrder, Smallest,
        OrderedQ, MinimumDegree, PositiveFactors, Sign, NonpositiveFactors,
        PolynomialInAuxQ, PolynomialInQ, ExponentInAux, ExponentIn,
        PolynomialInSubstAux, PolynomialInSubst, Distrib, DistributeDegree,
        FunctionOfPower, DivideDegreesOfFactors, MonomialFactor, FullSimplify,
        FunctionOfLinearSubst, FunctionOfLinear, NormalizeIntegrand,
        NormalizeIntegrandAux, NormalizeIntegrandFactor,
        NormalizeIntegrandFactorBase, NormalizeTogether,
        NormalizeLeadTermSigns, AbsorbMinusSign, NormalizeSumFactors,
        SignOfFactor, NormalizePowerOfLinear, SimplifyIntegrand, SimplifyTerm,
        TogetherSimplify, SmartSimplify, SubstForExpn, ExpandToSum, UnifySum,
        UnifyTerms, UnifyTerm, CalculusQ, FunctionOfInverseLinear,
        PureFunctionOfSinhQ, PureFunctionOfTanhQ, PureFunctionOfCoshQ,
        IntegerQuotientQ, OddQuotientQ, EvenQuotientQ, FindTrigFactor,
        FunctionOfSinhQ, FunctionOfCoshQ, OddHyperbolicPowerQ, FunctionOfTanhQ,
        FunctionOfTanhWeight, FunctionOfHyperbolicQ, SmartNumerator,
        SmartDenominator, SubstForAux, ActivateTrig, ExpandTrig, TrigExpand,
        SubstForTrig, SubstForHyperbolic, InertTrigFreeQ, LCM,
        SubstForFractionalPowerOfLinear, FractionalPowerOfLinear,
        InverseFunctionOfLinear, InertTrigQ, InertReciprocalQ, DeactivateTrig,
        FixInertTrigFunction, DeactivateTrigAux, PowerOfInertTrigSumQ,
        PiecewiseLinearQ, KnownTrigIntegrandQ, KnownSineIntegrandQ,
        KnownTangentIntegrandQ, KnownCotangentIntegrandQ,
        KnownSecantIntegrandQ, TryPureTanSubst, TryTanhSubst, TryPureTanhSubst,
        AbsurdNumberGCD, AbsurdNumberGCDList, ExpandTrigExpand,
        ExpandTrigReduce, ExpandTrigReduceAux, NormalizeTrig, TrigToExp,
        ExpandTrigToExp, TrigReduce, FunctionOfTrig, AlgebraicTrigFunctionQ,
        FunctionOfHyperbolic, FunctionOfQ, FunctionOfExpnQ, PureFunctionOfSinQ,
        PureFunctionOfCosQ, PureFunctionOfTanQ, PureFunctionOfCotQ,
        FunctionOfCosQ, FunctionOfSinQ, OddTrigPowerQ, FunctionOfTanQ,
        FunctionOfTanWeight, FunctionOfTrigQ, FunctionOfDensePolynomialsQ,
        FunctionOfLog, PowerVariableExpn, PowerVariableDegree,
        PowerVariableSubst, EulerIntegrandQ, FunctionOfSquareRootOfQuadratic,
        SquareRootOfQuadraticSubst, Divides, EasyDQ, ProductOfLinearPowersQ,
        Rt, NthRoot, AtomBaseQ, SumBaseQ, NegSumBaseQ, AllNegTermQ,
        SomeNegTermQ, TrigSquareQ, RtAux, TrigSquare, IntSum, IntTerm, Map2,
        ConstantFactor, SameQ, ReplacePart, CommonFactors,
        MostMainFactorPosition, FunctionOfExponentialQ, FunctionOfExponential,
        FunctionOfExponentialFunction, FunctionOfExponentialFunctionAux,
        FunctionOfExponentialTest, FunctionOfExponentialTestAux, stdev,
        rubi_test, If, IntQuadraticQ, IntBinomialQ, RectifyTangent,
        RectifyCotangent, Inequality, Condition, Simp, SimpHelp, SplitProduct,
        SplitSum, SubstFor, SubstForAux, FresnelS, FresnelC, Erfc, Erfi, Gamma,
        FunctionOfTrigOfLinearQ, ElementaryFunctionQ, Complex, UnsameQ,
        _SimpFixFactor, SimpFixFactor, _FixSimplify, FixSimplify,
        _SimplifyAntiderivativeSum, SimplifyAntiderivativeSum,
        _SimplifyAntiderivative, SimplifyAntiderivative, _TrigSimplifyAux,
        TrigSimplifyAux, Cancel, Part, PolyLog, D, Dist, Sum_doit, PolynomialQuotient, Floor,
        PolynomialRemainder, Factor, PolyLog, CosIntegral, SinIntegral, LogIntegral, SinhIntegral,
        CoshIntegral, Rule, Erf, PolyGamma, ExpIntegralEi, ExpIntegralE, LogGamma , UtilityOperator, Factorial,
        Zeta, ProductLog, DerivativeDivides, HypergeometricPFQ, IntHide, OneQ
    )
from sympy.core.add import Add
from sympy.core.mod import Mod
from sympy.core.mul import Mul
from sympy.core.numbers import (Float, I, Integer)
from sympy.core.power import Pow
from sympy.core.singleton import S
from sympy.functions.elementary.complexes import Abs
from sympy.functions.elementary.miscellaneous import sqrt
from sympy.integrals.integrals import Integral as Integrate
from sympy.logic.boolalg import (And, Or)
from sympy.simplify.simplify import simplify
from sympy.integrals.rubi.symbol import WC
from sympy.core.symbol import symbols, Symbol
from sympy.functions import (sin, cos, tan, cot, csc, sec, sqrt, erf, exp, log)
from sympy.functions.elementary.hyperbolic import (acosh, asinh, atanh, acoth, acsch, asech, cosh, sinh, tanh, coth, sech, csch)
from sympy.functions.elementary.trigonometric import (atan, acsc, asin, acot, acos, asec)
from sympy.integrals.rubi.rubimain import rubi_integrate
from sympy.core.numbers import pi as Pi
a, b, c, d, e, f, m, n, x, u , k, p, r, s, t, i, j= symbols('a b c d e f m n x u k p r s t i j')
A, B, C, D, a, b, c, d, e, f, g, h, y, z, m, n, p, q, u, v, w, F = symbols('A B C D a b c d e f g h y z m n p q u v w F', )

def test_1():
    assert rubi_test(rubi_integrate(x**S(4)*asin(a*x), x), x, x**S(5)*asin(a*x)/S(5) + (-a**S(2)*x**S(2) + S(1))**(S(5)/2)/(S(25)*a**S(5)) - S(2)*(-a**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(15)*a**S(5)) + sqrt(-a**S(2)*x**S(2) + S(1))/(S(5)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asin(a*x), x), x, x**S(4)*asin(a*x)/S(4) + x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(16)*a) + S(3)*x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(32)*a**S(3)) - S(3)*asin(a*x)/(S(32)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asin(a*x), x), x, x**S(3)*asin(a*x)/S(3) - (-a**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(9)*a**S(3)) + sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asin(a*x), x), x, x**S(2)*asin(a*x)/S(2) + x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(4)*a) - asin(a*x)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x), x), x, x*asin(a*x) + sqrt(-a**S(2)*x**S(2) + S(1))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)/x, x), x, -I*PolyLog(S(2), exp(S(2)*I*asin(a*x)))/S(2) + log(-exp(S(2)*I*asin(a*x)) + S(1))*asin(a*x) - I*asin(a*x)**S(2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)/x**S(2), x), x, -a*atanh(sqrt(-a**S(2)*x**S(2) + S(1))) - asin(a*x)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)/x**S(3), x), x, -a*sqrt(-a**S(2)*x**S(2) + S(1))/(S(2)*x) - asin(a*x)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)/x**S(4), x), x, -a**S(3)*atanh(sqrt(-a**S(2)*x**S(2) + S(1)))/S(6) - a*sqrt(-a**S(2)*x**S(2) + S(1))/(S(6)*x**S(2)) - asin(a*x)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)/x**S(5), x), x, -a**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(6)*x) - a*sqrt(-a**S(2)*x**S(2) + S(1))/(S(12)*x**S(3)) - asin(a*x)/(S(4)*x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)/x**S(6), x), x, -S(3)*a**S(5)*atanh(sqrt(-a**S(2)*x**S(2) + S(1)))/S(40) - S(3)*a**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(40)*x**S(2)) - a*sqrt(-a**S(2)*x**S(2) + S(1))/(S(20)*x**S(4)) - asin(a*x)/(S(5)*x**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asin(a*x)**S(2), x), x, x**S(5)*asin(a*x)**S(2)/S(5) - S(2)*x**S(5)/S(125) + S(2)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(25)*a) - S(8)*x**S(3)/(S(225)*a**S(2)) + S(8)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(75)*a**S(3)) - S(16)*x/(S(75)*a**S(4)) + S(16)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(75)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asin(a*x)**S(2), x), x, x**S(4)*asin(a*x)**S(2)/S(4) - x**S(4)/S(32) + x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(8)*a) - S(3)*x**S(2)/(S(32)*a**S(2)) + S(3)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(16)*a**S(3)) - S(3)*asin(a*x)**S(2)/(S(32)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asin(a*x)**S(2), x), x, x**S(3)*asin(a*x)**S(2)/S(3) - S(2)*x**S(3)/S(27) + S(2)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(9)*a) - S(4)*x/(S(9)*a**S(2)) + S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(9)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asin(a*x)**S(2), x), x, x**S(2)*asin(a*x)**S(2)/S(2) - x**S(2)/S(4) + x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(2)*a) - asin(a*x)**S(2)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(2), x), x, x*asin(a*x)**S(2) - S(2)*x + S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(2)/x, x), x, -I*PolyLog(S(2), exp(S(2)*I*asin(a*x)))*asin(a*x) + PolyLog(S(3), exp(S(2)*I*asin(a*x)))/S(2) + log(-exp(S(2)*I*asin(a*x)) + S(1))*asin(a*x)**S(2) - I*asin(a*x)**S(3)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(2)/x**S(2), x), x, S(2)*I*a*PolyLog(S(2), -exp(I*asin(a*x))) - S(2)*I*a*PolyLog(S(2), exp(I*asin(a*x))) - S(4)*a*asin(a*x)*atanh(exp(I*asin(a*x))) - asin(a*x)**S(2)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(2)/x**S(3), x), x, a**S(2)*log(x) - a*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/x - asin(a*x)**S(2)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)

    # sympy and mathematica assert rubi_test(rubi_integrate(asin(a*x)**S(2)/x**S(4), x), x, I*a**S(3)*PolyLog(S(2), -exp(I*asin(a*x)))/S(3) - I*a**S(3)*PolyLog(S(2), exp(I*asin(a*x)))/S(3) - S(2)*a**S(3)*asin(a*x)*atanh(exp(I*asin(a*x)))/S(3) - a**S(2)/(S(3)*x) - a*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(3)*x**S(2)) - asin(a*x)**S(2)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(asin(a*x)**S(2)/x**S(5), x), x, a**S(4)*log(x)/S(3) - a**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(3)*x) - a**S(2)/(S(12)*x**S(2)) - a*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(6)*x**S(3)) - asin(a*x)**S(2)/(S(4)*x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asin(a*x)**S(3), x), x, x**S(5)*asin(a*x)**S(3)/S(5) - S(6)*x**S(5)*asin(a*x)/S(125) + S(3)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(25)*a) - S(8)*x**S(3)*asin(a*x)/(S(75)*a**S(2)) + S(4)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(25)*a**S(3)) - S(16)*x*asin(a*x)/(S(25)*a**S(4)) - S(6)*(-a**S(2)*x**S(2) + S(1))**(S(5)/2)/(S(625)*a**S(5)) + S(76)*(-a**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(1125)*a**S(5)) + S(8)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(25)*a**S(5)) - S(298)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(375)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asin(a*x)**S(3), x), x, x**S(4)*asin(a*x)**S(3)/S(4) - S(3)*x**S(4)*asin(a*x)/S(32) + S(3)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(16)*a) - S(3)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(128)*a) - S(9)*x**S(2)*asin(a*x)/(S(32)*a**S(2)) + S(9)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(32)*a**S(3)) - S(45)*x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(256)*a**S(3)) - S(3)*asin(a*x)**S(3)/(S(32)*a**S(4)) + S(45)*asin(a*x)/(S(256)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asin(a*x)**S(3), x), x, x**S(3)*asin(a*x)**S(3)/S(3) - S(2)*x**S(3)*asin(a*x)/S(9) + x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(3)*a) - S(4)*x*asin(a*x)/(S(3)*a**S(2)) + S(2)*(-a**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(27)*a**S(3)) + S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(3)*a**S(3)) - S(14)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(9)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asin(a*x)**S(3), x), x, x**S(2)*asin(a*x)**S(3)/S(2) - S(3)*x**S(2)*asin(a*x)/S(4) + S(3)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(4)*a) - S(3)*x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(8)*a) - asin(a*x)**S(3)/(S(4)*a**S(2)) + S(3)*asin(a*x)/(S(8)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(3), x), x, x*asin(a*x)**S(3) - S(6)*x*asin(a*x) + S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/a - S(6)*sqrt(-a**S(2)*x**S(2) + S(1))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(3)/x, x), x, -S(3)*I*PolyLog(S(2), exp(S(2)*I*asin(a*x)))*asin(a*x)**S(2)/S(2) + S(3)*PolyLog(S(3), exp(S(2)*I*asin(a*x)))*asin(a*x)/S(2) + S(3)*I*PolyLog(S(4), exp(S(2)*I*asin(a*x)))/S(4) + log(-exp(S(2)*I*asin(a*x)) + S(1))*asin(a*x)**S(3) - I*asin(a*x)**S(4)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(3)/x**S(2), x), x, S(6)*I*a*PolyLog(S(2), -exp(I*asin(a*x)))*asin(a*x) - S(6)*I*a*PolyLog(S(2), exp(I*asin(a*x)))*asin(a*x) - S(6)*a*PolyLog(S(3), -exp(I*asin(a*x))) + S(6)*a*PolyLog(S(3), exp(I*asin(a*x))) - S(6)*a*asin(a*x)**S(2)*atanh(exp(I*asin(a*x))) - asin(a*x)**S(3)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(3)/x**S(3), x), x, -S(3)*I*a**S(2)*PolyLog(S(2), exp(S(2)*I*asin(a*x)))/S(2) + S(3)*a**S(2)*log(-exp(S(2)*I*asin(a*x)) + S(1))*asin(a*x) - S(3)*I*a**S(2)*asin(a*x)**S(2)/S(2) - S(3)*a*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(2)*x) - asin(a*x)**S(3)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)

    # sympy and mathematica assert rubi_test(rubi_integrate(asin(a*x)**S(3)/x**S(4), x), x, I*a**S(3)*PolyLog(S(2), -exp(I*asin(a*x)))*asin(a*x) - I*a**S(3)*PolyLog(S(2), exp(I*asin(a*x)))*asin(a*x) - a**S(3)*PolyLog(S(3), -exp(I*asin(a*x))) + a**S(3)*PolyLog(S(3), exp(I*asin(a*x))) - a**S(3)*asin(a*x)**S(2)*atanh(exp(I*asin(a*x))) - a**S(3)*atanh(sqrt(-a**S(2)*x**S(2) + S(1))) - a**S(2)*asin(a*x)/x - a*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(2)*x**S(2)) - asin(a*x)**S(3)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(asin(a*x)**S(3)/x**S(5), x), x, -I*a**S(4)*PolyLog(S(2), exp(S(2)*I*asin(a*x)))/S(2) + a**S(4)*log(-exp(S(2)*I*asin(a*x)) + S(1))*asin(a*x) - I*a**S(4)*asin(a*x)**S(2)/S(2) - a**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(2)*x) - a**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(4)*x) - a**S(2)*asin(a*x)/(S(4)*x**S(2)) - a*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(2)/(S(4)*x**S(3)) - asin(a*x)**S(3)/(S(4)*x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)*asin(a*x)**S(4), x), x, x**S(6)*asin(a*x)**S(4)/S(6) - x**S(6)*asin(a*x)**S(2)/S(18) + x**S(6)/S(324) + x**S(5)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(9)*a) - x**S(5)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(54)*a) - S(5)*x**S(4)*asin(a*x)**S(2)/(S(48)*a**S(2)) + S(65)*x**S(4)/(S(3456)*a**S(2)) + S(5)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(36)*a**S(3)) - S(65)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(864)*a**S(3)) - S(5)*x**S(2)*asin(a*x)**S(2)/(S(16)*a**S(4)) + S(245)*x**S(2)/(S(1152)*a**S(4)) + S(5)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(24)*a**S(5)) - S(245)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(576)*a**S(5)) - S(5)*asin(a*x)**S(4)/(S(96)*a**S(6)) + S(245)*asin(a*x)**S(2)/(S(1152)*a**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asin(a*x)**S(4), x), x, x**S(5)*asin(a*x)**S(4)/S(5) - S(12)*x**S(5)*asin(a*x)**S(2)/S(125) + S(24)*x**S(5)/S(3125) + S(4)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(25)*a) - S(24)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(625)*a) - S(16)*x**S(3)*asin(a*x)**S(2)/(S(75)*a**S(2)) + S(1088)*x**S(3)/(S(16875)*a**S(2)) + S(16)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(75)*a**S(3)) - S(1088)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(5625)*a**S(3)) - S(32)*x*asin(a*x)**S(2)/(S(25)*a**S(4)) + S(16576)*x/(S(5625)*a**S(4)) + S(32)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(75)*a**S(5)) - S(16576)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(5625)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asin(a*x)**S(4), x), x, x**S(4)*asin(a*x)**S(4)/S(4) - S(3)*x**S(4)*asin(a*x)**S(2)/S(16) + S(3)*x**S(4)/S(128) + x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(4)*a) - S(3)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(32)*a) - S(9)*x**S(2)*asin(a*x)**S(2)/(S(16)*a**S(2)) + S(45)*x**S(2)/(S(128)*a**S(2)) + S(3)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(8)*a**S(3)) - S(45)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(64)*a**S(3)) - S(3)*asin(a*x)**S(4)/(S(32)*a**S(4)) + S(45)*asin(a*x)**S(2)/(S(128)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asin(a*x)**S(4), x), x, x**S(3)*asin(a*x)**S(4)/S(3) - S(4)*x**S(3)*asin(a*x)**S(2)/S(9) + S(8)*x**S(3)/S(81) + S(4)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(9)*a) - S(8)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(27)*a) - S(8)*x*asin(a*x)**S(2)/(S(3)*a**S(2)) + S(160)*x/(S(27)*a**S(2)) + S(8)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(9)*a**S(3)) - S(160)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(27)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asin(a*x)**S(4), x), x, x**S(2)*asin(a*x)**S(4)/S(2) - S(3)*x**S(2)*asin(a*x)**S(2)/S(2) + S(3)*x**S(2)/S(4) + x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/a - S(3)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/(S(2)*a) - asin(a*x)**S(4)/(S(4)*a**S(2)) + S(3)*asin(a*x)**S(2)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(4), x), x, x*asin(a*x)**S(4) - S(12)*x*asin(a*x)**S(2) + S(24)*x + S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/a - S(24)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(4)/x, x), x, -S(2)*I*PolyLog(S(2), exp(S(2)*I*asin(a*x)))*asin(a*x)**S(3) + S(3)*PolyLog(S(3), exp(S(2)*I*asin(a*x)))*asin(a*x)**S(2) + S(3)*I*PolyLog(S(4), exp(S(2)*I*asin(a*x)))*asin(a*x) - S(3)*PolyLog(S(5), exp(S(2)*I*asin(a*x)))/S(2) + log(-exp(S(2)*I*asin(a*x)) + S(1))*asin(a*x)**S(4) - I*asin(a*x)**S(5)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(4)/x**S(2), x), x, S(12)*I*a*PolyLog(S(2), -exp(I*asin(a*x)))*asin(a*x)**S(2) - S(12)*I*a*PolyLog(S(2), exp(I*asin(a*x)))*asin(a*x)**S(2) - S(24)*a*PolyLog(S(3), -exp(I*asin(a*x)))*asin(a*x) + S(24)*a*PolyLog(S(3), exp(I*asin(a*x)))*asin(a*x) - S(24)*I*a*PolyLog(S(4), -exp(I*asin(a*x))) + S(24)*I*a*PolyLog(S(4), exp(I*asin(a*x))) - S(8)*a*asin(a*x)**S(3)*atanh(exp(I*asin(a*x))) - asin(a*x)**S(4)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(4)/x**S(3), x), x, -S(6)*I*a**S(2)*PolyLog(S(2), exp(S(2)*I*asin(a*x)))*asin(a*x) + S(3)*a**S(2)*PolyLog(S(3), exp(S(2)*I*asin(a*x))) + S(6)*a**S(2)*log(-exp(S(2)*I*asin(a*x)) + S(1))*asin(a*x)**S(2) - S(2)*I*a**S(2)*asin(a*x)**S(3) - S(2)*a*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/x - asin(a*x)**S(4)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**S(4)/x**S(4), x), x, S(2)*I*a**S(3)*PolyLog(S(2), -exp(I*asin(a*x)))*asin(a*x)**S(2) + S(4)*I*a**S(3)*PolyLog(S(2), -exp(I*asin(a*x))) - S(2)*I*a**S(3)*PolyLog(S(2), exp(I*asin(a*x)))*asin(a*x)**S(2) - S(4)*I*a**S(3)*PolyLog(S(2), exp(I*asin(a*x))) - S(4)*a**S(3)*PolyLog(S(3), -exp(I*asin(a*x)))*asin(a*x) + S(4)*a**S(3)*PolyLog(S(3), exp(I*asin(a*x)))*asin(a*x) - S(4)*I*a**S(3)*PolyLog(S(4), -exp(I*asin(a*x))) + S(4)*I*a**S(3)*PolyLog(S(4), exp(I*asin(a*x))) - S(4)*a**S(3)*asin(a*x)**S(3)*atanh(exp(I*asin(a*x)))/S(3) - S(8)*a**S(3)*asin(a*x)*atanh(exp(I*asin(a*x))) - S(2)*a**S(2)*asin(a*x)**S(2)/x - S(2)*a*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**S(3)/(S(3)*x**S(2)) - asin(a*x)**S(4)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(6)/asin(a*x), x), x, S(5)*CosIntegral(asin(a*x))/(S(64)*a**S(7)) - S(9)*CosIntegral(S(3)*asin(a*x))/(S(64)*a**S(7)) + S(5)*CosIntegral(S(5)*asin(a*x))/(S(64)*a**S(7)) - CosIntegral(S(7)*asin(a*x))/(S(64)*a**S(7)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)/asin(a*x), x), x, S(5)*SinIntegral(S(2)*asin(a*x))/(S(32)*a**S(6)) - SinIntegral(S(4)*asin(a*x))/(S(8)*a**S(6)) + SinIntegral(S(6)*asin(a*x))/(S(32)*a**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asin(a*x), x), x, CosIntegral(asin(a*x))/(S(8)*a**S(5)) - S(3)*CosIntegral(S(3)*asin(a*x))/(S(16)*a**S(5)) + CosIntegral(S(5)*asin(a*x))/(S(16)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asin(a*x), x), x, SinIntegral(S(2)*asin(a*x))/(S(4)*a**S(4)) - SinIntegral(S(4)*asin(a*x))/(S(8)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asin(a*x), x), x, CosIntegral(asin(a*x))/(S(4)*a**S(3)) - CosIntegral(S(3)*asin(a*x))/(S(4)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asin(a*x), x), x, SinIntegral(S(2)*asin(a*x))/(S(2)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/asin(a*x), x), x, CosIntegral(asin(a*x))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asin(a*x)), x), x, Integrate(S(1)/(x*asin(a*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*asin(a*x)), x), x, Integrate(S(1)/(x**S(2)*asin(a*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(6)/asin(a*x)**S(2), x), x, -x**S(6)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*asin(a*x)) - S(5)*SinIntegral(asin(a*x))/(S(64)*a**S(7)) + S(27)*SinIntegral(S(3)*asin(a*x))/(S(64)*a**S(7)) - S(25)*SinIntegral(S(5)*asin(a*x))/(S(64)*a**S(7)) + S(7)*SinIntegral(S(7)*asin(a*x))/(S(64)*a**S(7)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)/asin(a*x)**S(2), x), x, -x**S(5)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*asin(a*x)) + S(5)*CosIntegral(S(2)*asin(a*x))/(S(16)*a**S(6)) - CosIntegral(S(4)*asin(a*x))/(S(2)*a**S(6)) + S(3)*CosIntegral(S(6)*asin(a*x))/(S(16)*a**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asin(a*x)**S(2), x), x, -x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*asin(a*x)) - SinIntegral(asin(a*x))/(S(8)*a**S(5)) + S(9)*SinIntegral(S(3)*asin(a*x))/(S(16)*a**S(5)) - S(5)*SinIntegral(S(5)*asin(a*x))/(S(16)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asin(a*x)**S(2), x), x, -x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*asin(a*x)) + CosIntegral(S(2)*asin(a*x))/(S(2)*a**S(4)) - CosIntegral(S(4)*asin(a*x))/(S(2)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asin(a*x)**S(2), x), x, -x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*asin(a*x)) - SinIntegral(asin(a*x))/(S(4)*a**S(3)) + S(3)*SinIntegral(S(3)*asin(a*x))/(S(4)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asin(a*x)**S(2), x), x, -x*sqrt(-a**S(2)*x**S(2) + S(1))/(a*asin(a*x)) + CosIntegral(S(2)*asin(a*x))/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(-2)), x), x, -sqrt(-a**S(2)*x**S(2) + S(1))/(a*asin(a*x)) - SinIntegral(asin(a*x))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asin(a*x)**S(2)), x), x, Integrate(S(1)/(x*asin(a*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*asin(a*x)**S(2)), x), x, Integrate(S(1)/(x**S(2)*asin(a*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asin(a*x)**S(3), x), x, S(5)*x**S(5)/(S(2)*asin(a*x)) - x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(2)*a*asin(a*x)**S(2)) - S(2)*x**S(3)/(a**S(2)*asin(a*x)) - CosIntegral(asin(a*x))/(S(16)*a**S(5)) + S(27)*CosIntegral(S(3)*asin(a*x))/(S(32)*a**S(5)) - S(25)*CosIntegral(S(5)*asin(a*x))/(S(32)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asin(a*x)**S(3), x), x, S(2)*x**S(4)/asin(a*x) - x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(2)*a*asin(a*x)**S(2)) - S(3)*x**S(2)/(S(2)*a**S(2)*asin(a*x)) - SinIntegral(S(2)*asin(a*x))/(S(2)*a**S(4)) + SinIntegral(S(4)*asin(a*x))/a**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asin(a*x)**S(3), x), x, S(3)*x**S(3)/(S(2)*asin(a*x)) - x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(2)*a*asin(a*x)**S(2)) - x/(a**S(2)*asin(a*x)) - CosIntegral(asin(a*x))/(S(8)*a**S(3)) + S(9)*CosIntegral(S(3)*asin(a*x))/(S(8)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asin(a*x)**S(3), x), x, x**S(2)/asin(a*x) - x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(2)*a*asin(a*x)**S(2)) - SinIntegral(S(2)*asin(a*x))/a**S(2) - S(1)/(S(2)*a**S(2)*asin(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(-3)), x), x, x/(S(2)*asin(a*x)) - sqrt(-a**S(2)*x**S(2) + S(1))/(S(2)*a*asin(a*x)**S(2)) - CosIntegral(asin(a*x))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asin(a*x)**S(3)), x), x, Integrate(S(1)/(x*asin(a*x)**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*asin(a*x)**S(3)), x), x, Integrate(S(1)/(x**S(2)*asin(a*x)**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asin(a*x)**S(4), x), x, S(5)*x**S(5)/(S(6)*asin(a*x)**S(2)) + S(25)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(6)*a*asin(a*x)) - x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**S(3)) - S(2)*x**S(3)/(S(3)*a**S(2)*asin(a*x)**S(2)) - S(2)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(a**S(3)*asin(a*x)) + SinIntegral(asin(a*x))/(S(48)*a**S(5)) - S(27)*SinIntegral(S(3)*asin(a*x))/(S(32)*a**S(5)) + S(125)*SinIntegral(S(5)*asin(a*x))/(S(96)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asin(a*x)**S(4), x), x, S(2)*x**S(4)/(S(3)*asin(a*x)**S(2)) + S(8)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)) - x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**S(3)) - x**S(2)/(S(2)*a**S(2)*asin(a*x)**S(2)) - x*sqrt(-a**S(2)*x**S(2) + S(1))/(a**S(3)*asin(a*x)) - CosIntegral(S(2)*asin(a*x))/(S(3)*a**S(4)) + S(4)*CosIntegral(S(4)*asin(a*x))/(S(3)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asin(a*x)**S(4), x), x, x**S(3)/(S(2)*asin(a*x)**S(2)) + S(3)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(2)*a*asin(a*x)) - x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**S(3)) - x/(S(3)*a**S(2)*asin(a*x)**S(2)) - sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a**S(3)*asin(a*x)) + SinIntegral(asin(a*x))/(S(24)*a**S(3)) - S(9)*SinIntegral(S(3)*asin(a*x))/(S(8)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asin(a*x)**S(4), x), x, x**S(2)/(S(3)*asin(a*x)**S(2)) + S(2)*x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)) - x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**S(3)) - S(2)*CosIntegral(S(2)*asin(a*x))/(S(3)*a**S(2)) - S(1)/(S(6)*a**S(2)*asin(a*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(-4)), x), x, x/(S(6)*asin(a*x)**S(2)) + sqrt(-a**S(2)*x**S(2) + S(1))/(S(6)*a*asin(a*x)) - sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**S(3)) + SinIntegral(asin(a*x))/(S(6)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asin(a*x)**S(4)), x), x, Integrate(S(1)/(x*asin(a*x)**S(4)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*asin(a*x)**S(4)), x), x, Integrate(S(1)/(x**S(2)*asin(a*x)**S(4)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*sqrt(asin(a*x)), x), x, -sqrt(S(10))*sqrt(Pi)*FresnelS(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(800)*a**S(5)) - sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(16)*a**S(5)) + sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(96)*a**S(5)) + x**S(5)*sqrt(asin(a*x))/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*sqrt(asin(a*x)), x), x, sqrt(Pi)*FresnelC(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(16)*a**S(4)) - sqrt(S(2))*sqrt(Pi)*FresnelC(S(2)*sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(128)*a**S(4)) + x**S(4)*sqrt(asin(a*x))/S(4) - S(3)*sqrt(asin(a*x))/(S(32)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*sqrt(asin(a*x)), x), x, -sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(8)*a**S(3)) + sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(72)*a**S(3)) + x**S(3)*sqrt(asin(a*x))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*sqrt(asin(a*x)), x), x, sqrt(Pi)*FresnelC(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(8)*a**S(2)) + x**S(2)*sqrt(asin(a*x))/S(2) - sqrt(asin(a*x))/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(asin(a*x)), x), x, -sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(2)*a) + x*sqrt(asin(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(asin(a*x))/x, x), x, Integrate(sqrt(asin(a*x))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asin(a*x)**(S(3)/2), x), x, -S(3)*sqrt(S(10))*sqrt(Pi)*FresnelC(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(8000)*a**S(5)) - S(3)*sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(32)*a**S(5)) + sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(192)*a**S(5)) + x**S(5)*asin(a*x)**(S(3)/2)/S(5) + S(3)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(50)*a) + S(2)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(25)*a**S(3)) + S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(25)*a**S(5)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(x**S(4)*asin(a*x)**(S(3)/2), x), x, -S(3)*sqrt(S(10))*sqrt(Pi)*FresnelC(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(8000)*a**S(5)) - S(3)*sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(32)*a**S(5)) + sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(192)*a**S(5)) + x**S(5)*asin(a*x)**(S(3)/2)/S(5) + S(3)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(50)*a) + S(2)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(25)*a**S(3)) + S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(25)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asin(a*x)**(S(3)/2), x), x, -S(3)*sqrt(Pi)*FresnelS(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(64)*a**S(4)) + S(3)*sqrt(S(2))*sqrt(Pi)*FresnelS(S(2)*sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(1024)*a**S(4)) + x**S(4)*asin(a*x)**(S(3)/2)/S(4) + S(3)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(32)*a) + S(9)*x*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(64)*a**S(3)) - S(3)*asin(a*x)**(S(3)/2)/(S(32)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asin(a*x)**(S(3)/2), x), x, -S(3)*sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(16)*a**S(3)) + sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(144)*a**S(3)) + x**S(3)*asin(a*x)**(S(3)/2)/S(3) + x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(6)*a) + sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(3)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asin(a*x)**(S(3)/2), x), x, -S(3)*sqrt(Pi)*FresnelS(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(32)*a**S(2)) + x**S(2)*asin(a*x)**(S(3)/2)/S(2) + S(3)*x*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(8)*a) - asin(a*x)**(S(3)/2)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(3)/2), x), x, -S(3)*sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(4)*a) + x*asin(a*x)**(S(3)/2) + S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*sqrt(asin(a*x))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(3)/2)/x, x), x, Integrate(asin(a*x)**(S(3)/2)/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asin(a*x)**(S(5)/2), x), x, S(3)*sqrt(S(10))*sqrt(Pi)*FresnelS(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(16000)*a**S(5)) + S(15)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(64)*a**S(5)) - S(5)*sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(1152)*a**S(5)) + x**S(5)*asin(a*x)**(S(5)/2)/S(5) - S(3)*x**S(5)*sqrt(asin(a*x))/S(100) + x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(10)*a) - x**S(3)*sqrt(asin(a*x))/(S(15)*a**S(2)) + S(2)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(15)*a**S(3)) - S(2)*x*sqrt(asin(a*x))/(S(5)*a**S(4)) + S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(15)*a**S(5)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(x**S(4)*asin(a*x)**(S(5)/2), x), x, S(3)*sqrt(S(10))*sqrt(Pi)*FresnelS(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(16000)*a**S(5)) + S(15)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(64)*a**S(5)) - S(5)*sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(1152)*a**S(5)) + x**S(5)*asin(a*x)**(S(5)/2)/S(5) - S(3)*x**S(5)*sqrt(asin(a*x))/S(100) + x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(10)*a) - x**S(3)*sqrt(asin(a*x))/(S(15)*a**S(2)) + S(2)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(15)*a**S(3)) - S(2)*x*sqrt(asin(a*x))/(S(5)*a**S(4)) + S(4)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(15)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asin(a*x)**(S(5)/2), x), x, -S(15)*sqrt(Pi)*FresnelC(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(256)*a**S(4)) + S(15)*sqrt(S(2))*sqrt(Pi)*FresnelC(S(2)*sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(8192)*a**S(4)) + x**S(4)*asin(a*x)**(S(5)/2)/S(4) - S(15)*x**S(4)*sqrt(asin(a*x))/S(256) + S(5)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(32)*a) - S(45)*x**S(2)*sqrt(asin(a*x))/(S(256)*a**S(2)) + S(15)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(64)*a**S(3)) - S(3)*asin(a*x)**(S(5)/2)/(S(32)*a**S(4)) + S(225)*sqrt(asin(a*x))/(S(2048)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asin(a*x)**(S(5)/2), x), x, S(15)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(32)*a**S(3)) - S(5)*sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(864)*a**S(3)) + x**S(3)*asin(a*x)**(S(5)/2)/S(3) - S(5)*x**S(3)*sqrt(asin(a*x))/S(36) + S(5)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(18)*a) - S(5)*x*sqrt(asin(a*x))/(S(6)*a**S(2)) + S(5)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(9)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asin(a*x)**(S(5)/2), x), x, -S(15)*sqrt(Pi)*FresnelC(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(128)*a**S(2)) + x**S(2)*asin(a*x)**(S(5)/2)/S(2) - S(15)*x**S(2)*sqrt(asin(a*x))/S(32) + S(5)*x*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(8)*a) - asin(a*x)**(S(5)/2)/(S(4)*a**S(2)) + S(15)*sqrt(asin(a*x))/(S(64)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(5)/2), x), x, S(15)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(8)*a) + x*asin(a*x)**(S(5)/2) - S(15)*x*sqrt(asin(a*x))/S(4) + S(5)*sqrt(-a**S(2)*x**S(2) + S(1))*asin(a*x)**(S(3)/2)/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(5)/2)/x, x), x, Integrate(asin(a*x)**(S(5)/2)/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/sqrt(asin(a*x)), x), x, sqrt(S(10))*sqrt(Pi)*FresnelC(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(80)*a**S(5)) + sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(8)*a**S(5)) - sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(16)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/sqrt(asin(a*x)), x), x, sqrt(Pi)*FresnelS(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(4)*a**S(4)) - sqrt(S(2))*sqrt(Pi)*FresnelS(S(2)*sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(16)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/sqrt(asin(a*x)), x), x, sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(4)*a**S(3)) - sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(12)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/sqrt(asin(a*x)), x), x, sqrt(Pi)*FresnelS(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(2)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(asin(a*x)), x), x, sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(asin(a*x))), x), x, Integrate(S(1)/(x*sqrt(asin(a*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*sqrt(asin(a*x))), x), x, Integrate(S(1)/(x**S(2)*sqrt(asin(a*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(6)/asin(a*x)**(S(3)/2), x), x, -S(5)*sqrt(S(10))*sqrt(Pi)*FresnelS(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(32)*a**S(7)) + sqrt(S(14))*sqrt(Pi)*FresnelS(sqrt(S(14))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(32)*a**S(7)) - S(5)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(32)*a**S(7)) + S(9)*sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(32)*a**S(7)) - S(2)*x**S(6)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)/asin(a*x)**(S(3)/2), x), x, S(5)*sqrt(Pi)*FresnelC(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(8)*a**S(6)) - sqrt(S(2))*sqrt(Pi)*FresnelC(S(2)*sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(2)*a**S(6)) + sqrt(S(3))*sqrt(Pi)*FresnelC(S(2)*sqrt(S(3))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(8)*a**S(6)) - S(2)*x**S(5)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asin(a*x)**(S(3)/2), x), x, -sqrt(S(10))*sqrt(Pi)*FresnelS(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(8)*a**S(5)) - sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(4)*a**S(5)) + S(3)*sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(8)*a**S(5)) - S(2)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asin(a*x)**(S(3)/2), x), x, sqrt(Pi)*FresnelC(S(2)*sqrt(asin(a*x))/sqrt(Pi))/a**S(4) - sqrt(S(2))*sqrt(Pi)*FresnelC(S(2)*sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(2)*a**S(4)) - S(2)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asin(a*x)**(S(3)/2), x), x, -sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(2)*a**S(3)) + sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(2)*a**S(3)) - S(2)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asin(a*x)**(S(3)/2), x), x, S(2)*sqrt(Pi)*FresnelC(S(2)*sqrt(asin(a*x))/sqrt(Pi))/a**S(2) - S(2)*x*sqrt(-a**S(2)*x**S(2) + S(1))/(a*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(-3)/2), x), x, -S(2)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/a - S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(a*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asin(a*x)**(S(3)/2)), x), x, Integrate(S(1)/(x*asin(a*x)**(S(3)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asin(a*x)**(S(5)/2), x), x, -S(5)*sqrt(S(10))*sqrt(Pi)*FresnelC(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(12)*a**S(5)) - sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(6)*a**S(5)) + S(3)*sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(4)*a**S(5)) + S(20)*x**S(5)/(S(3)*sqrt(asin(a*x))) - S(2)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**(S(3)/2)) - S(16)*x**S(3)/(S(3)*a**S(2)*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(x**S(4)/asin(a*x)**(S(5)/2), x), x, -S(5)*sqrt(S(10))*sqrt(Pi)*FresnelC(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(12)*a**S(5)) - sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(6)*a**S(5)) + S(3)*sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(4)*a**S(5)) + S(20)*x**S(5)/(S(3)*sqrt(asin(a*x))) - S(2)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**(S(3)/2)) - S(16)*x**S(3)/(S(3)*a**S(2)*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asin(a*x)**(S(5)/2), x), x, -S(4)*sqrt(Pi)*FresnelS(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(3)*a**S(4)) + S(4)*sqrt(S(2))*sqrt(Pi)*FresnelS(S(2)*sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(3)*a**S(4)) + S(16)*x**S(4)/(S(3)*sqrt(asin(a*x))) - S(2)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**(S(3)/2)) - S(4)*x**S(2)/(a**S(2)*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asin(a*x)**(S(5)/2), x), x, -sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(3)*a**S(3)) + sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/a**S(3) + S(4)*x**S(3)/sqrt(asin(a*x)) - S(2)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**(S(3)/2)) - S(8)*x/(S(3)*a**S(2)*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asin(a*x)**(S(5)/2), x), x, -S(8)*sqrt(Pi)*FresnelS(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(3)*a**S(2)) + S(8)*x**S(2)/(S(3)*sqrt(asin(a*x))) - S(2)*x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**(S(3)/2)) - S(4)/(S(3)*a**S(2)*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(-5)/2), x), x, -S(4)*sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(3)*a) + S(4)*x/(S(3)*sqrt(asin(a*x))) - S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*asin(a*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asin(a*x)**(S(5)/2)), x), x, Integrate(S(1)/(x*asin(a*x)**(S(5)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asin(a*x)**(S(7)/2), x), x, S(5)*sqrt(S(10))*sqrt(Pi)*FresnelS(sqrt(S(10))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(6)*a**S(5)) + sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(15)*a**S(5)) - S(9)*sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(10)*a**S(5)) + S(4)*x**S(5)/(S(3)*asin(a*x)**(S(3)/2)) + S(40)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(3)*a*sqrt(asin(a*x))) - S(2)*x**S(4)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(5)*a*asin(a*x)**(S(5)/2)) - S(16)*x**S(3)/(S(15)*a**S(2)*asin(a*x)**(S(3)/2)) - S(32)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(5)*a**S(3)*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asin(a*x)**(S(7)/2), x), x, -S(16)*sqrt(Pi)*FresnelC(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(15)*a**S(4)) + S(32)*sqrt(S(2))*sqrt(Pi)*FresnelC(S(2)*sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(15)*a**S(4)) + S(16)*x**S(4)/(S(15)*asin(a*x)**(S(3)/2)) + S(128)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(15)*a*sqrt(asin(a*x))) - S(2)*x**S(3)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(5)*a*asin(a*x)**(S(5)/2)) - S(4)*x**S(2)/(S(5)*a**S(2)*asin(a*x)**(S(3)/2)) - S(16)*x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(5)*a**S(3)*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asin(a*x)**(S(7)/2), x), x, S(2)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(15)*a**S(3)) - S(6)*sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(5)*a**S(3)) + S(4)*x**S(3)/(S(5)*asin(a*x)**(S(3)/2)) + S(24)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(5)*a*sqrt(asin(a*x))) - S(2)*x**S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(5)*a*asin(a*x)**(S(5)/2)) - S(8)*x/(S(15)*a**S(2)*asin(a*x)**(S(3)/2)) - S(16)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(15)*a**S(3)*sqrt(asin(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asin(a*x)**(S(7)/2), x), x, -S(32)*sqrt(Pi)*FresnelC(S(2)*sqrt(asin(a*x))/sqrt(Pi))/(S(15)*a**S(2)) + S(8)*x**S(2)/(S(15)*asin(a*x)**(S(3)/2)) + S(32)*x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(15)*a*sqrt(asin(a*x))) - S(2)*x*sqrt(-a**S(2)*x**S(2) + S(1))/(S(5)*a*asin(a*x)**(S(5)/2)) - S(4)/(S(15)*a**S(2)*asin(a*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**(S(-7)/2), x), x, S(8)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(S(1)/Pi)*sqrt(asin(a*x)))/(S(15)*a) + S(4)*x/(S(15)*asin(a*x)**(S(3)/2)) + S(8)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(15)*a*sqrt(asin(a*x))) - S(2)*sqrt(-a**S(2)*x**S(2) + S(1))/(S(5)*a*asin(a*x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asin(a*x)**(S(7)/2)), x), x, Integrate(S(1)/(x*asin(a*x)**(S(7)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m*asin(a*x)**S(4), x), x, -S(4)*a*Integrate((b*x)**(m + S(1))*asin(a*x)**S(3)/sqrt(-a**S(2)*x**S(2) + S(1)), x)/(b*(m + S(1))) + (b*x)**(m + S(1))*asin(a*x)**S(4)/(b*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m*asin(a*x)**S(3), x), x, -S(3)*a*Integrate((b*x)**(m + S(1))*asin(a*x)**S(2)/sqrt(-a**S(2)*x**S(2) + S(1)), x)/(b*(m + S(1))) + (b*x)**(m + S(1))*asin(a*x)**S(3)/(b*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m*asin(a*x)**S(2), x), x, S(2)*a**S(2)*(b*x)**(m + S(3))*HypergeometricPFQ(List(S(1), m/S(2) + S(3)/2, m/S(2) + S(3)/2), List(m/S(2) + S(2), m/S(2) + S(5)/2), a**S(2)*x**S(2))/(b**S(3)*(m + S(1))*(m + S(2))*(m + S(3))) - S(2)*a*(b*x)**(m + S(2))*Hypergeometric2F1(S(1)/2, m/S(2) + S(1), m/S(2) + S(2), a**S(2)*x**S(2))*asin(a*x)/(b**S(2)*(m + S(1))*(m + S(2))) + (b*x)**(m + S(1))*asin(a*x)**S(2)/(b*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m*asin(a*x), x), x, -a*(b*x)**(m + S(2))*Hypergeometric2F1(S(1)/2, m/S(2) + S(1), m/S(2) + S(2), a**S(2)*x**S(2))/(b**S(2)*(m + S(1))*(m + S(2))) + (b*x)**(m + S(1))*asin(a*x)/(b*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m/asin(a*x), x), x, Integrate((b*x)**m/asin(a*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m/asin(a*x)**S(2), x), x, Integrate((b*x)**m/asin(a*x)**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m*asin(a*x)**(S(3)/2), x), x, Integrate((b*x)**m*asin(a*x)**(S(3)/2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m*sqrt(asin(a*x)), x), x, Integrate((b*x)**m*sqrt(asin(a*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m/sqrt(asin(a*x)), x), x, Integrate((b*x)**m/sqrt(asin(a*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m/asin(a*x)**(S(3)/2), x), x, Integrate((b*x)**m/asin(a*x)**(S(3)/2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m*asin(a*x)**n, x), x, Integrate((b*x)**m*asin(a*x)**n, x), expand=True, _diff=True, _numerical=True)

    # sympy and mathematicA assert rubi_test(rubi_integrate(x**S(3)*asin(a*x)**n, x), x, S(2)**(-S(2)*n + S(-6))*(-I*asin(a*x))**(-n)*Gamma(n + S(1), -S(4)*I*asin(a*x))*asin(a*x)**n/a**S(4) + S(2)**(-S(2)*n + S(-6))*(I*asin(a*x))**(-n)*Gamma(n + S(1), S(4)*I*asin(a*x))*asin(a*x)**n/a**S(4) - S(2)**(-n + S(-4))*(-I*asin(a*x))**(-n)*Gamma(n + S(1), -S(2)*I*asin(a*x))*asin(a*x)**n/a**S(4) - S(2)**(-n + S(-4))*(I*asin(a*x))**(-n)*Gamma(n + S(1), S(2)*I*asin(a*x))*asin(a*x)**n/a**S(4), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(x**S(2)*asin(a*x)**n, x), x, S(3)**(-n + S(-1))*I*(-I*asin(a*x))**(-n)*Gamma(n + S(1), -S(3)*I*asin(a*x))*asin(a*x)**n/(S(8)*a**S(3)) - S(3)**(-n + S(-1))*I*(I*asin(a*x))**(-n)*Gamma(n + S(1), S(3)*I*asin(a*x))*asin(a*x)**n/(S(8)*a**S(3)) - I*(-I*asin(a*x))**(-n)*Gamma(n + S(1), -I*asin(a*x))*asin(a*x)**n/(S(8)*a**S(3)) + I*(I*asin(a*x))**(-n)*Gamma(n + S(1), I*asin(a*x))*asin(a*x)**n/(S(8)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asin(a*x)**n, x), x, -S(2)**(-n + S(-3))*(-I*asin(a*x))**(-n)*Gamma(n + S(1), -S(2)*I*asin(a*x))*asin(a*x)**n/a**S(2) - S(2)**(-n + S(-3))*(I*asin(a*x))**(-n)*Gamma(n + S(1), S(2)*I*asin(a*x))*asin(a*x)**n/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**n, x), x, -I*(-I*asin(a*x))**(-n)*Gamma(n + S(1), -I*asin(a*x))*asin(a*x)**n/(S(2)*a) + I*(I*asin(a*x))**(-n)*Gamma(n + S(1), I*asin(a*x))*asin(a*x)**n/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**n/x, x), x, Integrate(asin(a*x)**n/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**n/x**S(2), x), x, Integrate(asin(a*x)**n/x**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**(S(3)/2)*asin(a*x)**n, x), x, Integrate((b*x)**(S(3)/2)*asin(a*x)**n, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*x)*asin(a*x)**n, x), x, Integrate(sqrt(b*x)*asin(a*x)**n, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**n/sqrt(b*x), x), x, Integrate(asin(a*x)**n/sqrt(b*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asin(a*x)**n/(b*x)**(S(3)/2), x), x, Integrate(asin(a*x)**n/(b*x)**(S(3)/2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*(a + b*asin(c*x)), x), x, b*x**S(3)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(16)*c) + S(3)*b*x*sqrt(-c**S(2)*x**S(2) + S(1))/(S(32)*c**S(3)) - S(3)*b*asin(c*x)/(S(32)*c**S(4)) + x**S(4)*(a + b*asin(c*x))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(a + b*asin(c*x)), x), x, -b*(-c**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(9)*c**S(3)) + b*sqrt(-c**S(2)*x**S(2) + S(1))/(S(3)*c**S(3)) + x**S(3)*(a + b*asin(c*x))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(a + b*asin(c*x)), x), x, b*x*sqrt(-c**S(2)*x**S(2) + S(1))/(S(4)*c) - b*asin(c*x)/(S(4)*c**S(2)) + x**S(2)*(a + b*asin(c*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a + b*asin(c*x), x), x, a*x + b*x*asin(c*x) + b*sqrt(-c**S(2)*x**S(2) + S(1))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))/x, x), x, -I*b*PolyLog(S(2), exp(S(2)*I*asin(c*x)))/S(2) + (a + b*asin(c*x))*log(-exp(S(2)*I*asin(c*x)) + S(1)) - I*(a + b*asin(c*x))**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))/x**S(2), x), x, -b*c*atanh(sqrt(-c**S(2)*x**S(2) + S(1))) - (a + b*asin(c*x))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))/x**S(3), x), x, -b*c*sqrt(-c**S(2)*x**S(2) + S(1))/(S(2)*x) - (a + b*asin(c*x))/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))/x**S(4), x), x, -b*c**S(3)*atanh(sqrt(-c**S(2)*x**S(2) + S(1)))/S(6) - b*c*sqrt(-c**S(2)*x**S(2) + S(1))/(S(6)*x**S(2)) - (a + b*asin(c*x))/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(a + b*asin(c*x))**S(2), x), x, -S(2)*b**S(2)*x**S(3)/S(27) - S(4)*b**S(2)*x/(S(9)*c**S(2)) + S(2)*b*x**S(2)*(a + b*asin(c*x))*sqrt(-c**S(2)*x**S(2) + S(1))/(S(9)*c) + S(4)*b*(a + b*asin(c*x))*sqrt(-c**S(2)*x**S(2) + S(1))/(S(9)*c**S(3)) + x**S(3)*(a + b*asin(c*x))**S(2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(a + b*asin(c*x))**S(2), x), x, -b**S(2)*x**S(2)/S(4) + b*x*(a + b*asin(c*x))*sqrt(-c**S(2)*x**S(2) + S(1))/(S(2)*c) + x**S(2)*(a + b*asin(c*x))**S(2)/S(2) - (a + b*asin(c*x))**S(2)/(S(4)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(2), x), x, -S(2)*b**S(2)*x + S(2)*b*(a + b*asin(c*x))*sqrt(-c**S(2)*x**S(2) + S(1))/c + x*(a + b*asin(c*x))**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(2)/x, x), x, b**S(2)*PolyLog(S(3), exp(S(2)*I*asin(c*x)))/S(2) - I*b*(a + b*asin(c*x))*PolyLog(S(2), exp(S(2)*I*asin(c*x))) + (a + b*asin(c*x))**S(2)*log(-exp(S(2)*I*asin(c*x)) + S(1)) - I*(a + b*asin(c*x))**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(2)/x**S(2), x), x, S(2)*I*b**S(2)*c*PolyLog(S(2), -exp(I*asin(c*x))) - S(2)*I*b**S(2)*c*PolyLog(S(2), exp(I*asin(c*x))) - S(4)*b*c*(a + b*asin(c*x))*atanh(exp(I*asin(c*x))) - (a + b*asin(c*x))**S(2)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(a + b*asin(c*x))**S(3), x), x, -S(4)*a*b**S(2)*x/(S(3)*c**S(2)) - S(4)*b**S(3)*x*asin(c*x)/(S(3)*c**S(2)) + S(2)*b**S(3)*(-c**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(27)*c**S(3)) - S(14)*b**S(3)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(9)*c**S(3)) - S(2)*b**S(2)*x**S(3)*(a + b*asin(c*x))/S(9) + b*x**S(2)*(a + b*asin(c*x))**S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(3)*c) + S(2)*b*(a + b*asin(c*x))**S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(3)*c**S(3)) + x**S(3)*(a + b*asin(c*x))**S(3)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(a + b*asin(c*x))**S(3), x), x, -S(3)*b**S(3)*x*sqrt(-c**S(2)*x**S(2) + S(1))/(S(8)*c) + S(3)*b**S(3)*asin(c*x)/(S(8)*c**S(2)) - S(3)*b**S(2)*x**S(2)*(a + b*asin(c*x))/S(4) + S(3)*b*x*(a + b*asin(c*x))**S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(4)*c) + x**S(2)*(a + b*asin(c*x))**S(3)/S(2) - (a + b*asin(c*x))**S(3)/(S(4)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(3), x), x, -S(6)*a*b**S(2)*x - S(6)*b**S(3)*x*asin(c*x) - S(6)*b**S(3)*sqrt(-c**S(2)*x**S(2) + S(1))/c + S(3)*b*(a + b*asin(c*x))**S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/c + x*(a + b*asin(c*x))**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(3)/x, x), x, S(3)*I*b**S(3)*PolyLog(S(4), exp(S(2)*I*asin(c*x)))/S(4) + S(3)*b**S(2)*(a + b*asin(c*x))*PolyLog(S(3), exp(S(2)*I*asin(c*x)))/S(2) - S(3)*I*b*(a + b*asin(c*x))**S(2)*PolyLog(S(2), exp(S(2)*I*asin(c*x)))/S(2) + (a + b*asin(c*x))**S(3)*log(-exp(S(2)*I*asin(c*x)) + S(1)) - I*(a + b*asin(c*x))**S(4)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(3)/x**S(2), x), x, -S(6)*b**S(3)*c*PolyLog(S(3), -exp(I*asin(c*x))) + S(6)*b**S(3)*c*PolyLog(S(3), exp(I*asin(c*x))) + S(6)*I*b**S(2)*c*(a + b*asin(c*x))*PolyLog(S(2), -exp(I*asin(c*x))) - S(6)*I*b**S(2)*c*(a + b*asin(c*x))*PolyLog(S(2), exp(I*asin(c*x))) - S(6)*b*c*(a + b*asin(c*x))**S(2)*atanh(exp(I*asin(c*x))) - (a + b*asin(c*x))**S(3)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*asin(c*x)), x), x, CosIntegral(a/b + asin(c*x))*cos(a/b)/(S(4)*b*c**S(3)) - CosIntegral(S(3)*a/b + S(3)*asin(c*x))*cos(S(3)*a/b)/(S(4)*b*c**S(3)) + SinIntegral(a/b + asin(c*x))*sin(a/b)/(S(4)*b*c**S(3)) - SinIntegral(S(3)*a/b + S(3)*asin(c*x))*sin(S(3)*a/b)/(S(4)*b*c**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*asin(c*x)), x), x, -CosIntegral(S(2)*a/b + S(2)*asin(c*x))*sin(S(2)*a/b)/(S(2)*b*c**S(2)) + SinIntegral(S(2)*a/b + S(2)*asin(c*x))*cos(S(2)*a/b)/(S(2)*b*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*asin(c*x)), x), x, CosIntegral((a + b*asin(c*x))/b)*cos(a/b)/(b*c) + SinIntegral((a + b*asin(c*x))/b)*sin(a/b)/(b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(a + b*asin(c*x))), x), x, Integrate(S(1)/(x*(a + b*asin(c*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*(a + b*asin(c*x))), x), x, Integrate(S(1)/(x**S(2)*(a + b*asin(c*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*asin(c*x))**S(2), x), x, -x**S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/(b*c*(a + b*asin(c*x))) + CosIntegral(a/b + asin(c*x))*sin(a/b)/(S(4)*b**S(2)*c**S(3)) - S(3)*CosIntegral(S(3)*a/b + S(3)*asin(c*x))*sin(S(3)*a/b)/(S(4)*b**S(2)*c**S(3)) - SinIntegral(a/b + asin(c*x))*cos(a/b)/(S(4)*b**S(2)*c**S(3)) + S(3)*SinIntegral(S(3)*a/b + S(3)*asin(c*x))*cos(S(3)*a/b)/(S(4)*b**S(2)*c**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*asin(c*x))**S(2), x), x, -x*sqrt(-c**S(2)*x**S(2) + S(1))/(b*c*(a + b*asin(c*x))) + CosIntegral(S(2)*a/b + S(2)*asin(c*x))*cos(S(2)*a/b)/(b**S(2)*c**S(2)) + SinIntegral(S(2)*a/b + S(2)*asin(c*x))*sin(S(2)*a/b)/(b**S(2)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(-2)), x), x, -sqrt(-c**S(2)*x**S(2) + S(1))/(b*c*(a + b*asin(c*x))) + CosIntegral(a/b + asin(c*x))*sin(a/b)/(b**S(2)*c) - SinIntegral(a/b + asin(c*x))*cos(a/b)/(b**S(2)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(a + b*asin(c*x))**S(2)), x), x, Integrate(S(1)/(x*(a + b*asin(c*x))**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*(a + b*asin(c*x))**S(2)), x), x, Integrate(S(1)/(x**S(2)*(a + b*asin(c*x))**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*asin(c*x))**S(3), x), x, -x**S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(2)*b*c*(a + b*asin(c*x))**S(2)) + S(3)*x**S(3)/(S(2)*b**S(2)*(a + b*asin(c*x))) - x/(b**S(2)*c**S(2)*(a + b*asin(c*x))) + CosIntegral((a + b*asin(c*x))/b)*cos(a/b)/(b**S(3)*c**S(3)) - S(9)*CosIntegral(a/b + asin(c*x))*cos(a/b)/(S(8)*b**S(3)*c**S(3)) + S(9)*CosIntegral(S(3)*a/b + S(3)*asin(c*x))*cos(S(3)*a/b)/(S(8)*b**S(3)*c**S(3)) + SinIntegral((a + b*asin(c*x))/b)*sin(a/b)/(b**S(3)*c**S(3)) - S(9)*SinIntegral(a/b + asin(c*x))*sin(a/b)/(S(8)*b**S(3)*c**S(3)) + S(9)*SinIntegral(S(3)*a/b + S(3)*asin(c*x))*sin(S(3)*a/b)/(S(8)*b**S(3)*c**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*asin(c*x))**S(3), x), x, -x*sqrt(-c**S(2)*x**S(2) + S(1))/(S(2)*b*c*(a + b*asin(c*x))**S(2)) + x**S(2)/(b**S(2)*(a + b*asin(c*x))) - S(1)/(S(2)*b**S(2)*c**S(2)*(a + b*asin(c*x))) + CosIntegral(S(2)*a/b + S(2)*asin(c*x))*sin(S(2)*a/b)/(b**S(3)*c**S(2)) - SinIntegral(S(2)*a/b + S(2)*asin(c*x))*cos(S(2)*a/b)/(b**S(3)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(-3)), x), x, -sqrt(-c**S(2)*x**S(2) + S(1))/(S(2)*b*c*(a + b*asin(c*x))**S(2)) + x/(S(2)*b**S(2)*(a + b*asin(c*x))) - CosIntegral((a + b*asin(c*x))/b)*cos(a/b)/(S(2)*b**S(3)*c) - SinIntegral((a + b*asin(c*x))/b)*sin(a/b)/(S(2)*b**S(3)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(a + b*asin(c*x))**S(3)), x), x, Integrate(S(1)/(x*(a + b*asin(c*x))**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*(a + b*asin(c*x))**S(3)), x), x, Integrate(S(1)/(x**S(2)*(a + b*asin(c*x))**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*sqrt(a + b*asin(c*x)), x), x, sqrt(S(2))*sqrt(Pi)*sqrt(b)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(8)*c**S(3)) - sqrt(S(6))*sqrt(Pi)*sqrt(b)*FresnelC(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(S(3)*a/b)/(S(72)*c**S(3)) - sqrt(S(2))*sqrt(Pi)*sqrt(b)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(8)*c**S(3)) + sqrt(S(6))*sqrt(Pi)*sqrt(b)*FresnelS(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(S(3)*a/b)/(S(72)*c**S(3)) + x**S(3)*sqrt(a + b*asin(c*x))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*sqrt(a + b*asin(c*x)), x), x, sqrt(Pi)*sqrt(b)*FresnelC(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*cos(S(2)*a/b)/(S(8)*c**S(2)) + sqrt(Pi)*sqrt(b)*FresnelS(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*sin(S(2)*a/b)/(S(8)*c**S(2)) + x**S(2)*sqrt(a + b*asin(c*x))/S(2) - sqrt(a + b*asin(c*x))/(S(4)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*asin(c*x)), x), x, sqrt(S(2))*sqrt(Pi)*sqrt(b)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(2)*c) - sqrt(S(2))*sqrt(Pi)*sqrt(b)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(2)*c) + x*sqrt(a + b*asin(c*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*asin(c*x))/x, x), x, Integrate(sqrt(a + b*asin(c*x))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*asin(c*x))/x**S(2), x), x, Integrate(sqrt(a + b*asin(c*x))/x**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(a + b*asin(c*x))**(S(3)/2), x), x, -S(3)*sqrt(S(2))*sqrt(Pi)*b**(S(3)/2)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(16)*c**S(3)) + sqrt(S(6))*sqrt(Pi)*b**(S(3)/2)*FresnelC(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(S(3)*a/b)/(S(144)*c**S(3)) - S(3)*sqrt(S(2))*sqrt(Pi)*b**(S(3)/2)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(16)*c**S(3)) + sqrt(S(6))*sqrt(Pi)*b**(S(3)/2)*FresnelS(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(S(3)*a/b)/(S(144)*c**S(3)) + b*x**S(2)*sqrt(a + b*asin(c*x))*sqrt(-c**S(2)*x**S(2) + S(1))/(S(6)*c) + b*sqrt(a + b*asin(c*x))*sqrt(-c**S(2)*x**S(2) + S(1))/(S(3)*c**S(3)) + x**S(3)*(a + b*asin(c*x))**(S(3)/2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(a + b*asin(c*x))**(S(3)/2), x), x, S(3)*sqrt(Pi)*b**(S(3)/2)*FresnelC(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*sin(S(2)*a/b)/(S(32)*c**S(2)) - S(3)*sqrt(Pi)*b**(S(3)/2)*FresnelS(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*cos(S(2)*a/b)/(S(32)*c**S(2)) + S(3)*b*x*sqrt(a + b*asin(c*x))*sqrt(-c**S(2)*x**S(2) + S(1))/(S(8)*c) + x**S(2)*(a + b*asin(c*x))**(S(3)/2)/S(2) - (a + b*asin(c*x))**(S(3)/2)/(S(4)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(3)/2), x), x, -S(3)*sqrt(S(2))*sqrt(Pi)*b**(S(3)/2)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(4)*c) - S(3)*sqrt(S(2))*sqrt(Pi)*b**(S(3)/2)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(4)*c) + S(3)*b*sqrt(a + b*asin(c*x))*sqrt(-c**S(2)*x**S(2) + S(1))/(S(2)*c) + x*(a + b*asin(c*x))**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(3)/2)/x, x), x, Integrate((a + b*asin(c*x))**(S(3)/2)/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(3)/2)/x**S(2), x), x, Integrate((a + b*asin(c*x))**(S(3)/2)/x**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(a + b*asin(c*x))**(S(5)/2), x), x, -S(15)*sqrt(S(2))*sqrt(Pi)*b**(S(5)/2)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(32)*c**S(3)) + S(5)*sqrt(S(6))*sqrt(Pi)*b**(S(5)/2)*FresnelC(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(S(3)*a/b)/(S(864)*c**S(3)) + S(15)*sqrt(S(2))*sqrt(Pi)*b**(S(5)/2)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(32)*c**S(3)) - S(5)*sqrt(S(6))*sqrt(Pi)*b**(S(5)/2)*FresnelS(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(S(3)*a/b)/(S(864)*c**S(3)) - S(5)*b**S(2)*x**S(3)*sqrt(a + b*asin(c*x))/S(36) - S(5)*b**S(2)*x*sqrt(a + b*asin(c*x))/(S(6)*c**S(2)) + S(5)*b*x**S(2)*(a + b*asin(c*x))**(S(3)/2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(18)*c) + S(5)*b*(a + b*asin(c*x))**(S(3)/2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(9)*c**S(3)) + x**S(3)*(a + b*asin(c*x))**(S(5)/2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(a + b*asin(c*x))**(S(5)/2), x), x, -S(15)*sqrt(Pi)*b**(S(5)/2)*FresnelC(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*cos(S(2)*a/b)/(S(128)*c**S(2)) - S(15)*sqrt(Pi)*b**(S(5)/2)*FresnelS(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*sin(S(2)*a/b)/(S(128)*c**S(2)) - S(15)*b**S(2)*x**S(2)*sqrt(a + b*asin(c*x))/S(32) + S(15)*b**S(2)*sqrt(a + b*asin(c*x))/(S(64)*c**S(2)) + S(5)*b*x*(a + b*asin(c*x))**(S(3)/2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(8)*c) + x**S(2)*(a + b*asin(c*x))**(S(5)/2)/S(2) - (a + b*asin(c*x))**(S(5)/2)/(S(4)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(5)/2), x), x, -S(15)*sqrt(S(2))*sqrt(Pi)*b**(S(5)/2)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(8)*c) + S(15)*sqrt(S(2))*sqrt(Pi)*b**(S(5)/2)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(8)*c) - S(15)*b**S(2)*x*sqrt(a + b*asin(c*x))/S(4) + S(5)*b*(a + b*asin(c*x))**(S(3)/2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(2)*c) + x*(a + b*asin(c*x))**(S(5)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(5)/2)/x, x), x, Integrate((a + b*asin(c*x))**(S(5)/2)/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(5)/2)/x**S(2), x), x, Integrate((a + b*asin(c*x))**(S(5)/2)/x**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/sqrt(a + b*asin(c*x)), x), x, sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(4)*sqrt(b)*c**S(3)) - sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(S(3)*a/b)/(S(12)*sqrt(b)*c**S(3)) + sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(4)*sqrt(b)*c**S(3)) - sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(S(3)*a/b)/(S(12)*sqrt(b)*c**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/sqrt(a + b*asin(c*x)), x), x, -sqrt(Pi)*FresnelC(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*sin(S(2)*a/b)/(S(2)*sqrt(b)*c**S(2)) + sqrt(Pi)*FresnelS(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*cos(S(2)*a/b)/(S(2)*sqrt(b)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(a + b*asin(c*x)), x), x, sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(sqrt(b)*c) + sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(sqrt(b)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(a + b*asin(c*x))), x), x, Integrate(S(1)/(x*sqrt(a + b*asin(c*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*sqrt(a + b*asin(c*x))), x), x, Integrate(S(1)/(x**S(2)*sqrt(a + b*asin(c*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*asin(c*x))**(S(3)/2), x), x, sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(2)*b**(S(3)/2)*c**S(3)) - sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(S(3)*a/b)/(S(2)*b**(S(3)/2)*c**S(3)) - sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(2)*b**(S(3)/2)*c**S(3)) + sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(S(3)*a/b)/(S(2)*b**(S(3)/2)*c**S(3)) - S(2)*x**S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/(b*c*sqrt(a + b*asin(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*asin(c*x))**(S(3)/2), x), x, S(2)*sqrt(Pi)*FresnelC(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*cos(S(2)*a/b)/(b**(S(3)/2)*c**S(2)) + S(2)*sqrt(Pi)*FresnelS(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*sin(S(2)*a/b)/(b**(S(3)/2)*c**S(2)) - S(2)*x*sqrt(-c**S(2)*x**S(2) + S(1))/(b*c*sqrt(a + b*asin(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(-3)/2), x), x, S(2)*sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(b**(S(3)/2)*c) - S(2)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(b**(S(3)/2)*c) - S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/(b*c*sqrt(a + b*asin(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(a + b*asin(c*x))**(S(3)/2)), x), x, Integrate(S(1)/(x*(a + b*asin(c*x))**(S(3)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*(a + b*asin(c*x))**(S(3)/2)), x), x, Integrate(S(1)/(x**S(2)*(a + b*asin(c*x))**(S(3)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*asin(c*x))**(S(5)/2), x), x, -sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(3)*b**(S(5)/2)*c**S(3)) + sqrt(S(6))*sqrt(Pi)*FresnelC(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(S(3)*a/b)/(b**(S(5)/2)*c**S(3)) - sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(3)*b**(S(5)/2)*c**S(3)) + sqrt(S(6))*sqrt(Pi)*FresnelS(sqrt(S(6))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(S(3)*a/b)/(b**(S(5)/2)*c**S(3)) - S(2)*x**S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(3)*b*c*(a + b*asin(c*x))**(S(3)/2)) + S(4)*x**S(3)/(b**S(2)*sqrt(a + b*asin(c*x))) - S(8)*x/(S(3)*b**S(2)*c**S(2)*sqrt(a + b*asin(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*asin(c*x))**(S(5)/2), x), x, S(8)*sqrt(Pi)*FresnelC(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*sin(S(2)*a/b)/(S(3)*b**(S(5)/2)*c**S(2)) - S(8)*sqrt(Pi)*FresnelS(S(2)*sqrt(a + b*asin(c*x))/(sqrt(Pi)*sqrt(b)))*cos(S(2)*a/b)/(S(3)*b**(S(5)/2)*c**S(2)) - S(2)*x*sqrt(-c**S(2)*x**S(2) + S(1))/(S(3)*b*c*(a + b*asin(c*x))**(S(3)/2)) + S(8)*x**S(2)/(S(3)*b**S(2)*sqrt(a + b*asin(c*x))) - S(4)/(S(3)*b**S(2)*c**S(2)*sqrt(a + b*asin(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**(S(-5)/2), x), x, -S(4)*sqrt(S(2))*sqrt(Pi)*FresnelC(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*cos(a/b)/(S(3)*b**(S(5)/2)*c) - S(4)*sqrt(S(2))*sqrt(Pi)*FresnelS(sqrt(S(2))*sqrt(a + b*asin(c*x))*sqrt(S(1)/Pi)/sqrt(b))*sin(a/b)/(S(3)*b**(S(5)/2)*c) - S(2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(3)*b*c*(a + b*asin(c*x))**(S(3)/2)) + S(4)*x/(S(3)*b**S(2)*sqrt(a + b*asin(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(a + b*asin(c*x))**(S(5)/2)), x), x, Integrate(S(1)/(x*(a + b*asin(c*x))**(S(5)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*(a + b*asin(c*x))**(S(5)/2)), x), x, Integrate(S(1)/(x**S(2)*(a + b*asin(c*x))**(S(5)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*x)**(S(5)/2)*(a + b*asin(c*x)), x), x, S(4)*b*(d*x)**(S(5)/2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(49)*c) + S(20)*b*d**S(2)*sqrt(d*x)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(147)*c**S(3)) - S(20)*b*d**(S(5)/2)*EllipticF(asin(sqrt(c)*sqrt(d*x)/sqrt(d)), S(-1))/(S(147)*c**(S(7)/2)) + S(2)*(d*x)**(S(7)/2)*(a + b*asin(c*x))/(S(7)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*x)**(S(3)/2)*(a + b*asin(c*x)), x), x, S(4)*b*(d*x)**(S(3)/2)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(25)*c) - S(12)*b*d**(S(3)/2)*EllipticE(asin(sqrt(c)*sqrt(d*x)/sqrt(d)), S(-1))/(S(25)*c**(S(5)/2)) + S(12)*b*d**(S(3)/2)*EllipticF(asin(sqrt(c)*sqrt(d*x)/sqrt(d)), S(-1))/(S(25)*c**(S(5)/2)) + S(2)*(d*x)**(S(5)/2)*(a + b*asin(c*x))/(S(5)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*x)*(a + b*asin(c*x)), x), x, S(4)*b*sqrt(d*x)*sqrt(-c**S(2)*x**S(2) + S(1))/(S(9)*c) - S(4)*b*sqrt(d)*EllipticF(asin(sqrt(c)*sqrt(d*x)/sqrt(d)), S(-1))/(S(9)*c**(S(3)/2)) + S(2)*(d*x)**(S(3)/2)*(a + b*asin(c*x))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))/sqrt(d*x), x), x, -S(4)*b*EllipticE(asin(sqrt(c)*sqrt(d*x)/sqrt(d)), S(-1))/(sqrt(c)*sqrt(d)) + S(4)*b*EllipticF(asin(sqrt(c)*sqrt(d*x)/sqrt(d)), S(-1))/(sqrt(c)*sqrt(d)) + S(2)*sqrt(d*x)*(a + b*asin(c*x))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))/(d*x)**(S(3)/2), x), x, S(4)*b*sqrt(c)*EllipticF(asin(sqrt(c)*sqrt(d*x)/sqrt(d)), S(-1))/d**(S(3)/2) - (S(2)*a + S(2)*b*asin(c*x))/(d*sqrt(d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))/(d*x)**(S(5)/2), x), x, -S(4)*b*c**(S(3)/2)*EllipticE(asin(sqrt(c)*sqrt(d*x)/sqrt(d)), S(-1))/(S(3)*d**(S(5)/2)) + S(4)*b*c**(S(3)/2)*EllipticF(asin(sqrt(c)*sqrt(d*x)/sqrt(d)), S(-1))/(S(3)*d**(S(5)/2)) - S(4)*b*c*sqrt(-c**S(2)*x**S(2) + S(1))/(S(3)*d**S(2)*sqrt(d*x)) - (S(2)*a + S(2)*b*asin(c*x))/(S(3)*d*(d*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*x)**(S(5)/2)*(a + b*asin(c*x))**S(2), x), x, S(16)*b**S(2)*c**S(2)*(d*x)**(S(11)/2)*HypergeometricPFQ(List(S(1), S(11)/4, S(11)/4), List(S(13)/4, S(15)/4), c**S(2)*x**S(2))/(S(693)*d**S(3)) - S(8)*b*c*(d*x)**(S(9)/2)*(a + b*asin(c*x))*Hypergeometric2F1(S(1)/2, S(9)/4, S(13)/4, c**S(2)*x**S(2))/(S(63)*d**S(2)) + S(2)*(d*x)**(S(7)/2)*(a + b*asin(c*x))**S(2)/(S(7)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*x)**(S(3)/2)*(a + b*asin(c*x))**S(2), x), x, S(16)*b**S(2)*c**S(2)*(d*x)**(S(9)/2)*HypergeometricPFQ(List(S(1), S(9)/4, S(9)/4), List(S(11)/4, S(13)/4), c**S(2)*x**S(2))/(S(315)*d**S(3)) - S(8)*b*c*(d*x)**(S(7)/2)*(a + b*asin(c*x))*Hypergeometric2F1(S(1)/2, S(7)/4, S(11)/4, c**S(2)*x**S(2))/(S(35)*d**S(2)) + S(2)*(d*x)**(S(5)/2)*(a + b*asin(c*x))**S(2)/(S(5)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*x)*(a + b*asin(c*x))**S(2), x), x, S(16)*b**S(2)*c**S(2)*(d*x)**(S(7)/2)*HypergeometricPFQ(List(S(1), S(7)/4, S(7)/4), List(S(9)/4, S(11)/4), c**S(2)*x**S(2))/(S(105)*d**S(3)) - S(8)*b*c*(d*x)**(S(5)/2)*(a + b*asin(c*x))*Hypergeometric2F1(S(1)/2, S(5)/4, S(9)/4, c**S(2)*x**S(2))/(S(15)*d**S(2)) + S(2)*(d*x)**(S(3)/2)*(a + b*asin(c*x))**S(2)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(2)/sqrt(d*x), x), x, S(16)*b**S(2)*c**S(2)*(d*x)**(S(5)/2)*HypergeometricPFQ(List(S(1), S(5)/4, S(5)/4), List(S(7)/4, S(9)/4), c**S(2)*x**S(2))/(S(15)*d**S(3)) - S(8)*b*c*(d*x)**(S(3)/2)*(a + b*asin(c*x))*Hypergeometric2F1(S(1)/2, S(3)/4, S(7)/4, c**S(2)*x**S(2))/(S(3)*d**S(2)) + S(2)*sqrt(d*x)*(a + b*asin(c*x))**S(2)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(2)/(d*x)**(S(3)/2), x), x, -S(16)*b**S(2)*c**S(2)*(d*x)**(S(3)/2)*HypergeometricPFQ(List(S(3)/4, S(3)/4, S(1)), List(S(5)/4, S(7)/4), c**S(2)*x**S(2))/(S(3)*d**S(3)) + S(8)*b*c*sqrt(d*x)*(a + b*asin(c*x))*Hypergeometric2F1(S(1)/4, S(1)/2, S(5)/4, c**S(2)*x**S(2))/d**S(2) - S(2)*(a + b*asin(c*x))**S(2)/(d*sqrt(d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(2)/(d*x)**(S(5)/2), x), x, S(16)*b**S(2)*c**S(2)*sqrt(d*x)*HypergeometricPFQ(List(S(1)/4, S(1)/4, S(1)), List(S(3)/4, S(5)/4), c**S(2)*x**S(2))/(S(3)*d**S(3)) - S(8)*b*c*(a + b*asin(c*x))*Hypergeometric2F1(S(-1)/4, S(1)/2, S(3)/4, c**S(2)*x**S(2))/(S(3)*d**S(2)*sqrt(d*x)) - S(2)*(a + b*asin(c*x))**S(2)/(S(3)*d*(d*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*x)**(S(3)/2)*(a + b*asin(c*x))**S(3), x), x, -S(6)*b*c*Integrate((d*x)**(S(5)/2)*(a + b*asin(c*x))**S(2)/sqrt(-c**S(2)*x**S(2) + S(1)), x)/(S(5)*d) + S(2)*(d*x)**(S(5)/2)*(a + b*asin(c*x))**S(3)/(S(5)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*x)*(a + b*asin(c*x))**S(3), x), x, -S(2)*b*c*Integrate((d*x)**(S(3)/2)*(a + b*asin(c*x))**S(2)/sqrt(-c**S(2)*x**S(2) + S(1)), x)/d + S(2)*(d*x)**(S(3)/2)*(a + b*asin(c*x))**S(3)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(3)/sqrt(d*x), x), x, -S(6)*b*c*Integrate(sqrt(d*x)*(a + b*asin(c*x))**S(2)/sqrt(-c**S(2)*x**S(2) + S(1)), x)/d + S(2)*sqrt(d*x)*(a + b*asin(c*x))**S(3)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(3)/(d*x)**(S(3)/2), x), x, S(6)*b*c*Integrate((a + b*asin(c*x))**S(2)/(sqrt(d*x)*sqrt(-c**S(2)*x**S(2) + S(1))), x)/d - S(2)*(a + b*asin(c*x))**S(3)/(d*sqrt(d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asin(c*x))**S(3)/(d*x)**(S(5)/2), x), x, S(2)*b*c*Integrate((a + b*asin(c*x))**S(2)/((d*x)**(S(3)/2)*sqrt(-c**S(2)*x**S(2) + S(1))), x)/d - S(2)*(a + b*asin(c*x))**S(3)/(S(3)*d*(d*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*x)**(S(3)/2)/(a + b*asin(c*x)), x), x, Integrate((d*x)**(S(3)/2)/(a + b*asin(c*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*x)/(a + b*asin(c*x)), x), x, Integrate(sqrt(d*x)/(a + b*asin(c*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(d*x)*(a + b*asin(c*x))), x), x, Integrate(S(1)/(sqrt(d*x)*(a + b*asin(c*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((d*x)**(S(3)/2)*(a + b*asin(c*x))), x), x, Integrate(S(1)/((d*x)**(S(3)/2)*(a + b*asin(c*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*x)**(S(3)/2)/(a + b*asin(c*x))**S(2), x), x, Integrate((d*x)**(S(3)/2)/(a + b*asin(c*x))**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*x)/(a + b*asin(c*x))**S(2), x), x, Integrate(sqrt(d*x)/(a + b*asin(c*x))**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(d*x)*(a + b*asin(c*x))**S(2)), x), x, Integrate(S(1)/(sqrt(d*x)*(a + b*asin(c*x))**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((d*x)**(S(3)/2)*(a + b*asin(c*x))**S(2)), x), x, Integrate(S(1)/((d*x)**(S(3)/2)*(a + b*asin(c*x))**S(2)), x), expand=True, _diff=True, _numerical=True)
