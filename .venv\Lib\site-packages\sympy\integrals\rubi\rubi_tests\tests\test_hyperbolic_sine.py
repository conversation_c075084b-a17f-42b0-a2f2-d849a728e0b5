import sys
from sympy.external import import_module
matchpy = import_module("matchpy")

if not matchpy:
    #bin/test will not execute any tests now
    disabled = True

if sys.version_info[:2] < (3, 6):
    disabled = True

from sympy.integrals.rubi.utility_function import (
        sympy_op_factory, Int, Sum, Set, With, Module, Scan, MapAnd, FalseQ,
        ZeroQ, NegativeQ, NonzeroQ, FreeQ, NFreeQ, List, Log, PositiveQ,
        PositiveIntegerQ, NegativeIntegerQ, IntegerQ, IntegersQ,
        ComplexNumberQ, PureComplexNumberQ, RealNumericQ, PositiveOrZeroQ,
        NegativeOrZeroQ, FractionOrNegativeQ, NegQ, Equal, Unequal, IntPart,
        FracPart, RationalQ, ProductQ, SumQ, NonsumQ, Subst, First, Rest,
        SqrtNumberQ, SqrtNumberSumQ, LinearQ, Sqrt, ArcCosh, Coefficient,
        Denominator, Hypergeometric2F1, Not, Simplify, FractionalPart,
        IntegerPart, AppellF1, EllipticPi, EllipticE, EllipticF, ArcTan,
        ArcCot, ArcCoth, ArcTanh, ArcSin, ArcSinh, ArcCos, ArcCsc, ArcSec,
        ArcCsch, ArcSech, Sinh, Tanh, Cosh, Sech, Csch, Coth, LessEqual, Less,
        Greater, GreaterEqual, FractionQ, IntLinearcQ, Expand, IndependentQ,
        PowerQ, IntegerPowerQ, PositiveIntegerPowerQ, FractionalPowerQ, AtomQ,
        ExpQ, LogQ, Head, MemberQ, TrigQ, SinQ, CosQ, TanQ, CotQ, SecQ, CscQ,
        Sin, Cos, Tan, Cot, Sec, Csc, HyperbolicQ, SinhQ, CoshQ, TanhQ, CothQ,
        SechQ, CschQ, InverseTrigQ, SinCosQ, SinhCoshQ, LeafCount, Numerator,
        NumberQ, NumericQ, Length, ListQ, Im, Re, InverseHyperbolicQ,
        InverseFunctionQ, TrigHyperbolicFreeQ, InverseFunctionFreeQ, RealQ,
        EqQ, FractionalPowerFreeQ, ComplexFreeQ, PolynomialQ, FactorSquareFree,
        PowerOfLinearQ, Exponent, QuadraticQ, LinearPairQ, BinomialParts,
        TrinomialParts, PolyQ, EvenQ, OddQ, PerfectSquareQ, NiceSqrtAuxQ,
        NiceSqrtQ, Together, PosAux, PosQ, CoefficientList, ReplaceAll,
        ExpandLinearProduct, GCD, ContentFactor, NumericFactor,
        NonnumericFactors, MakeAssocList, GensymSubst, KernelSubst,
        ExpandExpression, Apart, SmartApart, MatchQ,
        PolynomialQuotientRemainder, FreeFactors, NonfreeFactors,
        RemoveContentAux, RemoveContent, FreeTerms, NonfreeTerms,
        ExpandAlgebraicFunction, CollectReciprocals, ExpandCleanup,
        AlgebraicFunctionQ, Coeff, LeadTerm, RemainingTerms, LeadFactor,
        RemainingFactors, LeadBase, LeadDegree, Numer, Denom, hypergeom, Expon,
        MergeMonomials, PolynomialDivide, BinomialQ, TrinomialQ,
        GeneralizedBinomialQ, GeneralizedTrinomialQ, FactorSquareFreeList,
        PerfectPowerTest, SquareFreeFactorTest, RationalFunctionQ,
        RationalFunctionFactors, NonrationalFunctionFactors, Reverse,
        RationalFunctionExponents, RationalFunctionExpand, ExpandIntegrand,
        SimplerQ, SimplerSqrtQ, SumSimplerQ, BinomialDegree, TrinomialDegree,
        CancelCommonFactors, SimplerIntegrandQ, GeneralizedBinomialDegree,
        GeneralizedBinomialParts, GeneralizedTrinomialDegree,
        GeneralizedTrinomialParts, MonomialQ, MonomialSumQ,
        MinimumMonomialExponent, MonomialExponent, LinearMatchQ,
        PowerOfLinearMatchQ, QuadraticMatchQ, CubicMatchQ, BinomialMatchQ,
        TrinomialMatchQ, GeneralizedBinomialMatchQ, GeneralizedTrinomialMatchQ,
        QuotientOfLinearsMatchQ, PolynomialTermQ, PolynomialTerms,
        NonpolynomialTerms, PseudoBinomialParts, NormalizePseudoBinomial,
        PseudoBinomialPairQ, PseudoBinomialQ, PolynomialGCD, PolyGCD,
        AlgebraicFunctionFactors, NonalgebraicFunctionFactors,
        QuotientOfLinearsP, QuotientOfLinearsParts, QuotientOfLinearsQ,
        Flatten, Sort, AbsurdNumberQ, AbsurdNumberFactors,
        NonabsurdNumberFactors, SumSimplerAuxQ, Prepend, Drop,
        CombineExponents, FactorInteger, FactorAbsurdNumber,
        SubstForInverseFunction, SubstForFractionalPower,
        SubstForFractionalPowerOfQuotientOfLinears,
        FractionalPowerOfQuotientOfLinears, SubstForFractionalPowerQ,
        SubstForFractionalPowerAuxQ, FractionalPowerOfSquareQ,
        FractionalPowerSubexpressionQ, Apply, FactorNumericGcd,
        MergeableFactorQ, MergeFactor, MergeFactors, TrigSimplifyQ,
        TrigSimplify, TrigSimplifyRecur, Order, FactorOrder, Smallest,
        OrderedQ, MinimumDegree, PositiveFactors, Sign, NonpositiveFactors,
        PolynomialInAuxQ, PolynomialInQ, ExponentInAux, ExponentIn,
        PolynomialInSubstAux, PolynomialInSubst, Distrib, DistributeDegree,
        FunctionOfPower, DivideDegreesOfFactors, MonomialFactor, FullSimplify,
        FunctionOfLinearSubst, FunctionOfLinear, NormalizeIntegrand,
        NormalizeIntegrandAux, NormalizeIntegrandFactor,
        NormalizeIntegrandFactorBase, NormalizeTogether,
        NormalizeLeadTermSigns, AbsorbMinusSign, NormalizeSumFactors,
        SignOfFactor, NormalizePowerOfLinear, SimplifyIntegrand, SimplifyTerm,
        TogetherSimplify, SmartSimplify, SubstForExpn, ExpandToSum, UnifySum,
        UnifyTerms, UnifyTerm, CalculusQ, FunctionOfInverseLinear,
        PureFunctionOfSinhQ, PureFunctionOfTanhQ, PureFunctionOfCoshQ,
        IntegerQuotientQ, OddQuotientQ, EvenQuotientQ, FindTrigFactor,
        FunctionOfSinhQ, FunctionOfCoshQ, OddHyperbolicPowerQ, FunctionOfTanhQ,
        FunctionOfTanhWeight, FunctionOfHyperbolicQ, SmartNumerator,
        SmartDenominator, SubstForAux, ActivateTrig, ExpandTrig, TrigExpand,
        SubstForTrig, SubstForHyperbolic, InertTrigFreeQ, LCM,
        SubstForFractionalPowerOfLinear, FractionalPowerOfLinear,
        InverseFunctionOfLinear, InertTrigQ, InertReciprocalQ, DeactivateTrig,
        FixInertTrigFunction, DeactivateTrigAux, PowerOfInertTrigSumQ,
        PiecewiseLinearQ, KnownTrigIntegrandQ, KnownSineIntegrandQ,
        KnownTangentIntegrandQ, KnownCotangentIntegrandQ,
        KnownSecantIntegrandQ, TryPureTanSubst, TryTanhSubst, TryPureTanhSubst,
        AbsurdNumberGCD, AbsurdNumberGCDList, ExpandTrigExpand,
        ExpandTrigReduce, ExpandTrigReduceAux, NormalizeTrig, TrigToExp,
        ExpandTrigToExp, TrigReduce, FunctionOfTrig, AlgebraicTrigFunctionQ,
        FunctionOfHyperbolic, FunctionOfQ, FunctionOfExpnQ, PureFunctionOfSinQ,
        PureFunctionOfCosQ, PureFunctionOfTanQ, PureFunctionOfCotQ,
        FunctionOfCosQ, FunctionOfSinQ, OddTrigPowerQ, FunctionOfTanQ,
        FunctionOfTanWeight, FunctionOfTrigQ, FunctionOfDensePolynomialsQ,
        FunctionOfLog, PowerVariableExpn, PowerVariableDegree,
        PowerVariableSubst, EulerIntegrandQ, FunctionOfSquareRootOfQuadratic,
        SquareRootOfQuadraticSubst, Divides, EasyDQ, ProductOfLinearPowersQ,
        Rt, NthRoot, AtomBaseQ, SumBaseQ, NegSumBaseQ, AllNegTermQ,
        SomeNegTermQ, TrigSquareQ, RtAux, TrigSquare, IntSum, IntTerm, Map2,
        ConstantFactor, SameQ, ReplacePart, CommonFactors,
        MostMainFactorPosition, FunctionOfExponentialQ, FunctionOfExponential,
        FunctionOfExponentialFunction, FunctionOfExponentialFunctionAux,
        FunctionOfExponentialTest, FunctionOfExponentialTestAux, stdev,
        rubi_test, If, IntQuadraticQ, IntBinomialQ, RectifyTangent,
        RectifyCotangent, Inequality, Condition, Simp, SimpHelp, SplitProduct,
        SplitSum, SubstFor, SubstForAux, FresnelS, FresnelC, Erfc, Erfi, Gamma,
        FunctionOfTrigOfLinearQ, ElementaryFunctionQ, Complex, UnsameQ,
        _SimpFixFactor, SimpFixFactor, _FixSimplify, FixSimplify,
        _SimplifyAntiderivativeSum, SimplifyAntiderivativeSum,
        _SimplifyAntiderivative, SimplifyAntiderivative, _TrigSimplifyAux,
        TrigSimplifyAux, Cancel, Part, PolyLog, D, Dist, Sum_doit, PolynomialQuotient, Floor,
        PolynomialRemainder, Factor, PolyLog, CosIntegral, SinIntegral, LogIntegral, SinhIntegral,
        CoshIntegral, Rule, Erf, PolyGamma, ExpIntegralEi, ExpIntegralE, LogGamma , UtilityOperator, Factorial,
        Zeta, ProductLog, DerivativeDivides, HypergeometricPFQ, IntHide, OneQ
    )
from sympy.core.add import Add
from sympy.core.mod import Mod
from sympy.core.mul import Mul
from sympy.core.numbers import (Float, I, Integer)
from sympy.core.power import Pow
from sympy.core.singleton import S
from sympy.functions.elementary.complexes import Abs
from sympy.functions.elementary.miscellaneous import sqrt
from sympy.integrals.integrals import Integral as Integrate
from sympy.logic.boolalg import (And, Or)
from sympy.simplify.simplify import simplify
from sympy.integrals.rubi.symbol import WC
from sympy.core.symbol import symbols, Symbol
from sympy.functions import (sin, cos, tan, cot, csc, sec, sqrt, erf, exp, log)
from sympy.functions.elementary.hyperbolic import (acosh, asinh, atanh, acoth, acsch, asech, cosh, sinh, tanh, coth, sech, csch)
from sympy.functions.elementary.trigonometric import (atan, acsc, asin, acot, acos, asec)
from sympy.integrals.rubi.rubimain import rubi_integrate
from sympy.core.numbers import pi as Pi
a, b, c, d, e, f, m, n, x, u , k, p, r, s, t, i, j= symbols('a b c d e f m n x u k p r s t i j')
A, B, C, D, a, b, c, d, e, f, g, h, y, z, m, n, p, q, u, v, w, F = symbols('A B C D a b c d e f g h y z m n p q u v w F', )

def test_1():
    assert rubi_test(rubi_integrate((c + d*x)**S(4)*sinh(a + b*x), x), x, (c + d*x)**S(4)*cosh(a + b*x)/b - S(4)*d*(c + d*x)**S(3)*sinh(a + b*x)/b**S(2) + S(12)*d**S(2)*(c + d*x)**S(2)*cosh(a + b*x)/b**S(3) - S(24)*d**S(3)*(c + d*x)*sinh(a + b*x)/b**S(4) + S(24)*d**S(4)*cosh(a + b*x)/b**S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)*sinh(a + b*x), x), x, (c + d*x)**S(3)*cosh(a + b*x)/b - S(3)*d*(c + d*x)**S(2)*sinh(a + b*x)/b**S(2) + S(6)*d**S(2)*(c + d*x)*cosh(a + b*x)/b**S(3) - S(6)*d**S(3)*sinh(a + b*x)/b**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)*sinh(a + b*x), x), x, (c + d*x)**S(2)*cosh(a + b*x)/b - S(2)*d*(c + d*x)*sinh(a + b*x)/b**S(2) + S(2)*d**S(2)*cosh(a + b*x)/b**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)*sinh(a + b*x), x), x, (c + d*x)*cosh(a + b*x)/b - d*sinh(a + b*x)/b**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)/(c + d*x), x), x, CoshIntegral(b*c/d + b*x)*sinh(a - b*c/d)/d + SinhIntegral(b*c/d + b*x)*cosh(a - b*c/d)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)/(c + d*x)**S(2), x), x, b*CoshIntegral(b*c/d + b*x)*cosh(a - b*c/d)/d**S(2) + b*SinhIntegral(b*c/d + b*x)*sinh(a - b*c/d)/d**S(2) - sinh(a + b*x)/(d*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)/(c + d*x)**S(3), x), x, b**S(2)*CoshIntegral(b*c/d + b*x)*sinh(a - b*c/d)/(S(2)*d**S(3)) + b**S(2)*SinhIntegral(b*c/d + b*x)*cosh(a - b*c/d)/(S(2)*d**S(3)) - b*cosh(a + b*x)/(S(2)*d**S(2)*(c + d*x)) - sinh(a + b*x)/(S(2)*d*(c + d*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(4)*sinh(a + b*x)**S(2), x), x, -(c + d*x)**S(5)/(S(10)*d) + (c + d*x)**S(4)*sinh(a + b*x)*cosh(a + b*x)/(S(2)*b) - d*(c + d*x)**S(3)*sinh(a + b*x)**S(2)/b**S(2) - d*(c + d*x)**S(3)/(S(2)*b**S(2)) + S(3)*d**S(2)*(c + d*x)**S(2)*sinh(a + b*x)*cosh(a + b*x)/(S(2)*b**S(3)) - S(3)*d**S(4)*x/(S(4)*b**S(4)) - S(3)*d**S(3)*(c + d*x)*sinh(a + b*x)**S(2)/(S(2)*b**S(4)) + S(3)*d**S(4)*sinh(a + b*x)*cosh(a + b*x)/(S(4)*b**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)*sinh(a + b*x)**S(2), x), x, -(c + d*x)**S(4)/(S(8)*d) + (c + d*x)**S(3)*sinh(a + b*x)*cosh(a + b*x)/(S(2)*b) - S(3)*c*d**S(2)*x/(S(4)*b**S(2)) - S(3)*d**S(3)*x**S(2)/(S(8)*b**S(2)) - S(3)*d*(c + d*x)**S(2)*sinh(a + b*x)**S(2)/(S(4)*b**S(2)) + S(3)*d**S(2)*(c + d*x)*sinh(a + b*x)*cosh(a + b*x)/(S(4)*b**S(3)) - S(3)*d**S(3)*sinh(a + b*x)**S(2)/(S(8)*b**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)*sinh(a + b*x)**S(2), x), x, -(c + d*x)**S(3)/(S(6)*d) + (c + d*x)**S(2)*sinh(a + b*x)*cosh(a + b*x)/(S(2)*b) - d**S(2)*x/(S(4)*b**S(2)) - d*(c + d*x)*sinh(a + b*x)**S(2)/(S(2)*b**S(2)) + d**S(2)*sinh(a + b*x)*cosh(a + b*x)/(S(4)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)*sinh(a + b*x)**S(2), x), x, -c*x/S(2) - d*x**S(2)/S(4) + (c + d*x)*sinh(a + b*x)*cosh(a + b*x)/(S(2)*b) - d*sinh(a + b*x)**S(2)/(S(4)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(2)/(c + d*x), x), x, CoshIntegral(S(2)*b*c/d + S(2)*b*x)*cosh(S(2)*a - S(2)*b*c/d)/(S(2)*d) + SinhIntegral(S(2)*b*c/d + S(2)*b*x)*sinh(S(2)*a - S(2)*b*c/d)/(S(2)*d) - log(c + d*x)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(2)/(c + d*x)**S(2), x), x, b*CoshIntegral(S(2)*b*c/d + S(2)*b*x)*sinh(S(2)*a - S(2)*b*c/d)/d**S(2) + b*SinhIntegral(S(2)*b*c/d + S(2)*b*x)*cosh(S(2)*a - S(2)*b*c/d)/d**S(2) - sinh(a + b*x)**S(2)/(d*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(2)/(c + d*x)**S(3), x), x, b**S(2)*CoshIntegral(S(2)*b*c/d + S(2)*b*x)*cosh(S(2)*a - S(2)*b*c/d)/d**S(3) + b**S(2)*SinhIntegral(S(2)*b*c/d + S(2)*b*x)*sinh(S(2)*a - S(2)*b*c/d)/d**S(3) - b*sinh(a + b*x)*cosh(a + b*x)/(d**S(2)*(c + d*x)) - sinh(a + b*x)**S(2)/(S(2)*d*(c + d*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(2)/(c + d*x)**S(4), x), x, S(2)*b**S(3)*CoshIntegral(S(2)*b*c/d + S(2)*b*x)*sinh(S(2)*a - S(2)*b*c/d)/(S(3)*d**S(4)) + S(2)*b**S(3)*SinhIntegral(S(2)*b*c/d + S(2)*b*x)*cosh(S(2)*a - S(2)*b*c/d)/(S(3)*d**S(4)) - S(2)*b**S(2)*sinh(a + b*x)**S(2)/(S(3)*d**S(3)*(c + d*x)) - b**S(2)/(S(3)*d**S(3)*(c + d*x)) - b*sinh(a + b*x)*cosh(a + b*x)/(S(3)*d**S(2)*(c + d*x)**S(2)) - sinh(a + b*x)**S(2)/(S(3)*d*(c + d*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(4)*sinh(a + b*x)**S(3), x), x, (c + d*x)**S(4)*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(3)*b) - S(2)*(c + d*x)**S(4)*cosh(a + b*x)/(S(3)*b) - S(4)*d*(c + d*x)**S(3)*sinh(a + b*x)**S(3)/(S(9)*b**S(2)) + S(8)*d*(c + d*x)**S(3)*sinh(a + b*x)/(S(3)*b**S(2)) + S(4)*d**S(2)*(c + d*x)**S(2)*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(9)*b**S(3)) - S(80)*d**S(2)*(c + d*x)**S(2)*cosh(a + b*x)/(S(9)*b**S(3)) - S(8)*d**S(3)*(c + d*x)*sinh(a + b*x)**S(3)/(S(27)*b**S(4)) + S(160)*d**S(3)*(c + d*x)*sinh(a + b*x)/(S(9)*b**S(4)) + S(8)*d**S(4)*cosh(a + b*x)**S(3)/(S(81)*b**S(5)) - S(488)*d**S(4)*cosh(a + b*x)/(S(27)*b**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)*sinh(a + b*x)**S(3), x), x, (c + d*x)**S(3)*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(3)*b) - S(2)*(c + d*x)**S(3)*cosh(a + b*x)/(S(3)*b) - d*(c + d*x)**S(2)*sinh(a + b*x)**S(3)/(S(3)*b**S(2)) + S(2)*d*(c + d*x)**S(2)*sinh(a + b*x)/b**S(2) + S(2)*d**S(2)*(c + d*x)*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(9)*b**S(3)) - S(40)*d**S(2)*(c + d*x)*cosh(a + b*x)/(S(9)*b**S(3)) - S(2)*d**S(3)*sinh(a + b*x)**S(3)/(S(27)*b**S(4)) + S(40)*d**S(3)*sinh(a + b*x)/(S(9)*b**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)*sinh(a + b*x)**S(3), x), x, (c + d*x)**S(2)*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(3)*b) - S(2)*(c + d*x)**S(2)*cosh(a + b*x)/(S(3)*b) - S(2)*d*(c + d*x)*sinh(a + b*x)**S(3)/(S(9)*b**S(2)) + S(4)*d*(c + d*x)*sinh(a + b*x)/(S(3)*b**S(2)) + S(2)*d**S(2)*cosh(a + b*x)**S(3)/(S(27)*b**S(3)) - S(14)*d**S(2)*cosh(a + b*x)/(S(9)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)*sinh(a + b*x)**S(3), x), x, (c + d*x)*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(3)*b) - S(2)*(c + d*x)*cosh(a + b*x)/(S(3)*b) - d*sinh(a + b*x)**S(3)/(S(9)*b**S(2)) + S(2)*d*sinh(a + b*x)/(S(3)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(3)/(c + d*x), x), x, -S(3)*CoshIntegral(b*c/d + b*x)*sinh(a - b*c/d)/(S(4)*d) + CoshIntegral(S(3)*b*c/d + S(3)*b*x)*sinh(S(3)*a - S(3)*b*c/d)/(S(4)*d) - S(3)*SinhIntegral(b*c/d + b*x)*cosh(a - b*c/d)/(S(4)*d) + SinhIntegral(S(3)*b*c/d + S(3)*b*x)*cosh(S(3)*a - S(3)*b*c/d)/(S(4)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(3)/(c + d*x)**S(2), x), x, -S(3)*b*CoshIntegral(b*c/d + b*x)*cosh(a - b*c/d)/(S(4)*d**S(2)) + S(3)*b*CoshIntegral(S(3)*b*c/d + S(3)*b*x)*cosh(S(3)*a - S(3)*b*c/d)/(S(4)*d**S(2)) - S(3)*b*SinhIntegral(b*c/d + b*x)*sinh(a - b*c/d)/(S(4)*d**S(2)) + S(3)*b*SinhIntegral(S(3)*b*c/d + S(3)*b*x)*sinh(S(3)*a - S(3)*b*c/d)/(S(4)*d**S(2)) - sinh(a + b*x)**S(3)/(d*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(3)/(c + d*x)**S(3), x), x, -S(3)*b**S(2)*CoshIntegral(b*c/d + b*x)*sinh(a - b*c/d)/(S(8)*d**S(3)) + S(9)*b**S(2)*CoshIntegral(S(3)*b*c/d + S(3)*b*x)*sinh(S(3)*a - S(3)*b*c/d)/(S(8)*d**S(3)) - S(3)*b**S(2)*SinhIntegral(b*c/d + b*x)*cosh(a - b*c/d)/(S(8)*d**S(3)) + S(9)*b**S(2)*SinhIntegral(S(3)*b*c/d + S(3)*b*x)*cosh(S(3)*a - S(3)*b*c/d)/(S(8)*d**S(3)) - S(3)*b*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(2)*d**S(2)*(c + d*x)) - sinh(a + b*x)**S(3)/(S(2)*d*(c + d*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)/sinh(a + b*x), x), x, -S(2)*(c + d*x)**S(3)*atanh(exp(a + b*x))/b - S(3)*d*(c + d*x)**S(2)*PolyLog(S(2), -exp(a + b*x))/b**S(2) + S(3)*d*(c + d*x)**S(2)*PolyLog(S(2), exp(a + b*x))/b**S(2) + S(6)*d**S(2)*(c + d*x)*PolyLog(S(3), -exp(a + b*x))/b**S(3) - S(6)*d**S(2)*(c + d*x)*PolyLog(S(3), exp(a + b*x))/b**S(3) - S(6)*d**S(3)*PolyLog(S(4), -exp(a + b*x))/b**S(4) + S(6)*d**S(3)*PolyLog(S(4), exp(a + b*x))/b**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)/sinh(a + b*x), x), x, -S(2)*(c + d*x)**S(2)*atanh(exp(a + b*x))/b - S(2)*d*(c + d*x)*PolyLog(S(2), -exp(a + b*x))/b**S(2) + S(2)*d*(c + d*x)*PolyLog(S(2), exp(a + b*x))/b**S(2) + S(2)*d**S(2)*PolyLog(S(3), -exp(a + b*x))/b**S(3) - S(2)*d**S(2)*PolyLog(S(3), exp(a + b*x))/b**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)/sinh(a + b*x), x), x, -S(2)*(c + d*x)*atanh(exp(a + b*x))/b - d*PolyLog(S(2), -exp(a + b*x))/b**S(2) + d*PolyLog(S(2), exp(a + b*x))/b**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)*sinh(a + b*x)), x), x, Integrate(S(1)/((c + d*x)*sinh(a + b*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)**S(2)*sinh(a + b*x)), x), x, Integrate(S(1)/((c + d*x)**S(2)*sinh(a + b*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)/sinh(a + b*x)**S(2), x), x, -(c + d*x)**S(3)/b - (c + d*x)**S(3)/(b*tanh(a + b*x)) + S(3)*d*(c + d*x)**S(2)*log(-exp(S(2)*a + S(2)*b*x) + S(1))/b**S(2) + S(3)*d**S(2)*(c + d*x)*PolyLog(S(2), exp(S(2)*a + S(2)*b*x))/b**S(3) - S(3)*d**S(3)*PolyLog(S(3), exp(S(2)*a + S(2)*b*x))/(S(2)*b**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)/sinh(a + b*x)**S(2), x), x, -(c + d*x)**S(2)/b - (c + d*x)**S(2)/(b*tanh(a + b*x)) + S(2)*d*(c + d*x)*log(-exp(S(2)*a + S(2)*b*x) + S(1))/b**S(2) + d**S(2)*PolyLog(S(2), exp(S(2)*a + S(2)*b*x))/b**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)/sinh(a + b*x)**S(2), x), x, -(c + d*x)/(b*tanh(a + b*x)) + d*log(sinh(a + b*x))/b**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)*sinh(a + b*x)**S(2)), x), x, Integrate(S(1)/((c + d*x)*sinh(a + b*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)**S(2)*sinh(a + b*x)**S(2)), x), x, Integrate(S(1)/((c + d*x)**S(2)*sinh(a + b*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)/sinh(a + b*x)**S(3), x), x, (c + d*x)**S(3)*atanh(exp(a + b*x))/b - (c + d*x)**S(3)/(S(2)*b*sinh(a + b*x)*tanh(a + b*x)) + S(3)*d*(c + d*x)**S(2)*PolyLog(S(2), -exp(a + b*x))/(S(2)*b**S(2)) - S(3)*d*(c + d*x)**S(2)*PolyLog(S(2), exp(a + b*x))/(S(2)*b**S(2)) - S(3)*d*(c + d*x)**S(2)/(S(2)*b**S(2)*sinh(a + b*x)) - S(3)*d**S(2)*(c + d*x)*PolyLog(S(3), -exp(a + b*x))/b**S(3) + S(3)*d**S(2)*(c + d*x)*PolyLog(S(3), exp(a + b*x))/b**S(3) - S(6)*d**S(2)*(c + d*x)*atanh(exp(a + b*x))/b**S(3) - S(3)*d**S(3)*PolyLog(S(2), -exp(a + b*x))/b**S(4) + S(3)*d**S(3)*PolyLog(S(2), exp(a + b*x))/b**S(4) + S(3)*d**S(3)*PolyLog(S(4), -exp(a + b*x))/b**S(4) - S(3)*d**S(3)*PolyLog(S(4), exp(a + b*x))/b**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)/sinh(a + b*x)**S(3), x), x, (c + d*x)**S(2)*atanh(exp(a + b*x))/b - (c + d*x)**S(2)/(S(2)*b*sinh(a + b*x)*tanh(a + b*x)) + d*(c + d*x)*PolyLog(S(2), -exp(a + b*x))/b**S(2) - d*(c + d*x)*PolyLog(S(2), exp(a + b*x))/b**S(2) - d*(c + d*x)/(b**S(2)*sinh(a + b*x)) - d**S(2)*PolyLog(S(3), -exp(a + b*x))/b**S(3) + d**S(2)*PolyLog(S(3), exp(a + b*x))/b**S(3) - d**S(2)*atanh(cosh(a + b*x))/b**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)/sinh(a + b*x)**S(3), x), x, (c + d*x)*atanh(exp(a + b*x))/b - (c + d*x)/(S(2)*b*sinh(a + b*x)*tanh(a + b*x)) + d*PolyLog(S(2), -exp(a + b*x))/(S(2)*b**S(2)) - d*PolyLog(S(2), exp(a + b*x))/(S(2)*b**S(2)) - d/(S(2)*b**S(2)*sinh(a + b*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)*sinh(a + b*x)**S(3)), x), x, Integrate(S(1)/((c + d*x)*sinh(a + b*x)**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)**S(2)*sinh(a + b*x)**S(3)), x), x, Integrate(S(1)/((c + d*x)**S(2)*sinh(a + b*x)**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**(S(5)/2)*sinh(a + b*x), x), x, -S(15)*sqrt(Pi)*d**(S(5)/2)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(16)*b**(S(7)/2)) - S(15)*sqrt(Pi)*d**(S(5)/2)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(16)*b**(S(7)/2)) + (c + d*x)**(S(5)/2)*cosh(a + b*x)/b - S(5)*d*(c + d*x)**(S(3)/2)*sinh(a + b*x)/(S(2)*b**S(2)) + S(15)*d**S(2)*sqrt(c + d*x)*cosh(a + b*x)/(S(4)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**(S(3)/2)*sinh(a + b*x), x), x, -S(3)*sqrt(Pi)*d**(S(3)/2)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(8)*b**(S(5)/2)) + S(3)*sqrt(Pi)*d**(S(3)/2)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(8)*b**(S(5)/2)) + (c + d*x)**(S(3)/2)*cosh(a + b*x)/b - S(3)*d*sqrt(c + d*x)*sinh(a + b*x)/(S(2)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c + d*x)*sinh(a + b*x), x), x, -sqrt(Pi)*sqrt(d)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(4)*b**(S(3)/2)) - sqrt(Pi)*sqrt(d)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(4)*b**(S(3)/2)) + sqrt(c + d*x)*cosh(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)/sqrt(c + d*x), x), x, -sqrt(Pi)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(2)*sqrt(b)*sqrt(d)) + sqrt(Pi)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(2)*sqrt(b)*sqrt(d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)/(c + d*x)**(S(3)/2), x), x, sqrt(Pi)*sqrt(b)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/d**(S(3)/2) + sqrt(Pi)*sqrt(b)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/d**(S(3)/2) - S(2)*sinh(a + b*x)/(d*sqrt(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)/(c + d*x)**(S(5)/2), x), x, -S(2)*sqrt(Pi)*b**(S(3)/2)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(3)*d**(S(5)/2)) + S(2)*sqrt(Pi)*b**(S(3)/2)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(3)*d**(S(5)/2)) - S(4)*b*cosh(a + b*x)/(S(3)*d**S(2)*sqrt(c + d*x)) - S(2)*sinh(a + b*x)/(S(3)*d*(c + d*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)/(c + d*x)**(S(7)/2), x), x, S(4)*sqrt(Pi)*b**(S(5)/2)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(15)*d**(S(7)/2)) + S(4)*sqrt(Pi)*b**(S(5)/2)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(15)*d**(S(7)/2)) - S(8)*b**S(2)*sinh(a + b*x)/(S(15)*d**S(3)*sqrt(c + d*x)) - S(4)*b*cosh(a + b*x)/(S(15)*d**S(2)*(c + d*x)**(S(3)/2)) - S(2)*sinh(a + b*x)/(S(5)*d*(c + d*x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**(S(5)/2)*sinh(a + b*x)**S(2), x), x, S(15)*sqrt(S(2))*sqrt(Pi)*d**(S(5)/2)*Erf(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(2)*a + S(2)*b*c/d)/(S(512)*b**(S(7)/2)) - S(15)*sqrt(S(2))*sqrt(Pi)*d**(S(5)/2)*Erfi(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(2)*a - S(2)*b*c/d)/(S(512)*b**(S(7)/2)) - (c + d*x)**(S(7)/2)/(S(7)*d) + (c + d*x)**(S(5)/2)*sinh(a + b*x)*cosh(a + b*x)/(S(2)*b) - S(5)*d*(c + d*x)**(S(3)/2)*sinh(a + b*x)**S(2)/(S(8)*b**S(2)) - S(5)*d*(c + d*x)**(S(3)/2)/(S(16)*b**S(2)) + S(15)*d**S(2)*sqrt(c + d*x)*sinh(S(2)*a + S(2)*b*x)/(S(64)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**(S(3)/2)*sinh(a + b*x)**S(2), x), x, S(3)*sqrt(S(2))*sqrt(Pi)*d**(S(3)/2)*Erf(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(2)*a + S(2)*b*c/d)/(S(128)*b**(S(5)/2)) + S(3)*sqrt(S(2))*sqrt(Pi)*d**(S(3)/2)*Erfi(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(2)*a - S(2)*b*c/d)/(S(128)*b**(S(5)/2)) - (c + d*x)**(S(5)/2)/(S(5)*d) + (c + d*x)**(S(3)/2)*sinh(a + b*x)*cosh(a + b*x)/(S(2)*b) - S(3)*d*sqrt(c + d*x)*sinh(a + b*x)**S(2)/(S(8)*b**S(2)) - S(3)*d*sqrt(c + d*x)/(S(16)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c + d*x)*sinh(a + b*x)**S(2), x), x, sqrt(S(2))*sqrt(Pi)*sqrt(d)*Erf(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(2)*a + S(2)*b*c/d)/(S(32)*b**(S(3)/2)) - sqrt(S(2))*sqrt(Pi)*sqrt(d)*Erfi(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(2)*a - S(2)*b*c/d)/(S(32)*b**(S(3)/2)) - (c + d*x)**(S(3)/2)/(S(3)*d) + sqrt(c + d*x)*sinh(S(2)*a + S(2)*b*x)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(2)/sqrt(c + d*x), x), x, sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(2)*a + S(2)*b*c/d)/(S(8)*sqrt(b)*sqrt(d)) + sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(2)*a - S(2)*b*c/d)/(S(8)*sqrt(b)*sqrt(d)) - sqrt(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(2)/(c + d*x)**(S(3)/2), x), x, -sqrt(S(2))*sqrt(Pi)*sqrt(b)*Erf(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(2)*a + S(2)*b*c/d)/(S(2)*d**(S(3)/2)) + sqrt(S(2))*sqrt(Pi)*sqrt(b)*Erfi(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(2)*a - S(2)*b*c/d)/(S(2)*d**(S(3)/2)) - S(2)*sinh(a + b*x)**S(2)/(d*sqrt(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(2)/(c + d*x)**(S(5)/2), x), x, S(2)*sqrt(S(2))*sqrt(Pi)*b**(S(3)/2)*Erf(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(2)*a + S(2)*b*c/d)/(S(3)*d**(S(5)/2)) + S(2)*sqrt(S(2))*sqrt(Pi)*b**(S(3)/2)*Erfi(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(2)*a - S(2)*b*c/d)/(S(3)*d**(S(5)/2)) - S(8)*b*sinh(a + b*x)*cosh(a + b*x)/(S(3)*d**S(2)*sqrt(c + d*x)) - S(2)*sinh(a + b*x)**S(2)/(S(3)*d*(c + d*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(2)/(c + d*x)**(S(7)/2), x), x, -S(8)*sqrt(S(2))*sqrt(Pi)*b**(S(5)/2)*Erf(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(2)*a + S(2)*b*c/d)/(S(15)*d**(S(7)/2)) + S(8)*sqrt(S(2))*sqrt(Pi)*b**(S(5)/2)*Erfi(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(2)*a - S(2)*b*c/d)/(S(15)*d**(S(7)/2)) - S(32)*b**S(2)*sinh(a + b*x)**S(2)/(S(15)*d**S(3)*sqrt(c + d*x)) - S(16)*b**S(2)/(S(15)*d**S(3)*sqrt(c + d*x)) - S(8)*b*sinh(a + b*x)*cosh(a + b*x)/(S(15)*d**S(2)*(c + d*x)**(S(3)/2)) - S(2)*sinh(a + b*x)**S(2)/(S(5)*d*(c + d*x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)

    # taking long time assert rubi_test(rubi_integrate(sinh(a + b*x)**S(2)/(c + d*x)**(S(9)/2), x), x, S(32)*sqrt(S(2))*sqrt(Pi)*b**(S(7)/2)*Erf(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(2)*a + S(2)*b*c/d)/(S(105)*d**(S(9)/2)) + S(32)*sqrt(S(2))*sqrt(Pi)*b**(S(7)/2)*Erfi(sqrt(S(2))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(2)*a - S(2)*b*c/d)/(S(105)*d**(S(9)/2)) - S(128)*b**S(3)*sinh(a + b*x)*cosh(a + b*x)/(S(105)*d**S(4)*sqrt(c + d*x)) - S(32)*b**S(2)*sinh(a + b*x)**S(2)/(S(105)*d**S(3)*(c + d*x)**(S(3)/2)) - S(16)*b**S(2)/(S(105)*d**S(3)*(c + d*x)**(S(3)/2)) - S(8)*b*sinh(a + b*x)*cosh(a + b*x)/(S(35)*d**S(2)*(c + d*x)**(S(5)/2)) - S(2)*sinh(a + b*x)**S(2)/(S(7)*d*(c + d*x)**(S(7)/2)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((c + d*x)**(S(5)/2)*sinh(a + b*x)**S(3), x), x, S(45)*sqrt(Pi)*d**(S(5)/2)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(64)*b**(S(7)/2)) - S(5)*sqrt(S(3))*sqrt(Pi)*d**(S(5)/2)*Erf(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(3)*a + S(3)*b*c/d)/(S(1728)*b**(S(7)/2)) + S(45)*sqrt(Pi)*d**(S(5)/2)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(64)*b**(S(7)/2)) - S(5)*sqrt(S(3))*sqrt(Pi)*d**(S(5)/2)*Erfi(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(3)*a - S(3)*b*c/d)/(S(1728)*b**(S(7)/2)) + (c + d*x)**(S(5)/2)*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(3)*b) - S(2)*(c + d*x)**(S(5)/2)*cosh(a + b*x)/(S(3)*b) - S(5)*d*(c + d*x)**(S(3)/2)*sinh(a + b*x)**S(3)/(S(18)*b**S(2)) + S(5)*d*(c + d*x)**(S(3)/2)*sinh(a + b*x)/(S(3)*b**S(2)) - S(45)*d**S(2)*sqrt(c + d*x)*cosh(a + b*x)/(S(16)*b**S(3)) + S(5)*d**S(2)*sqrt(c + d*x)*cosh(S(3)*a + S(3)*b*x)/(S(144)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**(S(3)/2)*sinh(a + b*x)**S(3), x), x, S(9)*sqrt(Pi)*d**(S(3)/2)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(32)*b**(S(5)/2)) - sqrt(S(3))*sqrt(Pi)*d**(S(3)/2)*Erf(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(3)*a + S(3)*b*c/d)/(S(288)*b**(S(5)/2)) - S(9)*sqrt(Pi)*d**(S(3)/2)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(32)*b**(S(5)/2)) + sqrt(S(3))*sqrt(Pi)*d**(S(3)/2)*Erfi(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(3)*a - S(3)*b*c/d)/(S(288)*b**(S(5)/2)) + (c + d*x)**(S(3)/2)*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(3)*b) - S(2)*(c + d*x)**(S(3)/2)*cosh(a + b*x)/(S(3)*b) - d*sqrt(c + d*x)*sinh(a + b*x)**S(3)/(S(6)*b**S(2)) + d*sqrt(c + d*x)*sinh(a + b*x)/b**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c + d*x)*sinh(a + b*x)**S(3), x), x, S(3)*sqrt(Pi)*sqrt(d)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(16)*b**(S(3)/2)) - sqrt(S(3))*sqrt(Pi)*sqrt(d)*Erf(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(3)*a + S(3)*b*c/d)/(S(144)*b**(S(3)/2)) + S(3)*sqrt(Pi)*sqrt(d)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(16)*b**(S(3)/2)) - sqrt(S(3))*sqrt(Pi)*sqrt(d)*Erfi(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(3)*a - S(3)*b*c/d)/(S(144)*b**(S(3)/2)) - S(3)*sqrt(c + d*x)*cosh(a + b*x)/(S(4)*b) + sqrt(c + d*x)*cosh(S(3)*a + S(3)*b*x)/(S(12)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(3)/sqrt(c + d*x), x), x, S(3)*sqrt(Pi)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(8)*sqrt(b)*sqrt(d)) - sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(3)*a + S(3)*b*c/d)/(S(24)*sqrt(b)*sqrt(d)) - S(3)*sqrt(Pi)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(8)*sqrt(b)*sqrt(d)) + sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(3)*a - S(3)*b*c/d)/(S(24)*sqrt(b)*sqrt(d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(3)/(c + d*x)**(S(3)/2), x), x, -S(3)*sqrt(Pi)*sqrt(b)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(4)*d**(S(3)/2)) + sqrt(S(3))*sqrt(Pi)*sqrt(b)*Erf(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(3)*a + S(3)*b*c/d)/(S(4)*d**(S(3)/2)) - S(3)*sqrt(Pi)*sqrt(b)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(4)*d**(S(3)/2)) + sqrt(S(3))*sqrt(Pi)*sqrt(b)*Erfi(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(3)*a - S(3)*b*c/d)/(S(4)*d**(S(3)/2)) - S(2)*sinh(a + b*x)**S(3)/(d*sqrt(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(a + b*x)**S(3)/(c + d*x)**(S(5)/2), x), x, sqrt(Pi)*b**(S(3)/2)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(2)*d**(S(5)/2)) - sqrt(S(3))*sqrt(Pi)*b**(S(3)/2)*Erf(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(3)*a + S(3)*b*c/d)/(S(2)*d**(S(5)/2)) - sqrt(Pi)*b**(S(3)/2)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(2)*d**(S(5)/2)) + sqrt(S(3))*sqrt(Pi)*b**(S(3)/2)*Erfi(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(3)*a - S(3)*b*c/d)/(S(2)*d**(S(5)/2)) - S(4)*b*sinh(a + b*x)**S(2)*cosh(a + b*x)/(d**S(2)*sqrt(c + d*x)) - S(2)*sinh(a + b*x)**S(3)/(S(3)*d*(c + d*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate(sinh(a + b*x)**S(3)/(c + d*x)**(S(7)/2), x), x, -sqrt(Pi)*b**(S(5)/2)*Erf(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-a + b*c/d)/(S(5)*d**(S(7)/2)) + S(3)*sqrt(S(3))*sqrt(Pi)*b**(S(5)/2)*Erf(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(-S(3)*a + S(3)*b*c/d)/(S(5)*d**(S(7)/2)) - sqrt(Pi)*b**(S(5)/2)*Erfi(sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(a - b*c/d)/(S(5)*d**(S(7)/2)) + S(3)*sqrt(S(3))*sqrt(Pi)*b**(S(5)/2)*Erfi(sqrt(S(3))*sqrt(b)*sqrt(c + d*x)/sqrt(d))*exp(S(3)*a - S(3)*b*c/d)/(S(5)*d**(S(7)/2)) - S(24)*b**S(2)*sinh(a + b*x)**S(3)/(S(5)*d**S(3)*sqrt(c + d*x)) - S(16)*b**S(2)*sinh(a + b*x)/(S(5)*d**S(3)*sqrt(c + d*x)) - S(4)*b*sinh(a + b*x)**S(2)*cosh(a + b*x)/(S(5)*d**S(2)*(c + d*x)**(S(3)/2)) - S(2)*sinh(a + b*x)**S(3)/(S(5)*d*(c + d*x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((d*x)**(S(3)/2)*sinh(f*x), x), x, -S(3)*sqrt(Pi)*d**(S(3)/2)*Erf(sqrt(f)*sqrt(d*x)/sqrt(d))/(S(8)*f**(S(5)/2)) + S(3)*sqrt(Pi)*d**(S(3)/2)*Erfi(sqrt(f)*sqrt(d*x)/sqrt(d))/(S(8)*f**(S(5)/2)) - S(3)*d*sqrt(d*x)*sinh(f*x)/(S(2)*f**S(2)) + (d*x)**(S(3)/2)*cosh(f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*x)*sinh(f*x), x), x, -sqrt(Pi)*sqrt(d)*Erf(sqrt(f)*sqrt(d*x)/sqrt(d))/(S(4)*f**(S(3)/2)) - sqrt(Pi)*sqrt(d)*Erfi(sqrt(f)*sqrt(d*x)/sqrt(d))/(S(4)*f**(S(3)/2)) + sqrt(d*x)*cosh(f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(f*x)/sqrt(d*x), x), x, -sqrt(Pi)*Erf(sqrt(f)*sqrt(d*x)/sqrt(d))/(S(2)*sqrt(d)*sqrt(f)) + sqrt(Pi)*Erfi(sqrt(f)*sqrt(d*x)/sqrt(d))/(S(2)*sqrt(d)*sqrt(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(f*x)/(d*x)**(S(3)/2), x), x, sqrt(Pi)*sqrt(f)*Erf(sqrt(f)*sqrt(d*x)/sqrt(d))/d**(S(3)/2) + sqrt(Pi)*sqrt(f)*Erfi(sqrt(f)*sqrt(d*x)/sqrt(d))/d**(S(3)/2) - S(2)*sinh(f*x)/(d*sqrt(d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(f*x)/(d*x)**(S(5)/2), x), x, -S(2)*sqrt(Pi)*f**(S(3)/2)*Erf(sqrt(f)*sqrt(d*x)/sqrt(d))/(S(3)*d**(S(5)/2)) + S(2)*sqrt(Pi)*f**(S(3)/2)*Erfi(sqrt(f)*sqrt(d*x)/sqrt(d))/(S(3)*d**(S(5)/2)) - S(2)*sinh(f*x)/(S(3)*d*(d*x)**(S(3)/2)) - S(4)*f*cosh(f*x)/(S(3)*d**S(2)*sqrt(d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c + d*x)/sinh(a + b*x), x), x, Integrate(sqrt(c + d*x)/sinh(a + b*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(c + d*x)*sinh(a + b*x)), x), x, Integrate(S(1)/(sqrt(c + d*x)*sinh(a + b*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sinh(x)**(S(3)/2)/x**S(3), x), x, S(3)*Integrate(S(1)/(x*sqrt(sinh(x))), x)/S(8) + S(9)*Integrate(sinh(x)**(S(3)/2)/x, x)/S(8) - S(3)*sqrt(sinh(x))*cosh(x)/(S(4)*x) - sinh(x)**(S(3)/2)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(-x*sqrt(sinh(x)) + x/sinh(x)**(S(3)/2), x), x, -S(2)*x*cosh(x)/sqrt(sinh(x)) + S(4)*sqrt(sinh(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(S(3)*sqrt(sinh(x))) + x/sinh(x)**(S(5)/2), x), x, -S(2)*x*cosh(x)/(S(3)*sinh(x)**(S(3)/2)) - S(4)/(S(3)*sqrt(sinh(x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(3)*x*sqrt(sinh(x))/S(5) + x/sinh(x)**(S(7)/2), x), x, S(6)*x*cosh(x)/(S(5)*sqrt(sinh(x))) - S(2)*x*cosh(x)/(S(5)*sinh(x)**(S(5)/2)) - S(12)*sqrt(sinh(x))/S(5) - S(4)/(S(15)*sinh(x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(-x**S(2)*sqrt(sinh(x)) + x**S(2)/sinh(x)**(S(3)/2), x), x, -S(2)*x**S(2)*cosh(x)/sqrt(sinh(x)) + S(8)*x*sqrt(sinh(x)) - S(16)*I*EllipticE(Pi/S(4) - I*x/S(2), S(2))*sqrt(sinh(x))/sqrt(I*sinh(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sinh(e + f*x))**n*(c + d*x)**m, x), x, Integrate((b*sinh(e + f*x))**n*(c + d*x)**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m*sinh(a + b*x)**S(3), x), x, S(3)**(-m + S(-1))*(-b*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -S(3)*b*(c + d*x)/d)*exp(S(3)*a - S(3)*b*c/d)/(S(8)*b) + S(3)**(-m + S(-1))*(b*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), S(3)*b*(c + d*x)/d)*exp(-S(3)*a + S(3)*b*c/d)/(S(8)*b) - S(3)*(-b*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -b*(c + d*x)/d)*exp(a - b*c/d)/(S(8)*b) - S(3)*(b*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), b*(c + d*x)/d)*exp(-a + b*c/d)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m*sinh(a + b*x)**S(2), x), x, S(2)**(-m + S(-3))*(-b*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -S(2)*b*(c + d*x)/d)*exp(S(2)*a - S(2)*b*c/d)/b - S(2)**(-m + S(-3))*(b*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), S(2)*b*(c + d*x)/d)*exp(-S(2)*a + S(2)*b*c/d)/b - (c + d*x)**(m + S(1))/(S(2)*d*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m*sinh(a + b*x), x), x, (-b*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -b*(c + d*x)/d)*exp(a - b*c/d)/(S(2)*b) + (b*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), b*(c + d*x)/d)*exp(-a + b*c/d)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m/sinh(a + b*x), x), x, Integrate((c + d*x)**m/sinh(a + b*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m/sinh(a + b*x)**S(2), x), x, Integrate((c + d*x)**m/sinh(a + b*x)**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(3))*sinh(a + b*x), x), x, -x**m*(-b*x)**(-m)*Gamma(m + S(4), -b*x)*exp(a)/(S(2)*b**S(4)) + x**m*(b*x)**(-m)*Gamma(m + S(4), b*x)*exp(-a)/(S(2)*b**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(2))*sinh(a + b*x), x), x, x**m*(-b*x)**(-m)*Gamma(m + S(3), -b*x)*exp(a)/(S(2)*b**S(3)) + x**m*(b*x)**(-m)*Gamma(m + S(3), b*x)*exp(-a)/(S(2)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(1))*sinh(a + b*x), x), x, -x**m*(-b*x)**(-m)*Gamma(m + S(2), -b*x)*exp(a)/(S(2)*b**S(2)) + x**m*(b*x)**(-m)*Gamma(m + S(2), b*x)*exp(-a)/(S(2)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*sinh(a + b*x), x), x, x**m*(-b*x)**(-m)*Gamma(m + S(1), -b*x)*exp(a)/(S(2)*b) + x**m*(b*x)**(-m)*Gamma(m + S(1), b*x)*exp(-a)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(-1))*sinh(a + b*x), x), x, -x**m*(-b*x)**(-m)*Gamma(m, -b*x)*exp(a)/S(2) + x**m*(b*x)**(-m)*Gamma(m, b*x)*exp(-a)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(-2))*sinh(a + b*x), x), x, b*x**m*(-b*x)**(-m)*Gamma(m + S(-1), -b*x)*exp(a)/S(2) + b*x**m*(b*x)**(-m)*Gamma(m + S(-1), b*x)*exp(-a)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(-3))*sinh(a + b*x), x), x, -b**S(2)*x**m*(-b*x)**(-m)*Gamma(m + S(-2), -b*x)*exp(a)/S(2) + b**S(2)*x**m*(b*x)**(-m)*Gamma(m + S(-2), b*x)*exp(-a)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(3))*sinh(a + b*x)**S(2), x), x, -S(2)**(-m + S(-6))*x**m*(-b*x)**(-m)*Gamma(m + S(4), -S(2)*b*x)*exp(S(2)*a)/b**S(4) - S(2)**(-m + S(-6))*x**m*(b*x)**(-m)*Gamma(m + S(4), S(2)*b*x)*exp(-S(2)*a)/b**S(4) - x**(m + S(4))/(S(2)*m + S(8)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(2))*sinh(a + b*x)**S(2), x), x, S(2)**(-m + S(-5))*x**m*(-b*x)**(-m)*Gamma(m + S(3), -S(2)*b*x)*exp(S(2)*a)/b**S(3) - S(2)**(-m + S(-5))*x**m*(b*x)**(-m)*Gamma(m + S(3), S(2)*b*x)*exp(-S(2)*a)/b**S(3) - x**(m + S(3))/(S(2)*m + S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(1))*sinh(a + b*x)**S(2), x), x, -S(2)**(-m + S(-4))*x**m*(-b*x)**(-m)*Gamma(m + S(2), -S(2)*b*x)*exp(S(2)*a)/b**S(2) - S(2)**(-m + S(-4))*x**m*(b*x)**(-m)*Gamma(m + S(2), S(2)*b*x)*exp(-S(2)*a)/b**S(2) - x**(m + S(2))/(S(2)*m + S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*sinh(a + b*x)**S(2), x), x, S(2)**(-m + S(-3))*x**m*(-b*x)**(-m)*Gamma(m + S(1), -S(2)*b*x)*exp(S(2)*a)/b - S(2)**(-m + S(-3))*x**m*(b*x)**(-m)*Gamma(m + S(1), S(2)*b*x)*exp(-S(2)*a)/b - x**(m + S(1))/(S(2)*m + S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(-1))*sinh(a + b*x)**S(2), x), x, -S(2)**(-m + S(-2))*x**m*(-b*x)**(-m)*Gamma(m, -S(2)*b*x)*exp(S(2)*a) - S(2)**(-m + S(-2))*x**m*(b*x)**(-m)*Gamma(m, S(2)*b*x)*exp(-S(2)*a) - x**m/(S(2)*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(-2))*sinh(a + b*x)**S(2), x), x, S(2)**(-m + S(-1))*b*x**m*(-b*x)**(-m)*Gamma(m + S(-1), -S(2)*b*x)*exp(S(2)*a) - S(2)**(-m + S(-1))*b*x**m*(b*x)**(-m)*Gamma(m + S(-1), S(2)*b*x)*exp(-S(2)*a) + x**(m + S(-1))/(-S(2)*m + S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(m + S(-3))*sinh(a + b*x)**S(2), x), x, x**(m + S(-2))/(-S(2)*m + S(4)) - S(2)**(-m)*b**S(2)*x**m*(-b*x)**(-m)*Gamma(m + S(-2), -S(2)*b*x)*exp(S(2)*a) - S(2)**(-m)*b**S(2)*x**m*(b*x)**(-m)*Gamma(m + S(-2), S(2)*b*x)*exp(-S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*sqrt(S(1)/sinh(x))/S(3) + x/(S(1)/sinh(x))**(S(3)/2), x), x, S(2)*x*cosh(x)/(S(3)*sqrt(S(1)/sinh(x))) - S(4)/(S(9)*(S(1)/sinh(x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(3)*x/(S(5)*sqrt(S(1)/sinh(x))) + x/(S(1)/sinh(x))**(S(5)/2), x), x, S(2)*x*cosh(x)/(S(5)*(S(1)/sinh(x))**(S(3)/2)) - S(4)/(S(25)*(S(1)/sinh(x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(-S(5)*x*sqrt(S(1)/sinh(x))/S(21) + x/(S(1)/sinh(x))**(S(7)/2), x), x, -S(10)*x*cosh(x)/(S(21)*sqrt(S(1)/sinh(x))) + S(2)*x*cosh(x)/(S(7)*(S(1)/sinh(x))**(S(5)/2)) + S(20)/(S(63)*(S(1)/sinh(x))**(S(3)/2)) - S(4)/(S(49)*(S(1)/sinh(x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*sqrt(S(1)/sinh(x))/S(3) + x**S(2)/(S(1)/sinh(x))**(S(3)/2), x), x, S(2)*x**S(2)*cosh(x)/(S(3)*sqrt(S(1)/sinh(x))) - S(8)*x/(S(9)*(S(1)/sinh(x))**(S(3)/2)) - S(16)*I*sqrt(I*sinh(x))*sqrt(S(1)/sinh(x))*EllipticF(Pi/S(4) - I*x/S(2), S(2))/S(27) + S(16)*cosh(x)/(S(27)*sqrt(S(1)/sinh(x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)*(I*a*sinh(e + f*x) + a), x), x, -S(6)*I*a*d**S(3)*sinh(e + f*x)/f**S(4) + S(6)*I*a*d**S(2)*(c + d*x)*cosh(e + f*x)/f**S(3) - S(3)*I*a*d*(c + d*x)**S(2)*sinh(e + f*x)/f**S(2) + I*a*(c + d*x)**S(3)*cosh(e + f*x)/f + a*(c + d*x)**S(4)/(S(4)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)*(I*a*sinh(e + f*x) + a), x), x, S(2)*I*a*d**S(2)*cosh(e + f*x)/f**S(3) - S(2)*I*a*d*(c + d*x)*sinh(e + f*x)/f**S(2) + I*a*(c + d*x)**S(2)*cosh(e + f*x)/f + a*(c + d*x)**S(3)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)*(I*a*sinh(e + f*x) + a), x), x, -I*a*d*sinh(e + f*x)/f**S(2) + I*a*(c + d*x)*cosh(e + f*x)/f + a*(c + d*x)**S(2)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((I*a*sinh(e + f*x) + a)/(c + d*x), x), x, I*a*CoshIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d + I*a*SinhIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d + a*log(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((I*a*sinh(e + f*x) + a)/(c + d*x)**S(2), x), x, -I*a*sinh(e + f*x)/(d*(c + d*x)) - a/(d*(c + d*x)) + I*a*f*CoshIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d**S(2) + I*a*f*SinhIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((I*a*sinh(e + f*x) + a)/(c + d*x)**S(3), x), x, -I*a*sinh(e + f*x)/(S(2)*d*(c + d*x)**S(2)) - a/(S(2)*d*(c + d*x)**S(2)) - I*a*f*cosh(e + f*x)/(S(2)*d**S(2)*(c + d*x)) + I*a*f**S(2)*CoshIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/(S(2)*d**S(3)) + I*a*f**S(2)*SinhIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/(S(2)*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)*(I*a*sinh(e + f*x) + a)**S(2), x), x, S(3)*a**S(2)*c*d**S(2)*x/(S(4)*f**S(2)) + S(3)*a**S(2)*d**S(3)*x**S(2)/(S(8)*f**S(2)) + S(3)*a**S(2)*d**S(3)*sinh(e + f*x)**S(2)/(S(8)*f**S(4)) - S(12)*I*a**S(2)*d**S(3)*sinh(e + f*x)/f**S(4) - S(3)*a**S(2)*d**S(2)*(c + d*x)*sinh(e + f*x)*cosh(e + f*x)/(S(4)*f**S(3)) + S(12)*I*a**S(2)*d**S(2)*(c + d*x)*cosh(e + f*x)/f**S(3) + S(3)*a**S(2)*d*(c + d*x)**S(2)*sinh(e + f*x)**S(2)/(S(4)*f**S(2)) - S(6)*I*a**S(2)*d*(c + d*x)**S(2)*sinh(e + f*x)/f**S(2) - a**S(2)*(c + d*x)**S(3)*sinh(e + f*x)*cosh(e + f*x)/(S(2)*f) + S(2)*I*a**S(2)*(c + d*x)**S(3)*cosh(e + f*x)/f + S(3)*a**S(2)*(c + d*x)**S(4)/(S(8)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)*(I*a*sinh(e + f*x) + a)**S(2), x), x, a**S(2)*d**S(2)*x/(S(4)*f**S(2)) - a**S(2)*d**S(2)*sinh(e + f*x)*cosh(e + f*x)/(S(4)*f**S(3)) + S(4)*I*a**S(2)*d**S(2)*cosh(e + f*x)/f**S(3) + a**S(2)*d*(c + d*x)*sinh(e + f*x)**S(2)/(S(2)*f**S(2)) - S(4)*I*a**S(2)*d*(c + d*x)*sinh(e + f*x)/f**S(2) - a**S(2)*(c + d*x)**S(2)*sinh(e + f*x)*cosh(e + f*x)/(S(2)*f) + S(2)*I*a**S(2)*(c + d*x)**S(2)*cosh(e + f*x)/f + a**S(2)*(c + d*x)**S(3)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)*(I*a*sinh(e + f*x) + a)**S(2), x), x, a**S(2)*c*x/S(2) + a**S(2)*d*x**S(2)/S(4) + a**S(2)*d*sinh(e + f*x)**S(2)/(S(4)*f**S(2)) - S(2)*I*a**S(2)*d*sinh(e + f*x)/f**S(2) - a**S(2)*(c + d*x)*sinh(e + f*x)*cosh(e + f*x)/(S(2)*f) + S(2)*I*a**S(2)*(c + d*x)*cosh(e + f*x)/f + a**S(2)*(c + d*x)**S(2)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((I*a*sinh(e + f*x) + a)**S(2)/(c + d*x), x), x, S(2)*I*a**S(2)*CoshIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d - a**S(2)*CoshIntegral(S(2)*c*f/d + S(2)*f*x)*cosh(-S(2)*c*f/d + S(2)*e)/(S(2)*d) + S(2)*I*a**S(2)*SinhIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d - a**S(2)*SinhIntegral(S(2)*c*f/d + S(2)*f*x)*sinh(-S(2)*c*f/d + S(2)*e)/(S(2)*d) + S(3)*a**S(2)*log(c + d*x)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((I*a*sinh(e + f*x) + a)**S(2)/(c + d*x)**S(2), x), x, -S(4)*a**S(2)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(4)/(d*(c + d*x)) + S(2)*I*a**S(2)*f*CoshIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d**S(2) - a**S(2)*f*CoshIntegral(S(2)*c*f/d + S(2)*f*x)*sinh(-S(2)*c*f/d + S(2)*e)/d**S(2) + S(2)*I*a**S(2)*f*SinhIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d**S(2) - a**S(2)*f*SinhIntegral(S(2)*c*f/d + S(2)*f*x)*cosh(-S(2)*c*f/d + S(2)*e)/d**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((I*a*sinh(e + f*x) + a)**S(2)/(c + d*x)**S(3), x), x, -S(2)*a**S(2)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(4)/(d*(c + d*x)**S(2)) - S(4)*a**S(2)*f*sinh(I*Pi/S(4) + e/S(2) + f*x/S(2))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(3)/(d**S(2)*(c + d*x)) + I*a**S(2)*f**S(2)*CoshIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d**S(3) - a**S(2)*f**S(2)*CoshIntegral(S(2)*c*f/d + S(2)*f*x)*cosh(-S(2)*c*f/d + S(2)*e)/d**S(3) + I*a**S(2)*f**S(2)*SinhIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d**S(3) - a**S(2)*f**S(2)*SinhIntegral(S(2)*c*f/d + S(2)*f*x)*sinh(-S(2)*c*f/d + S(2)*e)/d**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)/(I*a*sinh(e + f*x) + a), x), x, S(12)*d**S(3)*PolyLog(S(3), -exp(I*Pi/S(2) + e + f*x))/(a*f**S(4)) - S(12)*d**S(2)*(c + d*x)*PolyLog(S(2), -exp(I*Pi/S(2) + e + f*x))/(a*f**S(3)) - S(6)*d*(c + d*x)**S(2)*log(exp(I*Pi/S(2) + e + f*x) + S(1))/(a*f**S(2)) + (c + d*x)**S(3)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f) + (c + d*x)**S(3)/(a*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)/(I*a*sinh(e + f*x) + a), x), x, -S(4)*d**S(2)*PolyLog(S(2), -exp(I*Pi/S(2) + e + f*x))/(a*f**S(3)) - S(4)*d*(c + d*x)*log(exp(I*Pi/S(2) + e + f*x) + S(1))/(a*f**S(2)) + (c + d*x)**S(2)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f) + (c + d*x)**S(2)/(a*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)/(I*a*sinh(e + f*x) + a), x), x, -S(2)*d*log(cosh(I*Pi/S(4) + e/S(2) + f*x/S(2)))/(a*f**S(2)) + (c + d*x)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)*(I*a*sinh(e + f*x) + a)), x), x, Integrate(S(1)/((c + d*x)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(2)), x)/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)**S(2)*(I*a*sinh(e + f*x) + a)), x), x, Integrate(S(1)/((c + d*x)**S(2)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(2)), x)/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)/(I*a*sinh(e + f*x) + a)**S(2), x), x, S(4)*d**S(3)*PolyLog(S(3), -exp(I*Pi/S(2) + e + f*x))/(a**S(2)*f**S(4)) + S(4)*d**S(3)*log(cosh(I*Pi/S(4) + e/S(2) + f*x/S(2)))/(a**S(2)*f**S(4)) - S(4)*d**S(2)*(c + d*x)*PolyLog(S(2), -exp(I*Pi/S(2) + e + f*x))/(a**S(2)*f**S(3)) - S(2)*d**S(2)*(c + d*x)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a**S(2)*f**S(3)) - S(2)*d*(c + d*x)**S(2)*log(exp(I*Pi/S(2) + e + f*x) + S(1))/(a**S(2)*f**S(2)) + d*(c + d*x)**S(2)/(S(2)*a**S(2)*f**S(2)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(2)) + (c + d*x)**S(3)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(3)*a**S(2)*f) + (c + d*x)**S(3)/(S(3)*a**S(2)*f) + (c + d*x)**S(3)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(6)*a**S(2)*f*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)/(I*a*sinh(e + f*x) + a)**S(2), x), x, -S(4)*d**S(2)*PolyLog(S(2), -exp(I*Pi/S(2) + e + f*x))/(S(3)*a**S(2)*f**S(3)) - S(2)*d**S(2)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(3)*a**S(2)*f**S(3)) - S(4)*d*(c + d*x)*log(exp(I*Pi/S(2) + e + f*x) + S(1))/(S(3)*a**S(2)*f**S(2)) + d*(c + d*x)/(S(3)*a**S(2)*f**S(2)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(2)) + (c + d*x)**S(2)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(3)*a**S(2)*f) + (c + d*x)**S(2)/(S(3)*a**S(2)*f) + (c + d*x)**S(2)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(6)*a**S(2)*f*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)/(I*a*sinh(e + f*x) + a)**S(2), x), x, -S(2)*d*log(cosh(I*Pi/S(4) + e/S(2) + f*x/S(2)))/(S(3)*a**S(2)*f**S(2)) + d/(S(6)*a**S(2)*f**S(2)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(2)) + (c + d*x)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(3)*a**S(2)*f) + (c + d*x)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(6)*a**S(2)*f*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)*(I*a*sinh(e + f*x) + a)**S(2)), x), x, Integrate(S(1)/((c + d*x)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(4)), x)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)**S(2)*(I*a*sinh(e + f*x) + a)**S(2)), x), x, Integrate(S(1)/((c + d*x)**S(2)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(4)), x)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(I*a*sinh(e + f*x) + a)/x, x), x, sqrt(I*a*sinh(e + f*x) + a)*CoshIntegral(f*x/S(2))*cosh(I*Pi/S(4) + e/S(2))/cosh(I*Pi/S(4) + e/S(2) + f*x/S(2)) + sqrt(I*a*sinh(e + f*x) + a)*SinhIntegral(f*x/S(2))*sinh(I*Pi/S(4) + e/S(2))/cosh(I*Pi/S(4) + e/S(2) + f*x/S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(I*a*sinh(e + f*x) + a)/x**S(2), x), x, f*sqrt(I*a*sinh(e + f*x) + a)*CoshIntegral(f*x/S(2))*sinh(I*Pi/S(4) + e/S(2))/(S(2)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))) + f*sqrt(I*a*sinh(e + f*x) + a)*SinhIntegral(f*x/S(2))*cosh(I*Pi/S(4) + e/S(2))/(S(2)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))) - sqrt(I*a*sinh(e + f*x) + a)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(I*a*sinh(e + f*x) + a)/x**S(3), x), x, f**S(2)*sqrt(I*a*sinh(e + f*x) + a)*CoshIntegral(f*x/S(2))*cosh(I*Pi/S(4) + e/S(2))/(S(8)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))) + f**S(2)*sqrt(I*a*sinh(e + f*x) + a)*SinhIntegral(f*x/S(2))*sinh(I*Pi/S(4) + e/S(2))/(S(8)*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))) - f*sqrt(I*a*sinh(e + f*x) + a)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(4)*x) - sqrt(I*a*sinh(e + f*x) + a)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(I*a*sinh(e + f*x) + a)), x), x, Integrate(S(1)/(x*sqrt(I*a*sinh(e + f*x) + a)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*sqrt(I*a*sinh(e + f*x) + a)), x), x, Integrate(S(1)/(x**S(2)*sqrt(I*a*sinh(e + f*x) + a)), x), expand=True, _diff=True, _numerical=True)

    ''' long time
    # assert rubi_test(rubi_integrate(x**S(3)/(I*a*sinh(e + f*x) + a)**(S(3)/2), x), x, x**S(3)*ArcTan(exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f*sqrt(I*a*sinh(e + f*x) + a)) + x**S(3)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(2)*a*f*sqrt(I*a*sinh(e + f*x) + a)) - S(3)*I*x**S(2)*PolyLog(S(2), -I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(2)*sqrt(I*a*sinh(e + f*x) + a)) + S(3)*I*x**S(2)*PolyLog(S(2), I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(2)*sqrt(I*a*sinh(e + f*x) + a)) + S(3)*x**S(2)/(a*f**S(2)*sqrt(I*a*sinh(e + f*x) + a)) - S(24)*x*ArcTan(exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(3)*sqrt(I*a*sinh(e + f*x) + a)) + S(12)*I*x*PolyLog(S(3), -I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(3)*sqrt(I*a*sinh(e + f*x) + a)) - S(12)*I*x*PolyLog(S(3), I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(3)*sqrt(I*a*sinh(e + f*x) + a)) + S(24)*I*PolyLog(S(2), -I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(4)*sqrt(I*a*sinh(e + f*x) + a)) - S(24)*I*PolyLog(S(2), I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(4)*sqrt(I*a*sinh(e + f*x) + a)) - S(24)*I*PolyLog(S(4), -I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(4)*sqrt(I*a*sinh(e + f*x) + a)) + S(24)*I*PolyLog(S(4), I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(4)*sqrt(I*a*sinh(e + f*x) + a)), expand=True, _diff=True, _numerical=True)
    # assert rubi_test(rubi_integrate(x**S(2)/(I*a*sinh(e + f*x) + a)**(S(3)/2), x), x, x**S(2)*ArcTan(exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f*sqrt(I*a*sinh(e + f*x) + a)) + x**S(2)*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(2)*a*f*sqrt(I*a*sinh(e + f*x) + a)) - S(2)*I*x*PolyLog(S(2), -I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(2)*sqrt(I*a*sinh(e + f*x) + a)) + S(2)*I*x*PolyLog(S(2), I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(2)*sqrt(I*a*sinh(e + f*x) + a)) + S(2)*x/(a*f**S(2)*sqrt(I*a*sinh(e + f*x) + a)) - S(4)*ArcTan(sinh(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(3)*sqrt(I*a*sinh(e + f*x) + a)) + S(4)*I*PolyLog(S(3), -I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(3)*sqrt(I*a*sinh(e + f*x) + a)) - S(4)*I*PolyLog(S(3), I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(3)*sqrt(I*a*sinh(e + f*x) + a)), expand=True, _diff=True, _numerical=True)
    # assert rubi_test(rubi_integrate(x/(I*a*sinh(e + f*x) + a)**(S(3)/2), x), x, x*ArcTan(exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f*sqrt(I*a*sinh(e + f*x) + a)) + x*tanh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(S(2)*a*f*sqrt(I*a*sinh(e + f*x) + a)) - I*PolyLog(S(2), -I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(2)*sqrt(I*a*sinh(e + f*x) + a)) + I*PolyLog(S(2), I*exp(I*Pi/S(4) + e/S(2) + f*x/S(2)))*cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))/(a*f**S(2)*sqrt(I*a*sinh(e + f*x) + a)) + S(1)/(a*f**S(2)*sqrt(I*a*sinh(e + f*x) + a)), expand=True, _diff=True, _numerical=True)
    '''
    assert rubi_test(rubi_integrate(S(1)/(x*(I*a*sinh(e + f*x) + a)**(S(3)/2)), x), x, Integrate(S(1)/(x*(I*a*sinh(e + f*x) + a)**(S(3)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*(I*a*sinh(e + f*x) + a)**(S(3)/2)), x), x, Integrate(S(1)/(x**S(2)*(I*a*sinh(e + f*x) + a)**(S(3)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(I*a*sinh(c + d*x) + a)**(S(5)/2)), x), x, Integrate(S(1)/(x*(I*a*sinh(c + d*x) + a)**(S(5)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((I*a*sinh(e + f*x) + a)**(S(1)/3)/x, x), x, Integrate((I*a*sinh(e + f*x) + a)**(S(1)/3)/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m*(I*a*sinh(e + f*x) + a)**n, x), x, Integrate((c + d*x)**m*(I*a*sinh(e + f*x) + a)**n, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m*(I*a*sinh(e + f*x) + a)**S(3), x), x, -S(3)*S(2)**(-m + S(-3))*a**S(3)*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -S(2)*f*(c + d*x)/d)*exp(-S(2)*c*f/d + S(2)*e)/f + S(3)*S(2)**(-m + S(-3))*a**S(3)*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), S(2)*f*(c + d*x)/d)*exp(S(2)*c*f/d - S(2)*e)/f - S(3)**(-m + S(-1))*I*a**S(3)*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -S(3)*f*(c + d*x)/d)*exp(-S(3)*c*f/d + S(3)*e)/(S(8)*f) - S(3)**(-m + S(-1))*I*a**S(3)*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), S(3)*f*(c + d*x)/d)*exp(S(3)*c*f/d - S(3)*e)/(S(8)*f) + S(15)*I*a**S(3)*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -f*(c + d*x)/d)*exp(-c*f/d + e)/(S(8)*f) + S(15)*I*a**S(3)*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), f*(c + d*x)/d)*exp(c*f/d - e)/(S(8)*f) + S(5)*a**S(3)*(c + d*x)**(m + S(1))/(S(2)*d*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m*(I*a*sinh(e + f*x) + a)**S(2), x), x, -S(2)**(-m + S(-3))*a**S(2)*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -S(2)*f*(c + d*x)/d)*exp(-S(2)*c*f/d + S(2)*e)/f + S(2)**(-m + S(-3))*a**S(2)*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), S(2)*f*(c + d*x)/d)*exp(S(2)*c*f/d - S(2)*e)/f + I*a**S(2)*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -f*(c + d*x)/d)*exp(-c*f/d + e)/f + I*a**S(2)*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), f*(c + d*x)/d)*exp(c*f/d - e)/f + S(3)*a**S(2)*(c + d*x)**(m + S(1))/(S(2)*d*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m*(I*a*sinh(e + f*x) + a), x), x, I*a*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -f*(c + d*x)/d)*exp(-c*f/d + e)/(S(2)*f) + I*a*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), f*(c + d*x)/d)*exp(c*f/d - e)/(S(2)*f) + a*(c + d*x)**(m + S(1))/(d*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m/(I*a*sinh(e + f*x) + a), x), x, Integrate((c + d*x)**m/cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(2), x)/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m/(I*a*sinh(e + f*x) + a)**S(2), x), x, Integrate((c + d*x)**m/cosh(I*Pi/S(4) + e/S(2) + f*x/S(2))**S(4), x)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))*(c + d*x)**S(3), x), x, a*(c + d*x)**S(4)/(S(4)*d) - S(6)*b*d**S(3)*sinh(e + f*x)/f**S(4) + S(6)*b*d**S(2)*(c + d*x)*cosh(e + f*x)/f**S(3) - S(3)*b*d*(c + d*x)**S(2)*sinh(e + f*x)/f**S(2) + b*(c + d*x)**S(3)*cosh(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))*(c + d*x)**S(2), x), x, a*(c + d*x)**S(3)/(S(3)*d) + S(2)*b*d**S(2)*cosh(e + f*x)/f**S(3) - S(2)*b*d*(c + d*x)*sinh(e + f*x)/f**S(2) + b*(c + d*x)**S(2)*cosh(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))*(c + d*x), x), x, a*(c + d*x)**S(2)/(S(2)*d) - b*d*sinh(e + f*x)/f**S(2) + b*(c + d*x)*cosh(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))/(c + d*x), x), x, a*log(c + d*x)/d + b*CoshIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d + b*SinhIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))/(c + d*x)**S(2), x), x, -a/(d*(c + d*x)) - b*sinh(e + f*x)/(d*(c + d*x)) + b*f*CoshIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d**S(2) + b*f*SinhIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))/(c + d*x)**S(3), x), x, -a/(S(2)*d*(c + d*x)**S(2)) - b*sinh(e + f*x)/(S(2)*d*(c + d*x)**S(2)) - b*f*cosh(e + f*x)/(S(2)*d**S(2)*(c + d*x)) + b*f**S(2)*CoshIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/(S(2)*d**S(3)) + b*f**S(2)*SinhIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/(S(2)*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))**S(2)*(c + d*x)**S(3), x), x, a**S(2)*(c + d*x)**S(4)/(S(4)*d) - S(12)*a*b*d**S(3)*sinh(e + f*x)/f**S(4) + S(12)*a*b*d**S(2)*(c + d*x)*cosh(e + f*x)/f**S(3) - S(6)*a*b*d*(c + d*x)**S(2)*sinh(e + f*x)/f**S(2) + S(2)*a*b*(c + d*x)**S(3)*cosh(e + f*x)/f - S(3)*b**S(2)*c*d**S(2)*x/(S(4)*f**S(2)) - S(3)*b**S(2)*d**S(3)*x**S(2)/(S(8)*f**S(2)) - S(3)*b**S(2)*d**S(3)*sinh(e + f*x)**S(2)/(S(8)*f**S(4)) + S(3)*b**S(2)*d**S(2)*(c + d*x)*sinh(e + f*x)*cosh(e + f*x)/(S(4)*f**S(3)) - S(3)*b**S(2)*d*(c + d*x)**S(2)*sinh(e + f*x)**S(2)/(S(4)*f**S(2)) + b**S(2)*(c + d*x)**S(3)*sinh(e + f*x)*cosh(e + f*x)/(S(2)*f) - b**S(2)*(c + d*x)**S(4)/(S(8)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))**S(2)*(c + d*x)**S(2), x), x, a**S(2)*(c + d*x)**S(3)/(S(3)*d) + S(4)*a*b*d**S(2)*cosh(e + f*x)/f**S(3) - S(4)*a*b*d*(c + d*x)*sinh(e + f*x)/f**S(2) + S(2)*a*b*(c + d*x)**S(2)*cosh(e + f*x)/f - b**S(2)*d**S(2)*x/(S(4)*f**S(2)) + b**S(2)*d**S(2)*sinh(e + f*x)*cosh(e + f*x)/(S(4)*f**S(3)) - b**S(2)*d*(c + d*x)*sinh(e + f*x)**S(2)/(S(2)*f**S(2)) + b**S(2)*(c + d*x)**S(2)*sinh(e + f*x)*cosh(e + f*x)/(S(2)*f) - b**S(2)*(c + d*x)**S(3)/(S(6)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))**S(2)*(c + d*x), x), x, a**S(2)*(c + d*x)**S(2)/(S(2)*d) - S(2)*a*b*d*sinh(e + f*x)/f**S(2) + S(2)*a*b*(c + d*x)*cosh(e + f*x)/f - b**S(2)*c*x/S(2) - b**S(2)*d*x**S(2)/S(4) - b**S(2)*d*sinh(e + f*x)**S(2)/(S(4)*f**S(2)) + b**S(2)*(c + d*x)*sinh(e + f*x)*cosh(e + f*x)/(S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))**S(2)/(c + d*x), x), x, a**S(2)*log(c + d*x)/d + S(2)*a*b*CoshIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d + S(2)*a*b*SinhIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d + b**S(2)*CoshIntegral(S(2)*c*f/d + S(2)*f*x)*cosh(-S(2)*c*f/d + S(2)*e)/(S(2)*d) + b**S(2)*SinhIntegral(S(2)*c*f/d + S(2)*f*x)*sinh(-S(2)*c*f/d + S(2)*e)/(S(2)*d) - b**S(2)*log(c + d*x)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))**S(2)/(c + d*x)**S(2), x), x, -a**S(2)/(d*(c + d*x)) - S(2)*a*b*sinh(e + f*x)/(d*(c + d*x)) + S(2)*a*b*f*CoshIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d**S(2) + S(2)*a*b*f*SinhIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d**S(2) - b**S(2)*sinh(e + f*x)**S(2)/(d*(c + d*x)) + b**S(2)*f*CoshIntegral(S(2)*c*f/d + S(2)*f*x)*sinh(-S(2)*c*f/d + S(2)*e)/d**S(2) + b**S(2)*f*SinhIntegral(S(2)*c*f/d + S(2)*f*x)*cosh(-S(2)*c*f/d + S(2)*e)/d**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))**S(2)/(c + d*x)**S(3), x), x, -a**S(2)/(S(2)*d*(c + d*x)**S(2)) - a*b*sinh(e + f*x)/(d*(c + d*x)**S(2)) - a*b*f*cosh(e + f*x)/(d**S(2)*(c + d*x)) + a*b*f**S(2)*CoshIntegral(c*f/d + f*x)*sinh(-c*f/d + e)/d**S(3) + a*b*f**S(2)*SinhIntegral(c*f/d + f*x)*cosh(-c*f/d + e)/d**S(3) - b**S(2)*sinh(e + f*x)**S(2)/(S(2)*d*(c + d*x)**S(2)) - b**S(2)*f*sinh(e + f*x)*cosh(e + f*x)/(d**S(2)*(c + d*x)) + b**S(2)*f**S(2)*CoshIntegral(S(2)*c*f/d + S(2)*f*x)*cosh(-S(2)*c*f/d + S(2)*e)/d**S(3) + b**S(2)*f**S(2)*SinhIntegral(S(2)*c*f/d + S(2)*f*x)*sinh(-S(2)*c*f/d + S(2)*e)/d**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)/(a + b*sinh(e + f*x)), x), x, S(6)*d**S(3)*PolyLog(S(4), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(4)*sqrt(a**S(2) + b**S(2))) - S(6)*d**S(3)*PolyLog(S(4), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(4)*sqrt(a**S(2) + b**S(2))) - S(6)*d**S(2)*(c + d*x)*PolyLog(S(3), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(3)*sqrt(a**S(2) + b**S(2))) + S(6)*d**S(2)*(c + d*x)*PolyLog(S(3), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(3)*sqrt(a**S(2) + b**S(2))) + S(3)*d*(c + d*x)**S(2)*PolyLog(S(2), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(2)*sqrt(a**S(2) + b**S(2))) - S(3)*d*(c + d*x)**S(2)*PolyLog(S(2), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(2)*sqrt(a**S(2) + b**S(2))) + (c + d*x)**S(3)*log(b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(f*sqrt(a**S(2) + b**S(2))) - (c + d*x)**S(3)*log(b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(f*sqrt(a**S(2) + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)/(a + b*sinh(e + f*x)), x), x, -S(2)*d**S(2)*PolyLog(S(3), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(3)*sqrt(a**S(2) + b**S(2))) + S(2)*d**S(2)*PolyLog(S(3), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(3)*sqrt(a**S(2) + b**S(2))) + S(2)*d*(c + d*x)*PolyLog(S(2), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(2)*sqrt(a**S(2) + b**S(2))) - S(2)*d*(c + d*x)*PolyLog(S(2), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(2)*sqrt(a**S(2) + b**S(2))) + (c + d*x)**S(2)*log(b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(f*sqrt(a**S(2) + b**S(2))) - (c + d*x)**S(2)*log(b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(f*sqrt(a**S(2) + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)/(a + b*sinh(e + f*x)), x), x, d*PolyLog(S(2), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(2)*sqrt(a**S(2) + b**S(2))) - d*PolyLog(S(2), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(2)*sqrt(a**S(2) + b**S(2))) + (c + d*x)*log(b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(f*sqrt(a**S(2) + b**S(2))) - (c + d*x)*log(b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(f*sqrt(a**S(2) + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*sinh(e + f*x))*(c + d*x)), x), x, Integrate(S(1)/((a + b*sinh(e + f*x))*(c + d*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*sinh(e + f*x))*(c + d*x)**S(2)), x), x, Integrate(S(1)/((a + b*sinh(e + f*x))*(c + d*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)/(a + b*sinh(e + f*x))**S(2), x), x, S(6)*a*d**S(3)*PolyLog(S(4), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(4)*(a**S(2) + b**S(2))**(S(3)/2)) - S(6)*a*d**S(3)*PolyLog(S(4), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(4)*(a**S(2) + b**S(2))**(S(3)/2)) - S(6)*a*d**S(2)*(c + d*x)*PolyLog(S(3), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(3)*(a**S(2) + b**S(2))**(S(3)/2)) + S(6)*a*d**S(2)*(c + d*x)*PolyLog(S(3), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(3)*(a**S(2) + b**S(2))**(S(3)/2)) + S(3)*a*d*(c + d*x)**S(2)*PolyLog(S(2), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) - S(3)*a*d*(c + d*x)**S(2)*PolyLog(S(2), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) + a*(c + d*x)**S(3)*log(b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(f*(a**S(2) + b**S(2))**(S(3)/2)) - a*(c + d*x)**S(3)*log(b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(f*(a**S(2) + b**S(2))**(S(3)/2)) - b*(c + d*x)**S(3)*cosh(e + f*x)/(f*(a + b*sinh(e + f*x))*(a**S(2) + b**S(2))) - S(6)*d**S(3)*PolyLog(S(3), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(4)*(a**S(2) + b**S(2))) - S(6)*d**S(3)*PolyLog(S(3), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(4)*(a**S(2) + b**S(2))) + S(6)*d**S(2)*(c + d*x)*PolyLog(S(2), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(3)*(a**S(2) + b**S(2))) + S(6)*d**S(2)*(c + d*x)*PolyLog(S(2), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(3)*(a**S(2) + b**S(2))) + S(3)*d*(c + d*x)**S(2)*log(b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(f**S(2)*(a**S(2) + b**S(2))) + S(3)*d*(c + d*x)**S(2)*log(b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(f**S(2)*(a**S(2) + b**S(2))) - (c + d*x)**S(3)/(f*(a**S(2) + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)/(a + b*sinh(e + f*x))**S(2), x), x, -S(2)*a*d**S(2)*PolyLog(S(3), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(3)*(a**S(2) + b**S(2))**(S(3)/2)) + S(2)*a*d**S(2)*PolyLog(S(3), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(3)*(a**S(2) + b**S(2))**(S(3)/2)) + S(2)*a*d*(c + d*x)*PolyLog(S(2), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) - S(2)*a*d*(c + d*x)*PolyLog(S(2), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) + a*(c + d*x)**S(2)*log(b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(f*(a**S(2) + b**S(2))**(S(3)/2)) - a*(c + d*x)**S(2)*log(b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(f*(a**S(2) + b**S(2))**(S(3)/2)) - b*(c + d*x)**S(2)*cosh(e + f*x)/(f*(a + b*sinh(e + f*x))*(a**S(2) + b**S(2))) + S(2)*d**S(2)*PolyLog(S(2), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(3)*(a**S(2) + b**S(2))) + S(2)*d**S(2)*PolyLog(S(2), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(3)*(a**S(2) + b**S(2))) + S(2)*d*(c + d*x)*log(b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(f**S(2)*(a**S(2) + b**S(2))) + S(2)*d*(c + d*x)*log(b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(f**S(2)*(a**S(2) + b**S(2))) - (c + d*x)**S(2)/(f*(a**S(2) + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)/(a + b*sinh(e + f*x))**S(2), x), x, a*d*PolyLog(S(2), -b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))))/(f**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) - a*d*PolyLog(S(2), -b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))))/(f**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) + a*(c + d*x)*log(b*exp(e + f*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(f*(a**S(2) + b**S(2))**(S(3)/2)) - a*(c + d*x)*log(b*exp(e + f*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(f*(a**S(2) + b**S(2))**(S(3)/2)) - b*(c + d*x)*cosh(e + f*x)/(f*(a + b*sinh(e + f*x))*(a**S(2) + b**S(2))) + d*log(a + b*sinh(e + f*x))/(f**S(2)*(a**S(2) + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*sinh(e + f*x))**S(2)*(c + d*x)), x), x, Integrate(S(1)/((a + b*sinh(e + f*x))**S(2)*(c + d*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*sinh(e + f*x))**S(2)*(c + d*x)**S(2)), x), x, Integrate(S(1)/((a + b*sinh(e + f*x))**S(2)*(c + d*x)**S(2)), x), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate((e + f*x)**S(2)/(a + b*sinh(c + d*x))**S(3), x), x, S(3)*a**S(2)*(e + f*x)**S(2)*log(b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(S(2)*d*(a**S(2) + b**S(2))**(S(5)/2)) - S(3)*a**S(2)*(e + f*x)**S(2)*log(b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(S(2)*d*(a**S(2) + b**S(2))**(S(5)/2)) + S(3)*a**S(2)*f*(e + f*x)*PolyLog(S(2), -b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))))/(d**S(2)*(a**S(2) + b**S(2))**(S(5)/2)) - S(3)*a**S(2)*f*(e + f*x)*PolyLog(S(2), -b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))))/(d**S(2)*(a**S(2) + b**S(2))**(S(5)/2)) - S(3)*a**S(2)*f**S(2)*PolyLog(S(3), -b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))))/(d**S(3)*(a**S(2) + b**S(2))**(S(5)/2)) + S(3)*a**S(2)*f**S(2)*PolyLog(S(3), -b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))))/(d**S(3)*(a**S(2) + b**S(2))**(S(5)/2)) - S(3)*a*b*(e + f*x)**S(2)*cosh(c + d*x)/(S(2)*d*(a + b*sinh(c + d*x))*(a**S(2) + b**S(2))**S(2)) - S(3)*a*(e + f*x)**S(2)/(S(2)*d*(a**S(2) + b**S(2))**S(2)) + S(3)*a*f*(e + f*x)*log(b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(d**S(2)*(a**S(2) + b**S(2))**S(2)) + S(3)*a*f*(e + f*x)*log(b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(d**S(2)*(a**S(2) + b**S(2))**S(2)) + S(3)*a*f**S(2)*PolyLog(S(2), -b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))))/(d**S(3)*(a**S(2) + b**S(2))**S(2)) + S(3)*a*f**S(2)*PolyLog(S(2), -b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))))/(d**S(3)*(a**S(2) + b**S(2))**S(2)) - b*(e + f*x)**S(2)*cosh(c + d*x)/(S(2)*d*(a + b*sinh(c + d*x))**S(2)*(a**S(2) + b**S(2))) - (e + f*x)**S(2)*log(b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(S(2)*d*(a**S(2) + b**S(2))**(S(3)/2)) + (e + f*x)**S(2)*log(b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(S(2)*d*(a**S(2) + b**S(2))**(S(3)/2)) - f*(e + f*x)*PolyLog(S(2), -b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))))/(d**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) + f*(e + f*x)*PolyLog(S(2), -b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))))/(d**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) - f*(e + f*x)/(d**S(2)*(a + b*sinh(c + d*x))*(a**S(2) + b**S(2))) + f**S(2)*PolyLog(S(3), -b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))))/(d**S(3)*(a**S(2) + b**S(2))**(S(3)/2)) - f**S(2)*PolyLog(S(3), -b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))))/(d**S(3)*(a**S(2) + b**S(2))**(S(3)/2)) - S(2)*f**S(2)*atanh((-a*tanh(c/S(2) + d*x/S(2)) + b)/sqrt(a**S(2) + b**S(2)))/(d**S(3)*(a**S(2) + b**S(2))**(S(3)/2)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((e + f*x)/(a + b*sinh(c + d*x))**S(3), x), x, S(3)*a**S(2)*(e + f*x)*log(b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(S(2)*d*(a**S(2) + b**S(2))**(S(5)/2)) - S(3)*a**S(2)*(e + f*x)*log(b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(S(2)*d*(a**S(2) + b**S(2))**(S(5)/2)) + S(3)*a**S(2)*f*PolyLog(S(2), -b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))))/(S(2)*d**S(2)*(a**S(2) + b**S(2))**(S(5)/2)) - S(3)*a**S(2)*f*PolyLog(S(2), -b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))))/(S(2)*d**S(2)*(a**S(2) + b**S(2))**(S(5)/2)) - S(3)*a*b*(e + f*x)*cosh(c + d*x)/(S(2)*d*(a + b*sinh(c + d*x))*(a**S(2) + b**S(2))**S(2)) + S(3)*a*f*log(a + b*sinh(c + d*x))/(S(2)*d**S(2)*(a**S(2) + b**S(2))**S(2)) - b*(e + f*x)*cosh(c + d*x)/(S(2)*d*(a + b*sinh(c + d*x))**S(2)*(a**S(2) + b**S(2))) - (e + f*x)*log(b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))) + S(1))/(S(2)*d*(a**S(2) + b**S(2))**(S(3)/2)) + (e + f*x)*log(b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))) + S(1))/(S(2)*d*(a**S(2) + b**S(2))**(S(3)/2)) - f*PolyLog(S(2), -b*exp(c + d*x)/(a - sqrt(a**S(2) + b**S(2))))/(S(2)*d**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) + f*PolyLog(S(2), -b*exp(c + d*x)/(a + sqrt(a**S(2) + b**S(2))))/(S(2)*d**S(2)*(a**S(2) + b**S(2))**(S(3)/2)) - f/(S(2)*d**S(2)*(a + b*sinh(c + d*x))*(a**S(2) + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*sinh(c + d*x))**S(3)*(e + f*x)), x), x, Integrate(S(1)/((a + b*sinh(c + d*x))**S(3)*(e + f*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*sinh(c + d*x))**S(3)*(e + f*x)**S(2)), x), x, Integrate(S(1)/((a + b*sinh(c + d*x))**S(3)*(e + f*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))**n*(c + d*x)**m, x), x, Integrate((a + b*sinh(e + f*x))**n*(c + d*x)**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))**S(3)*(c + d*x)**m, x), x, S(3)*S(2)**(-m + S(-3))*a*b**S(2)*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -S(2)*f*(c + d*x)/d)*exp(-S(2)*c*f/d + S(2)*e)/f - S(3)*S(2)**(-m + S(-3))*a*b**S(2)*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), S(2)*f*(c + d*x)/d)*exp(S(2)*c*f/d - S(2)*e)/f + S(3)**(-m + S(-1))*b**S(3)*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -S(3)*f*(c + d*x)/d)*exp(-S(3)*c*f/d + S(3)*e)/(S(8)*f) + S(3)**(-m + S(-1))*b**S(3)*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), S(3)*f*(c + d*x)/d)*exp(S(3)*c*f/d - S(3)*e)/(S(8)*f) + a**S(3)*(c + d*x)**(m + S(1))/(d*(m + S(1))) + S(3)*a**S(2)*b*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -f*(c + d*x)/d)*exp(-c*f/d + e)/(S(2)*f) + S(3)*a**S(2)*b*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), f*(c + d*x)/d)*exp(c*f/d - e)/(S(2)*f) - S(3)*a*b**S(2)*(c + d*x)**(m + S(1))/(S(2)*d*(m + S(1))) - S(3)*b**S(3)*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -f*(c + d*x)/d)*exp(-c*f/d + e)/(S(8)*f) - S(3)*b**S(3)*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), f*(c + d*x)/d)*exp(c*f/d - e)/(S(8)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))**S(2)*(c + d*x)**m, x), x, S(2)**(-m + S(-3))*b**S(2)*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -S(2)*f*(c + d*x)/d)*exp(-S(2)*c*f/d + S(2)*e)/f - S(2)**(-m + S(-3))*b**S(2)*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), S(2)*f*(c + d*x)/d)*exp(S(2)*c*f/d - S(2)*e)/f + a**S(2)*(c + d*x)**(m + S(1))/(d*(m + S(1))) + a*b*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -f*(c + d*x)/d)*exp(-c*f/d + e)/f + a*b*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), f*(c + d*x)/d)*exp(c*f/d - e)/f - b**S(2)*(c + d*x)**(m + S(1))/(S(2)*d*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*sinh(e + f*x))*(c + d*x)**m, x), x, a*(c + d*x)**(m + S(1))/(d*(m + S(1))) + b*(-f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), -f*(c + d*x)/d)*exp(-c*f/d + e)/(S(2)*f) + b*(f*(c + d*x)/d)**(-m)*(c + d*x)**m*Gamma(m + S(1), f*(c + d*x)/d)*exp(c*f/d - e)/(S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m/(a + b*sinh(e + f*x)), x), x, Integrate((c + d*x)**m/(a + b*sinh(e + f*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**m/(a + b*sinh(e + f*x))**S(2), x), x, Integrate((c + d*x)**m/(a + b*sinh(e + f*x))**S(2), x), expand=True, _diff=True, _numerical=True)
