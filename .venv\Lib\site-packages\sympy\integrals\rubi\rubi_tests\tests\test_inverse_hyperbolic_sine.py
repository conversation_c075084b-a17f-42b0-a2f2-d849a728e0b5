import sys
from sympy.external import import_module
matchpy = import_module("matchpy")

if not matchpy:
    #bin/test will not execute any tests now
    disabled = True

if sys.version_info[:2] < (3, 6):
    disabled = True

from sympy.integrals.rubi.utility_function import (
        sympy_op_factory, Int, Sum, Set, With, Module, Scan, MapAnd, FalseQ,
        ZeroQ, NegativeQ, NonzeroQ, FreeQ, NFreeQ, List, Log, PositiveQ,
        PositiveIntegerQ, NegativeIntegerQ, IntegerQ, IntegersQ,
        ComplexNumberQ, PureComplexNumberQ, RealNumericQ, PositiveOrZeroQ,
        NegativeOrZeroQ, FractionOrNegativeQ, NegQ, Equal, Unequal, IntPart,
        FracPart, RationalQ, ProductQ, SumQ, NonsumQ, Subst, First, Rest,
        SqrtNumberQ, SqrtNumberSumQ, LinearQ, Sqrt, ArcCosh, Coefficient,
        Denominator, Hypergeometric2F1, Not, Simplify, FractionalPart,
        IntegerPart, AppellF1, EllipticPi, EllipticE, EllipticF, ArcTan,
        ArcCot, ArcCoth, ArcTanh, ArcSin, ArcSinh, ArcCos, ArcCsc, ArcSec,
        ArcCsch, ArcSech, Sinh, Tanh, Cosh, Sech, Csch, Coth, LessEqual, Less,
        Greater, GreaterEqual, FractionQ, IntLinearcQ, Expand, IndependentQ,
        PowerQ, IntegerPowerQ, PositiveIntegerPowerQ, FractionalPowerQ, AtomQ,
        ExpQ, LogQ, Head, MemberQ, TrigQ, SinQ, CosQ, TanQ, CotQ, SecQ, CscQ,
        Sin, Cos, Tan, Cot, Sec, Csc, HyperbolicQ, SinhQ, CoshQ, TanhQ, CothQ,
        SechQ, CschQ, InverseTrigQ, SinCosQ, SinhCoshQ, LeafCount, Numerator,
        NumberQ, NumericQ, Length, ListQ, Im, Re, InverseHyperbolicQ,
        InverseFunctionQ, TrigHyperbolicFreeQ, InverseFunctionFreeQ, RealQ,
        EqQ, FractionalPowerFreeQ, ComplexFreeQ, PolynomialQ, FactorSquareFree,
        PowerOfLinearQ, Exponent, QuadraticQ, LinearPairQ, BinomialParts,
        TrinomialParts, PolyQ, EvenQ, OddQ, PerfectSquareQ, NiceSqrtAuxQ,
        NiceSqrtQ, Together, PosAux, PosQ, CoefficientList, ReplaceAll,
        ExpandLinearProduct, GCD, ContentFactor, NumericFactor,
        NonnumericFactors, MakeAssocList, GensymSubst, KernelSubst,
        ExpandExpression, Apart, SmartApart, MatchQ,
        PolynomialQuotientRemainder, FreeFactors, NonfreeFactors,
        RemoveContentAux, RemoveContent, FreeTerms, NonfreeTerms,
        ExpandAlgebraicFunction, CollectReciprocals, ExpandCleanup,
        AlgebraicFunctionQ, Coeff, LeadTerm, RemainingTerms, LeadFactor,
        RemainingFactors, LeadBase, LeadDegree, Numer, Denom, hypergeom, Expon,
        MergeMonomials, PolynomialDivide, BinomialQ, TrinomialQ,
        GeneralizedBinomialQ, GeneralizedTrinomialQ, FactorSquareFreeList,
        PerfectPowerTest, SquareFreeFactorTest, RationalFunctionQ,
        RationalFunctionFactors, NonrationalFunctionFactors, Reverse,
        RationalFunctionExponents, RationalFunctionExpand, ExpandIntegrand,
        SimplerQ, SimplerSqrtQ, SumSimplerQ, BinomialDegree, TrinomialDegree,
        CancelCommonFactors, SimplerIntegrandQ, GeneralizedBinomialDegree,
        GeneralizedBinomialParts, GeneralizedTrinomialDegree,
        GeneralizedTrinomialParts, MonomialQ, MonomialSumQ,
        MinimumMonomialExponent, MonomialExponent, LinearMatchQ,
        PowerOfLinearMatchQ, QuadraticMatchQ, CubicMatchQ, BinomialMatchQ,
        TrinomialMatchQ, GeneralizedBinomialMatchQ, GeneralizedTrinomialMatchQ,
        QuotientOfLinearsMatchQ, PolynomialTermQ, PolynomialTerms,
        NonpolynomialTerms, PseudoBinomialParts, NormalizePseudoBinomial,
        PseudoBinomialPairQ, PseudoBinomialQ, PolynomialGCD, PolyGCD,
        AlgebraicFunctionFactors, NonalgebraicFunctionFactors,
        QuotientOfLinearsP, QuotientOfLinearsParts, QuotientOfLinearsQ,
        Flatten, Sort, AbsurdNumberQ, AbsurdNumberFactors,
        NonabsurdNumberFactors, SumSimplerAuxQ, Prepend, Drop,
        CombineExponents, FactorInteger, FactorAbsurdNumber,
        SubstForInverseFunction, SubstForFractionalPower,
        SubstForFractionalPowerOfQuotientOfLinears,
        FractionalPowerOfQuotientOfLinears, SubstForFractionalPowerQ,
        SubstForFractionalPowerAuxQ, FractionalPowerOfSquareQ,
        FractionalPowerSubexpressionQ, Apply, FactorNumericGcd,
        MergeableFactorQ, MergeFactor, MergeFactors, TrigSimplifyQ,
        TrigSimplify, TrigSimplifyRecur, Order, FactorOrder, Smallest,
        OrderedQ, MinimumDegree, PositiveFactors, Sign, NonpositiveFactors,
        PolynomialInAuxQ, PolynomialInQ, ExponentInAux, ExponentIn,
        PolynomialInSubstAux, PolynomialInSubst, Distrib, DistributeDegree,
        FunctionOfPower, DivideDegreesOfFactors, MonomialFactor, FullSimplify,
        FunctionOfLinearSubst, FunctionOfLinear, NormalizeIntegrand,
        NormalizeIntegrandAux, NormalizeIntegrandFactor,
        NormalizeIntegrandFactorBase, NormalizeTogether,
        NormalizeLeadTermSigns, AbsorbMinusSign, NormalizeSumFactors,
        SignOfFactor, NormalizePowerOfLinear, SimplifyIntegrand, SimplifyTerm,
        TogetherSimplify, SmartSimplify, SubstForExpn, ExpandToSum, UnifySum,
        UnifyTerms, UnifyTerm, CalculusQ, FunctionOfInverseLinear,
        PureFunctionOfSinhQ, PureFunctionOfTanhQ, PureFunctionOfCoshQ,
        IntegerQuotientQ, OddQuotientQ, EvenQuotientQ, FindTrigFactor,
        FunctionOfSinhQ, FunctionOfCoshQ, OddHyperbolicPowerQ, FunctionOfTanhQ,
        FunctionOfTanhWeight, FunctionOfHyperbolicQ, SmartNumerator,
        SmartDenominator, SubstForAux, ActivateTrig, ExpandTrig, TrigExpand,
        SubstForTrig, SubstForHyperbolic, InertTrigFreeQ, LCM,
        SubstForFractionalPowerOfLinear, FractionalPowerOfLinear,
        InverseFunctionOfLinear, InertTrigQ, InertReciprocalQ, DeactivateTrig,
        FixInertTrigFunction, DeactivateTrigAux, PowerOfInertTrigSumQ,
        PiecewiseLinearQ, KnownTrigIntegrandQ, KnownSineIntegrandQ,
        KnownTangentIntegrandQ, KnownCotangentIntegrandQ,
        KnownSecantIntegrandQ, TryPureTanSubst, TryTanhSubst, TryPureTanhSubst,
        AbsurdNumberGCD, AbsurdNumberGCDList, ExpandTrigExpand,
        ExpandTrigReduce, ExpandTrigReduceAux, NormalizeTrig, TrigToExp,
        ExpandTrigToExp, TrigReduce, FunctionOfTrig, AlgebraicTrigFunctionQ,
        FunctionOfHyperbolic, FunctionOfQ, FunctionOfExpnQ, PureFunctionOfSinQ,
        PureFunctionOfCosQ, PureFunctionOfTanQ, PureFunctionOfCotQ,
        FunctionOfCosQ, FunctionOfSinQ, OddTrigPowerQ, FunctionOfTanQ,
        FunctionOfTanWeight, FunctionOfTrigQ, FunctionOfDensePolynomialsQ,
        FunctionOfLog, PowerVariableExpn, PowerVariableDegree,
        PowerVariableSubst, EulerIntegrandQ, FunctionOfSquareRootOfQuadratic,
        SquareRootOfQuadraticSubst, Divides, EasyDQ, ProductOfLinearPowersQ,
        Rt, NthRoot, AtomBaseQ, SumBaseQ, NegSumBaseQ, AllNegTermQ,
        SomeNegTermQ, TrigSquareQ, RtAux, TrigSquare, IntSum, IntTerm, Map2,
        ConstantFactor, SameQ, ReplacePart, CommonFactors,
        MostMainFactorPosition, FunctionOfExponentialQ, FunctionOfExponential,
        FunctionOfExponentialFunction, FunctionOfExponentialFunctionAux,
        FunctionOfExponentialTest, FunctionOfExponentialTestAux, stdev,
        rubi_test, If, IntQuadraticQ, IntBinomialQ, RectifyTangent,
        RectifyCotangent, Inequality, Condition, Simp, SimpHelp, SplitProduct,
        SplitSum, SubstFor, SubstForAux, FresnelS, FresnelC, Erfc, Erfi, Gamma,
        FunctionOfTrigOfLinearQ, ElementaryFunctionQ, Complex, UnsameQ,
        _SimpFixFactor, SimpFixFactor, _FixSimplify, FixSimplify,
        _SimplifyAntiderivativeSum, SimplifyAntiderivativeSum,
        _SimplifyAntiderivative, SimplifyAntiderivative, _TrigSimplifyAux,
        TrigSimplifyAux, Cancel, Part, PolyLog, D, Dist, Sum_doit, PolynomialQuotient, Floor,
        PolynomialRemainder, Factor, PolyLog, CosIntegral, SinIntegral, LogIntegral, SinhIntegral,
        CoshIntegral, Rule, Erf, PolyGamma, ExpIntegralEi, ExpIntegralE, LogGamma , UtilityOperator, Factorial,
        Zeta, ProductLog, DerivativeDivides, HypergeometricPFQ, IntHide, OneQ
    )
from sympy.core.add import Add
from sympy.core.mod import Mod
from sympy.core.mul import Mul
from sympy.core.numbers import (Float, I, Integer)
from sympy.core.power import Pow
from sympy.core.singleton import S
from sympy.functions.elementary.complexes import Abs
from sympy.functions.elementary.miscellaneous import sqrt
from sympy.integrals.integrals import Integral as Integrate
from sympy.logic.boolalg import (And, Or)
from sympy.simplify.simplify import simplify
from sympy.integrals.rubi.symbol import WC
from sympy.core.symbol import symbols, Symbol
from sympy.functions import (sin, cos, tan, cot, csc, sec, sqrt, erf, exp, log)
from sympy.functions.elementary.hyperbolic import (acosh, asinh, atanh, acoth, acsch, asech, cosh, sinh, tanh, coth, sech, csch)
from sympy.functions.elementary.trigonometric import (atan, acsc, asin, acot, acos, asec)
from sympy.integrals.rubi.rubimain import rubi_integrate
from sympy.core.numbers import pi as Pi
a, b, c, d, e, f, m, n, x, u , k, p, r, s, t, i, j= symbols('a b c d e f m n x u k p r s t i j')
A, B, C, D, a, b, c, d, e, f, g, h, y, z, m, n, p, q, u, v, w, F = symbols('A B C D a b c d e f g h y z m n p q u v w F', )

def test_1():
    assert rubi_test(rubi_integrate(x**S(4)*asinh(a*x), x), x, x**S(5)*asinh(a*x)/S(5) - (a**S(2)*x**S(2) + S(1))**(S(5)/2)/(S(25)*a**S(5)) + S(2)*(a**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(15)*a**S(5)) - sqrt(a**S(2)*x**S(2) + S(1))/(S(5)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asinh(a*x), x), x, x**S(4)*asinh(a*x)/S(4) - x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(16)*a) + S(3)*x*sqrt(a**S(2)*x**S(2) + S(1))/(S(32)*a**S(3)) - S(3)*asinh(a*x)/(S(32)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asinh(a*x), x), x, x**S(3)*asinh(a*x)/S(3) - (a**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(9)*a**S(3)) + sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asinh(a*x), x), x, x**S(2)*asinh(a*x)/S(2) - x*sqrt(a**S(2)*x**S(2) + S(1))/(S(4)*a) + asinh(a*x)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x), x), x, x*asinh(a*x) - sqrt(a**S(2)*x**S(2) + S(1))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)/x, x), x, PolyLog(S(2), exp(S(2)*asinh(a*x)))/S(2) + log(-exp(S(2)*asinh(a*x)) + S(1))*asinh(a*x) - asinh(a*x)**S(2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)/x**S(2), x), x, -a*atanh(sqrt(a**S(2)*x**S(2) + S(1))) - asinh(a*x)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)/x**S(3), x), x, -a*sqrt(a**S(2)*x**S(2) + S(1))/(S(2)*x) - asinh(a*x)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)/x**S(4), x), x, a**S(3)*atanh(sqrt(a**S(2)*x**S(2) + S(1)))/S(6) - a*sqrt(a**S(2)*x**S(2) + S(1))/(S(6)*x**S(2)) - asinh(a*x)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)/x**S(5), x), x, a**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(6)*x) - a*sqrt(a**S(2)*x**S(2) + S(1))/(S(12)*x**S(3)) - asinh(a*x)/(S(4)*x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)/x**S(6), x), x, -S(3)*a**S(5)*atanh(sqrt(a**S(2)*x**S(2) + S(1)))/S(40) + S(3)*a**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(40)*x**S(2)) - a*sqrt(a**S(2)*x**S(2) + S(1))/(S(20)*x**S(4)) - asinh(a*x)/(S(5)*x**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asinh(a*x)**S(2), x), x, x**S(5)*asinh(a*x)**S(2)/S(5) + S(2)*x**S(5)/S(125) - S(2)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(25)*a) - S(8)*x**S(3)/(S(225)*a**S(2)) + S(8)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(75)*a**S(3)) + S(16)*x/(S(75)*a**S(4)) - S(16)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(75)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asinh(a*x)**S(2), x), x, x**S(4)*asinh(a*x)**S(2)/S(4) + x**S(4)/S(32) - x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(8)*a) - S(3)*x**S(2)/(S(32)*a**S(2)) + S(3)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(16)*a**S(3)) - S(3)*asinh(a*x)**S(2)/(S(32)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asinh(a*x)**S(2), x), x, x**S(3)*asinh(a*x)**S(2)/S(3) + S(2)*x**S(3)/S(27) - S(2)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(9)*a) - S(4)*x/(S(9)*a**S(2)) + S(4)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(9)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asinh(a*x)**S(2), x), x, x**S(2)*asinh(a*x)**S(2)/S(2) + x**S(2)/S(4) - x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(2)*a) + asinh(a*x)**S(2)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(2), x), x, x*asinh(a*x)**S(2) + S(2)*x - S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(2)/x, x), x, PolyLog(S(2), exp(S(2)*asinh(a*x)))*asinh(a*x) - PolyLog(S(3), exp(S(2)*asinh(a*x)))/S(2) + log(-exp(S(2)*asinh(a*x)) + S(1))*asinh(a*x)**S(2) - asinh(a*x)**S(3)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(2)/x**S(2), x), x, -S(2)*a*PolyLog(S(2), -exp(asinh(a*x))) + S(2)*a*PolyLog(S(2), exp(asinh(a*x))) - S(4)*a*asinh(a*x)*atanh(exp(asinh(a*x))) - asinh(a*x)**S(2)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(2)/x**S(3), x), x, a**S(2)*log(x) - a*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/x - asinh(a*x)**S(2)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(2)/x**S(4), x), x, a**S(3)*PolyLog(S(2), -exp(asinh(a*x)))/S(3) - a**S(3)*PolyLog(S(2), exp(asinh(a*x)))/S(3) + S(2)*a**S(3)*asinh(a*x)*atanh(exp(asinh(a*x)))/S(3) - a**S(2)/(S(3)*x) - a*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(3)*x**S(2)) - asinh(a*x)**S(2)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(2)/x**S(5), x), x, -a**S(4)*log(x)/S(3) + a**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(3)*x) - a**S(2)/(S(12)*x**S(2)) - a*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(6)*x**S(3)) - asinh(a*x)**S(2)/(S(4)*x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asinh(a*x)**S(3), x), x, x**S(5)*asinh(a*x)**S(3)/S(5) + S(6)*x**S(5)*asinh(a*x)/S(125) - S(3)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(25)*a) - S(8)*x**S(3)*asinh(a*x)/(S(75)*a**S(2)) + S(4)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(25)*a**S(3)) + S(16)*x*asinh(a*x)/(S(25)*a**S(4)) - S(6)*(a**S(2)*x**S(2) + S(1))**(S(5)/2)/(S(625)*a**S(5)) + S(76)*(a**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(1125)*a**S(5)) - S(8)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(25)*a**S(5)) - S(298)*sqrt(a**S(2)*x**S(2) + S(1))/(S(375)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asinh(a*x)**S(3), x), x, x**S(4)*asinh(a*x)**S(3)/S(4) + S(3)*x**S(4)*asinh(a*x)/S(32) - S(3)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(16)*a) - S(3)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(128)*a) - S(9)*x**S(2)*asinh(a*x)/(S(32)*a**S(2)) + S(9)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(32)*a**S(3)) + S(45)*x*sqrt(a**S(2)*x**S(2) + S(1))/(S(256)*a**S(3)) - S(3)*asinh(a*x)**S(3)/(S(32)*a**S(4)) - S(45)*asinh(a*x)/(S(256)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asinh(a*x)**S(3), x), x, x**S(3)*asinh(a*x)**S(3)/S(3) + S(2)*x**S(3)*asinh(a*x)/S(9) - x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(3)*a) - S(4)*x*asinh(a*x)/(S(3)*a**S(2)) - S(2)*(a**S(2)*x**S(2) + S(1))**(S(3)/2)/(S(27)*a**S(3)) + S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(3)*a**S(3)) + S(14)*sqrt(a**S(2)*x**S(2) + S(1))/(S(9)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asinh(a*x)**S(3), x), x, x**S(2)*asinh(a*x)**S(3)/S(2) + S(3)*x**S(2)*asinh(a*x)/S(4) - S(3)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(4)*a) - S(3)*x*sqrt(a**S(2)*x**S(2) + S(1))/(S(8)*a) + asinh(a*x)**S(3)/(S(4)*a**S(2)) + S(3)*asinh(a*x)/(S(8)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(3), x), x, x*asinh(a*x)**S(3) + S(6)*x*asinh(a*x) - S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/a - S(6)*sqrt(a**S(2)*x**S(2) + S(1))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(3)/x, x), x, S(3)*PolyLog(S(2), exp(S(2)*asinh(a*x)))*asinh(a*x)**S(2)/S(2) - S(3)*PolyLog(S(3), exp(S(2)*asinh(a*x)))*asinh(a*x)/S(2) + S(3)*PolyLog(S(4), exp(S(2)*asinh(a*x)))/S(4) + log(-exp(S(2)*asinh(a*x)) + S(1))*asinh(a*x)**S(3) - asinh(a*x)**S(4)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(3)/x**S(2), x), x, -S(6)*a*PolyLog(S(2), -exp(asinh(a*x)))*asinh(a*x) + S(6)*a*PolyLog(S(2), exp(asinh(a*x)))*asinh(a*x) + S(6)*a*PolyLog(S(3), -exp(asinh(a*x))) - S(6)*a*PolyLog(S(3), exp(asinh(a*x))) - S(6)*a*asinh(a*x)**S(2)*atanh(exp(asinh(a*x))) - asinh(a*x)**S(3)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(3)/x**S(3), x), x, S(3)*a**S(2)*PolyLog(S(2), exp(S(2)*asinh(a*x)))/S(2) + S(3)*a**S(2)*log(-exp(S(2)*asinh(a*x)) + S(1))*asinh(a*x) - S(3)*a**S(2)*asinh(a*x)**S(2)/S(2) - S(3)*a*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(2)*x) - asinh(a*x)**S(3)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(3)/x**S(4), x), x, a**S(3)*PolyLog(S(2), -exp(asinh(a*x)))*asinh(a*x) - a**S(3)*PolyLog(S(2), exp(asinh(a*x)))*asinh(a*x) - a**S(3)*PolyLog(S(3), -exp(asinh(a*x))) + a**S(3)*PolyLog(S(3), exp(asinh(a*x))) + a**S(3)*asinh(a*x)**S(2)*atanh(exp(asinh(a*x))) - a**S(3)*atanh(sqrt(a**S(2)*x**S(2) + S(1))) - a**S(2)*asinh(a*x)/x - a*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(2)*x**S(2)) - asinh(a*x)**S(3)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(3)/x**S(5), x), x, -a**S(4)*PolyLog(S(2), exp(S(2)*asinh(a*x)))/S(2) - a**S(4)*log(-exp(S(2)*asinh(a*x)) + S(1))*asinh(a*x) + a**S(4)*asinh(a*x)**S(2)/S(2) + a**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(2)*x) - a**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(4)*x) - a**S(2)*asinh(a*x)/(S(4)*x**S(2)) - a*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(2)/(S(4)*x**S(3)) - asinh(a*x)**S(3)/(S(4)*x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)*asinh(a*x)**S(4), x), x, x**S(6)*asinh(a*x)**S(4)/S(6) + x**S(6)*asinh(a*x)**S(2)/S(18) + x**S(6)/S(324) - x**S(5)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(9)*a) - x**S(5)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(54)*a) - S(5)*x**S(4)*asinh(a*x)**S(2)/(S(48)*a**S(2)) - S(65)*x**S(4)/(S(3456)*a**S(2)) + S(5)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(36)*a**S(3)) + S(65)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(864)*a**S(3)) + S(5)*x**S(2)*asinh(a*x)**S(2)/(S(16)*a**S(4)) + S(245)*x**S(2)/(S(1152)*a**S(4)) - S(5)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(24)*a**S(5)) - S(245)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(576)*a**S(5)) + S(5)*asinh(a*x)**S(4)/(S(96)*a**S(6)) + S(245)*asinh(a*x)**S(2)/(S(1152)*a**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asinh(a*x)**S(4), x), x, x**S(5)*asinh(a*x)**S(4)/S(5) + S(12)*x**S(5)*asinh(a*x)**S(2)/S(125) + S(24)*x**S(5)/S(3125) - S(4)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(25)*a) - S(24)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(625)*a) - S(16)*x**S(3)*asinh(a*x)**S(2)/(S(75)*a**S(2)) - S(1088)*x**S(3)/(S(16875)*a**S(2)) + S(16)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(75)*a**S(3)) + S(1088)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(5625)*a**S(3)) + S(32)*x*asinh(a*x)**S(2)/(S(25)*a**S(4)) + S(16576)*x/(S(5625)*a**S(4)) - S(32)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(75)*a**S(5)) - S(16576)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(5625)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asinh(a*x)**S(4), x), x, x**S(4)*asinh(a*x)**S(4)/S(4) + S(3)*x**S(4)*asinh(a*x)**S(2)/S(16) + S(3)*x**S(4)/S(128) - x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(4)*a) - S(3)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(32)*a) - S(9)*x**S(2)*asinh(a*x)**S(2)/(S(16)*a**S(2)) - S(45)*x**S(2)/(S(128)*a**S(2)) + S(3)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(8)*a**S(3)) + S(45)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(64)*a**S(3)) - S(3)*asinh(a*x)**S(4)/(S(32)*a**S(4)) - S(45)*asinh(a*x)**S(2)/(S(128)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asinh(a*x)**S(4), x), x, x**S(3)*asinh(a*x)**S(4)/S(3) + S(4)*x**S(3)*asinh(a*x)**S(2)/S(9) + S(8)*x**S(3)/S(81) - S(4)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(9)*a) - S(8)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(27)*a) - S(8)*x*asinh(a*x)**S(2)/(S(3)*a**S(2)) - S(160)*x/(S(27)*a**S(2)) + S(8)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(9)*a**S(3)) + S(160)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(27)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asinh(a*x)**S(4), x), x, x**S(2)*asinh(a*x)**S(4)/S(2) + S(3)*x**S(2)*asinh(a*x)**S(2)/S(2) + S(3)*x**S(2)/S(4) - x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/a - S(3)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/(S(2)*a) + asinh(a*x)**S(4)/(S(4)*a**S(2)) + S(3)*asinh(a*x)**S(2)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(4), x), x, x*asinh(a*x)**S(4) + S(12)*x*asinh(a*x)**S(2) + S(24)*x - S(4)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/a - S(24)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(4)/x, x), x, S(2)*PolyLog(S(2), exp(S(2)*asinh(a*x)))*asinh(a*x)**S(3) - S(3)*PolyLog(S(3), exp(S(2)*asinh(a*x)))*asinh(a*x)**S(2) + S(3)*PolyLog(S(4), exp(S(2)*asinh(a*x)))*asinh(a*x) - S(3)*PolyLog(S(5), exp(S(2)*asinh(a*x)))/S(2) + log(-exp(S(2)*asinh(a*x)) + S(1))*asinh(a*x)**S(4) - asinh(a*x)**S(5)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(4)/x**S(2), x), x, -S(12)*a*PolyLog(S(2), -exp(asinh(a*x)))*asinh(a*x)**S(2) + S(12)*a*PolyLog(S(2), exp(asinh(a*x)))*asinh(a*x)**S(2) + S(24)*a*PolyLog(S(3), -exp(asinh(a*x)))*asinh(a*x) - S(24)*a*PolyLog(S(3), exp(asinh(a*x)))*asinh(a*x) - S(24)*a*PolyLog(S(4), -exp(asinh(a*x))) + S(24)*a*PolyLog(S(4), exp(asinh(a*x))) - S(8)*a*asinh(a*x)**S(3)*atanh(exp(asinh(a*x))) - asinh(a*x)**S(4)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(4)/x**S(3), x), x, S(6)*a**S(2)*PolyLog(S(2), exp(S(2)*asinh(a*x)))*asinh(a*x) - S(3)*a**S(2)*PolyLog(S(3), exp(S(2)*asinh(a*x))) + S(6)*a**S(2)*log(-exp(S(2)*asinh(a*x)) + S(1))*asinh(a*x)**S(2) - S(2)*a**S(2)*asinh(a*x)**S(3) - S(2)*a*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/x - asinh(a*x)**S(4)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**S(4)/x**S(4), x), x, S(2)*a**S(3)*PolyLog(S(2), -exp(asinh(a*x)))*asinh(a*x)**S(2) - S(4)*a**S(3)*PolyLog(S(2), -exp(asinh(a*x))) - S(2)*a**S(3)*PolyLog(S(2), exp(asinh(a*x)))*asinh(a*x)**S(2) + S(4)*a**S(3)*PolyLog(S(2), exp(asinh(a*x))) - S(4)*a**S(3)*PolyLog(S(3), -exp(asinh(a*x)))*asinh(a*x) + S(4)*a**S(3)*PolyLog(S(3), exp(asinh(a*x)))*asinh(a*x) + S(4)*a**S(3)*PolyLog(S(4), -exp(asinh(a*x))) - S(4)*a**S(3)*PolyLog(S(4), exp(asinh(a*x))) + S(4)*a**S(3)*asinh(a*x)**S(3)*atanh(exp(asinh(a*x)))/S(3) - S(8)*a**S(3)*asinh(a*x)*atanh(exp(asinh(a*x))) - S(2)*a**S(2)*asinh(a*x)**S(2)/x - S(2)*a*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**S(3)/(S(3)*x**S(2)) - asinh(a*x)**S(4)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(6)/asinh(a*x), x), x, -S(5)*CoshIntegral(asinh(a*x))/(S(64)*a**S(7)) + S(9)*CoshIntegral(S(3)*asinh(a*x))/(S(64)*a**S(7)) - S(5)*CoshIntegral(S(5)*asinh(a*x))/(S(64)*a**S(7)) + CoshIntegral(S(7)*asinh(a*x))/(S(64)*a**S(7)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)/asinh(a*x), x), x, S(5)*SinhIntegral(S(2)*asinh(a*x))/(S(32)*a**S(6)) - SinhIntegral(S(4)*asinh(a*x))/(S(8)*a**S(6)) + SinhIntegral(S(6)*asinh(a*x))/(S(32)*a**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asinh(a*x), x), x, CoshIntegral(asinh(a*x))/(S(8)*a**S(5)) - S(3)*CoshIntegral(S(3)*asinh(a*x))/(S(16)*a**S(5)) + CoshIntegral(S(5)*asinh(a*x))/(S(16)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asinh(a*x), x), x, -SinhIntegral(S(2)*asinh(a*x))/(S(4)*a**S(4)) + SinhIntegral(S(4)*asinh(a*x))/(S(8)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asinh(a*x), x), x, -CoshIntegral(asinh(a*x))/(S(4)*a**S(3)) + CoshIntegral(S(3)*asinh(a*x))/(S(4)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asinh(a*x), x), x, SinhIntegral(S(2)*asinh(a*x))/(S(2)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/asinh(a*x), x), x, CoshIntegral(asinh(a*x))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asinh(a*x)), x), x, Integrate(S(1)/(x*asinh(a*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*asinh(a*x)), x), x, Integrate(S(1)/(x**S(2)*asinh(a*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(6)/asinh(a*x)**S(2), x), x, -x**S(6)*sqrt(a**S(2)*x**S(2) + S(1))/(a*asinh(a*x)) - S(5)*SinhIntegral(asinh(a*x))/(S(64)*a**S(7)) + S(27)*SinhIntegral(S(3)*asinh(a*x))/(S(64)*a**S(7)) - S(25)*SinhIntegral(S(5)*asinh(a*x))/(S(64)*a**S(7)) + S(7)*SinhIntegral(S(7)*asinh(a*x))/(S(64)*a**S(7)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)/asinh(a*x)**S(2), x), x, -x**S(5)*sqrt(a**S(2)*x**S(2) + S(1))/(a*asinh(a*x)) + S(5)*CoshIntegral(S(2)*asinh(a*x))/(S(16)*a**S(6)) - CoshIntegral(S(4)*asinh(a*x))/(S(2)*a**S(6)) + S(3)*CoshIntegral(S(6)*asinh(a*x))/(S(16)*a**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asinh(a*x)**S(2), x), x, -x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))/(a*asinh(a*x)) + SinhIntegral(asinh(a*x))/(S(8)*a**S(5)) - S(9)*SinhIntegral(S(3)*asinh(a*x))/(S(16)*a**S(5)) + S(5)*SinhIntegral(S(5)*asinh(a*x))/(S(16)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asinh(a*x)**S(2), x), x, -x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(a*asinh(a*x)) - CoshIntegral(S(2)*asinh(a*x))/(S(2)*a**S(4)) + CoshIntegral(S(4)*asinh(a*x))/(S(2)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asinh(a*x)**S(2), x), x, -x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(a*asinh(a*x)) - SinhIntegral(asinh(a*x))/(S(4)*a**S(3)) + S(3)*SinhIntegral(S(3)*asinh(a*x))/(S(4)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asinh(a*x)**S(2), x), x, -x*sqrt(a**S(2)*x**S(2) + S(1))/(a*asinh(a*x)) + CoshIntegral(S(2)*asinh(a*x))/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(-2)), x), x, -sqrt(a**S(2)*x**S(2) + S(1))/(a*asinh(a*x)) + SinhIntegral(asinh(a*x))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asinh(a*x)**S(2)), x), x, Integrate(S(1)/(x*asinh(a*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*asinh(a*x)**S(2)), x), x, Integrate(S(1)/(x**S(2)*asinh(a*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asinh(a*x)**S(3), x), x, -S(5)*x**S(5)/(S(2)*asinh(a*x)) - x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))/(S(2)*a*asinh(a*x)**S(2)) - S(2)*x**S(3)/(a**S(2)*asinh(a*x)) + CoshIntegral(asinh(a*x))/(S(16)*a**S(5)) - S(27)*CoshIntegral(S(3)*asinh(a*x))/(S(32)*a**S(5)) + S(25)*CoshIntegral(S(5)*asinh(a*x))/(S(32)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asinh(a*x)**S(3), x), x, -S(2)*x**S(4)/asinh(a*x) - x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(2)*a*asinh(a*x)**S(2)) - S(3)*x**S(2)/(S(2)*a**S(2)*asinh(a*x)) - SinhIntegral(S(2)*asinh(a*x))/(S(2)*a**S(4)) + SinhIntegral(S(4)*asinh(a*x))/a**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asinh(a*x)**S(3), x), x, -S(3)*x**S(3)/(S(2)*asinh(a*x)) - x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(S(2)*a*asinh(a*x)**S(2)) - x/(a**S(2)*asinh(a*x)) - CoshIntegral(asinh(a*x))/(S(8)*a**S(3)) + S(9)*CoshIntegral(S(3)*asinh(a*x))/(S(8)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asinh(a*x)**S(3), x), x, -x**S(2)/asinh(a*x) - x*sqrt(a**S(2)*x**S(2) + S(1))/(S(2)*a*asinh(a*x)**S(2)) + SinhIntegral(S(2)*asinh(a*x))/a**S(2) - S(1)/(S(2)*a**S(2)*asinh(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(-3)), x), x, -x/(S(2)*asinh(a*x)) - sqrt(a**S(2)*x**S(2) + S(1))/(S(2)*a*asinh(a*x)**S(2)) + CoshIntegral(asinh(a*x))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asinh(a*x)**S(3)), x), x, Integrate(S(1)/(x*asinh(a*x)**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*asinh(a*x)**S(3)), x), x, Integrate(S(1)/(x**S(2)*asinh(a*x)**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asinh(a*x)**S(4), x), x, -S(5)*x**S(5)/(S(6)*asinh(a*x)**S(2)) - S(25)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))/(S(6)*a*asinh(a*x)) - x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**S(3)) - S(2)*x**S(3)/(S(3)*a**S(2)*asinh(a*x)**S(2)) - S(2)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(a**S(3)*asinh(a*x)) + SinhIntegral(asinh(a*x))/(S(48)*a**S(5)) - S(27)*SinhIntegral(S(3)*asinh(a*x))/(S(32)*a**S(5)) + S(125)*SinhIntegral(S(5)*asinh(a*x))/(S(96)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asinh(a*x)**S(4), x), x, -S(2)*x**S(4)/(S(3)*asinh(a*x)**S(2)) - S(8)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)) - x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**S(3)) - x**S(2)/(S(2)*a**S(2)*asinh(a*x)**S(2)) - x*sqrt(a**S(2)*x**S(2) + S(1))/(a**S(3)*asinh(a*x)) - CoshIntegral(S(2)*asinh(a*x))/(S(3)*a**S(4)) + S(4)*CoshIntegral(S(4)*asinh(a*x))/(S(3)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asinh(a*x)**S(4), x), x, -x**S(3)/(S(2)*asinh(a*x)**S(2)) - S(3)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(S(2)*a*asinh(a*x)) - x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**S(3)) - x/(S(3)*a**S(2)*asinh(a*x)**S(2)) - sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a**S(3)*asinh(a*x)) - SinhIntegral(asinh(a*x))/(S(24)*a**S(3)) + S(9)*SinhIntegral(S(3)*asinh(a*x))/(S(8)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asinh(a*x)**S(4), x), x, -x**S(2)/(S(3)*asinh(a*x)**S(2)) - S(2)*x*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)) - x*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**S(3)) + S(2)*CoshIntegral(S(2)*asinh(a*x))/(S(3)*a**S(2)) - S(1)/(S(6)*a**S(2)*asinh(a*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(-4)), x), x, -x/(S(6)*asinh(a*x)**S(2)) - sqrt(a**S(2)*x**S(2) + S(1))/(S(6)*a*asinh(a*x)) - sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**S(3)) + SinhIntegral(asinh(a*x))/(S(6)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asinh(a*x)**S(4)), x), x, Integrate(S(1)/(x*asinh(a*x)**S(4)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*asinh(a*x)**S(4)), x), x, Integrate(S(1)/(x**S(2)*asinh(a*x)**S(4)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*sqrt(asinh(a*x)), x), x, -sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(192)*a**S(5)) + sqrt(S(5))*sqrt(Pi)*Erf(sqrt(S(5))*sqrt(asinh(a*x)))/(S(1600)*a**S(5)) + sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(32)*a**S(5)) + sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(192)*a**S(5)) - sqrt(S(5))*sqrt(Pi)*Erfi(sqrt(S(5))*sqrt(asinh(a*x)))/(S(1600)*a**S(5)) - sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(32)*a**S(5)) + x**S(5)*sqrt(asinh(a*x))/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*sqrt(asinh(a*x)), x), x, sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(64)*a**S(4)) - sqrt(Pi)*Erf(S(2)*sqrt(asinh(a*x)))/(S(256)*a**S(4)) + sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(64)*a**S(4)) - sqrt(Pi)*Erfi(S(2)*sqrt(asinh(a*x)))/(S(256)*a**S(4)) + x**S(4)*sqrt(asinh(a*x))/S(4) - S(3)*sqrt(asinh(a*x))/(S(32)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*sqrt(asinh(a*x)), x), x, sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(144)*a**S(3)) - sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(16)*a**S(3)) - sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(144)*a**S(3)) + sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(16)*a**S(3)) + x**S(3)*sqrt(asinh(a*x))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*sqrt(asinh(a*x)), x), x, -sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(32)*a**S(2)) - sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(32)*a**S(2)) + x**S(2)*sqrt(asinh(a*x))/S(2) + sqrt(asinh(a*x))/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(asinh(a*x)), x), x, sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(4)*a) - sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(4)*a) + x*sqrt(asinh(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(asinh(a*x))/x, x), x, Integrate(sqrt(asinh(a*x))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asinh(a*x)**(S(3)/2), x), x, -sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(384)*a**S(5)) + S(3)*sqrt(S(5))*sqrt(Pi)*Erf(sqrt(S(5))*sqrt(asinh(a*x)))/(S(16000)*a**S(5)) + S(3)*sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(64)*a**S(5)) - sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(384)*a**S(5)) + S(3)*sqrt(S(5))*sqrt(Pi)*Erfi(sqrt(S(5))*sqrt(asinh(a*x)))/(S(16000)*a**S(5)) + S(3)*sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(64)*a**S(5)) + x**S(5)*asinh(a*x)**(S(3)/2)/S(5) - S(3)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))*sqrt(asinh(a*x))/(S(50)*a) + S(2)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*sqrt(asinh(a*x))/(S(25)*a**S(3)) - S(4)*sqrt(a**S(2)*x**S(2) + S(1))*sqrt(asinh(a*x))/(S(25)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asinh(a*x)**(S(3)/2), x), x, S(3)*sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(256)*a**S(4)) - S(3)*sqrt(Pi)*Erf(S(2)*sqrt(asinh(a*x)))/(S(2048)*a**S(4)) - S(3)*sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(256)*a**S(4)) + S(3)*sqrt(Pi)*Erfi(S(2)*sqrt(asinh(a*x)))/(S(2048)*a**S(4)) + x**S(4)*asinh(a*x)**(S(3)/2)/S(4) - S(3)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*sqrt(asinh(a*x))/(S(32)*a) + S(9)*x*sqrt(a**S(2)*x**S(2) + S(1))*sqrt(asinh(a*x))/(S(64)*a**S(3)) - S(3)*asinh(a*x)**(S(3)/2)/(S(32)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asinh(a*x)**(S(3)/2), x), x, sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(288)*a**S(3)) - S(3)*sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(32)*a**S(3)) + sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(288)*a**S(3)) - S(3)*sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(32)*a**S(3)) + x**S(3)*asinh(a*x)**(S(3)/2)/S(3) - x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*sqrt(asinh(a*x))/(S(6)*a) + sqrt(a**S(2)*x**S(2) + S(1))*sqrt(asinh(a*x))/(S(3)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asinh(a*x)**(S(3)/2), x), x, -S(3)*sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(128)*a**S(2)) + S(3)*sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(128)*a**S(2)) + x**S(2)*asinh(a*x)**(S(3)/2)/S(2) - S(3)*x*sqrt(a**S(2)*x**S(2) + S(1))*sqrt(asinh(a*x))/(S(8)*a) + asinh(a*x)**(S(3)/2)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(3)/2), x), x, S(3)*sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(8)*a) + S(3)*sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(8)*a) + x*asinh(a*x)**(S(3)/2) - S(3)*sqrt(a**S(2)*x**S(2) + S(1))*sqrt(asinh(a*x))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(3)/2)/x, x), x, Integrate(asinh(a*x)**(S(3)/2)/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asinh(a*x)**(S(5)/2), x), x, -S(5)*sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(2304)*a**S(5)) + S(3)*sqrt(S(5))*sqrt(Pi)*Erf(sqrt(S(5))*sqrt(asinh(a*x)))/(S(32000)*a**S(5)) + S(15)*sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(128)*a**S(5)) + S(5)*sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(2304)*a**S(5)) - S(3)*sqrt(S(5))*sqrt(Pi)*Erfi(sqrt(S(5))*sqrt(asinh(a*x)))/(S(32000)*a**S(5)) - S(15)*sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(128)*a**S(5)) + x**S(5)*asinh(a*x)**(S(5)/2)/S(5) + S(3)*x**S(5)*sqrt(asinh(a*x))/S(100) - x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**(S(3)/2)/(S(10)*a) - x**S(3)*sqrt(asinh(a*x))/(S(15)*a**S(2)) + S(2)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**(S(3)/2)/(S(15)*a**S(3)) + S(2)*x*sqrt(asinh(a*x))/(S(5)*a**S(4)) - S(4)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**(S(3)/2)/(S(15)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*asinh(a*x)**(S(5)/2), x), x, S(15)*sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(1024)*a**S(4)) - S(15)*sqrt(Pi)*Erf(S(2)*sqrt(asinh(a*x)))/(S(16384)*a**S(4)) + S(15)*sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(1024)*a**S(4)) - S(15)*sqrt(Pi)*Erfi(S(2)*sqrt(asinh(a*x)))/(S(16384)*a**S(4)) + x**S(4)*asinh(a*x)**(S(5)/2)/S(4) + S(15)*x**S(4)*sqrt(asinh(a*x))/S(256) - S(5)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**(S(3)/2)/(S(32)*a) - S(45)*x**S(2)*sqrt(asinh(a*x))/(S(256)*a**S(2)) + S(15)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**(S(3)/2)/(S(64)*a**S(3)) - S(3)*asinh(a*x)**(S(5)/2)/(S(32)*a**S(4)) - S(225)*sqrt(asinh(a*x))/(S(2048)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asinh(a*x)**(S(5)/2), x), x, S(5)*sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(1728)*a**S(3)) - S(15)*sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(64)*a**S(3)) - S(5)*sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(1728)*a**S(3)) + S(15)*sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(64)*a**S(3)) + x**S(3)*asinh(a*x)**(S(5)/2)/S(3) + S(5)*x**S(3)*sqrt(asinh(a*x))/S(36) - S(5)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**(S(3)/2)/(S(18)*a) - S(5)*x*sqrt(asinh(a*x))/(S(6)*a**S(2)) + S(5)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**(S(3)/2)/(S(9)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asinh(a*x)**(S(5)/2), x), x, -S(15)*sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(512)*a**S(2)) - S(15)*sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(512)*a**S(2)) + x**S(2)*asinh(a*x)**(S(5)/2)/S(2) + S(15)*x**S(2)*sqrt(asinh(a*x))/S(32) - S(5)*x*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**(S(3)/2)/(S(8)*a) + asinh(a*x)**(S(5)/2)/(S(4)*a**S(2)) + S(15)*sqrt(asinh(a*x))/(S(64)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(5)/2), x), x, S(15)*sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(16)*a) - S(15)*sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(16)*a) + x*asinh(a*x)**(S(5)/2) + S(15)*x*sqrt(asinh(a*x))/S(4) - S(5)*sqrt(a**S(2)*x**S(2) + S(1))*asinh(a*x)**(S(3)/2)/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(5)/2)/x, x), x, Integrate(asinh(a*x)**(S(5)/2)/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/sqrt(asinh(a*x)), x), x, -sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(32)*a**S(5)) + sqrt(S(5))*sqrt(Pi)*Erf(sqrt(S(5))*sqrt(asinh(a*x)))/(S(160)*a**S(5)) + sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(16)*a**S(5)) - sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(32)*a**S(5)) + sqrt(S(5))*sqrt(Pi)*Erfi(sqrt(S(5))*sqrt(asinh(a*x)))/(S(160)*a**S(5)) + sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(16)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/sqrt(asinh(a*x)), x), x, sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(16)*a**S(4)) - sqrt(Pi)*Erf(S(2)*sqrt(asinh(a*x)))/(S(32)*a**S(4)) - sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(16)*a**S(4)) + sqrt(Pi)*Erfi(S(2)*sqrt(asinh(a*x)))/(S(32)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/sqrt(asinh(a*x)), x), x, sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(24)*a**S(3)) - sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(8)*a**S(3)) + sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(24)*a**S(3)) - sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(8)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/sqrt(asinh(a*x)), x), x, -sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(8)*a**S(2)) + sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(8)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(asinh(a*x)), x), x, sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(2)*a) + sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(asinh(a*x))), x), x, Integrate(S(1)/(x*sqrt(asinh(a*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*sqrt(asinh(a*x))), x), x, Integrate(S(1)/(x**S(2)*sqrt(asinh(a*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asinh(a*x)**(S(3)/2), x), x, S(3)*sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(16)*a**S(5)) - sqrt(S(5))*sqrt(Pi)*Erf(sqrt(S(5))*sqrt(asinh(a*x)))/(S(16)*a**S(5)) - sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(8)*a**S(5)) - S(3)*sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(16)*a**S(5)) + sqrt(S(5))*sqrt(Pi)*Erfi(sqrt(S(5))*sqrt(asinh(a*x)))/(S(16)*a**S(5)) + sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(8)*a**S(5)) - S(2)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))/(a*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asinh(a*x)**(S(3)/2), x), x, -sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(4)*a**S(4)) + sqrt(Pi)*Erf(S(2)*sqrt(asinh(a*x)))/(S(4)*a**S(4)) - sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(4)*a**S(4)) + sqrt(Pi)*Erfi(S(2)*sqrt(asinh(a*x)))/(S(4)*a**S(4)) - S(2)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(a*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asinh(a*x)**(S(3)/2), x), x, -sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(4)*a**S(3)) + sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(4)*a**S(3)) + sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(4)*a**S(3)) - sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(4)*a**S(3)) - S(2)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(a*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asinh(a*x)**(S(3)/2), x), x, sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(2)*a**S(2)) + sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(2)*a**S(2)) - S(2)*x*sqrt(a**S(2)*x**S(2) + S(1))/(a*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(-3)/2), x), x, -sqrt(Pi)*Erf(sqrt(asinh(a*x)))/a + sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/a - S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(a*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asinh(a*x)**(S(3)/2)), x), x, Integrate(S(1)/(x*asinh(a*x)**(S(3)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asinh(a*x)**(S(5)/2), x), x, -S(3)*sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(8)*a**S(5)) + S(5)*sqrt(S(5))*sqrt(Pi)*Erf(sqrt(S(5))*sqrt(asinh(a*x)))/(S(24)*a**S(5)) + sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(12)*a**S(5)) - S(3)*sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(8)*a**S(5)) + S(5)*sqrt(S(5))*sqrt(Pi)*Erfi(sqrt(S(5))*sqrt(asinh(a*x)))/(S(24)*a**S(5)) + sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(12)*a**S(5)) - S(20)*x**S(5)/(S(3)*sqrt(asinh(a*x))) - S(2)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**(S(3)/2)) - S(16)*x**S(3)/(S(3)*a**S(2)*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asinh(a*x)**(S(5)/2), x), x, sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(3)*a**S(4)) - S(2)*sqrt(Pi)*Erf(S(2)*sqrt(asinh(a*x)))/(S(3)*a**S(4)) - sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(3)*a**S(4)) + S(2)*sqrt(Pi)*Erfi(S(2)*sqrt(asinh(a*x)))/(S(3)*a**S(4)) - S(16)*x**S(4)/(S(3)*sqrt(asinh(a*x))) - S(2)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**(S(3)/2)) - S(4)*x**S(2)/(a**S(2)*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asinh(a*x)**(S(5)/2), x), x, sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(2)*a**S(3)) - sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(6)*a**S(3)) + sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(2)*a**S(3)) - sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(6)*a**S(3)) - S(4)*x**S(3)/sqrt(asinh(a*x)) - S(2)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**(S(3)/2)) - S(8)*x/(S(3)*a**S(2)*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asinh(a*x)**(S(5)/2), x), x, -S(2)*sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(3)*a**S(2)) + S(2)*sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(3)*a**S(2)) - S(8)*x**S(2)/(S(3)*sqrt(asinh(a*x))) - S(2)*x*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**(S(3)/2)) - S(4)/(S(3)*a**S(2)*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(-5)/2), x), x, S(2)*sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(3)*a) + S(2)*sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(3)*a) - S(4)*x/(S(3)*sqrt(asinh(a*x))) - S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*asinh(a*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asinh(a*x)**(S(5)/2)), x), x, Integrate(S(1)/(x*asinh(a*x)**(S(5)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)/asinh(a*x)**(S(7)/2), x), x, S(9)*sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(20)*a**S(5)) - S(5)*sqrt(S(5))*sqrt(Pi)*Erf(sqrt(S(5))*sqrt(asinh(a*x)))/(S(12)*a**S(5)) - sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(30)*a**S(5)) - S(9)*sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(20)*a**S(5)) + S(5)*sqrt(S(5))*sqrt(Pi)*Erfi(sqrt(S(5))*sqrt(asinh(a*x)))/(S(12)*a**S(5)) + sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(30)*a**S(5)) - S(4)*x**S(5)/(S(3)*asinh(a*x)**(S(3)/2)) - S(40)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))/(S(3)*a*sqrt(asinh(a*x))) - S(2)*x**S(4)*sqrt(a**S(2)*x**S(2) + S(1))/(S(5)*a*asinh(a*x)**(S(5)/2)) - S(16)*x**S(3)/(S(15)*a**S(2)*asinh(a*x)**(S(3)/2)) - S(32)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(S(5)*a**S(3)*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/asinh(a*x)**(S(7)/2), x), x, -S(4)*sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(15)*a**S(4)) + S(16)*sqrt(Pi)*Erf(S(2)*sqrt(asinh(a*x)))/(S(15)*a**S(4)) - S(4)*sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(15)*a**S(4)) + S(16)*sqrt(Pi)*Erfi(S(2)*sqrt(asinh(a*x)))/(S(15)*a**S(4)) - S(16)*x**S(4)/(S(15)*asinh(a*x)**(S(3)/2)) - S(128)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(15)*a*sqrt(asinh(a*x))) - S(2)*x**S(3)*sqrt(a**S(2)*x**S(2) + S(1))/(S(5)*a*asinh(a*x)**(S(5)/2)) - S(4)*x**S(2)/(S(5)*a**S(2)*asinh(a*x)**(S(3)/2)) - S(16)*x*sqrt(a**S(2)*x**S(2) + S(1))/(S(5)*a**S(3)*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/asinh(a*x)**(S(7)/2), x), x, -S(3)*sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(asinh(a*x)))/(S(5)*a**S(3)) + sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(15)*a**S(3)) + S(3)*sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(asinh(a*x)))/(S(5)*a**S(3)) - sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(15)*a**S(3)) - S(4)*x**S(3)/(S(5)*asinh(a*x)**(S(3)/2)) - S(24)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(S(5)*a*sqrt(asinh(a*x))) - S(2)*x**S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(S(5)*a*asinh(a*x)**(S(5)/2)) - S(8)*x/(S(15)*a**S(2)*asinh(a*x)**(S(3)/2)) - S(16)*sqrt(a**S(2)*x**S(2) + S(1))/(S(15)*a**S(3)*sqrt(asinh(a*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/asinh(a*x)**(S(7)/2), x), x, S(8)*sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(asinh(a*x)))/(S(15)*a**S(2)) + S(8)*sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(asinh(a*x)))/(S(15)*a**S(2)) - S(8)*x**S(2)/(S(15)*asinh(a*x)**(S(3)/2)) - S(32)*x*sqrt(a**S(2)*x**S(2) + S(1))/(S(15)*a*sqrt(asinh(a*x))) - S(2)*x*sqrt(a**S(2)*x**S(2) + S(1))/(S(5)*a*asinh(a*x)**(S(5)/2)) - S(4)/(S(15)*a**S(2)*asinh(a*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**(S(-7)/2), x), x, -S(4)*sqrt(Pi)*Erf(sqrt(asinh(a*x)))/(S(15)*a) + S(4)*sqrt(Pi)*Erfi(sqrt(asinh(a*x)))/(S(15)*a) - S(4)*x/(S(15)*asinh(a*x)**(S(3)/2)) - S(8)*sqrt(a**S(2)*x**S(2) + S(1))/(S(15)*a*sqrt(asinh(a*x))) - S(2)*sqrt(a**S(2)*x**S(2) + S(1))/(S(5)*a*asinh(a*x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*asinh(a*x)**(S(7)/2)), x), x, Integrate(S(1)/(x*asinh(a*x)**(S(7)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*asinh(a*x)**S(4), x), x, -S(4)*a*Integrate(x**(m + S(1))*asinh(a*x)**S(3)/sqrt(a**S(2)*x**S(2) + S(1)), x)/(m + S(1)) + x**(m + S(1))*asinh(a*x)**S(4)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*asinh(a*x)**S(3), x), x, -S(3)*a*Integrate(x**(m + S(1))*asinh(a*x)**S(2)/sqrt(a**S(2)*x**S(2) + S(1)), x)/(m + S(1)) + x**(m + S(1))*asinh(a*x)**S(3)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*asinh(a*x)**S(2), x), x, S(2)*a**S(2)*x**(m + S(3))*HypergeometricPFQ(List(S(1), m/S(2) + S(3)/2, m/S(2) + S(3)/2), List(m/S(2) + S(2), m/S(2) + S(5)/2), -a**S(2)*x**S(2))/(m**S(3) + S(6)*m**S(2) + S(11)*m + S(6)) - S(2)*a*x**(m + S(2))*Hypergeometric2F1(S(1)/2, m/S(2) + S(1), m/S(2) + S(2), -a**S(2)*x**S(2))*asinh(a*x)/(m**S(2) + S(3)*m + S(2)) + x**(m + S(1))*asinh(a*x)**S(2)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*asinh(a*x), x), x, -a*x**(m + S(2))*Hypergeometric2F1(S(1)/2, m/S(2) + S(1), m/S(2) + S(2), -a**S(2)*x**S(2))/(m**S(2) + S(3)*m + S(2)) + x**(m + S(1))*asinh(a*x)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/asinh(a*x), x), x, Integrate(x**m/asinh(a*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/asinh(a*x)**S(2), x), x, Integrate(x**m/asinh(a*x)**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*asinh(a*x)**(S(5)/2), x), x, Integrate(x**m*asinh(a*x)**(S(5)/2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*asinh(a*x)**(S(3)/2), x), x, Integrate(x**m*asinh(a*x)**(S(3)/2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*sqrt(asinh(a*x)), x), x, Integrate(x**m*sqrt(asinh(a*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/sqrt(asinh(a*x)), x), x, Integrate(x**m/sqrt(asinh(a*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/asinh(a*x)**(S(3)/2), x), x, Integrate(x**m/asinh(a*x)**(S(3)/2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*x)**m*asinh(a*x)**n, x), x, Integrate((b*x)**m*asinh(a*x)**n, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*asinh(a*x)**n, x), x, -S(5)**(-n + S(-1))*Gamma(n + S(1), S(5)*asinh(a*x))/(S(32)*a**S(5)) + S(5)**(-n + S(-1))*(-asinh(a*x))**(-n)*Gamma(n + S(1), -S(5)*asinh(a*x))*asinh(a*x)**n/(S(32)*a**S(5)) - Gamma(n + S(1), asinh(a*x))/(S(16)*a**S(5)) + (-asinh(a*x))**(-n)*Gamma(n + S(1), -asinh(a*x))*asinh(a*x)**n/(S(16)*a**S(5)) + S(3)**(-n)*Gamma(n + S(1), S(3)*asinh(a*x))/(S(32)*a**S(5)) - S(3)**(-n)*(-asinh(a*x))**(-n)*Gamma(n + S(1), -S(3)*asinh(a*x))*asinh(a*x)**n/(S(32)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*asinh(a*x)**n, x), x, -S(3)**(-n + S(-1))*Gamma(n + S(1), S(3)*asinh(a*x))/(S(8)*a**S(3)) + S(3)**(-n + S(-1))*(-asinh(a*x))**(-n)*Gamma(n + S(1), -S(3)*asinh(a*x))*asinh(a*x)**n/(S(8)*a**S(3)) + Gamma(n + S(1), asinh(a*x))/(S(8)*a**S(3)) - (-asinh(a*x))**(-n)*Gamma(n + S(1), -asinh(a*x))*asinh(a*x)**n/(S(8)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*asinh(a*x)**n, x), x, S(2)**(-n + S(-3))*Gamma(n + S(1), S(2)*asinh(a*x))/a**S(2) + S(2)**(-n + S(-3))*(-asinh(a*x))**(-n)*Gamma(n + S(1), -S(2)*asinh(a*x))*asinh(a*x)**n/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**n, x), x, -Gamma(n + S(1), asinh(a*x))/(S(2)*a) + (-asinh(a*x))**(-n)*Gamma(n + S(1), -asinh(a*x))*asinh(a*x)**n/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**n/x, x), x, Integrate(asinh(a*x)**n/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(asinh(a*x)**n/x**S(2), x), x, Integrate(asinh(a*x)**n/x**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*sqrt(a + b*asinh(c*x)), x), x, -sqrt(Pi)*sqrt(b)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(16)*c**S(3)) + sqrt(S(3))*sqrt(Pi)*sqrt(b)*Erf(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(3)*a/b)/(S(144)*c**S(3)) + sqrt(Pi)*sqrt(b)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(16)*c**S(3)) - sqrt(S(3))*sqrt(Pi)*sqrt(b)*Erfi(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(3)*a/b)/(S(144)*c**S(3)) + x**S(3)*sqrt(a + b*asinh(c*x))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*sqrt(a + b*asinh(c*x)), x), x, -sqrt(S(2))*sqrt(Pi)*sqrt(b)*Erf(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(2)*a/b)/(S(32)*c**S(2)) - sqrt(S(2))*sqrt(Pi)*sqrt(b)*Erfi(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(2)*a/b)/(S(32)*c**S(2)) + x**S(2)*sqrt(a + b*asinh(c*x))/S(2) + sqrt(a + b*asinh(c*x))/(S(4)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*asinh(c*x)), x), x, sqrt(Pi)*sqrt(b)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(4)*c) - sqrt(Pi)*sqrt(b)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(4)*c) + x*sqrt(a + b*asinh(c*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(a + b*asinh(c*x))**(S(3)/2), x), x, -S(3)*sqrt(Pi)*b**(S(3)/2)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(32)*c**S(3)) + sqrt(S(3))*sqrt(Pi)*b**(S(3)/2)*Erf(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(3)*a/b)/(S(288)*c**S(3)) - S(3)*sqrt(Pi)*b**(S(3)/2)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(32)*c**S(3)) + sqrt(S(3))*sqrt(Pi)*b**(S(3)/2)*Erfi(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(3)*a/b)/(S(288)*c**S(3)) - b*x**S(2)*sqrt(a + b*asinh(c*x))*sqrt(c**S(2)*x**S(2) + S(1))/(S(6)*c) + b*sqrt(a + b*asinh(c*x))*sqrt(c**S(2)*x**S(2) + S(1))/(S(3)*c**S(3)) + x**S(3)*(a + b*asinh(c*x))**(S(3)/2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(a + b*asinh(c*x))**(S(3)/2), x), x, -S(3)*sqrt(S(2))*sqrt(Pi)*b**(S(3)/2)*Erf(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(2)*a/b)/(S(128)*c**S(2)) + S(3)*sqrt(S(2))*sqrt(Pi)*b**(S(3)/2)*Erfi(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(2)*a/b)/(S(128)*c**S(2)) - S(3)*b*x*sqrt(a + b*asinh(c*x))*sqrt(c**S(2)*x**S(2) + S(1))/(S(8)*c) + x**S(2)*(a + b*asinh(c*x))**(S(3)/2)/S(2) + (a + b*asinh(c*x))**(S(3)/2)/(S(4)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asinh(c*x))**(S(3)/2), x), x, S(3)*sqrt(Pi)*b**(S(3)/2)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(8)*c) + S(3)*sqrt(Pi)*b**(S(3)/2)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(8)*c) - S(3)*b*sqrt(a + b*asinh(c*x))*sqrt(c**S(2)*x**S(2) + S(1))/(S(2)*c) + x*(a + b*asinh(c*x))**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(a + b*asinh(c*x))**(S(5)/2), x), x, -S(15)*sqrt(Pi)*b**(S(5)/2)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(64)*c**S(3)) + S(5)*sqrt(S(3))*sqrt(Pi)*b**(S(5)/2)*Erf(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(3)*a/b)/(S(1728)*c**S(3)) + S(15)*sqrt(Pi)*b**(S(5)/2)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(64)*c**S(3)) - S(5)*sqrt(S(3))*sqrt(Pi)*b**(S(5)/2)*Erfi(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(3)*a/b)/(S(1728)*c**S(3)) + S(5)*b**S(2)*x**S(3)*sqrt(a + b*asinh(c*x))/S(36) - S(5)*b**S(2)*x*sqrt(a + b*asinh(c*x))/(S(6)*c**S(2)) - S(5)*b*x**S(2)*(a + b*asinh(c*x))**(S(3)/2)*sqrt(c**S(2)*x**S(2) + S(1))/(S(18)*c) + S(5)*b*(a + b*asinh(c*x))**(S(3)/2)*sqrt(c**S(2)*x**S(2) + S(1))/(S(9)*c**S(3)) + x**S(3)*(a + b*asinh(c*x))**(S(5)/2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(a + b*asinh(c*x))**(S(5)/2), x), x, -S(15)*sqrt(S(2))*sqrt(Pi)*b**(S(5)/2)*Erf(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(2)*a/b)/(S(512)*c**S(2)) - S(15)*sqrt(S(2))*sqrt(Pi)*b**(S(5)/2)*Erfi(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(2)*a/b)/(S(512)*c**S(2)) + S(15)*b**S(2)*x**S(2)*sqrt(a + b*asinh(c*x))/S(32) + S(15)*b**S(2)*sqrt(a + b*asinh(c*x))/(S(64)*c**S(2)) - S(5)*b*x*(a + b*asinh(c*x))**(S(3)/2)*sqrt(c**S(2)*x**S(2) + S(1))/(S(8)*c) + x**S(2)*(a + b*asinh(c*x))**(S(5)/2)/S(2) + (a + b*asinh(c*x))**(S(5)/2)/(S(4)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asinh(c*x))**(S(5)/2), x), x, S(15)*sqrt(Pi)*b**(S(5)/2)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(16)*c) - S(15)*sqrt(Pi)*b**(S(5)/2)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(16)*c) + S(15)*b**S(2)*x*sqrt(a + b*asinh(c*x))/S(4) - S(5)*b*(a + b*asinh(c*x))**(S(3)/2)*sqrt(c**S(2)*x**S(2) + S(1))/(S(2)*c) + x*(a + b*asinh(c*x))**(S(5)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/sqrt(a + b*asinh(c*x)), x), x, -sqrt(Pi)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(8)*sqrt(b)*c**S(3)) + sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(3)*a/b)/(S(24)*sqrt(b)*c**S(3)) - sqrt(Pi)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(8)*sqrt(b)*c**S(3)) + sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(3)*a/b)/(S(24)*sqrt(b)*c**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/sqrt(a + b*asinh(c*x)), x), x, -sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(2)*a/b)/(S(8)*sqrt(b)*c**S(2)) + sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(2)*a/b)/(S(8)*sqrt(b)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(a + b*asinh(c*x)), x), x, sqrt(Pi)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(2)*sqrt(b)*c) + sqrt(Pi)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(2)*sqrt(b)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*asinh(c*x))**(S(3)/2), x), x, sqrt(Pi)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(4)*b**(S(3)/2)*c**S(3)) - sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(3)*a/b)/(S(4)*b**(S(3)/2)*c**S(3)) - sqrt(Pi)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(4)*b**(S(3)/2)*c**S(3)) + sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(3)*a/b)/(S(4)*b**(S(3)/2)*c**S(3)) - S(2)*x**S(2)*sqrt(c**S(2)*x**S(2) + S(1))/(b*c*sqrt(a + b*asinh(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*asinh(c*x))**(S(3)/2), x), x, sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(2)*a/b)/(S(2)*b**(S(3)/2)*c**S(2)) + sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(2)*a/b)/(S(2)*b**(S(3)/2)*c**S(2)) - S(2)*x*sqrt(c**S(2)*x**S(2) + S(1))/(b*c*sqrt(a + b*asinh(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asinh(c*x))**(S(-3)/2), x), x, -sqrt(Pi)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(b**(S(3)/2)*c) + sqrt(Pi)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(b**(S(3)/2)*c) - S(2)*sqrt(c**S(2)*x**S(2) + S(1))/(b*c*sqrt(a + b*asinh(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*asinh(c*x))**(S(5)/2), x), x, -sqrt(Pi)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(6)*b**(S(5)/2)*c**S(3)) + sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(3)*a/b)/(S(2)*b**(S(5)/2)*c**S(3)) - sqrt(Pi)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(6)*b**(S(5)/2)*c**S(3)) + sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(3)*a/b)/(S(2)*b**(S(5)/2)*c**S(3)) - S(2)*x**S(2)*sqrt(c**S(2)*x**S(2) + S(1))/(S(3)*b*c*(a + b*asinh(c*x))**(S(3)/2)) - S(4)*x**S(3)/(b**S(2)*sqrt(a + b*asinh(c*x))) - S(8)*x/(S(3)*b**S(2)*c**S(2)*sqrt(a + b*asinh(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*asinh(c*x))**(S(5)/2), x), x, -S(2)*sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(2)*a/b)/(S(3)*b**(S(5)/2)*c**S(2)) + S(2)*sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(2)*a/b)/(S(3)*b**(S(5)/2)*c**S(2)) - S(2)*x*sqrt(c**S(2)*x**S(2) + S(1))/(S(3)*b*c*(a + b*asinh(c*x))**(S(3)/2)) - S(8)*x**S(2)/(S(3)*b**S(2)*sqrt(a + b*asinh(c*x))) - S(4)/(S(3)*b**S(2)*c**S(2)*sqrt(a + b*asinh(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asinh(c*x))**(S(-5)/2), x), x, S(2)*sqrt(Pi)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(3)*b**(S(5)/2)*c) + S(2)*sqrt(Pi)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(3)*b**(S(5)/2)*c) - S(2)*sqrt(c**S(2)*x**S(2) + S(1))/(S(3)*b*c*(a + b*asinh(c*x))**(S(3)/2)) - S(4)*x/(S(3)*b**S(2)*sqrt(a + b*asinh(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*asinh(c*x))**(S(7)/2), x), x, sqrt(Pi)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(15)*b**(S(7)/2)*c**S(3)) - S(3)*sqrt(S(3))*sqrt(Pi)*Erf(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(3)*a/b)/(S(5)*b**(S(7)/2)*c**S(3)) - sqrt(Pi)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(15)*b**(S(7)/2)*c**S(3)) + S(3)*sqrt(S(3))*sqrt(Pi)*Erfi(sqrt(S(3))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(3)*a/b)/(S(5)*b**(S(7)/2)*c**S(3)) - S(2)*x**S(2)*sqrt(c**S(2)*x**S(2) + S(1))/(S(5)*b*c*(a + b*asinh(c*x))**(S(5)/2)) - S(4)*x**S(3)/(S(5)*b**S(2)*(a + b*asinh(c*x))**(S(3)/2)) - S(8)*x/(S(15)*b**S(2)*c**S(2)*(a + b*asinh(c*x))**(S(3)/2)) - S(24)*x**S(2)*sqrt(c**S(2)*x**S(2) + S(1))/(S(5)*b**S(3)*c*sqrt(a + b*asinh(c*x))) - S(16)*sqrt(c**S(2)*x**S(2) + S(1))/(S(15)*b**S(3)*c**S(3)*sqrt(a + b*asinh(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*asinh(c*x))**(S(7)/2), x), x, S(8)*sqrt(S(2))*sqrt(Pi)*Erf(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(S(2)*a/b)/(S(15)*b**(S(7)/2)*c**S(2)) + S(8)*sqrt(S(2))*sqrt(Pi)*Erfi(sqrt(S(2))*sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-S(2)*a/b)/(S(15)*b**(S(7)/2)*c**S(2)) - S(2)*x*sqrt(c**S(2)*x**S(2) + S(1))/(S(5)*b*c*(a + b*asinh(c*x))**(S(5)/2)) - S(8)*x**S(2)/(S(15)*b**S(2)*(a + b*asinh(c*x))**(S(3)/2)) - S(4)/(S(15)*b**S(2)*c**S(2)*(a + b*asinh(c*x))**(S(3)/2)) - S(32)*x*sqrt(c**S(2)*x**S(2) + S(1))/(S(15)*b**S(3)*c*sqrt(a + b*asinh(c*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*asinh(c*x))**(S(-7)/2), x), x, -S(4)*sqrt(Pi)*Erf(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(a/b)/(S(15)*b**(S(7)/2)*c) + S(4)*sqrt(Pi)*Erfi(sqrt(a + b*asinh(c*x))/sqrt(b))*exp(-a/b)/(S(15)*b**(S(7)/2)*c) - S(2)*sqrt(c**S(2)*x**S(2) + S(1))/(S(5)*b*c*(a + b*asinh(c*x))**(S(5)/2)) - S(4)*x/(S(15)*b**S(2)*(a + b*asinh(c*x))**(S(3)/2)) - S(8)*sqrt(c**S(2)*x**S(2) + S(1))/(S(15)*b**S(3)*c*sqrt(a + b*asinh(c*x))), expand=True, _diff=True, _numerical=True)
