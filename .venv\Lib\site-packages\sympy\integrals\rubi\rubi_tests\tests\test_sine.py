import sys
from sympy.external import import_module
matchpy = import_module("matchpy")

if not matchpy:
    #bin/test will not execute any tests now
    disabled = True

if sys.version_info[:2] < (3, 6):
    disabled = True

from sympy.integrals.rubi.utility_function import (
        sympy_op_factory, Int, Sum, Set, With, Module, Scan, MapAnd, FalseQ,
        ZeroQ, NegativeQ, NonzeroQ, FreeQ, NFreeQ, List, Log, PositiveQ,
        PositiveIntegerQ, NegativeIntegerQ, IntegerQ, IntegersQ,
        ComplexNumberQ, PureComplexNumberQ, RealNumericQ, PositiveOrZeroQ,
        NegativeOrZeroQ, FractionOrNegativeQ, NegQ, Equal, Unequal, IntPart,
        FracPart, RationalQ, ProductQ, SumQ, NonsumQ, Subst, First, Rest,
        SqrtNumberQ, SqrtNumberSumQ, LinearQ, Sqrt, ArcCosh, Coefficient,
        Denominator, Hypergeometric2F1, Not, Simplify, FractionalPart,
        IntegerPart, AppellF1, EllipticPi, EllipticE, EllipticF, ArcTan,
        ArcCot, ArcCoth, ArcTanh, ArcSin, ArcSinh, ArcCos, ArcCsc, ArcSec,
        ArcCsch, ArcSech, Sinh, Tanh, Cosh, Sech, Csch, Coth, LessEqual, Less,
        Greater, GreaterEqual, FractionQ, IntLinearcQ, Expand, IndependentQ,
        PowerQ, IntegerPowerQ, PositiveIntegerPowerQ, FractionalPowerQ, AtomQ,
        ExpQ, LogQ, Head, MemberQ, TrigQ, SinQ, CosQ, TanQ, CotQ, SecQ, CscQ,
        Sin, Cos, Tan, Cot, Sec, Csc, HyperbolicQ, SinhQ, CoshQ, TanhQ, CothQ,
        SechQ, CschQ, InverseTrigQ, SinCosQ, SinhCoshQ, LeafCount, Numerator,
        NumberQ, NumericQ, Length, ListQ, Im, Re, InverseHyperbolicQ,
        InverseFunctionQ, TrigHyperbolicFreeQ, InverseFunctionFreeQ, RealQ,
        EqQ, FractionalPowerFreeQ, ComplexFreeQ, PolynomialQ, FactorSquareFree,
        PowerOfLinearQ, Exponent, QuadraticQ, LinearPairQ, BinomialParts,
        TrinomialParts, PolyQ, EvenQ, OddQ, PerfectSquareQ, NiceSqrtAuxQ,
        NiceSqrtQ, Together, PosAux, PosQ, CoefficientList, ReplaceAll,
        ExpandLinearProduct, GCD, ContentFactor, NumericFactor,
        NonnumericFactors, MakeAssocList, GensymSubst, KernelSubst,
        ExpandExpression, Apart, SmartApart, MatchQ,
        PolynomialQuotientRemainder, FreeFactors, NonfreeFactors,
        RemoveContentAux, RemoveContent, FreeTerms, NonfreeTerms,
        ExpandAlgebraicFunction, CollectReciprocals, ExpandCleanup,
        AlgebraicFunctionQ, Coeff, LeadTerm, RemainingTerms, LeadFactor,
        RemainingFactors, LeadBase, LeadDegree, Numer, Denom, hypergeom, Expon,
        MergeMonomials, PolynomialDivide, BinomialQ, TrinomialQ,
        GeneralizedBinomialQ, GeneralizedTrinomialQ, FactorSquareFreeList,
        PerfectPowerTest, SquareFreeFactorTest, RationalFunctionQ,
        RationalFunctionFactors, NonrationalFunctionFactors, Reverse,
        RationalFunctionExponents, RationalFunctionExpand, ExpandIntegrand,
        SimplerQ, SimplerSqrtQ, SumSimplerQ, BinomialDegree, TrinomialDegree,
        CancelCommonFactors, SimplerIntegrandQ, GeneralizedBinomialDegree,
        GeneralizedBinomialParts, GeneralizedTrinomialDegree,
        GeneralizedTrinomialParts, MonomialQ, MonomialSumQ,
        MinimumMonomialExponent, MonomialExponent, LinearMatchQ,
        PowerOfLinearMatchQ, QuadraticMatchQ, CubicMatchQ, BinomialMatchQ,
        TrinomialMatchQ, GeneralizedBinomialMatchQ, GeneralizedTrinomialMatchQ,
        QuotientOfLinearsMatchQ, PolynomialTermQ, PolynomialTerms,
        NonpolynomialTerms, PseudoBinomialParts, NormalizePseudoBinomial,
        PseudoBinomialPairQ, PseudoBinomialQ, PolynomialGCD, PolyGCD,
        AlgebraicFunctionFactors, NonalgebraicFunctionFactors,
        QuotientOfLinearsP, QuotientOfLinearsParts, QuotientOfLinearsQ,
        Flatten, Sort, AbsurdNumberQ, AbsurdNumberFactors,
        NonabsurdNumberFactors, SumSimplerAuxQ, Prepend, Drop,
        CombineExponents, FactorInteger, FactorAbsurdNumber,
        SubstForInverseFunction, SubstForFractionalPower,
        SubstForFractionalPowerOfQuotientOfLinears,
        FractionalPowerOfQuotientOfLinears, SubstForFractionalPowerQ,
        SubstForFractionalPowerAuxQ, FractionalPowerOfSquareQ,
        FractionalPowerSubexpressionQ, Apply, FactorNumericGcd,
        MergeableFactorQ, MergeFactor, MergeFactors, TrigSimplifyQ,
        TrigSimplify, TrigSimplifyRecur, Order, FactorOrder, Smallest,
        OrderedQ, MinimumDegree, PositiveFactors, Sign, NonpositiveFactors,
        PolynomialInAuxQ, PolynomialInQ, ExponentInAux, ExponentIn,
        PolynomialInSubstAux, PolynomialInSubst, Distrib, DistributeDegree,
        FunctionOfPower, DivideDegreesOfFactors, MonomialFactor, FullSimplify,
        FunctionOfLinearSubst, FunctionOfLinear, NormalizeIntegrand,
        NormalizeIntegrandAux, NormalizeIntegrandFactor,
        NormalizeIntegrandFactorBase, NormalizeTogether,
        NormalizeLeadTermSigns, AbsorbMinusSign, NormalizeSumFactors,
        SignOfFactor, NormalizePowerOfLinear, SimplifyIntegrand, SimplifyTerm,
        TogetherSimplify, SmartSimplify, SubstForExpn, ExpandToSum, UnifySum,
        UnifyTerms, UnifyTerm, CalculusQ, FunctionOfInverseLinear,
        PureFunctionOfSinhQ, PureFunctionOfTanhQ, PureFunctionOfCoshQ,
        IntegerQuotientQ, OddQuotientQ, EvenQuotientQ, FindTrigFactor,
        FunctionOfSinhQ, FunctionOfCoshQ, OddHyperbolicPowerQ, FunctionOfTanhQ,
        FunctionOfTanhWeight, FunctionOfHyperbolicQ, SmartNumerator,
        SmartDenominator, SubstForAux, ActivateTrig, ExpandTrig, TrigExpand,
        SubstForTrig, SubstForHyperbolic, InertTrigFreeQ, LCM,
        SubstForFractionalPowerOfLinear, FractionalPowerOfLinear,
        InverseFunctionOfLinear, InertTrigQ, InertReciprocalQ, DeactivateTrig,
        FixInertTrigFunction, DeactivateTrigAux, PowerOfInertTrigSumQ,
        PiecewiseLinearQ, KnownTrigIntegrandQ, KnownSineIntegrandQ,
        KnownTangentIntegrandQ, KnownCotangentIntegrandQ,
        KnownSecantIntegrandQ, TryPureTanSubst, TryTanhSubst, TryPureTanhSubst,
        AbsurdNumberGCD, AbsurdNumberGCDList, ExpandTrigExpand,
        ExpandTrigReduce, ExpandTrigReduceAux, NormalizeTrig, TrigToExp,
        ExpandTrigToExp, TrigReduce, FunctionOfTrig, AlgebraicTrigFunctionQ,
        FunctionOfHyperbolic, FunctionOfQ, FunctionOfExpnQ, PureFunctionOfSinQ,
        PureFunctionOfCosQ, PureFunctionOfTanQ, PureFunctionOfCotQ,
        FunctionOfCosQ, FunctionOfSinQ, OddTrigPowerQ, FunctionOfTanQ,
        FunctionOfTanWeight, FunctionOfTrigQ, FunctionOfDensePolynomialsQ,
        FunctionOfLog, PowerVariableExpn, PowerVariableDegree,
        PowerVariableSubst, EulerIntegrandQ, FunctionOfSquareRootOfQuadratic,
        SquareRootOfQuadraticSubst, Divides, EasyDQ, ProductOfLinearPowersQ,
        Rt, NthRoot, AtomBaseQ, SumBaseQ, NegSumBaseQ, AllNegTermQ,
        SomeNegTermQ, TrigSquareQ, RtAux, TrigSquare, IntSum, IntTerm, Map2,
        ConstantFactor, SameQ, ReplacePart, CommonFactors,
        MostMainFactorPosition, FunctionOfExponentialQ, FunctionOfExponential,
        FunctionOfExponentialFunction, FunctionOfExponentialFunctionAux,
        FunctionOfExponentialTest, FunctionOfExponentialTestAux, stdev,
        rubi_test, If, IntQuadraticQ, IntBinomialQ, RectifyTangent,
        RectifyCotangent, Inequality, Condition, Simp, SimpHelp, SplitProduct,
        SplitSum, SubstFor, SubstForAux, FresnelS, FresnelC, Erfc, Erfi, Gamma,
        FunctionOfTrigOfLinearQ, ElementaryFunctionQ, Complex, UnsameQ,
        _SimpFixFactor, SimpFixFactor, _FixSimplify, FixSimplify,
        _SimplifyAntiderivativeSum, SimplifyAntiderivativeSum,
        _SimplifyAntiderivative, SimplifyAntiderivative, _TrigSimplifyAux,
        TrigSimplifyAux, Cancel, Part, PolyLog, D, Dist, Sum_doit, PolynomialQuotient, Floor,
        PolynomialRemainder, Factor, PolyLog, CosIntegral, SinIntegral, LogIntegral, SinhIntegral,
        CoshIntegral, Rule, Erf, PolyGamma, ExpIntegralEi, ExpIntegralE, LogGamma , UtilityOperator, Factorial,
        Zeta, ProductLog, DerivativeDivides, HypergeometricPFQ, IntHide, OneQ
    )
from sympy.core.add import Add
from sympy.core.mod import Mod
from sympy.core.mul import Mul
from sympy.core.numbers import (Float, I, Integer)
from sympy.core.power import Pow
from sympy.core.singleton import S
from sympy.functions.elementary.complexes import Abs
from sympy.functions.elementary.miscellaneous import sqrt
from sympy.integrals.integrals import Integral
from sympy.logic.boolalg import (And, Or)
from sympy.simplify.simplify import simplify
from sympy.integrals.rubi.symbol import WC
from sympy.core.symbol import symbols, Symbol
from sympy.functions import (sin, cos, tan, cot, csc, sec, sqrt, erf, exp, log)
from sympy.functions.elementary.hyperbolic import (acosh, asinh, atanh, acoth, acsch, asech, cosh, sinh, tanh, coth, sech, csch)
from sympy.functions.elementary.trigonometric import (atan, acsc, asin, acot, acos, asec)
from sympy.integrals.rubi.rubimain import rubi_integrate
from sympy.core.numbers import pi as Pi
a, b, c, d, e, f, m, n, x, u , k, p, r, s, t, i, j= symbols('a b c d e f m n x u k p r s t i j')
A, B, C, D, a, b, c, d, e, f, g, h, y, z, m, n, p, q, u, v, w, F = symbols('A B C D a b c d e f g h y z m n p q u v w F', )


def test_1():

    assert rubi_test(rubi_integrate(sin(a + b*x), x), x, -cos(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2), x), x, x/S(2) - sin(a + b*x)*cos(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3), x), x, cos(a + b*x)**S(3)/(S(3)*b) - cos(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4), x), x, S(3)*x/S(8) - sin(a + b*x)**S(3)*cos(a + b*x)/(S(4)*b) - S(3)*sin(a + b*x)*cos(a + b*x)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5), x), x, -cos(a + b*x)**S(5)/(S(5)*b) + S(2)*cos(a + b*x)**S(3)/(S(3)*b) - cos(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(6), x), x, S(5)*x/S(16) - sin(a + b*x)**S(5)*cos(a + b*x)/(S(6)*b) - S(5)*sin(a + b*x)**S(3)*cos(a + b*x)/(S(24)*b) - S(5)*sin(a + b*x)*cos(a + b*x)/(S(16)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(7), x), x, cos(a + b*x)**S(7)/(S(7)*b) - S(3)*cos(a + b*x)**S(5)/(S(5)*b) + cos(a + b*x)**S(3)/b - cos(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(8), x), x, S(35)*x/S(128) - sin(a + b*x)**S(7)*cos(a + b*x)/(S(8)*b) - S(7)*sin(a + b*x)**S(5)*cos(a + b*x)/(S(48)*b) - S(35)*sin(a + b*x)**S(3)*cos(a + b*x)/(S(192)*b) - S(35)*sin(a + b*x)*cos(a + b*x)/(S(128)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(7)/2), x), x, S(10)*EllipticF(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/(S(21)*b) - S(2)*sin(a + b*x)**(S(5)/2)*cos(a + b*x)/(S(7)*b) - S(10)*sqrt(sin(a + b*x))*cos(a + b*x)/(S(21)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(5)/2), x), x, S(6)*EllipticE(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/(S(5)*b) - S(2)*sin(a + b*x)**(S(3)/2)*cos(a + b*x)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(3)/2), x), x, S(2)*EllipticF(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/(S(3)*b) - S(2)*sqrt(sin(a + b*x))*cos(a + b*x)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(sin(a + b*x)), x), x, S(2)*EllipticE(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(sin(a + b*x)), x), x, S(2)*EllipticF(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(-3)/2), x), x, -S(2)*EllipticE(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/b - S(2)*cos(a + b*x)/(b*sqrt(sin(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(-5)/2), x), x, S(2)*EllipticF(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/(S(3)*b) - S(2)*cos(a + b*x)/(S(3)*b*sin(a + b*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(-7)/2), x), x, -S(6)*EllipticE(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/(S(5)*b) - S(6)*cos(a + b*x)/(S(5)*b*sqrt(sin(a + b*x))) - S(2)*cos(a + b*x)/(S(5)*b*sin(a + b*x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(7)/2), x), x, S(10)*c**S(4)*EllipticF(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))*sqrt(sin(a + b*x))/(S(21)*b*sqrt(c*sin(a + b*x))) - S(10)*c**S(3)*sqrt(c*sin(a + b*x))*cos(a + b*x)/(S(21)*b) - S(2)*c*(c*sin(a + b*x))**(S(5)/2)*cos(a + b*x)/(S(7)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2), x), x, S(6)*c**S(2)*sqrt(c*sin(a + b*x))*EllipticE(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/(S(5)*b*sqrt(sin(a + b*x))) - S(2)*c*(c*sin(a + b*x))**(S(3)/2)*cos(a + b*x)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2), x), x, S(2)*c**S(2)*EllipticF(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))*sqrt(sin(a + b*x))/(S(3)*b*sqrt(c*sin(a + b*x))) - S(2)*c*sqrt(c*sin(a + b*x))*cos(a + b*x)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x)), x), x, S(2)*sqrt(c*sin(a + b*x))*EllipticE(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/(b*sqrt(sin(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(c*sin(a + b*x)), x), x, S(2)*EllipticF(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))*sqrt(sin(a + b*x))/(b*sqrt(c*sin(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(-3)/2), x), x, -S(2)*cos(a + b*x)/(b*c*sqrt(c*sin(a + b*x))) - S(2)*sqrt(c*sin(a + b*x))*EllipticE(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/(b*c**S(2)*sqrt(sin(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(-5)/2), x), x, -S(2)*cos(a + b*x)/(S(3)*b*c*(c*sin(a + b*x))**(S(3)/2)) + S(2)*EllipticF(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))*sqrt(sin(a + b*x))/(S(3)*b*c**S(2)*sqrt(c*sin(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(-7)/2), x), x, -S(2)*cos(a + b*x)/(S(5)*b*c*(c*sin(a + b*x))**(S(5)/2)) - S(6)*cos(a + b*x)/(S(5)*b*c**S(3)*sqrt(c*sin(a + b*x))) - S(6)*sqrt(c*sin(a + b*x))*EllipticE(-Pi/S(4) + a/S(2) + b*x/S(2), S(2))/(S(5)*b*c**S(4)*sqrt(sin(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(4)/3), x), x, S(3)*(c*sin(a + b*x))**(S(7)/3)*Hypergeometric2F1(S(1)/2, S(7)/6, S(13)/6, sin(a + b*x)**S(2))*cos(a + b*x)/(S(7)*b*c*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(2)/3), x), x, S(3)*(c*sin(a + b*x))**(S(5)/3)*Hypergeometric2F1(S(1)/2, S(5)/6, S(11)/6, sin(a + b*x)**S(2))*cos(a + b*x)/(S(5)*b*c*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(1)/3), x), x, -S(3)*c**(S(1)/3)*sqrt(S(1) - (c*sin(a + b*x))**(S(2)/3)/c**(S(2)/3))*sqrt(S(9)/2 - S(3)*sqrt(S(3))*I/S(2))*sqrt((-sqrt(S(3)) + I)/(-sqrt(S(3)) + S(3)*I) + S(2)*(c*sin(a + b*x))**(S(2)/3)/(c**(S(2)/3)*(S(3) + sqrt(S(3))*I)))*sqrt((sqrt(S(3)) + I)/(sqrt(S(3)) + S(3)*I) + S(2)*(c*sin(a + b*x))**(S(2)/3)/(c**(S(2)/3)*(S(3) - sqrt(S(3))*I)))*EllipticE(asin(sqrt(S(2))*sqrt(S(1) - (c*sin(a + b*x))**(S(2)/3)/c**(S(2)/3))/sqrt(S(3) + sqrt(S(3))*I)), (-sqrt(S(3)) + S(3)*I)/(sqrt(S(3)) + S(3)*I))*sec(a + b*x)/b + S(3)*sqrt(S(2))*c**(S(1)/3)*(S(1) - sqrt(S(3))*I)*sqrt(S(1) - (c*sin(a + b*x))**(S(2)/3)/c**(S(2)/3))*sqrt(S(3) - sqrt(S(3))*I)*sqrt((-sqrt(S(3)) + I)/(-sqrt(S(3)) + S(3)*I) + S(2)*(c*sin(a + b*x))**(S(2)/3)/(c**(S(2)/3)*(S(3) + sqrt(S(3))*I)))*sqrt((sqrt(S(3)) + I)/(sqrt(S(3)) + S(3)*I) + S(2)*(c*sin(a + b*x))**(S(2)/3)/(c**(S(2)/3)*(S(3) - sqrt(S(3))*I)))*EllipticF(asin(sqrt(S(2))*sqrt(S(1) - (c*sin(a + b*x))**(S(2)/3)/c**(S(2)/3))/sqrt(S(3) - sqrt(S(3))*I)), (sqrt(S(3)) + S(3)*I)/(-sqrt(S(3)) + S(3)*I))*sec(a + b*x)/(S(4)*b), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((c*sin(a + b*x))**(S(1)/3), x), x, S(3)*(c*sin(a + b*x))**(S(4)/3)*Hypergeometric2F1(S(1)/2, S(2)/3, S(5)/3, sin(a + b*x)**S(2))*cos(a + b*x)/(S(4)*b*c*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(-1)/3), x), x, -S(3)*sqrt(S(2))*sqrt(S(1) - (c*sin(a + b*x))**(S(2)/3)/c**(S(2)/3))*sqrt(S(3) - sqrt(S(3))*I)*sqrt((-sqrt(S(3)) + I)/(-sqrt(S(3)) + S(3)*I) + S(2)*(c*sin(a + b*x))**(S(2)/3)/(c**(S(2)/3)*(S(3) + sqrt(S(3))*I)))*sqrt((sqrt(S(3)) + I)/(sqrt(S(3)) + S(3)*I) + S(2)*(c*sin(a + b*x))**(S(2)/3)/(c**(S(2)/3)*(S(3) - sqrt(S(3))*I)))*EllipticF(asin(sqrt(S(2))*sqrt(S(1) - (c*sin(a + b*x))**(S(2)/3)/c**(S(2)/3))/sqrt(S(3) - sqrt(S(3))*I)), (sqrt(S(3)) + S(3)*I)/(-sqrt(S(3)) + S(3)*I))*sec(a + b*x)/(S(2)*b*c**(S(1)/3)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((c*sin(a + b*x))**(S(-1)/3), x), x, S(3)*(c*sin(a + b*x))**(S(2)/3)*Hypergeometric2F1(S(1)/3, S(1)/2, S(4)/3, sin(a + b*x)**S(2))*cos(a + b*x)/(S(2)*b*c*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(-2)/3), x), x, S(3)**(S(3)/4)*(c*sin(a + b*x))**(S(1)/3)*sqrt(c**(S(4)/3)*(S(1) + (c*sin(a + b*x))**(S(2)/3)/c**(S(2)/3) + (c*sin(a + b*x))**(S(4)/3)/c**(S(4)/3))/(c**(S(2)/3) - (c*sin(a + b*x))**(S(2)/3)*(S(1) + sqrt(S(3))))**S(2))*(c**(S(2)/3) - (c*sin(a + b*x))**(S(2)/3))*EllipticF(acos((c**(S(2)/3) - (c*sin(a + b*x))**(S(2)/3)*(-sqrt(S(3)) + S(1)))/(c**(S(2)/3) - (c*sin(a + b*x))**(S(2)/3)*(S(1) + sqrt(S(3))))), sqrt(S(3))/S(4) + S(1)/2)*sec(a + b*x)/(S(2)*b*c**(S(5)/3)*sqrt(-(c*sin(a + b*x))**(S(2)/3)*(c**(S(2)/3) - (c*sin(a + b*x))**(S(2)/3))/(c**(S(2)/3) - (c*sin(a + b*x))**(S(2)/3)*(S(1) + sqrt(S(3))))**S(2))), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((c*sin(a + b*x))**(S(-2)/3), x), x, S(3)*(c*sin(a + b*x))**(S(1)/3)*Hypergeometric2F1(S(1)/6, S(1)/2, S(7)/6, sin(a + b*x)**S(2))*cos(a + b*x)/(b*c*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(-4)/3), x), x, -S(3)*Hypergeometric2F1(S(-1)/6, S(1)/2, S(5)/6, sin(a + b*x)**S(2))*cos(a + b*x)/(b*c*(c*sin(a + b*x))**(S(1)/3)*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**n, x), x, Hypergeometric2F1(S(1)/2, n/S(2) + S(1)/2, n/S(2) + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)**(n + S(1))*cos(a + b*x)/(b*(n + S(1))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**n, x), x, (c*sin(a + b*x))**(n + S(1))*Hypergeometric2F1(S(1)/2, n/S(2) + S(1)/2, n/S(2) + S(3)/2, sin(a + b*x)**S(2))*cos(a + b*x)/(b*c*(n + S(1))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(2))**(S(5)/2), x), x, -S(8)*a**S(2)*sqrt(a*sin(x)**S(2))*cot(x)/S(15) - S(4)*a*(a*sin(x)**S(2))**(S(3)/2)*cot(x)/S(15) - (a*sin(x)**S(2))**(S(5)/2)*cot(x)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(2))**(S(3)/2), x), x, -S(2)*a*sqrt(a*sin(x)**S(2))*cot(x)/S(3) - (a*sin(x)**S(2))**(S(3)/2)*cot(x)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a*sin(x)**S(2)), x), x, -sqrt(a*sin(x)**S(2))*cot(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(a*sin(x)**S(2)), x), x, -sin(x)*atanh(cos(x))/sqrt(a*sin(x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(2))**(S(-3)/2), x), x, -sin(x)*atanh(cos(x))/(S(2)*a*sqrt(a*sin(x)**S(2))) - cot(x)/(S(2)*a*sqrt(a*sin(x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(2))**(S(-5)/2), x), x, -cot(x)/(S(4)*a*(a*sin(x)**S(2))**(S(3)/2)) - S(3)*sin(x)*atanh(cos(x))/(S(8)*a**S(2)*sqrt(a*sin(x)**S(2))) - S(3)*cot(x)/(S(8)*a**S(2)*sqrt(a*sin(x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(3))**(S(5)/2), x), x, -S(26)*a**S(2)*sqrt(a*sin(x)**S(3))*EllipticF(Pi/S(4) - x/S(2), S(2))/(S(77)*sin(x)**(S(3)/2)) - S(2)*a**S(2)*sqrt(a*sin(x)**S(3))*sin(x)**S(5)*cos(x)/S(15) - S(26)*a**S(2)*sqrt(a*sin(x)**S(3))*sin(x)**S(3)*cos(x)/S(165) - S(78)*a**S(2)*sqrt(a*sin(x)**S(3))*sin(x)*cos(x)/S(385) - S(26)*a**S(2)*sqrt(a*sin(x)**S(3))*cot(x)/S(77), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(3))**(S(3)/2), x), x, -S(14)*a*sqrt(a*sin(x)**S(3))*EllipticE(Pi/S(4) - x/S(2), S(2))/(S(15)*sin(x)**(S(3)/2)) - S(2)*a*sqrt(a*sin(x)**S(3))*sin(x)**S(2)*cos(x)/S(9) - S(14)*a*sqrt(a*sin(x)**S(3))*cos(x)/S(45), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a*sin(x)**S(3)), x), x, -S(2)*sqrt(a*sin(x)**S(3))*EllipticF(Pi/S(4) - x/S(2), S(2))/(S(3)*sin(x)**(S(3)/2)) - S(2)*sqrt(a*sin(x)**S(3))*cot(x)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(a*sin(x)**S(3)), x), x, S(2)*EllipticE(Pi/S(4) - x/S(2), S(2))*sin(x)**(S(3)/2)/sqrt(a*sin(x)**S(3)) - S(2)*sin(x)*cos(x)/sqrt(a*sin(x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(3))**(S(-3)/2), x), x, -S(10)*EllipticF(Pi/S(4) - x/S(2), S(2))*sin(x)**(S(3)/2)/(S(21)*a*sqrt(a*sin(x)**S(3))) - S(10)*cos(x)/(S(21)*a*sqrt(a*sin(x)**S(3))) - S(2)*cot(x)*csc(x)/(S(7)*a*sqrt(a*sin(x)**S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(3))**(S(-5)/2), x), x, S(154)*EllipticE(Pi/S(4) - x/S(2), S(2))*sin(x)**(S(3)/2)/(S(195)*a**S(2)*sqrt(a*sin(x)**S(3))) - S(154)*sin(x)*cos(x)/(S(195)*a**S(2)*sqrt(a*sin(x)**S(3))) - S(2)*cot(x)*csc(x)**S(4)/(S(13)*a**S(2)*sqrt(a*sin(x)**S(3))) - S(22)*cot(x)*csc(x)**S(2)/(S(117)*a**S(2)*sqrt(a*sin(x)**S(3))) - S(154)*cot(x)/(S(585)*a**S(2)*sqrt(a*sin(x)**S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(4))**(S(5)/2), x), x, S(63)*a**S(2)*x*sqrt(a*sin(x)**S(4))*csc(x)**S(2)/S(256) - a**S(2)*sqrt(a*sin(x)**S(4))*sin(x)**S(7)*cos(x)/S(10) - S(9)*a**S(2)*sqrt(a*sin(x)**S(4))*sin(x)**S(5)*cos(x)/S(80) - S(21)*a**S(2)*sqrt(a*sin(x)**S(4))*sin(x)**S(3)*cos(x)/S(160) - S(21)*a**S(2)*sqrt(a*sin(x)**S(4))*sin(x)*cos(x)/S(128) - S(63)*a**S(2)*sqrt(a*sin(x)**S(4))*cot(x)/S(256), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(4))**(S(3)/2), x), x, S(5)*a*x*sqrt(a*sin(x)**S(4))*csc(x)**S(2)/S(16) - a*sqrt(a*sin(x)**S(4))*sin(x)**S(3)*cos(x)/S(6) - S(5)*a*sqrt(a*sin(x)**S(4))*sin(x)*cos(x)/S(24) - S(5)*a*sqrt(a*sin(x)**S(4))*cot(x)/S(16), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a*sin(x)**S(4)), x), x, x*sqrt(a*sin(x)**S(4))*csc(x)**S(2)/S(2) - sqrt(a*sin(x)**S(4))*cot(x)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(a*sin(x)**S(4)), x), x, -sin(x)*cos(x)/sqrt(a*sin(x)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(4))**(S(-3)/2), x), x, -sin(x)*cos(x)/(a*sqrt(a*sin(x)**S(4))) - cos(x)**S(2)*cot(x)**S(3)/(S(5)*a*sqrt(a*sin(x)**S(4))) - S(2)*cos(x)**S(2)*cot(x)/(S(3)*a*sqrt(a*sin(x)**S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(x)**S(4))**(S(-5)/2), x), x, -sin(x)*cos(x)/(a**S(2)*sqrt(a*sin(x)**S(4))) - cos(x)**S(2)*cot(x)**S(7)/(S(9)*a**S(2)*sqrt(a*sin(x)**S(4))) - S(4)*cos(x)**S(2)*cot(x)**S(5)/(S(7)*a**S(2)*sqrt(a*sin(x)**S(4))) - S(6)*cos(x)**S(2)*cot(x)**S(3)/(S(5)*a**S(2)*sqrt(a*sin(x)**S(4))) - S(4)*cos(x)**S(2)*cot(x)/(S(3)*a**S(2)*sqrt(a*sin(x)**S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(c + d*x)**p)**n, x), x, (b*sin(c + d*x)**p)**n*Hypergeometric2F1(S(1)/2, n*p/S(2) + S(1)/2, n*p/S(2) + S(3)/2, sin(c + d*x)**S(2))*sin(c + d*x)*cos(c + d*x)/(d*(n*p + S(1))*sqrt(cos(c + d*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x)**S(2))**n, x), x, (c*sin(a + b*x)**S(2))**n*Hypergeometric2F1(S(1)/2, n + S(1)/2, n + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)*cos(a + b*x)/(b*(S(2)*n + S(1))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x)**S(3))**n, x), x, (c*sin(a + b*x)**S(3))**n*Hypergeometric2F1(S(1)/2, S(3)*n/S(2) + S(1)/2, S(3)*n/S(2) + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)*cos(a + b*x)/(b*(S(3)*n + S(1))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x)**S(4))**n, x), x, (c*sin(a + b*x)**S(4))**n*Hypergeometric2F1(S(1)/2, S(2)*n + S(1)/2, S(2)*n + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)*cos(a + b*x)/(b*(S(4)*n + S(1))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x)**m)**(S(5)/2), x), x, S(2)*c**S(2)*sqrt(c*sin(a + b*x)**m)*Hypergeometric2F1(S(1)/2, S(5)*m/S(4) + S(1)/2, S(5)*m/S(4) + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)**(S(2)*m + S(1))*cos(a + b*x)/(b*(S(5)*m + S(2))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x)**m)**(S(3)/2), x), x, S(2)*c*sqrt(c*sin(a + b*x)**m)*Hypergeometric2F1(S(1)/2, S(3)*m/S(4) + S(1)/2, S(3)*m/S(4) + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)**(m + S(1))*cos(a + b*x)/(b*(S(3)*m + S(2))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x)**m), x), x, S(2)*sqrt(c*sin(a + b*x)**m)*Hypergeometric2F1(S(1)/2, m/S(4) + S(1)/2, m/S(4) + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)*cos(a + b*x)/(b*(m + S(2))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(c*sin(a + b*x)**m), x), x, S(2)*Hypergeometric2F1(S(1)/2, -m/S(4) + S(1)/2, -m/S(4) + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)*cos(a + b*x)/(b*sqrt(c*sin(a + b*x)**m)*(-m + S(2))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x)**m)**(S(-3)/2), x), x, S(2)*Hypergeometric2F1(S(1)/2, -S(3)*m/S(4) + S(1)/2, -S(3)*m/S(4) + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)**(-m + S(1))*cos(a + b*x)/(b*c*sqrt(c*sin(a + b*x)**m)*(-S(3)*m + S(2))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x)**m)**(S(-5)/2), x), x, S(2)*Hypergeometric2F1(S(1)/2, -S(5)*m/S(4) + S(1)/2, -S(5)*m/S(4) + S(3)/2, sin(a + b*x)**S(2))*sin(a + b*x)**(-S(2)*m + S(1))*cos(a + b*x)/(b*c**S(2)*sqrt(c*sin(a + b*x)**m)*(-S(5)*m + S(2))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x)**m)**(S(1)/m), x), x, -(c*sin(a + b*x)**m)**(S(1)/m)*cot(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*(b*sin(c + d*x))**p)**n, x), x, (a*(b*sin(c + d*x))**p)**n*Hypergeometric2F1(S(1)/2, n*p/S(2) + S(1)/2, n*p/S(2) + S(3)/2, sin(c + d*x)**S(2))*sin(c + d*x)*cos(c + d*x)/(d*(n*p + S(1))*sqrt(cos(c + d*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(e + f*x))**m*(b*sin(e + f*x))**n, x), x, (a*sin(e + f*x))**(m + S(1))*(b*sin(e + f*x))**n*Hypergeometric2F1(S(1)/2, m/S(2) + n/S(2) + S(1)/2, m/S(2) + n/S(2) + S(3)/2, sin(e + f*x)**S(2))*cos(e + f*x)/(a*f*(m + n + S(1))*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)*cos(a + b*x)**S(3), x), x, -cos(a + b*x)**S(4)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)*cos(a + b*x)**S(2), x), x, -cos(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)*cos(a + b*x), x), x, sin(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)*sec(a + b*x), x), x, -log(cos(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)*sec(a + b*x)**S(2), x), x, sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)*sec(a + b*x)**S(3), x), x, sec(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)*sec(a + b*x)**S(4), x), x, sec(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*cos(a + b*x)**S(7), x), x, -sin(a + b*x)**S(9)/(S(9)*b) + S(3)*sin(a + b*x)**S(7)/(S(7)*b) - S(3)*sin(a + b*x)**S(5)/(S(5)*b) + sin(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*cos(a + b*x)**S(5), x), x, sin(a + b*x)**S(7)/(S(7)*b) - S(2)*sin(a + b*x)**S(5)/(S(5)*b) + sin(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*cos(a + b*x)**S(3), x), x, -sin(a + b*x)**S(5)/(S(5)*b) + sin(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*cos(a + b*x), x), x, sin(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*sec(a + b*x)**S(2), x), x, -x + tan(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*sec(a + b*x)**S(4), x), x, tan(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*sec(a + b*x)**S(6), x), x, tan(a + b*x)**S(5)/(S(5)*b) + tan(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*sec(a + b*x)**S(8), x), x, tan(a + b*x)**S(7)/(S(7)*b) + S(2)*tan(a + b*x)**S(5)/(S(5)*b) + tan(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*sec(a + b*x)**S(10), x), x, tan(a + b*x)**S(9)/(S(9)*b) + S(3)*tan(a + b*x)**S(7)/(S(7)*b) + S(3)*tan(a + b*x)**S(5)/(S(5)*b) + tan(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*cos(a + b*x)**S(6), x), x, S(5)*x/S(128) - sin(a + b*x)*cos(a + b*x)**S(7)/(S(8)*b) + sin(a + b*x)*cos(a + b*x)**S(5)/(S(48)*b) + S(5)*sin(a + b*x)*cos(a + b*x)**S(3)/(S(192)*b) + S(5)*sin(a + b*x)*cos(a + b*x)/(S(128)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*cos(a + b*x)**S(4), x), x, x/S(16) - sin(a + b*x)*cos(a + b*x)**S(5)/(S(6)*b) + sin(a + b*x)*cos(a + b*x)**S(3)/(S(24)*b) + sin(a + b*x)*cos(a + b*x)/(S(16)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*cos(a + b*x)**S(2), x), x, x/S(8) - sin(a + b*x)*cos(a + b*x)**S(3)/(S(4)*b) + sin(a + b*x)*cos(a + b*x)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2), x), x, x/S(2) - sin(a + b*x)*cos(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*sec(a + b*x), x), x, -sin(a + b*x)/b + atanh(sin(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*sec(a + b*x)**S(3), x), x, tan(a + b*x)*sec(a + b*x)/(S(2)*b) - atanh(sin(a + b*x))/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*sec(a + b*x)**S(5), x), x, tan(a + b*x)*sec(a + b*x)**S(3)/(S(4)*b) - tan(a + b*x)*sec(a + b*x)/(S(8)*b) - atanh(sin(a + b*x))/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)*sec(a + b*x)**S(7), x), x, tan(a + b*x)*sec(a + b*x)**S(5)/(S(6)*b) - tan(a + b*x)*sec(a + b*x)**S(3)/(S(24)*b) - tan(a + b*x)*sec(a + b*x)/(S(16)*b) - atanh(sin(a + b*x))/(S(16)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*cos(a + b*x)**S(5), x), x, cos(a + b*x)**S(8)/(S(8)*b) - cos(a + b*x)**S(6)/(S(6)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*cos(a + b*x)**S(4), x), x, cos(a + b*x)**S(7)/(S(7)*b) - cos(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*cos(a + b*x)**S(3), x), x, -sin(a + b*x)**S(6)/(S(6)*b) + sin(a + b*x)**S(4)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*cos(a + b*x)**S(2), x), x, cos(a + b*x)**S(5)/(S(5)*b) - cos(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*cos(a + b*x), x), x, sin(a + b*x)**S(4)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*sec(a + b*x), x), x, -log(cos(a + b*x))/b + cos(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*sec(a + b*x)**S(2), x), x, cos(a + b*x)/b + sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*sec(a + b*x)**S(3), x), x, log(cos(a + b*x))/b + tan(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*sec(a + b*x)**S(4), x), x, sec(a + b*x)**S(3)/(S(3)*b) - sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*sec(a + b*x)**S(5), x), x, tan(a + b*x)**S(4)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*sec(a + b*x)**S(6), x), x, sec(a + b*x)**S(5)/(S(5)*b) - sec(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*sec(a + b*x)**S(7), x), x, sec(a + b*x)**S(6)/(S(6)*b) - sec(a + b*x)**S(4)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*sec(a + b*x)**S(8), x), x, sec(a + b*x)**S(7)/(S(7)*b) - sec(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)*sec(a + b*x)**S(9), x), x, sec(a + b*x)**S(8)/(S(8)*b) - sec(a + b*x)**S(6)/(S(6)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*cos(a + b*x)**S(7), x), x, -sin(a + b*x)**S(11)/(S(11)*b) + sin(a + b*x)**S(9)/(S(3)*b) - S(3)*sin(a + b*x)**S(7)/(S(7)*b) + sin(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*cos(a + b*x)**S(5), x), x, sin(a + b*x)**S(9)/(S(9)*b) - S(2)*sin(a + b*x)**S(7)/(S(7)*b) + sin(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*cos(a + b*x)**S(3), x), x, -sin(a + b*x)**S(7)/(S(7)*b) + sin(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*cos(a + b*x), x), x, sin(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x)**S(2), x), x, -S(3)*x/S(2) - sin(a + b*x)**S(2)*tan(a + b*x)/(S(2)*b) + S(3)*tan(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x)**S(4), x), x, x + tan(a + b*x)**S(3)/(S(3)*b) - tan(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x)**S(6), x), x, tan(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x)**S(8), x), x, tan(a + b*x)**S(7)/(S(7)*b) + tan(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x)**S(10), x), x, tan(a + b*x)**S(9)/(S(9)*b) + S(2)*tan(a + b*x)**S(7)/(S(7)*b) + tan(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*cos(a + b*x)**S(6), x), x, S(3)*x/S(256) - sin(a + b*x)**S(3)*cos(a + b*x)**S(7)/(S(10)*b) - S(3)*sin(a + b*x)*cos(a + b*x)**S(7)/(S(80)*b) + sin(a + b*x)*cos(a + b*x)**S(5)/(S(160)*b) + sin(a + b*x)*cos(a + b*x)**S(3)/(S(128)*b) + S(3)*sin(a + b*x)*cos(a + b*x)/(S(256)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*cos(a + b*x)**S(4), x), x, S(3)*x/S(128) - sin(a + b*x)**S(3)*cos(a + b*x)**S(5)/(S(8)*b) - sin(a + b*x)*cos(a + b*x)**S(5)/(S(16)*b) + sin(a + b*x)*cos(a + b*x)**S(3)/(S(64)*b) + S(3)*sin(a + b*x)*cos(a + b*x)/(S(128)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*cos(a + b*x)**S(2), x), x, x/S(16) - sin(a + b*x)**S(3)*cos(a + b*x)**S(3)/(S(6)*b) - sin(a + b*x)*cos(a + b*x)**S(3)/(S(8)*b) + sin(a + b*x)*cos(a + b*x)/(S(16)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4), x), x, S(3)*x/S(8) - sin(a + b*x)**S(3)*cos(a + b*x)/(S(4)*b) - S(3)*sin(a + b*x)*cos(a + b*x)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x), x), x, -sin(a + b*x)**S(3)/(S(3)*b) - sin(a + b*x)/b + atanh(sin(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x)**S(3), x), x, sin(a + b*x)*tan(a + b*x)**S(2)/(S(2)*b) + S(3)*sin(a + b*x)/(S(2)*b) - S(3)*atanh(sin(a + b*x))/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x)**S(5), x), x, tan(a + b*x)**S(3)*sec(a + b*x)/(S(4)*b) - S(3)*tan(a + b*x)*sec(a + b*x)/(S(8)*b) + S(3)*atanh(sin(a + b*x))/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x)**S(7), x), x, tan(a + b*x)**S(3)*sec(a + b*x)**S(3)/(S(6)*b) - tan(a + b*x)*sec(a + b*x)**S(3)/(S(8)*b) + tan(a + b*x)*sec(a + b*x)/(S(16)*b) + atanh(sin(a + b*x))/(S(16)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)*sec(a + b*x)**S(9), x), x, tan(a + b*x)**S(3)*sec(a + b*x)**S(5)/(S(8)*b) - tan(a + b*x)*sec(a + b*x)**S(5)/(S(16)*b) + tan(a + b*x)*sec(a + b*x)**S(3)/(S(64)*b) + S(3)*tan(a + b*x)*sec(a + b*x)/(S(128)*b) + S(3)*atanh(sin(a + b*x))/(S(128)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*cos(a + b*x)**S(7), x), x, -cos(a + b*x)**S(12)/(S(12)*b) + cos(a + b*x)**S(10)/(S(5)*b) - cos(a + b*x)**S(8)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*cos(a + b*x)**S(6), x), x, -cos(a + b*x)**S(11)/(S(11)*b) + S(2)*cos(a + b*x)**S(9)/(S(9)*b) - cos(a + b*x)**S(7)/(S(7)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*cos(a + b*x)**S(5), x), x, sin(a + b*x)**S(10)/(S(10)*b) - sin(a + b*x)**S(8)/(S(4)*b) + sin(a + b*x)**S(6)/(S(6)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*cos(a + b*x)**S(4), x), x, -cos(a + b*x)**S(9)/(S(9)*b) + S(2)*cos(a + b*x)**S(7)/(S(7)*b) - cos(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*cos(a + b*x)**S(3), x), x, -sin(a + b*x)**S(8)/(S(8)*b) + sin(a + b*x)**S(6)/(S(6)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*cos(a + b*x)**S(2), x), x, -cos(a + b*x)**S(7)/(S(7)*b) + S(2)*cos(a + b*x)**S(5)/(S(5)*b) - cos(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*cos(a + b*x), x), x, sin(a + b*x)**S(6)/(S(6)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x), x), x, -log(cos(a + b*x))/b - cos(a + b*x)**S(4)/(S(4)*b) + cos(a + b*x)**S(2)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(2), x), x, -cos(a + b*x)**S(3)/(S(3)*b) + S(2)*cos(a + b*x)/b + sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(3), x), x, S(2)*log(cos(a + b*x))/b - cos(a + b*x)**S(2)/(S(2)*b) + sec(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(4), x), x, -cos(a + b*x)/b + sec(a + b*x)**S(3)/(S(3)*b) - S(2)*sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(5), x), x, -log(cos(a + b*x))/b + tan(a + b*x)**S(4)/(S(4)*b) - tan(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(6), x), x, sec(a + b*x)**S(5)/(S(5)*b) - S(2)*sec(a + b*x)**S(3)/(S(3)*b) + sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(7), x), x, tan(a + b*x)**S(6)/(S(6)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(8), x), x, sec(a + b*x)**S(7)/(S(7)*b) - S(2)*sec(a + b*x)**S(5)/(S(5)*b) + sec(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(9), x), x, tan(a + b*x)**S(8)/(S(8)*b) + tan(a + b*x)**S(6)/(S(6)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(10), x), x, sec(a + b*x)**S(9)/(S(9)*b) - S(2)*sec(a + b*x)**S(7)/(S(7)*b) + sec(a + b*x)**S(5)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(11), x), x, sec(a + b*x)**S(10)/(S(10)*b) - sec(a + b*x)**S(8)/(S(4)*b) + sec(a + b*x)**S(6)/(S(6)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(12), x), x, sec(a + b*x)**S(11)/(S(11)*b) - S(2)*sec(a + b*x)**S(9)/(S(9)*b) + sec(a + b*x)**S(7)/(S(7)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*sec(a + b*x)**S(13), x), x, sec(a + b*x)**S(12)/(S(12)*b) - sec(a + b*x)**S(10)/(S(5)*b) + sec(a + b*x)**S(8)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(6)*sec(a + b*x)**S(3), x), x, sin(a + b*x)**S(3)*tan(a + b*x)**S(2)/(S(2)*b) + S(5)*sin(a + b*x)**S(3)/(S(6)*b) + S(5)*sin(a + b*x)/(S(2)*b) - S(5)*atanh(sin(a + b*x))/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(7)*sec(a + b*x)**S(6), x), x, cos(a + b*x)/b + sec(a + b*x)**S(5)/(S(5)*b) - sec(a + b*x)**S(3)/b + S(3)*sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(6)/sin(a + b*x), x), x, cos(a + b*x)**S(5)/(S(5)*b) + cos(a + b*x)**S(3)/(S(3)*b) + cos(a + b*x)/b - atanh(cos(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(5)/sin(a + b*x), x), x, log(sin(a + b*x))/b + sin(a + b*x)**S(4)/(S(4)*b) - sin(a + b*x)**S(2)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(4)/sin(a + b*x), x), x, cos(a + b*x)**S(3)/(S(3)*b) + cos(a + b*x)/b - atanh(cos(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(3)/sin(a + b*x), x), x, log(sin(a + b*x))/b - sin(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(2)/sin(a + b*x), x), x, cos(a + b*x)/b - atanh(cos(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)/sin(a + b*x), x), x, log(sin(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)/sin(a + b*x), x), x, log(tan(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(2)/sin(a + b*x), x), x, -atanh(cos(a + b*x))/b + sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(3)/sin(a + b*x), x), x, log(tan(a + b*x))/b + tan(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(4)/sin(a + b*x), x), x, -atanh(cos(a + b*x))/b + sec(a + b*x)**S(3)/(S(3)*b) + sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(5)/sin(a + b*x), x), x, log(tan(a + b*x))/b + tan(a + b*x)**S(4)/(S(4)*b) + tan(a + b*x)**S(2)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(6)/sin(a + b*x), x), x, -atanh(cos(a + b*x))/b + sec(a + b*x)**S(5)/(S(5)*b) + sec(a + b*x)**S(3)/(S(3)*b) + sec(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(7)/sin(a + b*x), x), x, log(tan(a + b*x))/b + tan(a + b*x)**S(6)/(S(6)*b) + S(3)*tan(a + b*x)**S(4)/(S(4)*b) + S(3)*tan(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(7)/sin(a + b*x)**S(2), x), x, -sin(a + b*x)**S(5)/(S(5)*b) + sin(a + b*x)**S(3)/b - S(3)*sin(a + b*x)/b - csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(6)/sin(a + b*x)**S(2), x), x, -S(15)*x/S(8) + cos(a + b*x)**S(4)*cot(a + b*x)/(S(4)*b) + S(5)*cos(a + b*x)**S(2)*cot(a + b*x)/(S(8)*b) - S(15)*cot(a + b*x)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(5)/sin(a + b*x)**S(2), x), x, sin(a + b*x)**S(3)/(S(3)*b) - S(2)*sin(a + b*x)/b - csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(4)/sin(a + b*x)**S(2), x), x, -S(3)*x/S(2) + cos(a + b*x)**S(2)*cot(a + b*x)/(S(2)*b) - S(3)*cot(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(3)/sin(a + b*x)**S(2), x), x, -sin(a + b*x)/b - csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(2)/sin(a + b*x)**S(2), x), x, -x - cot(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)/sin(a + b*x)**S(2), x), x, -csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)/sin(a + b*x)**S(2), x), x, atanh(sin(a + b*x))/b - csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(2)/sin(a + b*x)**S(2), x), x, tan(a + b*x)/b - cot(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(3)/sin(a + b*x)**S(2), x), x, S(3)*atanh(sin(a + b*x))/(S(2)*b) + csc(a + b*x)*sec(a + b*x)**S(2)/(S(2)*b) - S(3)*csc(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(4)/sin(a + b*x)**S(2), x), x, tan(a + b*x)**S(3)/(S(3)*b) + S(2)*tan(a + b*x)/b - cot(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(5)/sin(a + b*x)**S(2), x), x, S(15)*atanh(sin(a + b*x))/(S(8)*b) + csc(a + b*x)*sec(a + b*x)**S(4)/(S(4)*b) + S(5)*csc(a + b*x)*sec(a + b*x)**S(2)/(S(8)*b) - S(15)*csc(a + b*x)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(7)/sin(a + b*x)**S(3), x), x, -S(3)*log(sin(a + b*x))/b - sin(a + b*x)**S(4)/(S(4)*b) + S(3)*sin(a + b*x)**S(2)/(S(2)*b) - csc(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(6)/sin(a + b*x)**S(3), x), x, -cos(a + b*x)**S(3)*cot(a + b*x)**S(2)/(S(2)*b) - S(5)*cos(a + b*x)**S(3)/(S(6)*b) - S(5)*cos(a + b*x)/(S(2)*b) + S(5)*atanh(cos(a + b*x))/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(5)/sin(a + b*x)**S(3), x), x, -S(2)*log(sin(a + b*x))/b + sin(a + b*x)**S(2)/(S(2)*b) - csc(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(4)/sin(a + b*x)**S(3), x), x, -cos(a + b*x)*cot(a + b*x)**S(2)/(S(2)*b) - S(3)*cos(a + b*x)/(S(2)*b) + S(3)*atanh(cos(a + b*x))/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(3)/sin(a + b*x)**S(3), x), x, -log(sin(a + b*x))/b - cot(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(2)/sin(a + b*x)**S(3), x), x, -cot(a + b*x)*csc(a + b*x)/(S(2)*b) + atanh(cos(a + b*x))/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)/sin(a + b*x)**S(3), x), x, -csc(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)/sin(a + b*x)**S(3), x), x, log(tan(a + b*x))/b - cot(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(2)/sin(a + b*x)**S(3), x), x, -S(3)*atanh(cos(a + b*x))/(S(2)*b) - csc(a + b*x)**S(2)*sec(a + b*x)/(S(2)*b) + S(3)*sec(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(3)/sin(a + b*x)**S(3), x), x, S(2)*log(tan(a + b*x))/b + tan(a + b*x)**S(2)/(S(2)*b) - cot(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(4)/sin(a + b*x)**S(3), x), x, -S(5)*atanh(cos(a + b*x))/(S(2)*b) - csc(a + b*x)**S(2)*sec(a + b*x)**S(3)/(S(2)*b) + S(5)*sec(a + b*x)**S(3)/(S(6)*b) + S(5)*sec(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(5)/sin(a + b*x)**S(3), x), x, S(3)*log(tan(a + b*x))/b + tan(a + b*x)**S(4)/(S(4)*b) + S(3)*tan(a + b*x)**S(2)/(S(2)*b) - cot(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(9)/sin(a + b*x)**S(4), x), x, sin(a + b*x)**S(5)/(S(5)*b) - S(4)*sin(a + b*x)**S(3)/(S(3)*b) + S(6)*sin(a + b*x)/b - csc(a + b*x)**S(3)/(S(3)*b) + S(4)*csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(8)/sin(a + b*x)**S(4), x), x, S(35)*x/S(8) + cos(a + b*x)**S(4)*cot(a + b*x)**S(3)/(S(4)*b) + S(7)*cos(a + b*x)**S(2)*cot(a + b*x)**S(3)/(S(8)*b) - S(35)*cot(a + b*x)**S(3)/(S(24)*b) + S(35)*cot(a + b*x)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(7)/sin(a + b*x)**S(4), x), x, -sin(a + b*x)**S(3)/(S(3)*b) + S(3)*sin(a + b*x)/b - csc(a + b*x)**S(3)/(S(3)*b) + S(3)*csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(6)/sin(a + b*x)**S(4), x), x, S(5)*x/S(2) + cos(a + b*x)**S(2)*cot(a + b*x)**S(3)/(S(2)*b) - S(5)*cot(a + b*x)**S(3)/(S(6)*b) + S(5)*cot(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(5)/sin(a + b*x)**S(4), x), x, sin(a + b*x)/b - csc(a + b*x)**S(3)/(S(3)*b) + S(2)*csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(4)/sin(a + b*x)**S(4), x), x, x - cot(a + b*x)**S(3)/(S(3)*b) + cot(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(3)/sin(a + b*x)**S(4), x), x, -csc(a + b*x)**S(3)/(S(3)*b) + csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(2)/sin(a + b*x)**S(4), x), x, -cot(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)/sin(a + b*x)**S(4), x), x, -csc(a + b*x)**S(3)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)/sin(a + b*x)**S(4), x), x, atanh(sin(a + b*x))/b - csc(a + b*x)**S(3)/(S(3)*b) - csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(2)/sin(a + b*x)**S(4), x), x, tan(a + b*x)/b - cot(a + b*x)**S(3)/(S(3)*b) - S(2)*cot(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(3)/sin(a + b*x)**S(4), x), x, S(5)*atanh(sin(a + b*x))/(S(2)*b) + csc(a + b*x)**S(3)*sec(a + b*x)**S(2)/(S(2)*b) - S(5)*csc(a + b*x)**S(3)/(S(6)*b) - S(5)*csc(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(4)/sin(a + b*x)**S(4), x), x, tan(a + b*x)**S(3)/(S(3)*b) + S(3)*tan(a + b*x)/b - cot(a + b*x)**S(3)/(S(3)*b) - S(3)*cot(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(5)/sin(a + b*x)**S(4), x), x, S(35)*atanh(sin(a + b*x))/(S(8)*b) + csc(a + b*x)**S(3)*sec(a + b*x)**S(4)/(S(4)*b) + S(7)*csc(a + b*x)**S(3)*sec(a + b*x)**S(2)/(S(8)*b) - S(35)*csc(a + b*x)**S(3)/(S(24)*b) - S(35)*csc(a + b*x)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(9)/sin(a + b*x)**S(5), x), x, S(6)*log(sin(a + b*x))/b + sin(a + b*x)**S(4)/(S(4)*b) - S(2)*sin(a + b*x)**S(2)/b - csc(a + b*x)**S(4)/(S(4)*b) + S(2)*csc(a + b*x)**S(2)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(8)/sin(a + b*x)**S(5), x), x, -cos(a + b*x)**S(3)*cot(a + b*x)**S(4)/(S(4)*b) + S(7)*cos(a + b*x)**S(3)*cot(a + b*x)**S(2)/(S(8)*b) + S(35)*cos(a + b*x)**S(3)/(S(24)*b) + S(35)*cos(a + b*x)/(S(8)*b) - S(35)*atanh(cos(a + b*x))/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(7)/sin(a + b*x)**S(5), x), x, S(3)*log(sin(a + b*x))/b - sin(a + b*x)**S(2)/(S(2)*b) - csc(a + b*x)**S(4)/(S(4)*b) + S(3)*csc(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(6)/sin(a + b*x)**S(5), x), x, -cos(a + b*x)*cot(a + b*x)**S(4)/(S(4)*b) + S(5)*cos(a + b*x)*cot(a + b*x)**S(2)/(S(8)*b) + S(15)*cos(a + b*x)/(S(8)*b) - S(15)*atanh(cos(a + b*x))/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(5)/sin(a + b*x)**S(5), x), x, log(sin(a + b*x))/b - cot(a + b*x)**S(4)/(S(4)*b) + cot(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(4)/sin(a + b*x)**S(5), x), x, -cot(a + b*x)**S(3)*csc(a + b*x)/(S(4)*b) + S(3)*cot(a + b*x)*csc(a + b*x)/(S(8)*b) - S(3)*atanh(cos(a + b*x))/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(3)/sin(a + b*x)**S(5), x), x, -cot(a + b*x)**S(4)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(2)/sin(a + b*x)**S(5), x), x, -cot(a + b*x)*csc(a + b*x)**S(3)/(S(4)*b) + cot(a + b*x)*csc(a + b*x)/(S(8)*b) + atanh(cos(a + b*x))/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)/sin(a + b*x)**S(5), x), x, -csc(a + b*x)**S(4)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)/sin(a + b*x)**S(5), x), x, log(tan(a + b*x))/b - cot(a + b*x)**S(4)/(S(4)*b) - cot(a + b*x)**S(2)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(2)/sin(a + b*x)**S(5), x), x, -S(15)*atanh(cos(a + b*x))/(S(8)*b) - csc(a + b*x)**S(4)*sec(a + b*x)/(S(4)*b) - S(5)*csc(a + b*x)**S(2)*sec(a + b*x)/(S(8)*b) + S(15)*sec(a + b*x)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(3)/sin(a + b*x)**S(5), x), x, S(3)*log(tan(a + b*x))/b + tan(a + b*x)**S(2)/(S(2)*b) - cot(a + b*x)**S(4)/(S(4)*b) - S(3)*cot(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(4)/sin(a + b*x)**S(5), x), x, -S(35)*atanh(cos(a + b*x))/(S(8)*b) - csc(a + b*x)**S(4)*sec(a + b*x)**S(3)/(S(4)*b) - S(7)*csc(a + b*x)**S(2)*sec(a + b*x)**S(3)/(S(8)*b) + S(35)*sec(a + b*x)**S(3)/(S(24)*b) + S(35)*sec(a + b*x)/(S(8)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(5)/sin(a + b*x)**S(5), x), x, S(6)*log(tan(a + b*x))/b + tan(a + b*x)**S(4)/(S(4)*b) + S(2)*tan(a + b*x)**S(2)/b - cot(a + b*x)**S(4)/(S(4)*b) - S(2)*cot(a + b*x)**S(2)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(x)**S(2)/sin(x)**S(6), x), x, -cot(x)**S(5)/S(5) - cot(x)**S(3)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(x)**S(3)/sin(x)**S(7), x), x, -csc(x)**S(6)/S(6) + csc(x)**S(4)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(3)/2)*sin(a + b*x), x), x, -S(2)*(d*cos(a + b*x))**(S(5)/2)/(S(5)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cos(a + b*x))*sin(a + b*x), x), x, -S(2)*(d*cos(a + b*x))**(S(3)/2)/(S(3)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)/sqrt(d*cos(a + b*x)), x), x, -S(2)*sqrt(d*cos(a + b*x))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)/(d*cos(a + b*x))**(S(3)/2), x), x, S(2)/(b*d*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)/(d*cos(a + b*x))**(S(5)/2), x), x, S(2)/(S(3)*b*d*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)/(d*cos(a + b*x))**(S(7)/2), x), x, S(2)/(S(5)*b*d*(d*cos(a + b*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)/(d*cos(a + b*x))**(S(9)/2), x), x, S(2)/(S(7)*b*d*(d*cos(a + b*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(9)/2)*sin(a + b*x)**S(2), x), x, S(28)*d**S(4)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(195)*b*sqrt(cos(a + b*x))) + S(28)*d**S(3)*(d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)/(S(585)*b) + S(4)*d*(d*cos(a + b*x))**(S(7)/2)*sin(a + b*x)/(S(117)*b) - S(2)*(d*cos(a + b*x))**(S(11)/2)*sin(a + b*x)/(S(13)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(7)/2)*sin(a + b*x)**S(2), x), x, S(20)*d**S(4)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(231)*b*sqrt(d*cos(a + b*x))) + S(20)*d**S(3)*sqrt(d*cos(a + b*x))*sin(a + b*x)/(S(231)*b) + S(4)*d*(d*cos(a + b*x))**(S(5)/2)*sin(a + b*x)/(S(77)*b) - S(2)*(d*cos(a + b*x))**(S(9)/2)*sin(a + b*x)/(S(11)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(5)/2)*sin(a + b*x)**S(2), x), x, S(4)*d**S(2)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(15)*b*sqrt(cos(a + b*x))) + S(4)*d*(d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)/(S(45)*b) - S(2)*(d*cos(a + b*x))**(S(7)/2)*sin(a + b*x)/(S(9)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)**S(2), x), x, S(4)*d**S(2)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(21)*b*sqrt(d*cos(a + b*x))) + S(4)*d*sqrt(d*cos(a + b*x))*sin(a + b*x)/(S(21)*b) - S(2)*(d*cos(a + b*x))**(S(5)/2)*sin(a + b*x)/(S(7)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cos(a + b*x))*sin(a + b*x)**S(2), x), x, S(4)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(5)*b*sqrt(cos(a + b*x))) - S(2)*(d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)/(S(5)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)/sqrt(d*cos(a + b*x)), x), x, S(4)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(3)*b*sqrt(d*cos(a + b*x))) - S(2)*sqrt(d*cos(a + b*x))*sin(a + b*x)/(S(3)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)/(d*cos(a + b*x))**(S(3)/2), x), x, S(2)*sin(a + b*x)/(b*d*sqrt(d*cos(a + b*x))) - S(4)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(b*d**S(2)*sqrt(cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)/(d*cos(a + b*x))**(S(5)/2), x), x, S(2)*sin(a + b*x)/(S(3)*b*d*(d*cos(a + b*x))**(S(3)/2)) - S(4)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(3)*b*d**S(2)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)/(d*cos(a + b*x))**(S(7)/2), x), x, S(2)*sin(a + b*x)/(S(5)*b*d*(d*cos(a + b*x))**(S(5)/2)) - S(4)*sin(a + b*x)/(S(5)*b*d**S(3)*sqrt(d*cos(a + b*x))) + S(4)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(5)*b*d**S(4)*sqrt(cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)/(d*cos(a + b*x))**(S(9)/2), x), x, S(2)*sin(a + b*x)/(S(7)*b*d*(d*cos(a + b*x))**(S(7)/2)) - S(4)*sin(a + b*x)/(S(21)*b*d**S(3)*(d*cos(a + b*x))**(S(3)/2)) - S(4)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(21)*b*d**S(4)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cos(a + b*x))*sin(a + b*x)**S(3), x), x, -S(2)*(d*cos(a + b*x))**(S(3)/2)/(S(3)*b*d) + S(2)*(d*cos(a + b*x))**(S(7)/2)/(S(7)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)/sqrt(d*cos(a + b*x)), x), x, -S(2)*sqrt(d*cos(a + b*x))/(b*d) + S(2)*(d*cos(a + b*x))**(S(5)/2)/(S(5)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)/(d*cos(a + b*x))**(S(3)/2), x), x, S(2)/(b*d*sqrt(d*cos(a + b*x))) + S(2)*(d*cos(a + b*x))**(S(3)/2)/(S(3)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)/(d*cos(a + b*x))**(S(5)/2), x), x, S(2)/(S(3)*b*d*(d*cos(a + b*x))**(S(3)/2)) + S(2)*sqrt(d*cos(a + b*x))/(b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)/(d*cos(a + b*x))**(S(7)/2), x), x, S(2)/(S(5)*b*d*(d*cos(a + b*x))**(S(5)/2)) - S(2)/(b*d**S(3)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)/(d*cos(a + b*x))**(S(9)/2), x), x, S(2)/(S(7)*b*d*(d*cos(a + b*x))**(S(7)/2)) - S(2)/(S(3)*b*d**S(3)*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)/(d*cos(a + b*x))**(S(11)/2), x), x, S(2)/(S(9)*b*d*(d*cos(a + b*x))**(S(9)/2)) - S(2)/(S(5)*b*d**S(3)*(d*cos(a + b*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(9)/2)*sin(a + b*x)**S(4), x), x, S(56)*d**S(4)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(1105)*b*sqrt(cos(a + b*x))) + S(56)*d**S(3)*(d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)/(S(3315)*b) + S(8)*d*(d*cos(a + b*x))**(S(7)/2)*sin(a + b*x)/(S(663)*b) - S(2)*(d*cos(a + b*x))**(S(11)/2)*sin(a + b*x)**S(3)/(S(17)*b*d) - S(12)*(d*cos(a + b*x))**(S(11)/2)*sin(a + b*x)/(S(221)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(7)/2)*sin(a + b*x)**S(4), x), x, S(8)*d**S(4)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(231)*b*sqrt(d*cos(a + b*x))) + S(8)*d**S(3)*sqrt(d*cos(a + b*x))*sin(a + b*x)/(S(231)*b) + S(8)*d*(d*cos(a + b*x))**(S(5)/2)*sin(a + b*x)/(S(385)*b) - S(2)*(d*cos(a + b*x))**(S(9)/2)*sin(a + b*x)**S(3)/(S(15)*b*d) - S(4)*(d*cos(a + b*x))**(S(9)/2)*sin(a + b*x)/(S(55)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(5)/2)*sin(a + b*x)**S(4), x), x, S(8)*d**S(2)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(65)*b*sqrt(cos(a + b*x))) + S(8)*d*(d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)/(S(195)*b) - S(2)*(d*cos(a + b*x))**(S(7)/2)*sin(a + b*x)**S(3)/(S(13)*b*d) - S(4)*(d*cos(a + b*x))**(S(7)/2)*sin(a + b*x)/(S(39)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)**S(4), x), x, S(8)*d**S(2)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(77)*b*sqrt(d*cos(a + b*x))) + S(8)*d*sqrt(d*cos(a + b*x))*sin(a + b*x)/(S(77)*b) - S(2)*(d*cos(a + b*x))**(S(5)/2)*sin(a + b*x)**S(3)/(S(11)*b*d) - S(12)*(d*cos(a + b*x))**(S(5)/2)*sin(a + b*x)/(S(77)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cos(a + b*x))*sin(a + b*x)**S(4), x), x, S(8)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(15)*b*sqrt(cos(a + b*x))) - S(2)*(d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)**S(3)/(S(9)*b*d) - S(4)*(d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)/(S(15)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)/sqrt(d*cos(a + b*x)), x), x, S(8)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(7)*b*sqrt(d*cos(a + b*x))) - S(2)*sqrt(d*cos(a + b*x))*sin(a + b*x)**S(3)/(S(7)*b*d) - S(4)*sqrt(d*cos(a + b*x))*sin(a + b*x)/(S(7)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)/(d*cos(a + b*x))**(S(3)/2), x), x, S(2)*sin(a + b*x)**S(3)/(b*d*sqrt(d*cos(a + b*x))) - S(24)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(5)*b*d**S(2)*sqrt(cos(a + b*x))) + S(12)*(d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)/(S(5)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)/(d*cos(a + b*x))**(S(5)/2), x), x, S(2)*sin(a + b*x)**S(3)/(S(3)*b*d*(d*cos(a + b*x))**(S(3)/2)) - S(8)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(3)*b*d**S(2)*sqrt(d*cos(a + b*x))) + S(4)*sqrt(d*cos(a + b*x))*sin(a + b*x)/(S(3)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)/(d*cos(a + b*x))**(S(7)/2), x), x, S(2)*sin(a + b*x)**S(3)/(S(5)*b*d*(d*cos(a + b*x))**(S(5)/2)) - S(12)*sin(a + b*x)/(S(5)*b*d**S(3)*sqrt(d*cos(a + b*x))) + S(24)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(5)*b*d**S(4)*sqrt(cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)/(d*cos(a + b*x))**(S(9)/2), x), x, S(2)*sin(a + b*x)**S(3)/(S(7)*b*d*(d*cos(a + b*x))**(S(7)/2)) - S(4)*sin(a + b*x)/(S(7)*b*d**S(3)*(d*cos(a + b*x))**(S(3)/2)) + S(8)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(7)*b*d**S(4)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)*cos(a + b*x)**(S(3)/2), x), x, -S(2)*cos(a + b*x)**(S(13)/2)/(S(13)*b) + S(4)*cos(a + b*x)**(S(9)/2)/(S(9)*b) - S(2)*cos(a + b*x)**(S(5)/2)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(9)/2)*csc(a + b*x), x), x, d**(S(9)/2)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/b - d**(S(9)/2)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/b + S(2)*d**S(3)*(d*cos(a + b*x))**(S(3)/2)/(S(3)*b) + S(2)*d*(d*cos(a + b*x))**(S(7)/2)/(S(7)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(7)/2)*csc(a + b*x), x), x, -d**(S(7)/2)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/b - d**(S(7)/2)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/b + S(2)*d**S(3)*sqrt(d*cos(a + b*x))/b + S(2)*d*(d*cos(a + b*x))**(S(5)/2)/(S(5)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(5)/2)*csc(a + b*x), x), x, d**(S(5)/2)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/b - d**(S(5)/2)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/b + S(2)*d*(d*cos(a + b*x))**(S(3)/2)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(3)/2)*csc(a + b*x), x), x, -d**(S(3)/2)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/b - d**(S(3)/2)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/b + S(2)*d*sqrt(d*cos(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cos(a + b*x))*csc(a + b*x), x), x, sqrt(d)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/b - sqrt(d)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)/sqrt(d*cos(a + b*x)), x), x, -ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(b*sqrt(d)) - atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(b*sqrt(d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)/(d*cos(a + b*x))**(S(3)/2), x), x, S(2)/(b*d*sqrt(d*cos(a + b*x))) + ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(b*d**(S(3)/2)) - atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(b*d**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)/(d*cos(a + b*x))**(S(5)/2), x), x, S(2)/(S(3)*b*d*(d*cos(a + b*x))**(S(3)/2)) - ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(b*d**(S(5)/2)) - atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(b*d**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)/(d*cos(a + b*x))**(S(7)/2), x), x, S(2)/(S(5)*b*d*(d*cos(a + b*x))**(S(5)/2)) + S(2)/(b*d**S(3)*sqrt(d*cos(a + b*x))) + ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(b*d**(S(7)/2)) - atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(b*d**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)/(d*cos(a + b*x))**(S(9)/2), x), x, S(2)/(S(7)*b*d*(d*cos(a + b*x))**(S(7)/2)) + S(2)/(S(3)*b*d**S(3)*(d*cos(a + b*x))**(S(3)/2)) - ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(b*d**(S(9)/2)) - atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(b*d**(S(9)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(11)/2)*csc(a + b*x)**S(2), x), x, -S(15)*d**S(6)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(7)*b*sqrt(d*cos(a + b*x))) - S(15)*d**S(5)*sqrt(d*cos(a + b*x))*sin(a + b*x)/(S(7)*b) - S(9)*d**S(3)*(d*cos(a + b*x))**(S(5)/2)*sin(a + b*x)/(S(7)*b) - d*(d*cos(a + b*x))**(S(9)/2)*csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(9)/2)*csc(a + b*x)**S(2), x), x, -S(21)*d**S(4)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(5)*b*sqrt(cos(a + b*x))) - S(7)*d**S(3)*(d*cos(a + b*x))**(S(3)/2)*sin(a + b*x)/(S(5)*b) - d*(d*cos(a + b*x))**(S(7)/2)*csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(7)/2)*csc(a + b*x)**S(2), x), x, -S(5)*d**S(4)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(3)*b*sqrt(d*cos(a + b*x))) - S(5)*d**S(3)*sqrt(d*cos(a + b*x))*sin(a + b*x)/(S(3)*b) - d*(d*cos(a + b*x))**(S(5)/2)*csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(5)/2)*csc(a + b*x)**S(2), x), x, -S(3)*d**S(2)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(b*sqrt(cos(a + b*x))) - d*(d*cos(a + b*x))**(S(3)/2)*csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(3)/2)*csc(a + b*x)**S(2), x), x, -d**S(2)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(b*sqrt(d*cos(a + b*x))) - d*sqrt(d*cos(a + b*x))*csc(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cos(a + b*x))*csc(a + b*x)**S(2), x), x, -sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(b*sqrt(cos(a + b*x))) - (d*cos(a + b*x))**(S(3)/2)*csc(a + b*x)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(2)/sqrt(d*cos(a + b*x)), x), x, EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(b*sqrt(d*cos(a + b*x))) - sqrt(d*cos(a + b*x))*csc(a + b*x)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(2)/(d*cos(a + b*x))**(S(3)/2), x), x, S(3)*sin(a + b*x)/(b*d*sqrt(d*cos(a + b*x))) - csc(a + b*x)/(b*d*sqrt(d*cos(a + b*x))) - S(3)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(b*d**S(2)*sqrt(cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(2)/(d*cos(a + b*x))**(S(5)/2), x), x, S(5)*sin(a + b*x)/(S(3)*b*d*(d*cos(a + b*x))**(S(3)/2)) - csc(a + b*x)/(b*d*(d*cos(a + b*x))**(S(3)/2)) + S(5)*EllipticF(a/S(2) + b*x/S(2), S(2))*sqrt(cos(a + b*x))/(S(3)*b*d**S(2)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(2)/(d*cos(a + b*x))**(S(7)/2), x), x, S(7)*sin(a + b*x)/(S(5)*b*d*(d*cos(a + b*x))**(S(5)/2)) - csc(a + b*x)/(b*d*(d*cos(a + b*x))**(S(5)/2)) + S(21)*sin(a + b*x)/(S(5)*b*d**S(3)*sqrt(d*cos(a + b*x))) - S(21)*sqrt(d*cos(a + b*x))*EllipticE(a/S(2) + b*x/S(2), S(2))/(S(5)*b*d**S(4)*sqrt(cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(11)/2)*csc(a + b*x)**S(3), x), x, S(9)*d**(S(11)/2)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) + S(9)*d**(S(11)/2)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) - S(9)*d**S(5)*sqrt(d*cos(a + b*x))/(S(2)*b) - S(9)*d**S(3)*(d*cos(a + b*x))**(S(5)/2)/(S(10)*b) - d*(d*cos(a + b*x))**(S(9)/2)*csc(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(9)/2)*csc(a + b*x)**S(3), x), x, -S(7)*d**(S(9)/2)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) + S(7)*d**(S(9)/2)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) - S(7)*d**S(3)*(d*cos(a + b*x))**(S(3)/2)/(S(6)*b) - d*(d*cos(a + b*x))**(S(7)/2)*csc(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(7)/2)*csc(a + b*x)**S(3), x), x, S(5)*d**(S(7)/2)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) + S(5)*d**(S(7)/2)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) - S(5)*d**S(3)*sqrt(d*cos(a + b*x))/(S(2)*b) - d*(d*cos(a + b*x))**(S(5)/2)*csc(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(5)/2)*csc(a + b*x)**S(3), x), x, -S(3)*d**(S(5)/2)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) + S(3)*d**(S(5)/2)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) - d*(d*cos(a + b*x))**(S(3)/2)*csc(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(3)/2)*csc(a + b*x)**S(3), x), x, d**(S(3)/2)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) + d**(S(3)/2)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) - d*sqrt(d*cos(a + b*x))*csc(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cos(a + b*x))*csc(a + b*x)**S(3), x), x, sqrt(d)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) - sqrt(d)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b) - (d*cos(a + b*x))**(S(3)/2)*csc(a + b*x)**S(2)/(S(2)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(3)/sqrt(d*cos(a + b*x)), x), x, -sqrt(d*cos(a + b*x))*csc(a + b*x)**S(2)/(S(2)*b*d) - S(3)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b*sqrt(d)) - S(3)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b*sqrt(d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(3)/(d*cos(a + b*x))**(S(3)/2), x), x, -csc(a + b*x)**S(2)/(S(2)*b*d*sqrt(d*cos(a + b*x))) + S(5)/(S(2)*b*d*sqrt(d*cos(a + b*x))) + S(5)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b*d**(S(3)/2)) - S(5)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b*d**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(3)/(d*cos(a + b*x))**(S(5)/2), x), x, -csc(a + b*x)**S(2)/(S(2)*b*d*(d*cos(a + b*x))**(S(3)/2)) + S(7)/(S(6)*b*d*(d*cos(a + b*x))**(S(3)/2)) - S(7)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b*d**(S(5)/2)) - S(7)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b*d**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(3)/(d*cos(a + b*x))**(S(7)/2), x), x, -csc(a + b*x)**S(2)/(S(2)*b*d*(d*cos(a + b*x))**(S(5)/2)) + S(9)/(S(10)*b*d*(d*cos(a + b*x))**(S(5)/2)) + S(9)/(S(2)*b*d**S(3)*sqrt(d*cos(a + b*x))) + S(9)*ArcTan(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b*d**(S(7)/2)) - S(9)*atanh(sqrt(d*cos(a + b*x))/sqrt(d))/(S(4)*b*d**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(1)/5)*sin(a + b*x), x), x, -S(5)*(d*cos(a + b*x))**(S(6)/5)/(S(6)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(sin(x))*cos(x)**S(3), x), x, -S(2)*sin(x)**(S(7)/2)/S(7) + S(2)*sin(x)**(S(3)/2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(x)**(S(3)/2)*cos(x)**S(3), x), x, -S(2)*sin(x)**(S(9)/2)/S(9) + S(2)*sin(x)**(S(5)/2)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(x)**(S(5)/2)*cos(x)**S(3), x), x, -S(2)*sin(x)**(S(11)/2)/S(11) + S(2)*sin(x)**(S(7)/2)/S(7), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(x)**S(3)/sqrt(sin(x)), x), x, -S(2)*sin(x)**(S(5)/2)/S(5) + S(2)*sqrt(sin(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(9)/2), x), x, S(7)*d**S(4)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(S(20)*b*sqrt(sin(S(2)*a + S(2)*b*x))) + S(7)*d**S(3)*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(3)/2)/(S(30)*b*c) + d*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(7)/2)/(S(5)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(5)/2), x), x, d**S(2)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(S(2)*b*sqrt(sin(S(2)*a + S(2)*b*x))) + d*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(3)/2)/(S(3)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x)), x), x, sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(b*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))/(d*cos(a + b*x))**(S(3)/2), x), x, -S(2)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(b*d**S(2)*sqrt(sin(S(2)*a + S(2)*b*x))) + S(2)*(c*sin(a + b*x))**(S(3)/2)/(b*c*d*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))/(d*cos(a + b*x))**(S(7)/2), x), x, -S(4)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(S(5)*b*d**S(4)*sqrt(sin(S(2)*a + S(2)*b*x))) + S(2)*(c*sin(a + b*x))**(S(3)/2)/(S(5)*b*c*d*(d*cos(a + b*x))**(S(5)/2)) + S(4)*(c*sin(a + b*x))**(S(3)/2)/(S(5)*b*c*d**S(3)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(3)/2), x), x, -sqrt(S(2))*sqrt(c)*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/(sqrt(c)*sqrt(d*cos(a + b*x))))/(S(8)*b) + sqrt(S(2))*sqrt(c)*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/(sqrt(c)*sqrt(d*cos(a + b*x))))/(S(8)*b) + sqrt(S(2))*sqrt(c)*d**(S(3)/2)*log(sqrt(c)*tan(a + b*x) + sqrt(c) - sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/sqrt(d*cos(a + b*x)))/(S(16)*b) - sqrt(S(2))*sqrt(c)*d**(S(3)/2)*log(sqrt(c)*tan(a + b*x) + sqrt(c) + sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/sqrt(d*cos(a + b*x)))/(S(16)*b) + d*(c*sin(a + b*x))**(S(3)/2)*sqrt(d*cos(a + b*x))/(S(2)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))/sqrt(d*cos(a + b*x)), x), x, -sqrt(S(2))*sqrt(c)*ArcTan(S(1) - sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/(sqrt(c)*sqrt(d*cos(a + b*x))))/(S(2)*b*sqrt(d)) + sqrt(S(2))*sqrt(c)*ArcTan(S(1) + sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/(sqrt(c)*sqrt(d*cos(a + b*x))))/(S(2)*b*sqrt(d)) + sqrt(S(2))*sqrt(c)*log(sqrt(c)*tan(a + b*x) + sqrt(c) - sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/sqrt(d*cos(a + b*x)))/(S(4)*b*sqrt(d)) - sqrt(S(2))*sqrt(c)*log(sqrt(c)*tan(a + b*x) + sqrt(c) + sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/sqrt(d*cos(a + b*x)))/(S(4)*b*sqrt(d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))/(d*cos(a + b*x))**(S(5)/2), x), x, S(2)*(c*sin(a + b*x))**(S(3)/2)/(S(3)*b*c*d*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))/(d*cos(a + b*x))**(S(9)/2), x), x, S(2)*(c*sin(a + b*x))**(S(3)/2)/(S(7)*b*c*d*(d*cos(a + b*x))**(S(7)/2)) + S(8)*(c*sin(a + b*x))**(S(3)/2)/(S(21)*b*c*d**S(3)*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))/(d*cos(a + b*x))**(S(13)/2), x), x, S(2)*(c*sin(a + b*x))**(S(3)/2)/(S(11)*b*c*d*(d*cos(a + b*x))**(S(11)/2)) + S(16)*(c*sin(a + b*x))**(S(3)/2)/(S(77)*b*c*d**S(3)*(d*cos(a + b*x))**(S(7)/2)) + S(64)*(c*sin(a + b*x))**(S(3)/2)/(S(231)*b*c*d**S(5)*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(3)/2), x), x, c**S(2)*d**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))/(S(12)*b*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))) + c*d*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))/(S(6)*b) - c*sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(5)/2)/(S(3)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)/sqrt(d*cos(a + b*x)), x), x, c**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))/(S(2)*b*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))) - c*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)/(d*cos(a + b*x))**(S(5)/2), x), x, -c**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))/(S(3)*b*d**S(2)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))) + S(2)*c*sqrt(c*sin(a + b*x))/(S(3)*b*d*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)/(d*cos(a + b*x))**(S(9)/2), x), x, -S(2)*c**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))/(S(21)*b*d**S(4)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))) + S(2)*c*sqrt(c*sin(a + b*x))/(S(7)*b*d*(d*cos(a + b*x))**(S(7)/2)) - S(2)*c*sqrt(c*sin(a + b*x))/(S(21)*b*d**S(3)*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)*sqrt(d*cos(a + b*x)), x), x, sqrt(S(2))*c**(S(3)/2)*sqrt(d)*ArcTan(-sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/(sqrt(d)*sqrt(c*sin(a + b*x))) + S(1))/(S(8)*b) - sqrt(S(2))*c**(S(3)/2)*sqrt(d)*ArcTan(sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/(sqrt(d)*sqrt(c*sin(a + b*x))) + S(1))/(S(8)*b) - sqrt(S(2))*c**(S(3)/2)*sqrt(d)*log(-sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/sqrt(c*sin(a + b*x)) + sqrt(d)*cot(a + b*x) + sqrt(d))/(S(16)*b) + sqrt(S(2))*c**(S(3)/2)*sqrt(d)*log(sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/sqrt(c*sin(a + b*x)) + sqrt(d)*cot(a + b*x) + sqrt(d))/(S(16)*b) - c*sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(3)/2)/(S(2)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)/(d*cos(a + b*x))**(S(3)/2), x), x, -sqrt(S(2))*c**(S(3)/2)*ArcTan(-sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/(sqrt(d)*sqrt(c*sin(a + b*x))) + S(1))/(S(2)*b*d**(S(3)/2)) + sqrt(S(2))*c**(S(3)/2)*ArcTan(sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/(sqrt(d)*sqrt(c*sin(a + b*x))) + S(1))/(S(2)*b*d**(S(3)/2)) + sqrt(S(2))*c**(S(3)/2)*log(-sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/sqrt(c*sin(a + b*x)) + sqrt(d)*cot(a + b*x) + sqrt(d))/(S(4)*b*d**(S(3)/2)) - sqrt(S(2))*c**(S(3)/2)*log(sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/sqrt(c*sin(a + b*x)) + sqrt(d)*cot(a + b*x) + sqrt(d))/(S(4)*b*d**(S(3)/2)) + S(2)*c*sqrt(c*sin(a + b*x))/(b*d*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)/(d*cos(a + b*x))**(S(7)/2), x), x, S(2)*(c*sin(a + b*x))**(S(5)/2)/(S(5)*b*c*d*(d*cos(a + b*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)/(d*cos(a + b*x))**(S(11)/2), x), x, S(2)*c*sqrt(c*sin(a + b*x))/(S(9)*b*d*(d*cos(a + b*x))**(S(9)/2)) - S(2)*c*sqrt(c*sin(a + b*x))/(S(45)*b*d**S(3)*(d*cos(a + b*x))**(S(5)/2)) - S(8)*c*sqrt(c*sin(a + b*x))/(S(45)*b*d**S(5)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)/(d*cos(a + b*x))**(S(15)/2), x), x, S(2)*c*sqrt(c*sin(a + b*x))/(S(13)*b*d*(d*cos(a + b*x))**(S(13)/2)) - S(2)*c*sqrt(c*sin(a + b*x))/(S(117)*b*d**S(3)*(d*cos(a + b*x))**(S(9)/2)) - S(16)*c*sqrt(c*sin(a + b*x))/(S(585)*b*d**S(5)*(d*cos(a + b*x))**(S(5)/2)) - S(64)*c*sqrt(c*sin(a + b*x))/(S(585)*b*d**S(7)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)*(d*cos(a + b*x))**(S(9)/2), x), x, S(3)*c**S(2)*d**S(4)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(S(40)*b*sqrt(sin(S(2)*a + S(2)*b*x))) + c*d**S(3)*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(3)/2)/(S(20)*b) + S(3)*c*d*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(7)/2)/(S(70)*b) - c*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(11)/2)/(S(7)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)*(d*cos(a + b*x))**(S(5)/2), x), x, S(3)*c**S(2)*d**S(2)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(S(20)*b*sqrt(sin(S(2)*a + S(2)*b*x))) + c*d*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(3)/2)/(S(10)*b) - c*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(7)/2)/(S(5)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)*sqrt(d*cos(a + b*x)), x), x, c**S(2)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(S(2)*b*sqrt(sin(S(2)*a + S(2)*b*x))) - c*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(S(3)/2)/(S(3)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)/(d*cos(a + b*x))**(S(3)/2), x), x, -S(3)*c**S(2)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(b*d**S(2)*sqrt(sin(S(2)*a + S(2)*b*x))) + S(2)*c*(c*sin(a + b*x))**(S(3)/2)/(b*d*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)/(d*cos(a + b*x))**(S(7)/2), x), x, S(6)*c**S(2)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(S(5)*b*d**S(4)*sqrt(sin(S(2)*a + S(2)*b*x))) + S(2)*c*(c*sin(a + b*x))**(S(3)/2)/(S(5)*b*d*(d*cos(a + b*x))**(S(5)/2)) - S(6)*c*(c*sin(a + b*x))**(S(3)/2)/(S(5)*b*d**S(3)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)/(d*cos(a + b*x))**(S(11)/2), x), x, S(4)*c**S(2)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))/(S(15)*b*d**S(6)*sqrt(sin(S(2)*a + S(2)*b*x))) + S(2)*c*(c*sin(a + b*x))**(S(3)/2)/(S(9)*b*d*(d*cos(a + b*x))**(S(9)/2)) - S(2)*c*(c*sin(a + b*x))**(S(3)/2)/(S(15)*b*d**S(3)*(d*cos(a + b*x))**(S(5)/2)) - S(4)*c*(c*sin(a + b*x))**(S(3)/2)/(S(15)*b*d**S(5)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)/sqrt(d*cos(a + b*x)), x), x, -S(3)*sqrt(S(2))*c**(S(5)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/(sqrt(c)*sqrt(d*cos(a + b*x))))/(S(8)*b*sqrt(d)) + S(3)*sqrt(S(2))*c**(S(5)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/(sqrt(c)*sqrt(d*cos(a + b*x))))/(S(8)*b*sqrt(d)) + S(3)*sqrt(S(2))*c**(S(5)/2)*log(sqrt(c)*tan(a + b*x) + sqrt(c) - sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/sqrt(d*cos(a + b*x)))/(S(16)*b*sqrt(d)) - S(3)*sqrt(S(2))*c**(S(5)/2)*log(sqrt(c)*tan(a + b*x) + sqrt(c) + sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/sqrt(d*cos(a + b*x)))/(S(16)*b*sqrt(d)) - c*(c*sin(a + b*x))**(S(3)/2)*sqrt(d*cos(a + b*x))/(S(2)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)/(d*cos(a + b*x))**(S(5)/2), x), x, sqrt(S(2))*c**(S(5)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/(sqrt(c)*sqrt(d*cos(a + b*x))))/(S(2)*b*d**(S(5)/2)) - sqrt(S(2))*c**(S(5)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/(sqrt(c)*sqrt(d*cos(a + b*x))))/(S(2)*b*d**(S(5)/2)) - sqrt(S(2))*c**(S(5)/2)*log(sqrt(c)*tan(a + b*x) + sqrt(c) - sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/sqrt(d*cos(a + b*x)))/(S(4)*b*d**(S(5)/2)) + sqrt(S(2))*c**(S(5)/2)*log(sqrt(c)*tan(a + b*x) + sqrt(c) + sqrt(S(2))*sqrt(d)*sqrt(c*sin(a + b*x))/sqrt(d*cos(a + b*x)))/(S(4)*b*d**(S(5)/2)) + S(2)*c*(c*sin(a + b*x))**(S(3)/2)/(S(3)*b*d*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)/(d*cos(a + b*x))**(S(9)/2), x), x, S(2)*(c*sin(a + b*x))**(S(7)/2)/(S(7)*b*c*d*(d*cos(a + b*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)/(d*cos(a + b*x))**(S(13)/2), x), x, S(2)*c*(c*sin(a + b*x))**(S(3)/2)/(S(11)*b*d*(d*cos(a + b*x))**(S(11)/2)) - S(6)*c*(c*sin(a + b*x))**(S(3)/2)/(S(77)*b*d**S(3)*(d*cos(a + b*x))**(S(7)/2)) - S(8)*c*(c*sin(a + b*x))**(S(3)/2)/(S(77)*b*d**S(5)*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)/(d*cos(a + b*x))**(S(17)/2), x), x, S(2)*c*(c*sin(a + b*x))**(S(3)/2)/(S(15)*b*d*(d*cos(a + b*x))**(S(15)/2)) - S(2)*c*(c*sin(a + b*x))**(S(3)/2)/(S(55)*b*d**S(3)*(d*cos(a + b*x))**(S(11)/2)) - S(16)*c*(c*sin(a + b*x))**(S(3)/2)/(S(385)*b*d**S(5)*(d*cos(a + b*x))**(S(7)/2)) - S(64)*c*(c*sin(a + b*x))**(S(3)/2)/(S(1155)*b*d**S(7)*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(7)/2)/cos(a + b*x)**(S(7)/2), x), x, sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(2)*b) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(2)*b) - sqrt(S(2))*log(cot(a + b*x) + S(1) - sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(4)*b) + sqrt(S(2))*log(cot(a + b*x) + S(1) + sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(4)*b) + S(2)*sin(a + b*x)**(S(5)/2)/(S(5)*b*cos(a + b*x)**(S(5)/2)) - S(2)*sqrt(sin(a + b*x))/(b*sqrt(cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(x)**(S(3)/2)/cos(x)**(S(7)/2), x), x, S(2)*sin(x)**(S(5)/2)/(S(5)*cos(x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(sin(x))/sqrt(cos(x)), x), x, -sqrt(S(2))*ArcTan(-sqrt(S(2))*sqrt(sin(x))/sqrt(cos(x)) + S(1))/S(2) + sqrt(S(2))*ArcTan(sqrt(S(2))*sqrt(sin(x))/sqrt(cos(x)) + S(1))/S(2) + sqrt(S(2))*log(-sqrt(S(2))*sqrt(sin(x))/sqrt(cos(x)) + tan(x) + S(1))/S(4) - sqrt(S(2))*log(sqrt(S(2))*sqrt(sin(x))/sqrt(cos(x)) + tan(x) + S(1))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(x)**(S(5)/2)/sqrt(cos(x)), x), x, -S(3)*sqrt(S(2))*ArcTan(-sqrt(S(2))*sqrt(sin(x))/sqrt(cos(x)) + S(1))/S(8) + S(3)*sqrt(S(2))*ArcTan(sqrt(S(2))*sqrt(sin(x))/sqrt(cos(x)) + S(1))/S(8) + S(3)*sqrt(S(2))*log(-sqrt(S(2))*sqrt(sin(x))/sqrt(cos(x)) + tan(x) + S(1))/S(16) - S(3)*sqrt(S(2))*log(sqrt(S(2))*sqrt(sin(x))/sqrt(cos(x)) + tan(x) + S(1))/S(16) - sin(x)**(S(3)/2)*sqrt(cos(x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(7)/2)/sqrt(c*sin(a + b*x)), x), x, S(5)*d**S(4)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))/(S(12)*b*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))) + S(5)*d**S(3)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))/(S(6)*b*c) + d*sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(5)/2)/(S(3)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**(S(3)/2)/sqrt(c*sin(a + b*x)), x), x, d**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))/(S(2)*b*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))) + d*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))/(b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))), x), x, EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))/(b*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(5)/2)), x), x, S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))/(S(3)*b*d**S(2)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))) + S(2)*sqrt(c*sin(a + b*x))/(S(3)*b*c*d*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(9)/2)), x), x, S(4)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))/(S(7)*b*d**S(4)*sqrt(c*sin(a + b*x))*sqrt(d*cos(a + b*x))) + S(2)*sqrt(c*sin(a + b*x))/(S(7)*b*c*d*(d*cos(a + b*x))**(S(7)/2)) + S(4)*sqrt(c*sin(a + b*x))/(S(7)*b*c*d**S(3)*(d*cos(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cos(a + b*x))/sqrt(c*sin(a + b*x)), x), x, sqrt(S(2))*sqrt(d)*ArcTan(-sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/(sqrt(d)*sqrt(c*sin(a + b*x))) + S(1))/(S(2)*b*sqrt(c)) - sqrt(S(2))*sqrt(d)*ArcTan(sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/(sqrt(d)*sqrt(c*sin(a + b*x))) + S(1))/(S(2)*b*sqrt(c)) - sqrt(S(2))*sqrt(d)*log(-sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/sqrt(c*sin(a + b*x)) + sqrt(d)*cot(a + b*x) + sqrt(d))/(S(4)*b*sqrt(c)) + sqrt(S(2))*sqrt(d)*log(sqrt(S(2))*sqrt(c)*sqrt(d*cos(a + b*x))/sqrt(c*sin(a + b*x)) + sqrt(d)*cot(a + b*x) + sqrt(d))/(S(4)*b*sqrt(c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(3)/2)), x), x, S(2)*sqrt(c*sin(a + b*x))/(b*c*d*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(7)/2)), x), x, S(2)*sqrt(c*sin(a + b*x))/(S(5)*b*c*d*(d*cos(a + b*x))**(S(5)/2)) + S(8)*sqrt(c*sin(a + b*x))/(S(5)*b*c*d**S(3)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(S(11)/2)), x), x, S(2)*sqrt(c*sin(a + b*x))/(S(9)*b*c*d*(d*cos(a + b*x))**(S(9)/2)) + S(16)*sqrt(c*sin(a + b*x))/(S(45)*b*c*d**S(3)*(d*cos(a + b*x))**(S(5)/2)) + S(64)*sqrt(c*sin(a + b*x))/(S(45)*b*c*d**S(5)*sqrt(d*cos(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(cos(a + b*x))/sqrt(sin(a + b*x)), x), x, sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(2)*b) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(2)*b) - sqrt(S(2))*log(cot(a + b*x) + S(1) - sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(4)*b) + sqrt(S(2))*log(cot(a + b*x) + S(1) + sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**(S(3)/2)/sin(a + b*x)**(S(3)/2), x), x, sqrt(S(2))*ArcTan(-sqrt(S(2))*sqrt(sin(a + b*x))/sqrt(cos(a + b*x)) + S(1))/(S(2)*b) - sqrt(S(2))*ArcTan(sqrt(S(2))*sqrt(sin(a + b*x))/sqrt(cos(a + b*x)) + S(1))/(S(2)*b) - sqrt(S(2))*log(-sqrt(S(2))*sqrt(sin(a + b*x))/sqrt(cos(a + b*x)) + tan(a + b*x) + S(1))/(S(4)*b) + sqrt(S(2))*log(sqrt(S(2))*sqrt(sin(a + b*x))/sqrt(cos(a + b*x)) + tan(a + b*x) + S(1))/(S(4)*b) - S(2)*sqrt(cos(a + b*x))/(b*sqrt(sin(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**(S(5)/2)/sin(a + b*x)**(S(5)/2), x), x, -sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(2)*b) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(2)*b) + sqrt(S(2))*log(cot(a + b*x) + S(1) - sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(4)*b) - sqrt(S(2))*log(cot(a + b*x) + S(1) + sqrt(S(2))*sqrt(cos(a + b*x))/sqrt(sin(a + b*x)))/(S(4)*b) - S(2)*cos(a + b*x)**(S(3)/2)/(S(3)*b*sin(a + b*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**(S(7)/2)/sin(a + b*x)**(S(7)/2), x), x, -sqrt(S(2))*ArcTan(-sqrt(S(2))*sqrt(sin(a + b*x))/sqrt(cos(a + b*x)) + S(1))/(S(2)*b) + sqrt(S(2))*ArcTan(sqrt(S(2))*sqrt(sin(a + b*x))/sqrt(cos(a + b*x)) + S(1))/(S(2)*b) + sqrt(S(2))*log(-sqrt(S(2))*sqrt(sin(a + b*x))/sqrt(cos(a + b*x)) + tan(a + b*x) + S(1))/(S(4)*b) - sqrt(S(2))*log(sqrt(S(2))*sqrt(sin(a + b*x))/sqrt(cos(a + b*x)) + tan(a + b*x) + S(1))/(S(4)*b) + S(2)*sqrt(cos(a + b*x))/(b*sqrt(sin(a + b*x))) - S(2)*cos(a + b*x)**(S(5)/2)/(S(5)*b*sin(a + b*x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(1)/3)*cos(e + f*x)**S(4), x), x, S(3)*(b*sin(e + f*x))**(S(4)/3)*Hypergeometric2F1(S(-3)/2, S(2)/3, S(5)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(4)*b*f*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(1)/3)*cos(e + f*x)**S(2), x), x, S(3)*(b*sin(e + f*x))**(S(4)/3)*Hypergeometric2F1(S(-1)/2, S(2)/3, S(5)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(4)*b*f*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(1)/3), x), x, S(3)*(b*sin(e + f*x))**(S(4)/3)*Hypergeometric2F1(S(1)/2, S(2)/3, S(5)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(4)*b*f*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(1)/3)*sec(e + f*x)**S(2), x), x, S(3)*(b*sin(e + f*x))**(S(4)/3)*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(2)/3, S(3)/2, S(5)/3, sin(e + f*x)**S(2))*sec(e + f*x)/(S(4)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(1)/3)*sec(e + f*x)**S(4), x), x, S(3)*(b*sin(e + f*x))**(S(4)/3)*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(2)/3, S(5)/2, S(5)/3, sin(e + f*x)**S(2))*sec(e + f*x)/(S(4)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(5)/3)*cos(e + f*x)**S(4), x), x, S(3)*(b*sin(e + f*x))**(S(8)/3)*Hypergeometric2F1(S(-3)/2, S(4)/3, S(7)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(8)*b*f*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(5)/3)*cos(e + f*x)**S(2), x), x, S(3)*(b*sin(e + f*x))**(S(8)/3)*Hypergeometric2F1(S(-1)/2, S(4)/3, S(7)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(8)*b*f*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(5)/3), x), x, S(3)*(b*sin(e + f*x))**(S(8)/3)*Hypergeometric2F1(S(1)/2, S(4)/3, S(7)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(8)*b*f*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(5)/3)*sec(e + f*x)**S(2), x), x, S(3)*(b*sin(e + f*x))**(S(8)/3)*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(4)/3, S(3)/2, S(7)/3, sin(e + f*x)**S(2))*sec(e + f*x)/(S(8)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(5)/3)*sec(e + f*x)**S(4), x), x, S(3)*(b*sin(e + f*x))**(S(8)/3)*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(4)/3, S(5)/2, S(7)/3, sin(e + f*x)**S(2))*sec(e + f*x)/(S(8)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(e + f*x)**S(4)/(b*sin(e + f*x))**(S(1)/3), x), x, S(3)*(b*sin(e + f*x))**(S(2)/3)*Hypergeometric2F1(S(-3)/2, S(1)/3, S(4)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(2)*b*f*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(e + f*x)**S(2)/(b*sin(e + f*x))**(S(1)/3), x), x, S(3)*(b*sin(e + f*x))**(S(2)/3)*Hypergeometric2F1(S(-1)/2, S(1)/3, S(4)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(2)*b*f*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(-1)/3), x), x, S(3)*(b*sin(e + f*x))**(S(2)/3)*Hypergeometric2F1(S(1)/3, S(1)/2, S(4)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(2)*b*f*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(e + f*x)**S(2)/(b*sin(e + f*x))**(S(1)/3), x), x, S(3)*(b*sin(e + f*x))**(S(2)/3)*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(1)/3, S(3)/2, S(4)/3, sin(e + f*x)**S(2))*sec(e + f*x)/(S(2)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(e + f*x)**S(4)/(b*sin(e + f*x))**(S(1)/3), x), x, S(3)*(b*sin(e + f*x))**(S(2)/3)*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(1)/3, S(5)/2, S(4)/3, sin(e + f*x)**S(2))*sec(e + f*x)/(S(2)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(e + f*x)**S(4)/(b*sin(e + f*x))**(S(5)/3), x), x, -S(3)*Hypergeometric2F1(S(-3)/2, S(-1)/3, S(2)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(2)*b*f*(b*sin(e + f*x))**(S(2)/3)*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(e + f*x)**S(2)/(b*sin(e + f*x))**(S(5)/3), x), x, -S(3)*Hypergeometric2F1(S(-1)/2, S(-1)/3, S(2)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(2)*b*f*(b*sin(e + f*x))**(S(2)/3)*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(-5)/3), x), x, -S(3)*Hypergeometric2F1(S(-1)/3, S(1)/2, S(2)/3, sin(e + f*x)**S(2))*cos(e + f*x)/(S(2)*b*f*(b*sin(e + f*x))**(S(2)/3)*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(e + f*x)**S(2)/(b*sin(e + f*x))**(S(5)/3), x), x, -S(3)*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(-1)/3, S(3)/2, S(2)/3, sin(e + f*x)**S(2))*sec(e + f*x)/(S(2)*b*f*(b*sin(e + f*x))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(e + f*x)**S(4)/(b*sin(e + f*x))**(S(5)/3), x), x, -S(3)*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(-1)/3, S(5)/2, S(2)/3, sin(e + f*x)**S(2))*sec(e + f*x)/(S(2)*b*f*(b*sin(e + f*x))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3), x), x, -sqrt(S(3))*ArcTan(sqrt(S(3))*(-S(2)*sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + S(1))/S(3))/(S(2)*b) - log(sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + S(1))/(S(2)*b) + log(sin(a + b*x)**(S(4)/3)/cos(a + b*x)**(S(4)/3) - sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + S(1))/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3), x), x, ArcTan(sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3))/b - ArcTan(-S(2)*sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3) + sqrt(S(3)))/(S(2)*b) + ArcTan(S(2)*sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3) + sqrt(S(3)))/(S(2)*b) + sqrt(S(3))*log(sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) - sqrt(S(3))*sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3) + S(1))/(S(4)*b) - sqrt(S(3))*log(sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + sqrt(S(3))*sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3) + S(1))/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(4)/3)/cos(a + b*x)**(S(4)/3), x), x, ArcTan(cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3))/b - ArcTan(sqrt(S(3)) - S(2)*cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3))/(S(2)*b) + ArcTan(sqrt(S(3)) + S(2)*cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3))/(S(2)*b) + sqrt(S(3))*log(S(1) - sqrt(S(3))*cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3) + cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/(S(4)*b) - sqrt(S(3))*log(S(1) + sqrt(S(3))*cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3) + cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/(S(4)*b) + S(3)*sin(a + b*x)**(S(1)/3)/(b*cos(a + b*x)**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(5)/3)/cos(a + b*x)**(S(5)/3), x), x, -sqrt(S(3))*ArcTan(sqrt(S(3))*(S(1) - S(2)*cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/S(3))/(S(2)*b) - log(S(1) + cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/(S(2)*b) + log(S(1) - cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3) + cos(a + b*x)**(S(4)/3)/sin(a + b*x)**(S(4)/3))/(S(4)*b) + S(3)*sin(a + b*x)**(S(2)/3)/(S(2)*b*cos(a + b*x)**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**(S(7)/3)/cos(a + b*x)**(S(7)/3), x), x, sqrt(S(3))*ArcTan(sqrt(S(3))*(-S(2)*sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + S(1))/S(3))/(S(2)*b) + log(sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + S(1))/(S(2)*b) - log(sin(a + b*x)**(S(4)/3)/cos(a + b*x)**(S(4)/3) - sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + S(1))/(S(4)*b) + S(3)*sin(a + b*x)**(S(4)/3)/(S(4)*b*cos(a + b*x)**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3), x), x, sqrt(S(3))*ArcTan(sqrt(S(3))*(S(1) - S(2)*cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/S(3))/(S(2)*b) + log(S(1) + cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/(S(2)*b) - log(S(1) - cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3) + cos(a + b*x)**(S(4)/3)/sin(a + b*x)**(S(4)/3))/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3), x), x, -ArcTan(cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3))/b + ArcTan(sqrt(S(3)) - S(2)*cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3))/(S(2)*b) - ArcTan(sqrt(S(3)) + S(2)*cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3))/(S(2)*b) - sqrt(S(3))*log(S(1) - sqrt(S(3))*cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3) + cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/(S(4)*b) + sqrt(S(3))*log(S(1) + sqrt(S(3))*cos(a + b*x)**(S(1)/3)/sin(a + b*x)**(S(1)/3) + cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**(S(4)/3)/sin(a + b*x)**(S(4)/3), x), x, -ArcTan(sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3))/b + ArcTan(-S(2)*sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3) + sqrt(S(3)))/(S(2)*b) - ArcTan(S(2)*sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3) + sqrt(S(3)))/(S(2)*b) - sqrt(S(3))*log(sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) - sqrt(S(3))*sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3) + S(1))/(S(4)*b) + sqrt(S(3))*log(sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + sqrt(S(3))*sin(a + b*x)**(S(1)/3)/cos(a + b*x)**(S(1)/3) + S(1))/(S(4)*b) - S(3)*cos(a + b*x)**(S(1)/3)/(b*sin(a + b*x)**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**(S(5)/3)/sin(a + b*x)**(S(5)/3), x), x, sqrt(S(3))*ArcTan(sqrt(S(3))*(-S(2)*sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + S(1))/S(3))/(S(2)*b) + log(sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + S(1))/(S(2)*b) - log(sin(a + b*x)**(S(4)/3)/cos(a + b*x)**(S(4)/3) - sin(a + b*x)**(S(2)/3)/cos(a + b*x)**(S(2)/3) + S(1))/(S(4)*b) - S(3)*cos(a + b*x)**(S(2)/3)/(S(2)*b*sin(a + b*x)**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**(S(7)/3)/sin(a + b*x)**(S(7)/3), x), x, -sqrt(S(3))*ArcTan(sqrt(S(3))*(S(1) - S(2)*cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/S(3))/(S(2)*b) - log(S(1) + cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3))/(S(2)*b) + log(S(1) - cos(a + b*x)**(S(2)/3)/sin(a + b*x)**(S(2)/3) + cos(a + b*x)**(S(4)/3)/sin(a + b*x)**(S(4)/3))/(S(4)*b) - S(3)*cos(a + b*x)**(S(4)/3)/(S(4)*b*sin(a + b*x)**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(x)**(S(2)/3)/sin(x)**(S(8)/3), x), x, -S(3)*cos(x)**(S(5)/3)/(S(5)*sin(x)**(S(5)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(x)**(S(2)/3)/cos(x)**(S(8)/3), x), x, S(3)*sin(x)**(S(5)/3)/(S(5)*cos(x)**(S(5)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**m*cos(e + f*x)**n, x), x, (cos(e + f*x)**S(2))**(-n/S(2) + S(1)/2)*Hypergeometric2F1(m/S(2) + S(1)/2, -n/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(e + f*x)**S(2))*sin(e + f*x)**(m + S(1))*cos(e + f*x)**(n + S(-1))/(f*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(e + f*x))**n*sin(e + f*x)**m, x), x, -(d*cos(e + f*x))**(n + S(1))*(sin(e + f*x)**S(2))**(-m/S(2) + S(1)/2)*Hypergeometric2F1(-m/S(2) + S(1)/2, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(e + f*x)**S(2))*sin(e + f*x)**(m + S(-1))/(d*f*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*cos(e + f*x)**n, x), x, (b*sin(e + f*x))**(m + S(1))*(cos(e + f*x)**S(2))**(-n/S(2) + S(1)/2)*Hypergeometric2F1(m/S(2) + S(1)/2, -n/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(e + f*x)**S(2))*cos(e + f*x)**(n + S(-1))/(b*f*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*(d*cos(e + f*x))**n, x), x, d*(b*sin(e + f*x))**(m + S(1))*(d*cos(e + f*x))**(n + S(-1))*(cos(e + f*x)**S(2))**(-n/S(2) + S(1)/2)*Hypergeometric2F1(m/S(2) + S(1)/2, -n/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(e + f*x)**S(2))/(b*f*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*cos(a + b*x)**S(5), x), x, (c*sin(a + b*x))**(m + S(1))/(b*c*(m + S(1))) - S(2)*(c*sin(a + b*x))**(m + S(3))/(b*c**S(3)*(m + S(3))) + (c*sin(a + b*x))**(m + S(5))/(b*c**S(5)*(m + S(5))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*cos(a + b*x)**S(3), x), x, (c*sin(a + b*x))**(m + S(1))/(b*c*(m + S(1))) - (c*sin(a + b*x))**(m + S(3))/(b*c**S(3)*(m + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*cos(a + b*x), x), x, (c*sin(a + b*x))**(m + S(1))/(b*c*(m + S(1))), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*sec(a + b*x), x), x, (c*sin(a + b*x))**(m + S(1))*Hypergeometric2F1(S(1), m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))/(b*c*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*sec(a + b*x)**S(3), x), x, (c*sin(a + b*x))**(m + S(1))*Hypergeometric2F1(S(2), m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))/(b*c*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*cos(a + b*x)**S(4), x), x, (c*sin(a + b*x))**(m + S(1))*Hypergeometric2F1(S(-3)/2, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))*cos(a + b*x)/(b*c*(m + S(1))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*cos(a + b*x)**S(2), x), x, (c*sin(a + b*x))**(m + S(1))*Hypergeometric2F1(S(-1)/2, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))*cos(a + b*x)/(b*c*(m + S(1))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m, x), x, (c*sin(a + b*x))**(m + S(1))*Hypergeometric2F1(S(1)/2, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))*cos(a + b*x)/(b*c*(m + S(1))*sqrt(cos(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*sec(a + b*x)**S(2), x), x, (c*sin(a + b*x))**(m + S(1))*sqrt(cos(a + b*x)**S(2))*Hypergeometric2F1(S(3)/2, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))*sec(a + b*x)/(b*c*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*sec(a + b*x)**S(4), x), x, (c*sin(a + b*x))**(m + S(1))*sqrt(cos(a + b*x)**S(2))*Hypergeometric2F1(S(5)/2, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))*sec(a + b*x)/(b*c*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*(d*cos(a + b*x))**(S(3)/2), x), x, d*(c*sin(a + b*x))**(m + S(1))*sqrt(d*cos(a + b*x))*Hypergeometric2F1(S(-1)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))/(b*c*(m + S(1))*(cos(a + b*x)**S(2))**(S(1)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*sqrt(d*cos(a + b*x)), x), x, d*(c*sin(a + b*x))**(m + S(1))*(cos(a + b*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(1)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))/(b*c*sqrt(d*cos(a + b*x))*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m/sqrt(d*cos(a + b*x)), x), x, d*(c*sin(a + b*x))**(m + S(1))*(cos(a + b*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(3)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))/(b*c*(d*cos(a + b*x))**(S(3)/2)*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m/(d*cos(a + b*x))**(S(3)/2), x), x, (c*sin(a + b*x))**(m + S(1))*(cos(a + b*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(5)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))/(b*c*d*sqrt(d*cos(a + b*x))*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m/(d*cos(a + b*x))**(S(5)/2), x), x, (c*sin(a + b*x))**(m + S(1))*(cos(a + b*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(7)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))/(b*c*d*(d*cos(a + b*x))**(S(3)/2)*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*sin(a + b*x)**S(5), x), x, -(d*cos(a + b*x))**(n + S(1))/(b*d*(n + S(1))) + S(2)*(d*cos(a + b*x))**(n + S(3))/(b*d**S(3)*(n + S(3))) - (d*cos(a + b*x))**(n + S(5))/(b*d**S(5)*(n + S(5))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*sin(a + b*x)**S(3), x), x, -(d*cos(a + b*x))**(n + S(1))/(b*d*(n + S(1))) + (d*cos(a + b*x))**(n + S(3))/(b*d**S(3)*(n + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*sin(a + b*x), x), x, -(d*cos(a + b*x))**(n + S(1))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*csc(a + b*x), x), x, -(d*cos(a + b*x))**(n + S(1))*Hypergeometric2F1(S(1), n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*csc(a + b*x)**S(3), x), x, -(d*cos(a + b*x))**(n + S(1))*Hypergeometric2F1(S(2), n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*csc(a + b*x)**S(5), x), x, -(d*cos(a + b*x))**(n + S(1))*Hypergeometric2F1(S(3), n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*sin(a + b*x)**S(4), x), x, -(d*cos(a + b*x))**(n + S(1))*Hypergeometric2F1(S(-3)/2, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))*sin(a + b*x)/(b*d*(n + S(1))*sqrt(sin(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*sin(a + b*x)**S(2), x), x, -(d*cos(a + b*x))**(n + S(1))*Hypergeometric2F1(S(-1)/2, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))*sin(a + b*x)/(b*d*(n + S(1))*sqrt(sin(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n, x), x, -(d*cos(a + b*x))**(n + S(1))*Hypergeometric2F1(S(1)/2, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))*sin(a + b*x)/(b*d*(n + S(1))*sqrt(sin(a + b*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*csc(a + b*x)**S(2), x), x, -(d*cos(a + b*x))**(n + S(1))*sqrt(sin(a + b*x)**S(2))*Hypergeometric2F1(S(3)/2, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))*csc(a + b*x)/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n*csc(a + b*x)**S(4), x), x, -(d*cos(a + b*x))**(n + S(1))*sqrt(sin(a + b*x)**S(2))*Hypergeometric2F1(S(5)/2, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))*csc(a + b*x)/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(5)/2)*(d*cos(a + b*x))**n, x), x, -c*(c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**(n + S(1))*Hypergeometric2F1(S(-3)/4, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))/(b*d*(n + S(1))*(sin(a + b*x)**S(2))**(S(3)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**(S(3)/2)*(d*cos(a + b*x))**n, x), x, -c*sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**(n + S(1))*Hypergeometric2F1(S(-1)/4, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))/(b*d*(n + S(1))*(sin(a + b*x)**S(2))**(S(1)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(c*sin(a + b*x))*(d*cos(a + b*x))**n, x), x, -c*(d*cos(a + b*x))**(n + S(1))*(sin(a + b*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(1)/4, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))/(b*d*sqrt(c*sin(a + b*x))*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n/sqrt(c*sin(a + b*x)), x), x, -c*(d*cos(a + b*x))**(n + S(1))*(sin(a + b*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(3)/4, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))/(b*d*(c*sin(a + b*x))**(S(3)/2)*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cos(a + b*x))**n/(c*sin(a + b*x))**(S(3)/2), x), x, -(d*cos(a + b*x))**(n + S(1))*(sin(a + b*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(5)/4, n/S(2) + S(1)/2, n/S(2) + S(3)/2, cos(a + b*x)**S(2))/(b*c*d*sqrt(c*sin(a + b*x))*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*sin(e + f*x)**S(7), x), x, S(2)*b**S(7)/(S(13)*f*(b*sec(e + f*x))**(S(13)/2)) - S(2)*b**S(5)/(S(3)*f*(b*sec(e + f*x))**(S(9)/2)) + S(6)*b**S(3)/(S(5)*f*(b*sec(e + f*x))**(S(5)/2)) - S(2)*b/(f*sqrt(b*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*sin(e + f*x)**S(5), x), x, -S(2)*b**S(5)/(S(9)*f*(b*sec(e + f*x))**(S(9)/2)) + S(4)*b**S(3)/(S(5)*f*(b*sec(e + f*x))**(S(5)/2)) - S(2)*b/(f*sqrt(b*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*sin(e + f*x)**S(3), x), x, S(2)*b**S(3)/(S(5)*f*(b*sec(e + f*x))**(S(5)/2)) - S(2)*b/(f*sqrt(b*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*sin(e + f*x), x), x, -S(2)*b/(f*sqrt(b*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*csc(e + f*x), x), x, sqrt(b)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/f - sqrt(b)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*csc(e + f*x)**S(3), x), x, S(3)*sqrt(b)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*f) - S(3)*sqrt(b)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*f) - (b*sec(e + f*x))**(S(3)/2)*cot(e + f*x)**S(2)/(S(2)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*csc(e + f*x)**S(5), x), x, S(21)*sqrt(b)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*f) - S(21)*sqrt(b)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*f) - S(7)*(b*sec(e + f*x))**(S(3)/2)*cot(e + f*x)**S(2)/(S(16)*b*f) - (b*sec(e + f*x))**(S(7)/2)*cot(e + f*x)**S(4)/(S(4)*b**S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*sin(e + f*x)**S(6), x), x, -S(2)*b*sin(e + f*x)**S(5)/(S(11)*f*sqrt(b*sec(e + f*x))) - S(20)*b*sin(e + f*x)**S(3)/(S(77)*f*sqrt(b*sec(e + f*x))) - S(40)*b*sin(e + f*x)/(S(77)*f*sqrt(b*sec(e + f*x))) + S(80)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(77)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*sin(e + f*x)**S(4), x), x, -S(2)*b*sin(e + f*x)**S(3)/(S(7)*f*sqrt(b*sec(e + f*x))) - S(4)*b*sin(e + f*x)/(S(7)*f*sqrt(b*sec(e + f*x))) + S(8)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(7)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*sin(e + f*x)**S(2), x), x, -S(2)*b*sin(e + f*x)/(S(3)*f*sqrt(b*sec(e + f*x))) + S(4)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x)), x), x, S(2)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*csc(e + f*x)**S(2), x), x, -b*csc(e + f*x)/(f*sqrt(b*sec(e + f*x))) + sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*csc(e + f*x)**S(4), x), x, -b*csc(e + f*x)**S(3)/(S(3)*f*sqrt(b*sec(e + f*x))) - S(5)*b*csc(e + f*x)/(S(6)*f*sqrt(b*sec(e + f*x))) + S(5)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(6)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*csc(e + f*x)**S(6), x), x, -b*csc(e + f*x)**S(5)/(S(5)*f*sqrt(b*sec(e + f*x))) - S(3)*b*csc(e + f*x)**S(3)/(S(10)*f*sqrt(b*sec(e + f*x))) - S(3)*b*csc(e + f*x)/(S(4)*f*sqrt(b*sec(e + f*x))) + S(3)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(4)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**S(7), x), x, S(2)*b**S(7)/(S(11)*f*(b*sec(e + f*x))**(S(11)/2)) - S(6)*b**S(5)/(S(7)*f*(b*sec(e + f*x))**(S(7)/2)) + S(2)*b**S(3)/(f*(b*sec(e + f*x))**(S(3)/2)) + S(2)*b*sqrt(b*sec(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**S(5), x), x, -S(2)*b**S(5)/(S(7)*f*(b*sec(e + f*x))**(S(7)/2)) + S(4)*b**S(3)/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)) + S(2)*b*sqrt(b*sec(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**S(3), x), x, S(2)*b**S(3)/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)) + S(2)*b*sqrt(b*sec(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*sin(e + f*x), x), x, S(2)*b*sqrt(b*sec(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*csc(e + f*x), x), x, -b**(S(3)/2)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/f - b**(S(3)/2)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/f + S(2)*b*sqrt(b*sec(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*csc(e + f*x)**S(3), x), x, -S(5)*b**(S(3)/2)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*f) - S(5)*b**(S(3)/2)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*f) + S(5)*b*sqrt(b*sec(e + f*x))/(S(2)*f) - (b*sec(e + f*x))**(S(5)/2)*cot(e + f*x)**S(2)/(S(2)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**S(6), x), x, S(20)*b**S(3)*sin(e + f*x)**S(3)/(S(9)*f*(b*sec(e + f*x))**(S(3)/2)) + S(8)*b**S(3)*sin(e + f*x)/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)) - S(16)*b**S(2)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(3)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))) + S(2)*b*sqrt(b*sec(e + f*x))*sin(e + f*x)**S(5)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**S(4), x), x, S(12)*b**S(3)*sin(e + f*x)/(S(5)*f*(b*sec(e + f*x))**(S(3)/2)) - S(24)*b**S(2)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(5)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))) + S(2)*b*sqrt(b*sec(e + f*x))*sin(e + f*x)**S(3)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**S(2), x), x, -S(4)*b**S(2)*EllipticE(e/S(2) + f*x/S(2), S(2))/(f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))) + S(2)*b*sqrt(b*sec(e + f*x))*sin(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2), x), x, -S(2)*b**S(2)*EllipticE(e/S(2) + f*x/S(2), S(2))/(f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))) + S(2)*b*sqrt(b*sec(e + f*x))*sin(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*csc(e + f*x)**S(2), x), x, -S(3)*b**S(2)*EllipticE(e/S(2) + f*x/S(2), S(2))/(f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))) + S(3)*b*sqrt(b*sec(e + f*x))*sin(e + f*x)/f - b*sqrt(b*sec(e + f*x))*csc(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*csc(e + f*x)**S(4), x), x, -S(7)*b**S(2)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(2)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))) + S(7)*b*sqrt(b*sec(e + f*x))*sin(e + f*x)/(S(2)*f) - b*sqrt(b*sec(e + f*x))*csc(e + f*x)**S(3)/(S(3)*f) - S(7)*b*sqrt(b*sec(e + f*x))*csc(e + f*x)/(S(6)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*sin(e + f*x)**S(7), x), x, S(2)*b**S(7)/(S(9)*f*(b*sec(e + f*x))**(S(9)/2)) - S(6)*b**S(5)/(S(5)*f*(b*sec(e + f*x))**(S(5)/2)) + S(6)*b**S(3)/(f*sqrt(b*sec(e + f*x))) + S(2)*b*(b*sec(e + f*x))**(S(3)/2)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*sin(e + f*x)**S(5), x), x, -S(2)*b**S(5)/(S(5)*f*(b*sec(e + f*x))**(S(5)/2)) + S(4)*b**S(3)/(f*sqrt(b*sec(e + f*x))) + S(2)*b*(b*sec(e + f*x))**(S(3)/2)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*sin(e + f*x)**S(3), x), x, S(2)*b**S(3)/(f*sqrt(b*sec(e + f*x))) + S(2)*b*(b*sec(e + f*x))**(S(3)/2)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*sin(e + f*x), x), x, S(2)*b*(b*sec(e + f*x))**(S(3)/2)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*csc(e + f*x), x), x, b**(S(5)/2)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/f - b**(S(5)/2)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/f + S(2)*b*(b*sec(e + f*x))**(S(3)/2)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*csc(e + f*x)**S(3), x), x, S(7)*b**(S(5)/2)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*f) - S(7)*b**(S(5)/2)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*f) + S(7)*b*(b*sec(e + f*x))**(S(3)/2)/(S(6)*f) - (b*sec(e + f*x))**(S(7)/2)*cot(e + f*x)**S(2)/(S(2)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*csc(e + f*x)**S(5), x), x, S(77)*b**(S(5)/2)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*f) - S(77)*b**(S(5)/2)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*f) + S(77)*b*(b*sec(e + f*x))**(S(3)/2)/(S(48)*f) - S(11)*(b*sec(e + f*x))**(S(7)/2)*cot(e + f*x)**S(2)/(S(16)*b*f) - (b*sec(e + f*x))**(S(11)/2)*cot(e + f*x)**S(4)/(S(4)*b**S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*sin(e + f*x)**S(6), x), x, S(20)*b**S(3)*sin(e + f*x)**S(3)/(S(21)*f*sqrt(b*sec(e + f*x))) + S(40)*b**S(3)*sin(e + f*x)/(S(21)*f*sqrt(b*sec(e + f*x))) - S(80)*b**S(2)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(21)*f) + S(2)*b*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**S(5)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*sin(e + f*x)**S(4), x), x, S(4)*b**S(3)*sin(e + f*x)/(S(3)*f*sqrt(b*sec(e + f*x))) - S(8)*b**S(2)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(3)*f) + S(2)*b*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**S(3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*sin(e + f*x)**S(2), x), x, -S(4)*b**S(2)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(3)*f) + S(2)*b*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2), x), x, S(2)*b**S(2)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(3)*f) + S(2)*b*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*csc(e + f*x)**S(2), x), x, S(5)*b**S(2)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(3)*f) + S(5)*b*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)/(S(3)*f) - b*(b*sec(e + f*x))**(S(3)/2)*csc(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(5)/2)*csc(e + f*x)**S(4), x), x, S(5)*b**S(2)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(2)*f) + S(5)*b*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)/(S(2)*f) - b*(b*sec(e + f*x))**(S(3)/2)*csc(e + f*x)**S(3)/(S(3)*f) - S(3)*b*(b*sec(e + f*x))**(S(3)/2)*csc(e + f*x)/(S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(7)/sqrt(b*sec(e + f*x)), x), x, S(2)*b**S(7)/(S(15)*f*(b*sec(e + f*x))**(S(15)/2)) - S(6)*b**S(5)/(S(11)*f*(b*sec(e + f*x))**(S(11)/2)) + S(6)*b**S(3)/(S(7)*f*(b*sec(e + f*x))**(S(7)/2)) - S(2)*b/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(5)/sqrt(b*sec(e + f*x)), x), x, -S(2)*b**S(5)/(S(11)*f*(b*sec(e + f*x))**(S(11)/2)) + S(4)*b**S(3)/(S(7)*f*(b*sec(e + f*x))**(S(7)/2)) - S(2)*b/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(3)/sqrt(b*sec(e + f*x)), x), x, S(2)*b**S(3)/(S(7)*f*(b*sec(e + f*x))**(S(7)/2)) - S(2)*b/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)/sqrt(b*sec(e + f*x)), x), x, -S(2)*b/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)/sqrt(b*sec(e + f*x)), x), x, -ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(sqrt(b)*f) - atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(sqrt(b)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(3)/sqrt(b*sec(e + f*x)), x), x, -sqrt(b*sec(e + f*x))*cot(e + f*x)**S(2)/(S(2)*b*f) - ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*sqrt(b)*f) - atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*sqrt(b)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(5)/sqrt(b*sec(e + f*x)), x), x, -S(5)*sqrt(b*sec(e + f*x))*cot(e + f*x)**S(2)/(S(16)*b*f) - (b*sec(e + f*x))**(S(5)/2)*cot(e + f*x)**S(4)/(S(4)*b**S(3)*f) - S(5)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*sqrt(b)*f) - S(5)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*sqrt(b)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(6)/sqrt(b*sec(e + f*x)), x), x, -S(2)*b*sin(e + f*x)**S(5)/(S(13)*f*(b*sec(e + f*x))**(S(3)/2)) - S(20)*b*sin(e + f*x)**S(3)/(S(117)*f*(b*sec(e + f*x))**(S(3)/2)) - S(8)*b*sin(e + f*x)/(S(39)*f*(b*sec(e + f*x))**(S(3)/2)) + S(16)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(39)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(4)/sqrt(b*sec(e + f*x)), x), x, -S(2)*b*sin(e + f*x)**S(3)/(S(9)*f*(b*sec(e + f*x))**(S(3)/2)) - S(4)*b*sin(e + f*x)/(S(15)*f*(b*sec(e + f*x))**(S(3)/2)) + S(8)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(15)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(2)/sqrt(b*sec(e + f*x)), x), x, -S(2)*b*sin(e + f*x)/(S(5)*f*(b*sec(e + f*x))**(S(3)/2)) + S(4)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(5)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(b*sec(e + f*x)), x), x, S(2)*EllipticE(e/S(2) + f*x/S(2), S(2))/(f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(2)/sqrt(b*sec(e + f*x)), x), x, -b*csc(e + f*x)/(f*(b*sec(e + f*x))**(S(3)/2)) - EllipticE(e/S(2) + f*x/S(2), S(2))/(f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(4)/sqrt(b*sec(e + f*x)), x), x, -b*csc(e + f*x)**S(3)/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)) - b*csc(e + f*x)/(S(2)*f*(b*sec(e + f*x))**(S(3)/2)) - EllipticE(e/S(2) + f*x/S(2), S(2))/(S(2)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(6)/sqrt(b*sec(e + f*x)), x), x, -b*csc(e + f*x)**S(5)/(S(5)*f*(b*sec(e + f*x))**(S(3)/2)) - S(7)*b*csc(e + f*x)**S(3)/(S(30)*f*(b*sec(e + f*x))**(S(3)/2)) - S(7)*b*csc(e + f*x)/(S(20)*f*(b*sec(e + f*x))**(S(3)/2)) - S(7)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(20)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(7)/(b*sec(e + f*x))**(S(3)/2), x), x, S(2)*b**S(7)/(S(17)*f*(b*sec(e + f*x))**(S(17)/2)) - S(6)*b**S(5)/(S(13)*f*(b*sec(e + f*x))**(S(13)/2)) + S(2)*b**S(3)/(S(3)*f*(b*sec(e + f*x))**(S(9)/2)) - S(2)*b/(S(5)*f*(b*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(5)/(b*sec(e + f*x))**(S(3)/2), x), x, -S(2)*b**S(5)/(S(13)*f*(b*sec(e + f*x))**(S(13)/2)) + S(4)*b**S(3)/(S(9)*f*(b*sec(e + f*x))**(S(9)/2)) - S(2)*b/(S(5)*f*(b*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(3)/(b*sec(e + f*x))**(S(3)/2), x), x, S(2)*b**S(3)/(S(9)*f*(b*sec(e + f*x))**(S(9)/2)) - S(2)*b/(S(5)*f*(b*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)/(b*sec(e + f*x))**(S(3)/2), x), x, -S(2)*b/(S(5)*f*(b*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)/(b*sec(e + f*x))**(S(3)/2), x), x, S(2)/(b*f*sqrt(b*sec(e + f*x))) + ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(b**(S(3)/2)*f) - atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(b**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(3)/(b*sec(e + f*x))**(S(3)/2), x), x, -(b*sec(e + f*x))**(S(3)/2)*cot(e + f*x)**S(2)/(S(2)*b**S(3)*f) - ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*b**(S(3)/2)*f) + atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*b**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(5)/(b*sec(e + f*x))**(S(3)/2), x), x, -(b*sec(e + f*x))**(S(3)/2)*cot(e + f*x)**S(4)/(S(4)*b**S(3)*f) - S(3)*(b*sec(e + f*x))**(S(3)/2)*cot(e + f*x)**S(2)/(S(16)*b**S(3)*f) - S(3)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*b**(S(3)/2)*f) + S(3)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*b**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(4)/(b*sec(e + f*x))**(S(3)/2), x), x, -S(2)*b*sin(e + f*x)**S(3)/(S(11)*f*(b*sec(e + f*x))**(S(5)/2)) - S(12)*b*sin(e + f*x)/(S(77)*f*(b*sec(e + f*x))**(S(5)/2)) + S(8)*sin(e + f*x)/(S(77)*b*f*sqrt(b*sec(e + f*x))) + S(8)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(77)*b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(2)/(b*sec(e + f*x))**(S(3)/2), x), x, -S(2)*b*sin(e + f*x)/(S(7)*f*(b*sec(e + f*x))**(S(5)/2)) + S(4)*sin(e + f*x)/(S(21)*b*f*sqrt(b*sec(e + f*x))) + S(4)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(21)*b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(-3)/2), x), x, S(2)*sin(e + f*x)/(S(3)*b*f*sqrt(b*sec(e + f*x))) + S(2)*sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(3)*b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(2)/(b*sec(e + f*x))**(S(3)/2), x), x, -csc(e + f*x)/(b*f*sqrt(b*sec(e + f*x))) - sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(4)/(b*sec(e + f*x))**(S(3)/2), x), x, -csc(e + f*x)**S(3)/(S(3)*b*f*sqrt(b*sec(e + f*x))) + csc(e + f*x)/(S(6)*b*f*sqrt(b*sec(e + f*x))) - sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(6)*b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(6)/(b*sec(e + f*x))**(S(3)/2), x), x, -csc(e + f*x)**S(5)/(S(5)*b*f*sqrt(b*sec(e + f*x))) + csc(e + f*x)**S(3)/(S(30)*b*f*sqrt(b*sec(e + f*x))) + csc(e + f*x)/(S(12)*b*f*sqrt(b*sec(e + f*x))) - sqrt(b*sec(e + f*x))*EllipticF(e/S(2) + f*x/S(2), S(2))*sqrt(cos(e + f*x))/(S(12)*b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(7)/(b*sec(e + f*x))**(S(5)/2), x), x, S(2)*b**S(7)/(S(19)*f*(b*sec(e + f*x))**(S(19)/2)) - S(2)*b**S(5)/(S(5)*f*(b*sec(e + f*x))**(S(15)/2)) + S(6)*b**S(3)/(S(11)*f*(b*sec(e + f*x))**(S(11)/2)) - S(2)*b/(S(7)*f*(b*sec(e + f*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(5)/(b*sec(e + f*x))**(S(5)/2), x), x, -S(2)*b**S(5)/(S(15)*f*(b*sec(e + f*x))**(S(15)/2)) + S(4)*b**S(3)/(S(11)*f*(b*sec(e + f*x))**(S(11)/2)) - S(2)*b/(S(7)*f*(b*sec(e + f*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(3)/(b*sec(e + f*x))**(S(5)/2), x), x, S(2)*b**S(3)/(S(11)*f*(b*sec(e + f*x))**(S(11)/2)) - S(2)*b/(S(7)*f*(b*sec(e + f*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)/(b*sec(e + f*x))**(S(5)/2), x), x, -S(2)*b/(S(7)*f*(b*sec(e + f*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)/(b*sec(e + f*x))**(S(5)/2), x), x, S(2)/(S(3)*b*f*(b*sec(e + f*x))**(S(3)/2)) - ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(b**(S(5)/2)*f) - atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(b**(S(5)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(3)/(b*sec(e + f*x))**(S(5)/2), x), x, -sqrt(b*sec(e + f*x))*cot(e + f*x)**S(2)/(S(2)*b**S(3)*f) + S(3)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*b**(S(5)/2)*f) + S(3)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(4)*b**(S(5)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(5)/(b*sec(e + f*x))**(S(5)/2), x), x, -sqrt(b*sec(e + f*x))*cot(e + f*x)**S(4)/(S(4)*b**S(3)*f) - sqrt(b*sec(e + f*x))*cot(e + f*x)**S(2)/(S(16)*b**S(3)*f) + S(3)*ArcTan(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*b**(S(5)/2)*f) + S(3)*atanh(sqrt(b*sec(e + f*x))/sqrt(b))/(S(32)*b**(S(5)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(4)/(b*sec(e + f*x))**(S(5)/2), x), x, -S(2)*b*sin(e + f*x)**S(3)/(S(13)*f*(b*sec(e + f*x))**(S(7)/2)) - S(4)*b*sin(e + f*x)/(S(39)*f*(b*sec(e + f*x))**(S(7)/2)) + S(8)*sin(e + f*x)/(S(195)*b*f*(b*sec(e + f*x))**(S(3)/2)) + S(8)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(65)*b**S(2)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(2)/(b*sec(e + f*x))**(S(5)/2), x), x, -S(2)*b*sin(e + f*x)/(S(9)*f*(b*sec(e + f*x))**(S(7)/2)) + S(4)*sin(e + f*x)/(S(45)*b*f*(b*sec(e + f*x))**(S(3)/2)) + S(4)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(15)*b**S(2)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(-5)/2), x), x, S(2)*sin(e + f*x)/(S(5)*b*f*(b*sec(e + f*x))**(S(3)/2)) + S(6)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(5)*b**S(2)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(2)/(b*sec(e + f*x))**(S(5)/2), x), x, -csc(e + f*x)/(b*f*(b*sec(e + f*x))**(S(3)/2)) - S(3)*EllipticE(e/S(2) + f*x/S(2), S(2))/(b**S(2)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(4)/(b*sec(e + f*x))**(S(5)/2), x), x, -csc(e + f*x)**S(3)/(S(3)*b*f*(b*sec(e + f*x))**(S(3)/2)) + csc(e + f*x)/(S(2)*b*f*(b*sec(e + f*x))**(S(3)/2)) + EllipticE(e/S(2) + f*x/S(2), S(2))/(S(2)*b**S(2)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(6)/(b*sec(e + f*x))**(S(5)/2), x), x, -csc(e + f*x)**S(5)/(S(5)*b*f*(b*sec(e + f*x))**(S(3)/2)) + csc(e + f*x)**S(3)/(S(10)*b*f*(b*sec(e + f*x))**(S(3)/2)) + S(3)*csc(e + f*x)/(S(20)*b*f*(b*sec(e + f*x))**(S(3)/2)) + S(3)*EllipticE(e/S(2) + f*x/S(2), S(2))/(S(20)*b**S(2)*f*sqrt(b*sec(e + f*x))*sqrt(cos(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**(S(9)/2)/sqrt(b*sec(e + f*x)), x), x, -b*sin(e + f*x)**(S(7)/2)/(S(5)*f*(b*sec(e + f*x))**(S(3)/2)) - S(7)*b*sin(e + f*x)**(S(3)/2)/(S(30)*f*(b*sec(e + f*x))**(S(3)/2)) + S(7)*sqrt(b*sec(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(e + f*x))*cos(e + f*x)/(S(20)*b*f*sqrt(sin(S(2)*e + S(2)*f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**(S(5)/2)/sqrt(b*sec(e + f*x)), x), x, -b*sin(e + f*x)**(S(3)/2)/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)) + sqrt(b*sec(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(e + f*x))*cos(e + f*x)/(S(2)*b*f*sqrt(sin(S(2)*e + S(2)*f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(sin(e + f*x))/sqrt(b*sec(e + f*x)), x), x, sqrt(b*sec(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(e + f*x))*cos(e + f*x)/(b*f*sqrt(sin(S(2)*e + S(2)*f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*sec(e + f*x))*sin(e + f*x)**(S(3)/2)), x), x, -S(2)*b/(f*(b*sec(e + f*x))**(S(3)/2)*sqrt(sin(e + f*x))) - S(2)*sqrt(b*sec(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(e + f*x))*cos(e + f*x)/(b*f*sqrt(sin(S(2)*e + S(2)*f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*sec(e + f*x))*sin(e + f*x)**(S(7)/2)), x), x, -S(4)*b/(S(5)*f*(b*sec(e + f*x))**(S(3)/2)*sqrt(sin(e + f*x))) - S(2)*b/(S(5)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(5)/2)) - S(4)*sqrt(b*sec(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(e + f*x))*cos(e + f*x)/(S(5)*b*f*sqrt(sin(S(2)*e + S(2)*f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**(S(3)/2)/sqrt(b*sec(e + f*x)), x), x, -b*sqrt(sin(e + f*x))/(S(2)*f*(b*sec(e + f*x))**(S(3)/2)) + sqrt(S(2))*sqrt(cos(e + f*x)/b)*sqrt(b*sec(e + f*x))*ArcTan(-sqrt(S(2))*sqrt(b)*sqrt(cos(e + f*x)/b)/sqrt(sin(e + f*x)) + S(1))/(S(8)*sqrt(b)*f) - sqrt(S(2))*sqrt(cos(e + f*x)/b)*sqrt(b*sec(e + f*x))*ArcTan(sqrt(S(2))*sqrt(b)*sqrt(cos(e + f*x)/b)/sqrt(sin(e + f*x)) + S(1))/(S(8)*sqrt(b)*f) - sqrt(S(2))*sqrt(cos(e + f*x)/b)*sqrt(b*sec(e + f*x))*log(-sqrt(S(2))*sqrt(b)*sqrt(cos(e + f*x)/b)/sqrt(sin(e + f*x)) + cot(e + f*x) + S(1))/(S(16)*sqrt(b)*f) + sqrt(S(2))*sqrt(cos(e + f*x)/b)*sqrt(b*sec(e + f*x))*log(sqrt(S(2))*sqrt(b)*sqrt(cos(e + f*x)/b)/sqrt(sin(e + f*x)) + cot(e + f*x) + S(1))/(S(16)*sqrt(b)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*sec(e + f*x))*sqrt(sin(e + f*x))), x), x, sqrt(S(2))*sqrt(cos(e + f*x)/b)*sqrt(b*sec(e + f*x))*ArcTan(-sqrt(S(2))*sqrt(b)*sqrt(cos(e + f*x)/b)/sqrt(sin(e + f*x)) + S(1))/(S(2)*sqrt(b)*f) - sqrt(S(2))*sqrt(cos(e + f*x)/b)*sqrt(b*sec(e + f*x))*ArcTan(sqrt(S(2))*sqrt(b)*sqrt(cos(e + f*x)/b)/sqrt(sin(e + f*x)) + S(1))/(S(2)*sqrt(b)*f) - sqrt(S(2))*sqrt(cos(e + f*x)/b)*sqrt(b*sec(e + f*x))*log(-sqrt(S(2))*sqrt(b)*sqrt(cos(e + f*x)/b)/sqrt(sin(e + f*x)) + cot(e + f*x) + S(1))/(S(4)*sqrt(b)*f) + sqrt(S(2))*sqrt(cos(e + f*x)/b)*sqrt(b*sec(e + f*x))*log(sqrt(S(2))*sqrt(b)*sqrt(cos(e + f*x)/b)/sqrt(sin(e + f*x)) + cot(e + f*x) + S(1))/(S(4)*sqrt(b)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*sec(e + f*x))*sin(e + f*x)**(S(5)/2)), x), x, -S(2)*b/(S(3)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*sec(e + f*x))*sin(e + f*x)**(S(9)/2)), x), x, -S(8)*b/(S(21)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(3)/2)) - S(2)*b/(S(7)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*sec(e + f*x))*sin(e + f*x)**(S(13)/2)), x), x, -S(64)*b/(S(231)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(3)/2)) - S(16)*b/(S(77)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(7)/2)) - S(2)*b/(S(11)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(11)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*sec(e + f*x))*sin(e + f*x)**(S(17)/2)), x), x, -S(256)*b/(S(1155)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(3)/2)) - S(64)*b/(S(385)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(7)/2)) - S(8)*b/(S(55)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(11)/2)) - S(2)*b/(S(15)*f*(b*sec(e + f*x))**(S(3)/2)*sin(e + f*x)**(S(15)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*(d*sec(a + b*x))**(S(5)/2), x), x, d**S(2)*(c*sin(a + b*x))**(m + S(1))*sqrt(d*sec(a + b*x))*(cos(a + b*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(7)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))*sec(a + b*x)/(b*c*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*(d*sec(a + b*x))**(S(3)/2), x), x, d*(c*sin(a + b*x))**(m + S(1))*sqrt(d*sec(a + b*x))*(cos(a + b*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(5)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))/(b*c*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m*sqrt(d*sec(a + b*x)), x), x, (c*sin(a + b*x))**(m + S(1))*sqrt(d*sec(a + b*x))*(cos(a + b*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(3)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))*sec(a + b*x)/(b*c*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m/sqrt(d*sec(a + b*x)), x), x, (c*sin(a + b*x))**(m + S(1))*sqrt(d*sec(a + b*x))*(cos(a + b*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(1)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))/(b*c*d*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c*sin(a + b*x))**m/(d*sec(a + b*x))**(S(3)/2), x), x, (c*sin(a + b*x))**(m + S(1))*sqrt(d*sec(a + b*x))*Hypergeometric2F1(S(-1)/4, m/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(a + b*x)**S(2))*cos(a + b*x)/(b*c*d**S(2)*(m + S(1))*(cos(a + b*x)**S(2))**(S(1)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**m*sec(e + f*x)**n, x), x, (cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(m/S(2) + S(1)/2, n/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(e + f*x)**S(2))*sin(e + f*x)**(m + S(1))*sec(e + f*x)**(n + S(1))/(f*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(e + f*x))**m*sec(e + f*x)**n, x), x, (a*sin(e + f*x))**(m + S(1))*(cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(m/S(2) + S(1)/2, n/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(e + f*x)**S(2))*sec(e + f*x)**(n + S(1))/(a*f*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*sin(e + f*x)**m, x), x, -b*(b*sec(e + f*x))**(n + S(-1))*(sin(e + f*x)**S(2))**(-m/S(2) + S(1)/2)*Hypergeometric2F1(-m/S(2) + S(1)/2, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(e + f*x)**S(2))*sin(e + f*x)**(m + S(-1))/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((b*sec(e + f*x))**n*sin(e + f*x)**m, x), x, -(b*sec(e + f*x))**n*(sin(e + f*x)**S(2))**(-m/S(2) + S(1)/2)*Hypergeometric2F1(-m/S(2) + S(1)/2, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(e + f*x)**S(2))*sin(e + f*x)**(m + S(-1))*cos(e + f*x)/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(e + f*x))**m*(b*sec(e + f*x))**n, x), x, (a*sin(e + f*x))**(m + S(1))*(b*sec(e + f*x))**(n + S(1))*(cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(m/S(2) + S(1)/2, n/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(e + f*x)**S(2))/(a*b*f*(m + S(1))), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((a*sin(e + f*x))**m*(b*sec(e + f*x))**n, x), x, (a*sin(e + f*x))**(m + S(1))*(b*sec(e + f*x))**n*(cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(m/S(2) + S(1)/2, n/S(2) + S(1)/2, m/S(2) + S(3)/2, sin(e + f*x)**S(2))*sec(e + f*x)/(a*f*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*sin(e + f*x)**S(5), x), x, -b**S(5)*(b*sec(e + f*x))**(n + S(-5))/(f*(-n + S(5))) + S(2)*b**S(3)*(b*sec(e + f*x))**(n + S(-3))/(f*(-n + S(3))) - b*(b*sec(e + f*x))**(n + S(-1))/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*sin(e + f*x)**S(3), x), x, b**S(3)*(b*sec(e + f*x))**(n + S(-3))/(f*(-n + S(3))) - b*(b*sec(e + f*x))**(n + S(-1))/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*sin(e + f*x), x), x, -b*(b*sec(e + f*x))**(n + S(-1))/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*csc(e + f*x), x), x, -(b*sec(e + f*x))**(n + S(1))*Hypergeometric2F1(S(1), n/S(2) + S(1)/2, n/S(2) + S(3)/2, sec(e + f*x)**S(2))/(b*f*(n + S(1))), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*csc(e + f*x)**S(3), x), x, (b*sec(e + f*x))**(n + S(3))*Hypergeometric2F1(S(2), n/S(2) + S(3)/2, n/S(2) + S(5)/2, sec(e + f*x)**S(2))/(b**S(3)*f*(n + S(3))), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*sin(e + f*x)**S(6), x), x, -(b*sec(e + f*x))**n*Hypergeometric2F1(S(-5)/2, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(e + f*x)**S(2))*sin(e + f*x)*cos(e + f*x)/(f*(-n + S(1))*sqrt(sin(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*sin(e + f*x)**S(4), x), x, -(b*sec(e + f*x))**n*Hypergeometric2F1(S(-3)/2, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(e + f*x)**S(2))*sin(e + f*x)*cos(e + f*x)/(f*(-n + S(1))*sqrt(sin(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*sin(e + f*x)**S(2), x), x, -(b*sec(e + f*x))**n*Hypergeometric2F1(S(-1)/2, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(e + f*x)**S(2))*sin(e + f*x)*cos(e + f*x)/(f*(-n + S(1))*sqrt(sin(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n, x), x, -b*(b*sec(e + f*x))**(n + S(-1))*Hypergeometric2F1(S(1)/2, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(e + f*x)**S(2))*sin(e + f*x)/(f*(-n + S(1))*sqrt(sin(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*csc(e + f*x)**S(2), x), x, -(b*sec(e + f*x))**n*sqrt(sin(e + f*x)**S(2))*Hypergeometric2F1(S(3)/2, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(e + f*x)**S(2))*cot(e + f*x)/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**n*csc(e + f*x)**S(4), x), x, -(b*sec(e + f*x))**n*sqrt(sin(e + f*x)**S(2))*Hypergeometric2F1(S(5)/2, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(e + f*x)**S(2))*cot(e + f*x)/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(a + b*x))**n*(c*sin(a + b*x))**(S(3)/2), x), x, -c*(b*sec(a + b*x))**n*sqrt(c*sin(a + b*x))*Hypergeometric2F1(S(-1)/4, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(a + b*x)**S(2))*cos(a + b*x)/(b*(-n + S(1))*(sin(a + b*x)**S(2))**(S(1)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(a + b*x))**n*sqrt(c*sin(a + b*x)), x), x, -c*(b*sec(a + b*x))**n*(sin(a + b*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(1)/4, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(a + b*x)**S(2))*cos(a + b*x)/(b*sqrt(c*sin(a + b*x))*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(a + b*x))**n/sqrt(c*sin(a + b*x)), x), x, -c*(b*sec(a + b*x))**n*(sin(a + b*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(3)/4, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(a + b*x)**S(2))*cos(a + b*x)/(b*(c*sin(a + b*x))**(S(3)/2)*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(a + b*x))**n/(c*sin(a + b*x))**(S(3)/2), x), x, -(b*sec(a + b*x))**n*(sin(a + b*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(5)/4, -n/S(2) + S(1)/2, -n/S(2) + S(3)/2, cos(a + b*x)**S(2))*cos(a + b*x)/(b*c*sqrt(c*sin(a + b*x))*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*csc(e + f*x))*sin(e + f*x)**S(4), x), x, -S(2)*d**S(3)*cos(e + f*x)/(S(7)*f*(d*csc(e + f*x))**(S(5)/2)) - S(10)*d*cos(e + f*x)/(S(21)*f*sqrt(d*csc(e + f*x))) + S(10)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(21)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*csc(e + f*x))*sin(e + f*x)**S(3), x), x, -S(2)*d**S(2)*cos(e + f*x)/(S(5)*f*(d*csc(e + f*x))**(S(3)/2)) + S(6)*d*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*csc(e + f*x))*sin(e + f*x)**S(2), x), x, -S(2)*d*cos(e + f*x)/(S(3)*f*sqrt(d*csc(e + f*x))) + S(2)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*csc(e + f*x))*sin(e + f*x), x), x, S(2)*d*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*csc(e + f*x)), x), x, S(2)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*csc(e + f*x))*csc(e + f*x), x), x, -S(2)*d*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))) - S(2)*sqrt(d*csc(e + f*x))*cos(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*csc(e + f*x))*csc(e + f*x)**S(2), x), x, S(2)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*f) - S(2)*(d*csc(e + f*x))**(S(3)/2)*cos(e + f*x)/(S(3)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*csc(e + f*x))*csc(e + f*x)**S(3), x), x, -S(6)*d*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))) - S(6)*sqrt(d*csc(e + f*x))*cos(e + f*x)/(S(5)*f) - S(2)*(d*csc(e + f*x))**(S(5)/2)*cos(e + f*x)/(S(5)*d**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*csc(e + f*x))**(S(3)/2)*sin(e + f*x)**S(5), x), x, -S(2)*d**S(4)*cos(e + f*x)/(S(7)*f*(d*csc(e + f*x))**(S(5)/2)) - S(10)*d**S(2)*cos(e + f*x)/(S(21)*f*sqrt(d*csc(e + f*x))) + S(10)*d*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(21)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*csc(e + f*x))**(S(3)/2)*sin(e + f*x)**S(4), x), x, -S(2)*d**S(3)*cos(e + f*x)/(S(5)*f*(d*csc(e + f*x))**(S(3)/2)) + S(6)*d**S(2)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*csc(e + f*x))**(S(3)/2)*sin(e + f*x)**S(3), x), x, -S(2)*d**S(2)*cos(e + f*x)/(S(3)*f*sqrt(d*csc(e + f*x))) + S(2)*d*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*csc(e + f*x))**(S(3)/2)*sin(e + f*x)**S(2), x), x, S(2)*d**S(2)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*csc(e + f*x))**(S(3)/2)*sin(e + f*x), x), x, S(2)*d*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*csc(e + f*x))**(S(3)/2), x), x, -S(2)*d**S(2)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))) - S(2)*d*sqrt(d*csc(e + f*x))*cos(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*csc(e + f*x))**(S(3)/2)*csc(e + f*x), x), x, S(2)*d*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*f) - S(2)*(d*csc(e + f*x))**(S(3)/2)*cos(e + f*x)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*csc(e + f*x))**(S(3)/2)*csc(e + f*x)**S(2), x), x, -S(6)*d**S(2)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))) - S(6)*d*sqrt(d*csc(e + f*x))*cos(e + f*x)/(S(5)*f) - S(2)*(d*csc(e + f*x))**(S(5)/2)*cos(e + f*x)/(S(5)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(3)/sqrt(d*csc(e + f*x)), x), x, -S(2)*d**S(2)*cos(e + f*x)/(S(7)*f*(d*csc(e + f*x))**(S(5)/2)) - S(10)*cos(e + f*x)/(S(21)*f*sqrt(d*csc(e + f*x))) + S(10)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(21)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(2)/sqrt(d*csc(e + f*x)), x), x, -S(2)*d*cos(e + f*x)/(S(5)*f*(d*csc(e + f*x))**(S(3)/2)) + S(6)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)/sqrt(d*csc(e + f*x)), x), x, -S(2)*cos(e + f*x)/(S(3)*f*sqrt(d*csc(e + f*x))) + S(2)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(d*csc(e + f*x)), x), x, S(2)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)/sqrt(d*csc(e + f*x)), x), x, S(2)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(2)/sqrt(d*csc(e + f*x)), x), x, -S(2)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))) - S(2)*sqrt(d*csc(e + f*x))*cos(e + f*x)/(d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(3)/sqrt(d*csc(e + f*x)), x), x, S(2)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*d*f) - S(2)*(d*csc(e + f*x))**(S(3)/2)*cos(e + f*x)/(S(3)*d**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)**S(2)/(d*csc(e + f*x))**(S(3)/2), x), x, -S(2)*d*cos(e + f*x)/(S(7)*f*(d*csc(e + f*x))**(S(5)/2)) - S(10)*cos(e + f*x)/(S(21)*d*f*sqrt(d*csc(e + f*x))) + S(10)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(21)*d**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(e + f*x)/(d*csc(e + f*x))**(S(3)/2), x), x, -S(2)*cos(e + f*x)/(S(5)*f*(d*csc(e + f*x))**(S(3)/2)) + S(6)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*d*f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*csc(e + f*x))**(S(-3)/2), x), x, -S(2)*cos(e + f*x)/(S(3)*d*f*sqrt(d*csc(e + f*x))) + S(2)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*d**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)/(d*csc(e + f*x))**(S(3)/2), x), x, S(2)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(d*f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(2)/(d*csc(e + f*x))**(S(3)/2), x), x, S(2)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(d**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(3)/(d*csc(e + f*x))**(S(3)/2), x), x, -S(2)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(d*f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))) - S(2)*sqrt(d*csc(e + f*x))*cos(e + f*x)/(d**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(4)/(d*csc(e + f*x))**(S(3)/2), x), x, S(2)*sqrt(d*csc(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*d**S(2)*f) - S(2)*(d*csc(e + f*x))**(S(3)/2)*cos(e + f*x)/(S(3)*d**S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(e + f*x)**S(5)/(d*csc(e + f*x))**(S(3)/2), x), x, -S(6)*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*d*f*sqrt(d*csc(e + f*x))*sqrt(sin(e + f*x))) - S(6)*sqrt(d*csc(e + f*x))*cos(e + f*x)/(S(5)*d**S(2)*f) - S(2)*(d*csc(e + f*x))**(S(5)/2)*cos(e + f*x)/(S(5)*d**S(4)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(e + f*x))**m*(b*csc(e + f*x))**n, x), x, (a*sin(e + f*x))**(m + S(1))*(b*csc(e + f*x))**n*Hypergeometric2F1(S(1)/2, m/S(2) - n/S(2) + S(1)/2, m/S(2) - n/S(2) + S(3)/2, sin(e + f*x)**S(2))*cos(e + f*x)/(a*f*(m - n + S(1))*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
