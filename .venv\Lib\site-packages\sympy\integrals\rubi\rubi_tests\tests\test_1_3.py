import sys
from sympy.external import import_module
matchpy = import_module("matchpy")

if not matchpy:
    #bin/test will not execute any tests now
    disabled = True

if sys.version_info[:2] < (3, 6):
    disabled = True

from sympy.integrals.rubi.rubimain import rubi_integrate
from sympy.functions import log, sqrt, exp, cos, sin, tan, sec, csc, cot
from sympy.functions.elementary.hyperbolic import atanh as arctanh
from sympy.functions.elementary.hyperbolic import asinh as arcsinh
from sympy.functions.elementary.hyperbolic import acosh as arccosh
from sympy.functions.elementary.trigonometric import atan as arctan
from sympy.functions.elementary.trigonometric import asin as arcsin
from sympy.functions.elementary.trigonometric import acos as arccos
from sympy.integrals.rubi.utility_function import EllipticE, EllipticF, hypergeom, rubi_test
from sympy.core.numbers import (I, pi as Pi)
from sympy.core.singleton import S
from sympy.core.symbol import symbols
from sympy.functions.elementary.exponential import exp_polar
from sympy.functions.special.hyper import hyper
from sympy.simplify.simplify import simplify
from sympy.testing.pytest import slow, skip, ON_TRAVIS

A, B, C, D, a, b, c, d, e, f, m, n, p, x, u = symbols('A B C D a b c d e f m n p x u', real=True, imaginary=False)

@slow
def test_1():
    if ON_TRAVIS:
        skip('Too slow for travis.')

    test = [
        [x**S(2)*(a + b*x)*(a*c - b*c*x)**S(3), x, S(2), S(1)/S(3)*a**S(4)*c**S(3)*x**S(3) - S(1)/S(2)*a**S(3)*b*c**S(3)*x**S(4) + S(1)/S(3)*a*b**S(3)*c**S(3)*x**S(6) - S(1)/S(7)*b**S(4)*c**S(3)*x**S(7)],
        [x*(a + b*x)*(a*c - b*c*x)**S(3), x, S(2), S(1)/S(2)*a**S(4)*c**S(3)*x**S(2) - S(2)/S(3)*a**S(3)*b*c**S(3)*x**S(3) + S(2)/S(5)*a*b**S(3)*c**S(3)*x**S(5) - S(1)/S(6)*b**S(4)*c**S(3)*x**S(6)],
        [x**S(3)*(a + b*x)*(A + B*x), x, S(2), S(1)/S(4)*a*A*x**S(4) + S(1)/S(5)*(A*b + a*B)*x**S(5) + S(1)/S(6)*b*B*x**S(6)],
        [x**S(4)*(A + B*x)/(a + b*x), x, S(2), - a**S(3)*(A*b - a*B)*x/b**S(5) + S(1)/S(2)*a**S(2)*(A*b - a*B)*x**S(2)/b**S(4) - S(1)/S(3)*a*(A*b - a*B)*x**S(3)/b**S(3) + S(1)/S(4)*(A*b - a*B)*x**S(4)/b**S(2) + S(1)/S(5)*B*x**S(5)/b + a**S(4)*(A*b - a*B)*log(a + b*x)/b**S(6)],
        [x**S(2)*(c + d*x)/(a + b*x), x, S(2), - a*(b*c - a*d)*x/b**S(3) + S(1)/S(2)*(b*c - a*d)*x**S(2)/b**S(2) + S(1)/S(3)*d*x**S(3)/b + a**S(2)*(b*c - a*d)*log(a + b*x)/b**S(4)],
        [x**S(3)*(c + d*x)**S(2)/(a + b*x)**S(2), x, S(2), - S(2)*a*(b*c - S(2)*a*d)*(b*c - a*d)*x/b**S(5) + S(1)/S(2)*(b*c - S(3)*a*d)*(b*c - a*d)*x**S(2)/b**S(4) + S(2)/S(3)*d*(b*c - a*d)*x**S(3)/b**S(3) + S(1)/S(4)*d**S(2)*x**S(4)/b**S(2) + a**S(3)*(b*c - a*d)**S(2)/(b**S(6)*(a + b*x)) + a**S(2)*(S(3)*b*c - S(5)*a*d)*(b*c - a*d)*log(a + b*x)/b**S(6)],
        [x**S(2)*(c + d*x)**S(3)/(a + b*x)**S(3), x, S(2), S(3)*d*(b*c - S(2)*a*d)*(b*c - a*d)*x/b**S(5) + S(3)/S(2)*d**S(2)*(b*c - a*d)*x**S(2)/b**S(4) + S(1)/S(3)*d**S(3)*x**S(3)/b**S(3) - S(1)/S(2)*a**S(2)*(b*c - a*d)**S(3)/(b**S(6)*(a + b*x)**S(2)) + a*(S(2)*b*c - S(5)*a*d)*(b*c - a*d)**S(2)/(b**S(6)*(a + b*x)) + (b*c - a*d)*(b**S(2)*c**S(2) - S(8)*a*b*c*d + S(10)*a**S(2)*d**S(2))*log(a + b*x)/b**S(6)],
        [x**(S(5)/S(2))*(A + B*x)/(a + b*x), x, S(6), - S(2)/S(3)*a*(A*b - a*B)*x**(S(3)/S(2))/b**S(3) + S(2)/S(5)*(A*b - a*B)*x**(S(5)/S(2))/b**S(2) + S(2)/S(7)*B*x**(S(7)/S(2))/b - S(2)*a**(S(5)/S(2))*(A*b - a*B)*arctan(sqrt(b)*sqrt(x)/sqrt(a))/b**(S(9)/S(2)) + S(2)*a**S(2)*(A*b - a*B)*sqrt(x)/b**S(4)],
        [x**m*(a + b*x)**S(3)*(A + B*x), x, S(2), a**S(3)*A*x**(S(1) + m)/(S(1) + m) + a**S(2)*(S(3)*A*b + a*B)*x**(S(2) + m)/(S(2) + m) + S(3)*a*b*(A*b + a*B)*x**(S(3) + m)/(S(3) + m) + b**S(2)*(A*b + S(3)*a*B)*x**(S(4) + m)/(S(4) + m) + b**S(3)*B*x**(S(5) + m)/(S(5) + m)],
        [x**m*(c + d*x)**S(3)/(a + b*x), x, S(7), d*(S(3)*b**S(2)*c**S(2) - S(3)*a*b*c*d + a**S(2)*d**S(2))*x**(S(1) + m)/(b**S(3)*(S(1) + m)) + d**S(2)*(S(3)*b*c - a*d)*x**(S(2) + m)/(b**S(2)*(S(2) + m)) + d**S(3)*x**(S(3) + m)/(b*(S(3) + m)) + (b*c - a*d)**S(3)*x**(S(1) + m)*hypergeom([S(1), S(1)], [S(1) - m], a/(a + b*x))/(b**S(3)*m*(a + b*x)), c**S(2)*d*x**(S(1) + m)/(b*(S(1) + m)) + c*d*(b*c - a*d)*x**(S(1) + m)/(b**S(2)*(S(1) + m)) + d*(b*c - a*d)**S(2)*x**(S(1) + m)/(b**S(3)*(S(1) + m)) + S(2)*c*d**S(2)*x**(S(2) + m)/(b*(S(2) + m)) + d**S(2)*(b*c - a*d)*x**(S(2) + m)/(b**S(2)*(S(2) + m)) + d**S(3)*x**(S(3) + m)/(b*(S(3) + m)) + (b*c - a*d)**S(3)*x**(S(1) + m)*hypergeom([S(1), S(1) + m], [S(2) + m], - b*x/a)/(a*b**S(3)*(S(1) + m))],
        [x**m*(c + d*x)**S(2)/(a + b*x), x, S(5), c*d*x**(S(1) + m)/(b*(S(1) + m)) + d*(b*c - a*d)*x**(S(1) + m)/(b**S(2)*(S(1) + m)) + d**S(2)*x**(S(2) + m)/(b*(S(2) + m)) + (b*c - a*d)**S(2)*x**(S(1) + m)*hypergeom([S(1), S(1) + m], [S(2) + m], - b*x/a)/(a*b**S(2)*(S(1) + m))],
        [b**S(2)*x**m/(b + a*x**S(2))**S(2), x, S(2), x**(S(1) + m)*hypergeom([S(2), S(1)/S(2)*(S(1) + m)], [S(1)/S(2)*(S(3) + m)], - a*x**S(2)/b)/(S(1) + m)],
        [x**m/((S(1) - x*sqrt(a)/sqrt( - b))**S(2)*(S(1) + x*sqrt(a)/sqrt( - b))**S(2)), x, S(2), x**(S(1) + m)*hypergeom([S(2), S(1)/S(2)*(S(1) + m)], [S(1)/S(2)*(S(3) + m)], - a*x**S(2)/b)/(S(1) + m)],
        [x**S(3)*(A + B*x)*sqrt(a + b*x), x, S(2), - S(2)/S(3)*a**S(3)*(A*b - a*B)*(a + b*x)**(S(3)/S(2))/b**S(5) + S(2)/S(5)*a**S(2)*(S(3)*A*b - S(4)*a*B)*(a + b*x)**(S(5)/S(2))/b**S(5) - S(6)/S(7)*a*(A*b - S(2)*a*B)*(a + b*x)**(S(7)/S(2))/b**S(5) + S(2)/S(9)*(A*b - S(4)*a*B)*(a + b*x)**(S(9)/S(2))/b**S(5) + S(2)/S(11)*B*(a + b*x)**(S(11)/S(2))/b**S(5)],
        [x**S(3)*(A + B*x)/sqrt(a + b*x), x, S(2), S(2)/S(3)*a**S(2)*(S(3)*A*b - S(4)*a*B)*(a + b*x)**(S(3)/S(2))/b**S(5) - S(6)/S(5)*a*(A*b - S(2)*a*B)*(a + b*x)**(S(5)/S(2))/b**S(5) + S(2)/S(7)*(A*b - S(4)*a*B)*(a + b*x)**(S(7)/S(2))/b**S(5) + S(2)/S(9)*B*(a + b*x)**(S(9)/S(2))/b**S(5) - S(2)*a**S(3)*(A*b - a*B)*sqrt(a + b*x)/b**S(5)],

        [x**(S(5)/S(2))*(A + B*x)*sqrt(a + b*x), x, S(7), S(1)/S(5)*B*x**(S(7)/S(2))*(a + b*x)**(S(3)/S(2))/b - S(1)/S(128)*a**S(4)*(S(10)*A*b - S(7)*a*B)*arctanh(sqrt(b)*sqrt(x)/sqrt(a + b*x))/b**(S(9)/S(2)) - S(1)/S(192)*a**S(2)*(S(10)*A*b - S(7)*a*B)*x**(S(3)/S(2))*sqrt(a + b*x)/b**S(3) + S(1)/S(240)*a*(S(10)*A*b - S(7)*a*B)*x**(S(5)/S(2))*sqrt(a + b*x)/b**S(2) + S(1)/S(40)*(S(10)*A*b - S(7)*a*B)*x**(S(7)/S(2))*sqrt(a + b*x)/b + S(1)/S(128)*a**S(3)*(S(10)*A*b - S(7)*a*B)*sqrt(x)*sqrt(a + b*x)/b**S(4)],
        [x**(S(3)/S(2))*(A + B*x)*sqrt(a + b*x), x, S(6), S(1)/S(4)*B*x**(S(5)/S(2))*(a + b*x)**(S(3)/S(2))/b + S(1)/S(64)*a**S(3)*(S(8)*A*b - S(5)*a*B)*arctanh(sqrt(b)*sqrt(x)/sqrt(a + b*x))/b**(S(7)/S(2)) + S(1)/S(96)*a*(S(8)*A*b - S(5)*a*B)*x**(S(3)/S(2))*sqrt(a + b*x)/b**S(2) + S(1)/S(24)*(S(8)*A*b - S(5)*a*B)*x**(S(5)/S(2))*sqrt(a + b*x)/b - S(1)/S(64)*a**S(2)*(S(8)*A*b - S(5)*a*B)*sqrt(x)*sqrt(a + b*x)/b**S(3)],
        [x**(S(7)/S(2))*(A + B*x)/sqrt(a + b*x), x, S(7), S(7)/S(128)*a**S(4)*(S(10)*A*b - S(9)*a*B)*arctanh(sqrt(b)*sqrt(x)/sqrt(a + b*x))/b**(S(11)/S(2)) + S(7)/S(192)*a**S(2)*(S(10)*A*b - S(9)*a*B)*x**(S(3)/S(2))*sqrt(a + b*x)/b**S(4) - S(7)/S(240)*a*(S(10)*A*b - S(9)*a*B)*x**(S(5)/S(2))*sqrt(a + b*x)/b**S(3) + S(1)/S(40)*(S(10)*A*b - S(9)*a*B)*x**(S(7)/S(2))*sqrt(a + b*x)/b**S(2) + S(1)/S(5)*B*x**(S(9)/S(2))*sqrt(a + b*x)/b - S(7)/S(128)*a**S(3)*(S(10)*A*b - S(9)*a*B)*sqrt(x)*sqrt(a + b*x)/b**S(5)],
        [x**(S(5)/S(2))*(A + B*x)/sqrt(a + b*x), x, S(6), - S(5)/S(64)*a**S(3)*(S(8)*A*b - S(7)*a*B)*arctanh(sqrt(b)*sqrt(x)/sqrt(a + b*x))/b**(S(9)/S(2)) - S(5)/S(96)*a*(S(8)*A*b - S(7)*a*B)*x**(S(3)/S(2))*sqrt(a + b*x)/b**S(3) + S(1)/S(24)*(S(8)*A*b - S(7)*a*B)*x**(S(5)/S(2))*sqrt(a + b*x)/b**S(2) + S(1)/S(4)*B*x**(S(7)/S(2))*sqrt(a + b*x)/b + S(5)/S(64)*a**S(2)*(S(8)*A*b - S(7)*a*B)*sqrt(x)*sqrt(a + b*x)/b**S(4)],
        [x**S(3)*sqrt(a + b*x)*sqrt(c + d*x), x, S(6), S(1)/S(5)*x**S(2)*(a + b*x)**(S(3)/S(2))*(c + d*x)**(S(3)/S(2))/(b*d) + S(1)/S(240)*(a + b*x)**(S(3)/S(2))*(c + d*x)**(S(3)/S(2))*(S(35)*b**S(2)*c**S(2) + S(38)*a*b*c*d + S(35)*a**S(2)*d**S(2) - S(42)*b*d*(b*c + a*d)*x)/(b**S(3)*d**S(3)) + S(1)/S(128)*(b*c - a*d)**S(2)*(b*c + a*d)*(S(7)*b**S(2)*c**S(2) + S(2)*a*b*c*d + S(7)*a**S(2)*d**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(9)/S(2))*d**(S(9)/S(2))) - S(1)/S(64)*(b*c + a*d)*(S(7)*b**S(2)*c**S(2) + S(2)*a*b*c*d + S(7)*a**S(2)*d**S(2))*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b**S(4)*d**S(3)) - S(1)/S(128)*(S(7)*b**S(4)*c**S(4) + S(2)*a*b**S(3)*c**S(3)*d - S(2)*a**S(3)*b*c*d**S(3) - S(7)*a**S(4)*d**S(4))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(4)*d**S(4))],
        [x**S(2)*sqrt(a + b*x)*sqrt(c + d*x), x, S(6), - S(5)/S(24)*(b*c + a*d)*(a + b*x)**(S(3)/S(2))*(c + d*x)**(S(3)/S(2))/(b**S(2)*d**S(2)) + S(1)/S(4)*x*(a + b*x)**(S(3)/S(2))*(c + d*x)**(S(3)/S(2))/(b*d) + S(1)/S(64)*(b*c - a*d)**S(2)*(S(4)*a*b*c*d - S(5)*(b*c + a*d)**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(7)/S(2))*d**(S(7)/S(2))) - S(1)/S(32)*(S(4)*a*b*c*d - S(5)*(b*c + a*d)**S(2))*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b**S(3)*d**S(2)) - S(1)/S(64)*(b*c - a*d)*(S(4)*a*b*c*d - S(5)*(b*c + a*d)**S(2))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(3)*d**S(3))],
        [x**S(3)*sqrt(a + b*x)/sqrt(c + d*x), x, S(5), S(1)/S(64)*(b*c - a*d)*(S(35)*b**S(3)*c**S(3) + S(15)*a*b**S(2)*c**S(2)*d + S(9)*a**S(2)*b*c*d**S(2) + S(5)*a**S(3)*d**S(3))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(7)/S(2))*d**(S(9)/S(2))) + S(1)/S(4)*x**S(2)*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b*d) + S(1)/S(96)*(a + b*x)**(S(3)/S(2))*(S(35)*b**S(2)*c**S(2) + S(22)*a*b*c*d + S(15)*a**S(2)*d**S(2) - S(4)*b*d*(S(7)*b*c + S(5)*a*d)*x)*sqrt(c + d*x)/(b**S(3)*d**S(3)) - S(1)/S(64)*(S(35)*b**S(3)*c**S(3) + S(15)*a*b**S(2)*c**S(2)*d + S(9)*a**S(2)*b*c*d**S(2) + S(5)*a**S(3)*d**S(3))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(3)*d**S(4))],
        [x**S(2)*sqrt(a + b*x)/sqrt(c + d*x), x, S(5), - S(1)/S(8)*(b*c - a*d)*(S(5)*b**S(2)*c**S(2) + S(2)*a*b*c*d + a**S(2)*d**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(5)/S(2))*d**(S(7)/S(2))) - S(1)/S(12)*(S(5)*b*c + S(3)*a*d)*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b**S(2)*d**S(2)) + S(1)/S(3)*x*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b*d) + S(1)/S(8)*(S(5)*b**S(2)*c**S(2) + S(2)*a*b*c*d + a**S(2)*d**S(2))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(2)*d**S(3))],
        [x**S(2)*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x), x, S(7), - S(1)/S(40)*(S(7)*b*c + S(5)*a*d)*(a + b*x)**(S(5)/S(2))*(c + d*x)**(S(3)/S(2))/(b**S(2)*d**S(2)) + S(1)/S(5)*x*(a + b*x)**(S(5)/S(2))*(c + d*x)**(S(3)/S(2))/(b*d) + S(1)/S(128)*(b*c - a*d)**S(3)*(S(7)*b**S(2)*c**S(2) + S(6)*a*b*c*d + S(3)*a**S(2)*d**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(7)/S(2))*d**(S(9)/S(2))) + S(1)/S(192)*(b*c - a*d)*(S(7)*b**S(2)*c**S(2) + S(6)*a*b*c*d + S(3)*a**S(2)*d**S(2))*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b**S(3)*d**S(3)) + S(1)/S(48)*(S(7)*b**S(2)*c**S(2) + S(6)*a*b*c*d + S(3)*a**S(2)*d**S(2))*(a + b*x)**(S(5)/S(2))*sqrt(c + d*x)/(b**S(3)*d**S(2)) - S(1)/S(128)*(b*c - a*d)**S(2)*(S(7)*b**S(2)*c**S(2) + S(6)*a*b*c*d + S(3)*a**S(2)*d**S(2))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(3)*d**S(4))],
        [x*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x), x, S(6), S(1)/S(4)*(a + b*x)**(S(5)/S(2))*(c + d*x)**(S(3)/S(2))/(b*d) - S(1)/S(64)*(b*c - a*d)**S(3)*(S(5)*b*c + S(3)*a*d)*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(5)/S(2))*d**(S(7)/S(2))) - S(1)/S(96)*(b*c - a*d)*(S(5)*b*c + S(3)*a*d)*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b**S(2)*d**S(2)) - S(1)/S(24)*(S(5)*b*c + S(3)*a*d)*(a + b*x)**(S(5)/S(2))*sqrt(c + d*x)/(b**S(2)*d) + S(1)/S(64)*(b*c - a*d)**S(2)*(S(5)*b*c + S(3)*a*d)*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(2)*d**S(3))],
        [x**S(2)*(a + b*x)**(S(3)/S(2))/sqrt(c + d*x), x, S(6), S(1)/S(64)*(b*c - a*d)**S(2)*(S(35)*b**S(2)*c**S(2) + S(10)*a*b*c*d + S(3)*a**S(2)*d**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(5)/S(2))*d**(S(9)/S(2))) + S(1)/S(96)*(S(35)*b**S(2)*c**S(2) + S(10)*a*b*c*d + S(3)*a**S(2)*d**S(2))*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b**S(2)*d**S(3)) - S(1)/S(24)*(S(7)*b*c + S(3)*a*d)*(a + b*x)**(S(5)/S(2))*sqrt(c + d*x)/(b**S(2)*d**S(2)) + S(1)/S(4)*x*(a + b*x)**(S(5)/S(2))*sqrt(c + d*x)/(b*d) - S(1)/S(64)*(b*c - a*d)*(S(35)*b**S(2)*c**S(2) + S(10)*a*b*c*d + S(3)*a**S(2)*d**S(2))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(2)*d**S(4))],
        [x*(a + b*x)**(S(3)/S(2))/sqrt(c + d*x), x, S(5), - S(1)/S(8)*(b*c - a*d)**S(2)*(S(5)*b*c + a*d)*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(3)/S(2))*d**(S(7)/S(2))) - S(1)/S(12)*(S(5)*b*c + a*d)*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b*d**S(2)) + S(1)/S(3)*(a + b*x)**(S(5)/S(2))*sqrt(c + d*x)/(b*d) + S(1)/S(8)*(b*c - a*d)*(S(5)*b*c + a*d)*sqrt(a + b*x)*sqrt(c + d*x)/(b*d**S(3))],
        [x**S(2)*(a + b*x)**(S(5)/S(2))*sqrt(c + d*x), x, S(8), - S(1)/S(60)*(S(9)*b*c + S(5)*a*d)*(a + b*x)**(S(7)/S(2))*(c + d*x)**(S(3)/S(2))/(b**S(2)*d**S(2)) + S(1)/S(6)*x*(a + b*x)**(S(7)/S(2))*(c + d*x)**(S(3)/S(2))/(b*d) - S(1)/S(512)*(b*c - a*d)**S(4)*(S(21)*b**S(2)*c**S(2) + S(14)*a*b*c*d + S(5)*a**S(2)*d**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(7)/S(2))*d**(S(11)/S(2))) - S(1)/S(768)*(b*c - a*d)**S(2)*(S(21)*b**S(2)*c**S(2) + S(14)*a*b*c*d + S(5)*a**S(2)*d**S(2))*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b**S(3)*d**S(4)) + S(1)/S(960)*(b*c - a*d)*(S(21)*b**S(2)*c**S(2) + S(14)*a*b*c*d + S(5)*a**S(2)*d**S(2))*(a + b*x)**(S(5)/S(2))*sqrt(c + d*x)/(b**S(3)*d**S(3)) + S(1)/S(160)*(S(21)*b**S(2)*c**S(2) + S(14)*a*b*c*d + S(5)*a**S(2)*d**S(2))*(a + b*x)**(S(7)/S(2))*sqrt(c + d*x)/(b**S(3)*d**S(2)) + S(1)/S(512)*(b*c - a*d)**S(3)*(S(21)*b**S(2)*c**S(2) + S(14)*a*b*c*d + S(5)*a**S(2)*d**S(2))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(3)*d**S(5))],
        [x*(a + b*x)**(S(5)/S(2))*sqrt(c + d*x), x, S(7), S(1)/S(5)*(a + b*x)**(S(7)/S(2))*(c + d*x)**(S(3)/S(2))/(b*d) + S(1)/S(128)*(b*c - a*d)**S(4)*(S(7)*b*c + S(3)*a*d)*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(5)/S(2))*d**(S(9)/S(2))) + S(1)/S(192)*(b*c - a*d)**S(2)*(S(7)*b*c + S(3)*a*d)*(a + b*x)**(S(3)/S(2))*sqrt(c + d*x)/(b**S(2)*d**S(3)) - S(1)/S(240)*(b*c - a*d)*(S(7)*b*c + S(3)*a*d)*(a + b*x)**(S(5)/S(2))*sqrt(c + d*x)/(b**S(2)*d**S(2)) - S(1)/S(40)*(S(7)*b*c + S(3)*a*d)*(a + b*x)**(S(7)/S(2))*sqrt(c + d*x)/(b**S(2)*d) - S(1)/S(128)*(b*c - a*d)**S(3)*(S(7)*b*c + S(3)*a*d)*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(2)*d**S(4))],
        [x**S(2)*sqrt(c + d*x)/sqrt(a + b*x), x, S(5), S(1)/S(8)*(b*c - a*d)*(b**S(2)*c**S(2) + S(2)*a*b*c*d + S(5)*a**S(2)*d**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(7)/S(2))*d**(S(5)/S(2))) - S(1)/S(12)*(S(3)*b*c + S(5)*a*d)*(c + d*x)**(S(3)/S(2))*sqrt(a + b*x)/(b**S(2)*d**S(2)) + S(1)/S(3)*x*(c + d*x)**(S(3)/S(2))*sqrt(a + b*x)/(b*d) + S(1)/S(8)*(b**S(2)*c**S(2) + S(2)*a*b*c*d + S(5)*a**S(2)*d**S(2))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(3)*d**S(2))],
        [x*sqrt(c + d*x)/sqrt(a + b*x), x, S(4), - S(1)/S(4)*(b*c - a*d)*(b*c + S(3)*a*d)*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(5)/S(2))*d**(S(3)/S(2))) + S(1)/S(2)*(c + d*x)**(S(3)/S(2))*sqrt(a + b*x)/(b*d) - S(1)/S(4)*(b*c + S(3)*a*d)*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(2)*d)],
        [x**S(3)/(sqrt(a + b*x)*sqrt(c + d*x)), x, S(4), - S(1)/S(8)*(b*c + a*d)*(S(5)*b**S(2)*c**S(2) - S(2)*a*b*c*d + S(5)*a**S(2)*d**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(7)/S(2))*d**(S(7)/S(2))) + S(1)/S(3)*x**S(2)*sqrt(a + b*x)*sqrt(c + d*x)/(b*d) + S(1)/S(24)*(S(15)*b**S(2)*c**S(2) + S(14)*a*b*c*d + S(15)*a**S(2)*d**S(2) - S(10)*b*d*(b*c + a*d)*x)*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(3)*d**S(3))],
        [x**S(2)/(sqrt(a + b*x)*sqrt(c + d*x)), x, S(4), - S(1)/S(4)*(S(4)*a*b*c*d - S(3)*(b*c + a*d)**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(5)/S(2))*d**(S(5)/S(2))) - S(3)/S(4)*(b*c + a*d)*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(2)*d**S(2)) + S(1)/S(2)*x*sqrt(a + b*x)*sqrt(c + d*x)/(b*d)],
        [x**S(4)/((a + b*x)**(S(3)/S(2))*(c + d*x)**(S(3)/S(2))), x, S(5), S(3)/S(4)*(S(5)*b**S(2)*c**S(2) + S(6)*a*b*c*d + S(5)*a**S(2)*d**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(7)/S(2))*d**(S(7)/S(2))) + S(2)*a*x**S(3)/(b*(b*c - a*d)*sqrt(a + b*x)*sqrt(c + d*x)) - S(2)*c*(b*c + a*d)*x**S(2)*sqrt(a + b*x)/(b*d*(b*c - a*d)**S(2)*sqrt(c + d*x)) - S(1)/S(4)*((b*c + a*d)*(S(15)*b**S(2)*c**S(2) - S(22)*a*b*c*d + S(15)*a**S(2)*d**S(2)) - S(2)*b*d*(S(5)*b**S(2)*c**S(2) - S(2)*a*b*c*d + S(5)*a**S(2)*d**S(2))*x)*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(3)*d**S(3)*(b*c - a*d)**S(2))],
        [x**S(3)/((a + b*x)**(S(3)/S(2))*(c + d*x)**(S(3)/S(2))), x, S(4), - S(3)*(b*c + a*d)*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(5)/S(2))*d**(S(5)/S(2))) + S(2)*a*x**S(2)/(b*(b*c - a*d)*sqrt(a + b*x)*sqrt(c + d*x)) + (c*(S(3)*b**S(2)*c**S(2) - S(2)*a*b*c*d + S(3)*a**S(2)*d**S(2)) + d*(b*c - S(3)*a*d)*(b*c - a*d)*x)*sqrt(a + b*x)/(b**S(2)*d**S(2)*(b*c - a*d)**S(2)*sqrt(c + d*x))],
        [x**S(3)*(a + b*x)**(S(1)/S(4))/(c + d*x)**(S(1)/S(4)), x, S(7), - S(1)/S(512)*(S(195)*b**S(3)*c**S(3) + S(135)*a*b**S(2)*c**S(2)*d + S(105)*a**S(2)*b*c*d**S(2) + S(77)*a**S(3)*d**S(3))*(a + b*x)**(S(1)/S(4))*(c + d*x)**(S(3)/S(4))/(b**S(3)*d**S(4)) + S(1)/S(4)*x**S(2)*(a + b*x)**(S(5)/S(4))*(c + d*x)**(S(3)/S(4))/(b*d) + S(1)/S(384)*(a + b*x)**(S(5)/S(4))*(c + d*x)**(S(3)/S(4))*(S(117)*b**S(2)*c**S(2) + S(94)*a*b*c*d + S(77)*a**S(2)*d**S(2) - S(8)*b*d*(S(13)*b*c + S(11)*a*d)*x)/(b**S(3)*d**S(3)) + S(1)/S(1024)*(b*c - a*d)*(S(195)*b**S(3)*c**S(3) + S(135)*a*b**S(2)*c**S(2)*d + S(105)*a**S(2)*b*c*d**S(2) + S(77)*a**S(3)*d**S(3))*arctan(d**(S(1)/S(4))*(a + b*x)**(S(1)/S(4))/(b**(S(1)/S(4))*(c + d*x)**(S(1)/S(4))))/(b**(S(15)/S(4))*d**(S(17)/S(4))) + S(1)/S(1024)*(b*c - a*d)*(S(195)*b**S(3)*c**S(3) + S(135)*a*b**S(2)*c**S(2)*d + S(105)*a**S(2)*b*c*d**S(2) + S(77)*a**S(3)*d**S(3))*arctanh(d**(S(1)/S(4))*(a + b*x)**(S(1)/S(4))/(b**(S(1)/S(4))*(c + d*x)**(S(1)/S(4))))/(b**(S(15)/S(4))*d**(S(17)/S(4)))],
        [x**S(2)*(a + b*x)**(S(1)/S(4))/(c + d*x)**(S(1)/S(4)), x, S(7), S(1)/S(32)*(S(15)*b**S(2)*c**S(2) + S(10)*a*b*c*d + S(7)*a**S(2)*d**S(2))*(a + b*x)**(S(1)/S(4))*(c + d*x)**(S(3)/S(4))/(b**S(2)*d**S(3)) - S(1)/S(24)*(S(9)*b*c + S(7)*a*d)*(a + b*x)**(S(5)/S(4))*(c + d*x)**(S(3)/S(4))/(b**S(2)*d**S(2)) + S(1)/S(3)*x*(a + b*x)**(S(5)/S(4))*(c + d*x)**(S(3)/S(4))/(b*d) - S(1)/S(64)*(b*c - a*d)*(S(15)*b**S(2)*c**S(2) + S(10)*a*b*c*d + S(7)*a**S(2)*d**S(2))*arctan(d**(S(1)/S(4))*(a + b*x)**(S(1)/S(4))/(b**(S(1)/S(4))*(c + d*x)**(S(1)/S(4))))/(b**(S(11)/S(4))*d**(S(13)/S(4))) - S(1)/S(64)*(b*c - a*d)*(S(15)*b**S(2)*c**S(2) + S(10)*a*b*c*d + S(7)*a**S(2)*d**S(2))*arctanh(d**(S(1)/S(4))*(a + b*x)**(S(1)/S(4))/(b**(S(1)/S(4))*(c + d*x)**(S(1)/S(4))))/(b**(S(11)/S(4))*d**(S(13)/S(4)))],
        [x*(a + b*x)**n*(c + d*x), x, S(2), - a*(b*c - a*d)*(a + b*x)**(S(1) + n)/(b**S(3)*(S(1) + n)) + (b*c - S(2)*a*d)*(a + b*x)**(S(2) + n)/(b**S(3)*(S(2) + n)) + d*(a + b*x)**(S(3) + n)/(b**S(3)*(S(3) + n))],
        [x**S(2)*(a + b*x)**n/(c + d*x), x, S(3), - (b*c + a*d)*(a + b*x)**(S(1) + n)/(b**S(2)*d**S(2)*(S(1) + n)) + (a + b*x)**(S(2) + n)/(b**S(2)*d*(S(2) + n)) + c**S(2)*(a + b*x)**(S(1) + n)*hypergeom([S(1), S(1) + n], [S(2) + n], - d*(a + b*x)/(b*c - a*d))/(d**S(2)*(b*c - a*d)*(S(1) + n))],
        [x*(a + b*x)**n/(c + d*x), x, S(2), (a + b*x)**(S(1) + n)/(b*d*(S(1) + n)) - c*(a + b*x)**(S(1) + n)*hypergeom([S(1), S(1) + n], [S(2) + n], - d*(a + b*x)/(b*c - a*d))/(d*(b*c - a*d)*(S(1) + n))],
        [x**m*(S(3) - S(2)*a*x)**(S(2) + n)*(S(6) + S(4)*a*x)**n, x, S(8), S(2)**n*S(9)**(S(1) + n)*x**(S(1) + m)*hypergeom([S(1)/S(2)*(S(1) + m), - n], [S(1)/S(2)*(S(3) + m)], S(4)/S(9)*a**S(2)*x**S(2))/(S(1) + m) - S(2)**(S(2) + n)*S(3)**(S(1) + S(2)*n)*a*x**(S(2) + m)*hypergeom([S(1)/S(2)*(S(2) + m), - n], [S(1)/S(2)*(S(4) + m)], S(4)/S(9)*a**S(2)*x**S(2))/(S(2) + m) + S(2)**(S(2) + n)*S(9)**n*a**S(2)*x**(S(3) + m)*hypergeom([S(1)/S(2)*(S(3) + m), - n], [S(1)/S(2)*(S(5) + m)], S(4)/S(9)*a**S(2)*x**S(2))/(S(3) + m)],
        [x**m*(S(3) - S(2)*a*x)**(S(1) + n)*(S(6) + S(4)*a*x)**n, x, S(5), S(2)**n*S(3)**(S(1) + S(2)*n)*x**(S(1) + m)*hypergeom([S(1)/S(2)*(S(1) + m), - n], [S(1)/S(2)*(S(3) + m)], S(4)/S(9)*a**S(2)*x**S(2))/(S(1) + m) - S(2)**(S(1) + n)*S(9)**n*a*x**(S(2) + m)*hypergeom([S(1)/S(2)*(S(2) + m), - n], [S(1)/S(2)*(S(4) + m)], S(4)/S(9)*a**S(2)*x**S(2))/(S(2) + m)],
        [(a + b*x)*(A + B*x)*(d + e*x)**m, x, S(2), (b*d - a*e)*(B*d - A*e)*(d + e*x)**(S(1) + m)/(e**S(3)*(S(1) + m)) - (S(2)*b*B*d - A*b*e - a*B*e)*(d + e*x)**(S(2) + m)/(e**S(3)*(S(2) + m)) + b*B*(d + e*x)**(S(3) + m)/(e**S(3)*(S(3) + m))],
        [(A + B*x)*(d + e*x)**S(5)/(a + b*x), x, S(2), (A*b - a*B)*e*(b*d - a*e)**S(4)*x/b**S(6) + S(1)/S(2)*(A*b - a*B)*(b*d - a*e)**S(3)*(d + e*x)**S(2)/b**S(5) + S(1)/S(3)*(A*b - a*B)*(b*d - a*e)**S(2)*(d + e*x)**S(3)/b**S(4) + S(1)/S(4)*(A*b - a*B)*(b*d - a*e)*(d + e*x)**S(4)/b**S(3) + S(1)/S(5)*(A*b - a*B)*(d + e*x)**S(5)/b**S(2) + S(1)/S(6)*B*(d + e*x)**S(6)/(b*e) + (A*b - a*B)*(b*d - a*e)**S(5)*log(a + b*x)/b**S(7)],
        [(S(1) - S(2)*x)*(S(2) + S(3)*x)**m*(S(3) + S(5)*x), x, S(2), - S(7)/S(27)*(S(2) + S(3)*x)**(S(1) + m)/(S(1) + m) + S(37)/S(27)*(S(2) + S(3)*x)**(S(2) + m)/(S(2) + m) - S(10)/S(27)*(S(2) + S(3)*x)**(S(3) + m)/(S(3) + m)],
        [(S(1) - S(2)*x)*(S(2) + S(3)*x)**S(8)*(S(3) + S(5)*x), x, S(2), - S(7)/S(243)*(S(2) + S(3)*x)**S(9) + S(37)/S(270)*(S(2) + S(3)*x)**S(10) - S(10)/S(297)*(S(2) + S(3)*x)**S(11)],
        [(S(1) - S(2)*x)*(S(2) + S(3)*x)**m/(S(3) + S(5)*x), x, S(2), - S(2)/S(15)*(S(2) + S(3)*x)**(S(1) + m)/(S(1) + m) - S(11)/S(5)*(S(2) + S(3)*x)**(S(1) + m)*hypergeom([S(1), S(1) + m], [S(2) + m], S(5)*(S(2) + S(3)*x))/(S(1) + m)],
        [(S(1) - S(2)*x)*(S(2) + S(3)*x)**S(6)/(S(3) + S(5)*x), x, S(2), S(1666663)/S(78125)*x + S(1777779)/S(31250)*x**S(2) + S(152469)/S(3125)*x**S(3) - S(152469)/S(2500)*x**S(4) - S(106677)/S(625)*x**S(5) - S(7047)/S(50)*x**S(6) - S(1458)/S(35)*x**S(7) + S(11)/S(390625)*log(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**S(2)*(S(2) + S(3)*x)**S(8)*(S(3) + S(5)*x), x, S(2), - S(49)/S(729)*(S(2) + S(3)*x)**S(9) + S(91)/S(270)*(S(2) + S(3)*x)**S(10) - S(16)/S(99)*(S(2) + S(3)*x)**S(11) + S(5)/S(243)*(S(2) + S(3)*x)**S(12)],
        [(S(1) - S(2)*x)**S(2)*(S(2) + S(3)*x)**S(7)*(S(3) + S(5)*x), x, S(2), - S(49)/S(648)*(S(2) + S(3)*x)**S(8) + S(91)/S(243)*(S(2) + S(3)*x)**S(9) - S(8)/S(45)*(S(2) + S(3)*x)**S(10) + S(20)/S(891)*(S(2) + S(3)*x)**S(11)],
        [(S(1) - S(2)*x)**S(2)*(S(2) + S(3)*x)**S(7)/(S(3) + S(5)*x), x, S(2), S(83333293)/S(1953125)*x + S(80555569)/S(781250)*x**S(2) + S(1327159)/S(78125)*x**S(3) - S(20577159)/S(62500)*x**S(4) - S(7315947)/S(15625)*x**S(5) + S(130383)/S(1250)*x**S(6) + S(672867)/S(875)*x**S(7) + S(16767)/S(25)*x**S(8) + S(972)/S(5)*x**S(9) + S(121)/S(9765625)*log(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**S(2)*(S(2) + S(3)*x)**S(6)/(S(3) + S(5)*x), x, S(2), S(8333293)/S(390625)*x + S(5555569)/S(156250)*x**S(2) - S(422841)/S(15625)*x**S(3) - S(1677159)/S(12500)*x**S(4) - S(228447)/S(3125)*x**S(5) + S(35883)/S(250)*x**S(6) + S(34992)/S(175)*x**S(7) + S(729)/S(10)*x**S(8) + S(121)/S(1953125)*log(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**S(3)*(S(2) + S(3)*x)**S(8)*(S(3) + S(5)*x), x, S(2), - S(343)/S(2187)*(S(2) + S(3)*x)**S(9) + S(2009)/S(2430)*(S(2) + S(3)*x)**S(10) - S(518)/S(891)*(S(2) + S(3)*x)**S(11) + S(107)/S(729)*(S(2) + S(3)*x)**S(12) - S(40)/S(3159)*(S(2) + S(3)*x)**S(13)],
        [(S(1) - S(2)*x)**S(3)*(S(2) + S(3)*x)**S(7)*(S(3) + S(5)*x), x, S(2), S(384)*x + S(1184)*x**S(2) + S(480)*x**S(3) - S(5148)*x**S(4) - S(48968)/S(5)*x**S(5) + S(3514)*x**S(6) + S(29106)*x**S(7) + S(208035)/S(8)*x**S(8) - S(15507)*x**S(9) - S(217971)/S(5)*x**S(10) - S(329508)/S(11)*x**S(11) - S(7290)*x**S(12)],
        [(S(1) - S(2)*x)**S(3)*(S(2) + S(3)*x)**S(6)/(S(3) + S(5)*x), x, S(2), S(41666223)/S(1953125)*x + S(11111259)/S(781250)*x**S(2) - S(17453753)/S(234375)*x**S(3) - S(5848749)/S(62500)*x**S(4) + S(2212083)/S(15625)*x**S(5) + S(331713)/S(1250)*x**S(6) - S(40338)/S(875)*x**S(7) - S(13851)/S(50)*x**S(8) - S(648)/S(5)*x**S(9) + S(1331)/S(9765625)*log(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**S(3)*(S(2) + S(3)*x)**S(5)/(S(3) + S(5)*x), x, S(2), S(4166223)/S(390625)*x - S(138741)/S(156250)*x**S(2) - S(1703753)/S(46875)*x**S(3) - S(73749)/S(12500)*x**S(4) + S(243333)/S(3125)*x**S(5) + S(4419)/S(125)*x**S(6) - S(11988)/S(175)*x**S(7) - S(243)/S(5)*x**S(8) + S(1331)/S(1953125)*log(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**m*(S(3) + S(5)*x)/(S(1) - S(2)*x), x, S(2), - S(5)/S(6)*(S(2) + S(3)*x)**(S(1) + m)/(S(1) + m) + S(11)/S(14)*(S(2) + S(3)*x)**(S(1) + m)*hypergeom([S(1), S(1) + m], [S(2) + m], S(2)/S(7)*(S(2) + S(3)*x))/(S(1) + m)],
        [(S(2) + S(3)*x)**S(8)*(S(3) + S(5)*x)/(S(1) - S(2)*x), x, S(2), - S(63019595)/S(512)*x - S(60332619)/S(512)*x**S(2) - S(17391129)/S(128)*x**S(3) - S(37722699)/S(256)*x**S(4) - S(21272139)/S(160)*x**S(5) - S(2929689)/S(32)*x**S(6) - S(353565)/S(8)*x**S(7) - S(422091)/S(32)*x**S(8) - S(3645)/S(2)*x**S(9) - S(63412811)/S(1024)*log(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**m/((S(1) - S(2)*x)*(S(3) + S(5)*x)), x, S(3), S(2)/S(77)*(S(2) + S(3)*x)**(S(1) + m)*hypergeom([S(1), S(1) + m], [S(2) + m], S(2)/S(7)*(S(2) + S(3)*x))/(S(1) + m) - S(5)/S(11)*(S(2) + S(3)*x)**(S(1) + m)*hypergeom([S(1), S(1) + m], [S(2) + m], S(5)*(S(2) + S(3)*x))/(S(1) + m)],
        [(S(2) + S(3)*x)**S(8)*(S(3) + S(5)*x)/(S(1) - S(2)*x)**S(2), x, S(2), S(63412811)/S(1024)/(S(1) - S(2)*x) + S(91609881)/S(256)*x + S(122887143)/S(512)*x**S(2) + S(5892813)/S(32)*x**S(3) + S(32991057)/S(256)*x**S(4) + S(5859459)/S(80)*x**S(5) + S(976617)/S(32)*x**S(6) + S(56862)/S(7)*x**S(7) + S(32805)/S(32)*x**S(8) + S(246239357)/S(1024)*log(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(7)*(S(3) + S(5)*x)/(S(1) - S(2)*x)**S(2), x, S(2), S(9058973)/S(512)/(S(1) - S(2)*x) + S(22333965)/S(256)*x + S(873207)/S(16)*x**S(2) + S(2399985)/S(64)*x**S(3) + S(1423899)/S(64)*x**S(4) + S(793881)/S(80)*x**S(5) + S(11421)/S(4)*x**S(6) + S(10935)/S(28)*x**S(7) + S(15647317)/S(256)*log(S(1) - S(2)*x)],
        [(a + b*x)**m/(e + f*x)**S(2), x, S(1), b*(a + b*x)**(S(1) + m)*hypergeom([S(2), S(1) + m], [S(2) + m], - f*(a + b*x)/(b*e - a*f))/((b*e - a*f)**S(2)*(S(1) + m))],
        [(a + b*x)**m/((c + d*x)*(e + f*x)**S(2)), x, S(4), - f*(a + b*x)**(S(1) + m)/((b*e - a*f)*(d*e - c*f)*(e + f*x)) + d**S(2)*(a + b*x)**(S(1) + m)*hypergeom([S(1), S(1) + m], [S(2) + m], - d*(a + b*x)/(b*c - a*d))/((b*c - a*d)*(d*e - c*f)**S(2)*(S(1) + m)) + f*(a*d*f - b*(d*e*(S(1) - m) + c*f*m))*(a + b*x)**(S(1) + m)*hypergeom([S(1), S(1) + m], [S(2) + m], - f*(a + b*x)/(b*e - a*f))/((b*e - a*f)**S(2)*(d*e - c*f)**S(2)*(S(1) + m))],
        [(S(2) + S(3)*x)**S(7)*(S(3) + S(5)*x)/(S(1) - S(2)*x)**S(3), x, S(2), S(9058973)/S(1024)/(S(1) - S(2)*x)**S(2) + ( - S(15647317)/S(256))/(S(1) - S(2)*x) - S(24960933)/S(256)*x - S(10989621)/S(256)*x**S(2) - S(631611)/S(32)*x**S(3) - S(235467)/S(32)*x**S(4) - S(147987)/S(80)*x**S(5) - S(3645)/S(16)*x**S(6) - S(23647449)/S(256)*log(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(8)/((S(1) - S(2)*x)**S(3)*(S(3) + S(5)*x)), x, S(2), S(5764801)/S(5632)/(S(1) - S(2)*x)**S(2) + ( - S(188591347)/S(30976))/(S(1) - S(2)*x) - S(2941619571)/S(400000)*x - S(110180817)/S(40000)*x**S(2) - S(124416)/S(125)*x**S(3) - S(408969)/S(1600)*x**S(4) - S(6561)/S(200)*x**S(5) - S(2644396573)/S(340736)*log(S(1) - S(2)*x) + S(1)/S(20796875)*log(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(7)/((S(1) - S(2)*x)**S(3)*(S(3) + S(5)*x)), x, S(2), S(823543)/S(2816)/(S(1) - S(2)*x)**S(2) + ( - S(5764801)/S(3872))/(S(1) - S(2)*x) - S(26161299)/S(20000)*x - S(792423)/S(2000)*x**S(2) - S(40581)/S(400)*x**S(3) - S(2187)/S(160)*x**S(4) - S(269063263)/S(170368)*log(S(1) - S(2)*x) + S(1)/S(4159375)*log(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(6)*(S(3) + S(5)*x)*sqrt(S(1) - S(2)*x), x, S(2), - S(1294139)/S(384)*(S(1) - S(2)*x)**(S(3)/S(2)) + S(3916031)/S(640)*(S(1) - S(2)*x)**(S(5)/S(2)) - S(725445)/S(128)*(S(1) - S(2)*x)**(S(7)/S(2)) + S(406455)/S(128)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(1580985)/S(1408)*(S(1) - S(2)*x)**(S(11)/S(2)) + S(409941)/S(1664)*(S(1) - S(2)*x)**(S(13)/S(2)) - S(19683)/S(640)*(S(1) - S(2)*x)**(S(15)/S(2)) + S(3645)/S(2176)*(S(1) - S(2)*x)**(S(17)/S(2))],
        [(S(2) + S(3)*x)**S(5)*(S(3) + S(5)*x)*sqrt(S(1) - S(2)*x), x, S(2), - S(184877)/S(192)*(S(1) - S(2)*x)**(S(3)/S(2)) + S(12005)/S(8)*(S(1) - S(2)*x)**(S(5)/S(2)) - S(74235)/S(64)*(S(1) - S(2)*x)**(S(7)/S(2)) + S(4165)/S(8)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(97335)/S(704)*(S(1) - S(2)*x)**(S(11)/S(2)) + S(81)/S(4)*(S(1) - S(2)*x)**(S(13)/S(2)) - S(81)/S(64)*(S(1) - S(2)*x)**(S(15)/S(2))],
        [(S(2) + S(3)*x)**S(4)*sqrt(S(1) - S(2)*x)/(S(3) + S(5)*x), x, S(5), - S(45473)/S(5000)*(S(1) - S(2)*x)**(S(3)/S(2)) + S(34371)/S(5000)*(S(1) - S(2)*x)**(S(5)/S(2)) - S(2889)/S(1400)*(S(1) - S(2)*x)**(S(7)/S(2)) + S(9)/S(40)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(2)/S(3125)*arctanh(sqrt(S(5)/S(11))*sqrt(S(1) - S(2)*x))*sqrt(S(11)/S(5)) + S(2)/S(3125)*sqrt(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(3)*sqrt(S(1) - S(2)*x)/(S(3) + S(5)*x), x, S(5), - S(1299)/S(500)*(S(1) - S(2)*x)**(S(3)/S(2)) + S(162)/S(125)*(S(1) - S(2)*x)**(S(5)/S(2)) - S(27)/S(140)*(S(1) - S(2)*x)**(S(7)/S(2)) - S(2)/S(625)*arctanh(sqrt(S(5)/S(11))*sqrt(S(1) - S(2)*x))*sqrt(S(11)/S(5)) + S(2)/S(625)*sqrt(S(1) - S(2)*x)],
        [(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(6)*(S(3) + S(5)*x), x, S(2), - S(1294139)/S(640)*(S(1) - S(2)*x)**(S(5)/S(2)) + S(559433)/S(128)*(S(1) - S(2)*x)**(S(7)/S(2)) - S(564235)/S(128)*(S(1) - S(2)*x)**(S(9)/S(2)) + S(3658095)/S(1408)*(S(1) - S(2)*x)**(S(11)/S(2)) - S(1580985)/S(1664)*(S(1) - S(2)*x)**(S(13)/S(2)) + S(136647)/S(640)*(S(1) - S(2)*x)**(S(15)/S(2)) - S(59049)/S(2176)*(S(1) - S(2)*x)**(S(17)/S(2)) + S(3645)/S(2432)*(S(1) - S(2)*x)**(S(19)/S(2))],
        [(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(5)*(S(3) + S(5)*x), x, S(2), - S(184877)/S(320)*(S(1) - S(2)*x)**(S(5)/S(2)) + S(8575)/S(8)*(S(1) - S(2)*x)**(S(7)/S(2)) - S(173215)/S(192)*(S(1) - S(2)*x)**(S(9)/S(2)) + S(37485)/S(88)*(S(1) - S(2)*x)**(S(11)/S(2)) - S(97335)/S(832)*(S(1) - S(2)*x)**(S(13)/S(2)) + S(351)/S(20)*(S(1) - S(2)*x)**(S(15)/S(2)) - S(1215)/S(1088)*(S(1) - S(2)*x)**(S(17)/S(2))],
        [(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(6)/(S(3) + S(5)*x), x, S(6), S(2)/S(234375)*(S(1) - S(2)*x)**(S(3)/S(2)) - S(167115051)/S(2500000)*(S(1) - S(2)*x)**(S(5)/S(2)) + S(70752609)/S(700000)*(S(1) - S(2)*x)**(S(7)/S(2)) - S(665817)/S(10000)*(S(1) - S(2)*x)**(S(9)/S(2)) + S(507627)/S(22000)*(S(1) - S(2)*x)**(S(11)/S(2)) - S(43011)/S(10400)*(S(1) - S(2)*x)**(S(13)/S(2)) + S(243)/S(800)*(S(1) - S(2)*x)**(S(15)/S(2)) - S(22)/S(390625)*arctanh(sqrt(S(5)/S(11))*sqrt(S(1) - S(2)*x))*sqrt(S(11)/S(5)) + S(22)/S(390625)*sqrt(S(1) - S(2)*x)],
        [(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(5)/(S(3) + S(5)*x), x, S(6), S(2)/S(46875)*(S(1) - S(2)*x)**(S(3)/S(2)) - S(4774713)/S(250000)*(S(1) - S(2)*x)**(S(5)/S(2)) + S(806121)/S(35000)*(S(1) - S(2)*x)**(S(7)/S(2)) - S(5673)/S(500)*(S(1) - S(2)*x)**(S(9)/S(2)) + S(5751)/S(2200)*(S(1) - S(2)*x)**(S(11)/S(2)) - S(243)/S(1040)*(S(1) - S(2)*x)**(S(13)/S(2)) - S(22)/S(78125)*arctanh(sqrt(S(5)/S(11))*sqrt(S(1) - S(2)*x))*sqrt(S(11)/S(5)) + S(22)/S(78125)*sqrt(S(1) - S(2)*x)],
        [(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(6)*(S(3) + S(5)*x), x, S(2), - S(184877)/S(128)*(S(1) - S(2)*x)**(S(7)/S(2)) + S(3916031)/S(1152)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(5078115)/S(1408)*(S(1) - S(2)*x)**(S(11)/S(2)) + S(3658095)/S(1664)*(S(1) - S(2)*x)**(S(13)/S(2)) - S(105399)/S(128)*(S(1) - S(2)*x)**(S(15)/S(2)) + S(409941)/S(2176)*(S(1) - S(2)*x)**(S(17)/S(2)) - S(59049)/S(2432)*(S(1) - S(2)*x)**(S(19)/S(2)) + S(1215)/S(896)*(S(1) - S(2)*x)**(S(21)/S(2))],
        [(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(5)*(S(3) + S(5)*x), x, S(2), - S(26411)/S(64)*(S(1) - S(2)*x)**(S(7)/S(2)) + S(60025)/S(72)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(519645)/S(704)*(S(1) - S(2)*x)**(S(11)/S(2)) + S(37485)/S(104)*(S(1) - S(2)*x)**(S(13)/S(2)) - S(6489)/S(64)*(S(1) - S(2)*x)**(S(15)/S(2)) + S(1053)/S(68)*(S(1) - S(2)*x)**(S(17)/S(2)) - S(1215)/S(1216)*(S(1) - S(2)*x)**(S(19)/S(2))],
        [(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(4)/(S(3) + S(5)*x), x, S(7), S(22)/S(46875)*(S(1) - S(2)*x)**(S(3)/S(2)) + S(2)/S(15625)*(S(1) - S(2)*x)**(S(5)/S(2)) - S(136419)/S(35000)*(S(1) - S(2)*x)**(S(7)/S(2)) + S(3819)/S(1000)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(2889)/S(2200)*(S(1) - S(2)*x)**(S(11)/S(2)) + S(81)/S(520)*(S(1) - S(2)*x)**(S(13)/S(2)) - S(242)/S(78125)*arctanh(sqrt(S(5)/S(11))*sqrt(S(1) - S(2)*x))*sqrt(S(11)/S(5)) + S(242)/S(78125)*sqrt(S(1) - S(2)*x)],
        [(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(3)/(S(3) + S(5)*x), x, S(7), S(22)/S(9375)*(S(1) - S(2)*x)**(S(3)/S(2)) + S(2)/S(3125)*(S(1) - S(2)*x)**(S(5)/S(2)) - S(3897)/S(3500)*(S(1) - S(2)*x)**(S(7)/S(2)) + S(18)/S(25)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(27)/S(220)*(S(1) - S(2)*x)**(S(11)/S(2)) - S(242)/S(15625)*arctanh(sqrt(S(5)/S(11))*sqrt(S(1) - S(2)*x))*sqrt(S(11)/S(5)) + S(242)/S(15625)*sqrt(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(5)*(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x), x, S(2), S(60025)/S(24)*(S(1) - S(2)*x)**(S(3)/S(2)) - S(103929)/S(64)*(S(1) - S(2)*x)**(S(5)/S(2)) + S(5355)/S(8)*(S(1) - S(2)*x)**(S(7)/S(2)) - S(10815)/S(64)*(S(1) - S(2)*x)**(S(9)/S(2)) + S(1053)/S(44)*(S(1) - S(2)*x)**(S(11)/S(2)) - S(1215)/S(832)*(S(1) - S(2)*x)**(S(13)/S(2)) - S(184877)/S(64)*sqrt(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(4)*(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x), x, S(2), S(57281)/S(96)*(S(1) - S(2)*x)**(S(3)/S(2)) - S(24843)/S(80)*(S(1) - S(2)*x)**(S(5)/S(2)) + S(1539)/S(16)*(S(1) - S(2)*x)**(S(7)/S(2)) - S(519)/S(32)*(S(1) - S(2)*x)**(S(9)/S(2)) + S(405)/S(352)*(S(1) - S(2)*x)**(S(11)/S(2)) - S(26411)/S(32)*sqrt(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(5)/((S(3) + S(5)*x)*sqrt(S(1) - S(2)*x)), x, S(4), S(268707)/S(5000)*(S(1) - S(2)*x)**(S(3)/S(2)) - S(51057)/S(2500)*(S(1) - S(2)*x)**(S(5)/S(2)) + S(5751)/S(1400)*(S(1) - S(2)*x)**(S(7)/S(2)) - S(27)/S(80)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(2)/S(3125)*arctanh(sqrt(S(5)/S(11))*sqrt(S(1) - S(2)*x))/sqrt(S(55)) - S(4774713)/S(50000)*sqrt(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(7)*(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(3)/S(2)), x, S(2), - S(7882483)/S(128)*(S(1) - S(2)*x)**(S(3)/S(2)) + S(4084101)/S(128)*(S(1) - S(2)*x)**(S(5)/S(2)) - S(787185)/S(64)*(S(1) - S(2)*x)**(S(7)/S(2)) + S(422919)/S(128)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(821583)/S(1408)*(S(1) - S(2)*x)**(S(11)/S(2)) + S(101331)/S(1664)*(S(1) - S(2)*x)**(S(13)/S(2)) - S(729)/S(256)*(S(1) - S(2)*x)**(S(15)/S(2)) + S(9058973)/S(256)/sqrt(S(1) - S(2)*x) + S(15647317)/S(128)*sqrt(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(6)*(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(3)/S(2)), x, S(2), - S(1692705)/S(128)*(S(1) - S(2)*x)**(S(3)/S(2)) + S(731619)/S(128)*(S(1) - S(2)*x)**(S(5)/S(2)) - S(225855)/S(128)*(S(1) - S(2)*x)**(S(7)/S(2)) + S(45549)/S(128)*(S(1) - S(2)*x)**(S(9)/S(2)) - S(59049)/S(1408)*(S(1) - S(2)*x)**(S(11)/S(2)) + S(3645)/S(1664)*(S(1) - S(2)*x)**(S(13)/S(2)) + S(1294139)/S(128)/sqrt(S(1) - S(2)*x) + S(3916031)/S(128)*sqrt(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(5)*(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(5)/S(2)), x, S(2), S(184877)/S(192)/(S(1) - S(2)*x)**(S(3)/S(2)) + S(12495)/S(8)*(S(1) - S(2)*x)**(S(3)/S(2)) - S(19467)/S(64)*(S(1) - S(2)*x)**(S(5)/S(2)) + S(1053)/S(28)*(S(1) - S(2)*x)**(S(7)/S(2)) - S(135)/S(64)*(S(1) - S(2)*x)**(S(9)/S(2)) + ( - S(60025)/S(8))/sqrt(S(1) - S(2)*x) - S(519645)/S(64)*sqrt(S(1) - S(2)*x)],
        [(S(2) + S(3)*x)**S(4)*(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(5)/S(2)), x, S(2), S(26411)/S(96)/(S(1) - S(2)*x)**(S(3)/S(2)) + S(3591)/S(16)*(S(1) - S(2)*x)**(S(3)/S(2)) - S(4671)/S(160)*(S(1) - S(2)*x)**(S(5)/S(2)) + S(405)/S(224)*(S(1) - S(2)*x)**(S(7)/S(2)) + ( - S(57281)/S(32))/sqrt(S(1) - S(2)*x) - S(24843)/S(16)*sqrt(S(1) - S(2)*x)],
        [(A + B*x)*(d + e*x)**(S(5)/S(2))*sqrt(a + b*x), x, S(7), - S(1)/S(48)*(b*d - a*e)*(S(3)*b*B*d - S(10)*A*b*e + S(7)*a*B*e)*(a + b*x)**(S(3)/S(2))*(d + e*x)**(S(3)/S(2))/(b**S(3)*e) - S(1)/S(40)*(S(3)*b*B*d - S(10)*A*b*e + S(7)*a*B*e)*(a + b*x)**(S(3)/S(2))*(d + e*x)**(S(5)/S(2))/(b**S(2)*e) + S(1)/S(5)*B*(a + b*x)**(S(3)/S(2))*(d + e*x)**(S(7)/S(2))/(b*e) + S(1)/S(128)*(b*d - a*e)**S(4)*(S(3)*b*B*d - S(10)*A*b*e + S(7)*a*B*e)*arctanh(sqrt(e)*sqrt(a + b*x)/(sqrt(b)*sqrt(d + e*x)))/(b**(S(9)/S(2))*e**(S(5)/S(2))) - S(1)/S(64)*(b*d - a*e)**S(2)*(S(3)*b*B*d - S(10)*A*b*e + S(7)*a*B*e)*(a + b*x)**(S(3)/S(2))*sqrt(d + e*x)/(b**S(4)*e) - S(1)/S(128)*(b*d - a*e)**S(3)*(S(3)*b*B*d - S(10)*A*b*e + S(7)*a*B*e)*sqrt(a + b*x)*sqrt(d + e*x)/(b**S(4)*e**S(2))],
        [(A + B*x)*(d + e*x)**(S(3)/S(2))*sqrt(a + b*x), x, S(6), - S(1)/S(24)*(S(3)*b*B*d - S(8)*A*b*e + S(5)*a*B*e)*(a + b*x)**(S(3)/S(2))*(d + e*x)**(S(3)/S(2))/(b**S(2)*e) + S(1)/S(4)*B*(a + b*x)**(S(3)/S(2))*(d + e*x)**(S(5)/S(2))/(b*e) + S(1)/S(64)*(b*d - a*e)**S(3)*(S(3)*b*B*d - S(8)*A*b*e + S(5)*a*B*e)*arctanh(sqrt(e)*sqrt(a + b*x)/(sqrt(b)*sqrt(d + e*x)))/(b**(S(7)/S(2))*e**(S(5)/S(2))) - S(1)/S(32)*(b*d - a*e)*(S(3)*b*B*d - S(8)*A*b*e + S(5)*a*B*e)*(a + b*x)**(S(3)/S(2))*sqrt(d + e*x)/(b**S(3)*e) - S(1)/S(64)*(b*d - a*e)**S(2)*(S(3)*b*B*d - S(8)*A*b*e + S(5)*a*B*e)*sqrt(a + b*x)*sqrt(d + e*x)/(b**S(3)*e**S(2))],
        [(A + B*x)*(d + e*x)**(S(5)/S(2))/sqrt(a + b*x), x, S(6), - S(5)/S(64)*(b*d - a*e)**S(3)*(b*B*d - S(8)*A*b*e + S(7)*a*B*e)*arctanh(sqrt(e)*sqrt(a + b*x)/(sqrt(b)*sqrt(d + e*x)))/(b**(S(9)/S(2))*e**(S(3)/S(2))) - S(5)/S(96)*(b*d - a*e)*(b*B*d - S(8)*A*b*e + S(7)*a*B*e)*(d + e*x)**(S(3)/S(2))*sqrt(a + b*x)/(b**S(3)*e) - S(1)/S(24)*(b*B*d - S(8)*A*b*e + S(7)*a*B*e)*(d + e*x)**(S(5)/S(2))*sqrt(a + b*x)/(b**S(2)*e) + S(1)/S(4)*B*(d + e*x)**(S(7)/S(2))*sqrt(a + b*x)/(b*e) - S(5)/S(64)*(b*d - a*e)**S(2)*(b*B*d - S(8)*A*b*e + S(7)*a*B*e)*sqrt(a + b*x)*sqrt(d + e*x)/(b**S(4)*e)],
        [(A + B*x)*(d + e*x)**(S(3)/S(2))/sqrt(a + b*x), x, S(5), - S(1)/S(8)*(b*d - a*e)**S(2)*(b*B*d - S(6)*A*b*e + S(5)*a*B*e)*arctanh(sqrt(e)*sqrt(a + b*x)/(sqrt(b)*sqrt(d + e*x)))/(b**(S(7)/S(2))*e**(S(3)/S(2))) - S(1)/S(12)*(b*B*d - S(6)*A*b*e + S(5)*a*B*e)*(d + e*x)**(S(3)/S(2))*sqrt(a + b*x)/(b**S(2)*e) + S(1)/S(3)*B*(d + e*x)**(S(5)/S(2))*sqrt(a + b*x)/(b*e) - S(1)/S(8)*(b*d - a*e)*(b*B*d - S(6)*A*b*e + S(5)*a*B*e)*sqrt(a + b*x)*sqrt(d + e*x)/(b**S(3)*e)],
        [(S(2) + S(3)*x)**S(4)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x), x, S(7), - S(333)/S(2000)*(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(2)*(S(3) + S(5)*x)**(S(3)/S(2)) - S(1)/S(20)*(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(3)*(S(3) + S(5)*x)**(S(3)/S(2)) - S(7)/S(640000)*(S(1) - S(2)*x)**(S(3)/S(2))*(S(3) + S(5)*x)**(S(3)/S(2))*(S(231223) + S(140652)*x) + S(4122385421)/S(51200000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) - S(34069301)/S(5120000)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) + S(374762311)/S(51200000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(3)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x), x, S(6), - S(3)/S(50)*(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(2)*(S(3) + S(5)*x)**(S(3)/S(2)) - S(21)/S(16000)*(S(1) - S(2)*x)**(S(3)/S(2))*(S(3) + S(5)*x)**(S(3)/S(2))*(S(731) + S(444)*x) + S(39142411)/S(1280000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) - S(323491)/S(128000)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) + S(3558401)/S(1280000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(3)*sqrt(S(1) - S(2)*x)/sqrt(S(3) + S(5)*x), x, S(5), S(525371)/S(64000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) - S(3)/S(40)*(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(2)*sqrt(S(3) + S(5)*x) - S(21)/S(6400)*(S(1) - S(2)*x)**(S(3)/S(2))*(S(335) + S(216)*x)*sqrt(S(3) + S(5)*x) + S(47761)/S(64000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(2)*sqrt(S(1) - S(2)*x)/sqrt(S(3) + S(5)*x), x, S(5), S(3047)/S(800)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) - S(23)/S(80)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) - S(1)/S(10)*(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)*sqrt(S(3) + S(5)*x) + S(277)/S(800)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x), x, S(7), - S(1)/S(20)*(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(2)*(S(3) + S(5)*x)**(S(3)/S(2)) - S(1)/S(160000)*(S(1) - S(2)*x)**(S(5)/S(2))*(S(3) + S(5)*x)**(S(3)/S(2))*(S(88987) + S(63120)*x) + S(452517373)/S(25600000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(3739813)/S(7680000)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) - S(339983)/S(384000)*(S(1) - S(2)*x)**(S(5)/S(2))*sqrt(S(3) + S(5)*x) + S(41137943)/S(25600000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(2)*sqrt(S(3) + S(5)*x), x, S(7), - S(567)/S(4000)*(S(1) - S(2)*x)**(S(5)/S(2))*(S(3) + S(5)*x)**(S(3)/S(2)) - S(3)/S(50)*(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)*(S(3) + S(5)*x)**(S(3)/S(2)) + S(5487713)/S(640000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(45353)/S(192000)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) - S(4123)/S(9600)*(S(1) - S(2)*x)**(S(5)/S(2))*sqrt(S(3) + S(5)*x) + S(498883)/S(640000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(3)/sqrt(S(3) + S(5)*x), x, S(6), S(18648399)/S(3200000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(51373)/S(320000)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) - S(3)/S(50)*(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(2)*sqrt(S(3) + S(5)*x) - S(3)/S(80000)*(S(1) - S(2)*x)**(S(5)/S(2))*(S(14629) + S(11580)*x)*sqrt(S(3) + S(5)*x) + S(1695309)/S(3200000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**(S(3)/S(2))*(S(2) + S(3)*x)**S(2)/sqrt(S(3) + S(5)*x), x, S(6), S(109263)/S(32000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(301)/S(3200)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) - S(119)/S(800)*(S(1) - S(2)*x)**(S(5)/S(2))*sqrt(S(3) + S(5)*x) - S(3)/S(40)*(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)*sqrt(S(3) + S(5)*x) + S(9933)/S(32000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x), x, S(8), - S(3)/S(70)*(S(1) - S(2)*x)**(S(7)/S(2))*(S(2) + S(3)*x)**S(2)*(S(3) + S(5)*x)**(S(3)/S(2)) - S(3)/S(280000)*(S(1) - S(2)*x)**(S(7)/S(2))*(S(3) + S(5)*x)**(S(3)/S(2))*(S(33857) + S(26700)*x) + S(3735929329)/S(256000000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(30875449)/S(76800000)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) + S(2806859)/S(19200000)*(S(1) - S(2)*x)**(S(5)/S(2))*sqrt(S(3) + S(5)*x) - S(255169)/S(640000)*(S(1) - S(2)*x)**(S(7)/S(2))*sqrt(S(3) + S(5)*x) + S(339629939)/S(256000000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(2)*sqrt(S(3) + S(5)*x), x, S(8), - S(193)/S(2000)*(S(1) - S(2)*x)**(S(7)/S(2))*(S(3) + S(5)*x)**(S(3)/S(2)) - S(1)/S(20)*(S(1) - S(2)*x)**(S(7)/S(2))*(S(2) + S(3)*x)*(S(3) + S(5)*x)**(S(3)/S(2)) + S(105254149)/S(12800000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(869869)/S(3840000)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) + S(79079)/S(960000)*(S(1) - S(2)*x)**(S(5)/S(2))*sqrt(S(3) + S(5)*x) - S(7189)/S(32000)*(S(1) - S(2)*x)**(S(7)/S(2))*sqrt(S(3) + S(5)*x) + S(9568559)/S(12800000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(4)/sqrt(S(3) + S(5)*x), x, S(8), S(12679836719)/S(1280000000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(104792039)/S(384000000)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) + S(9526549)/S(96000000)*(S(1) - S(2)*x)**(S(5)/S(2))*sqrt(S(3) + S(5)*x) - S(271)/S(2800)*(S(1) - S(2)*x)**(S(7)/S(2))*(S(2) + S(3)*x)**S(2)*sqrt(S(3) + S(5)*x) - S(3)/S(70)*(S(1) - S(2)*x)**(S(7)/S(2))*(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x) - S(1)/S(22400000)*(S(1) - S(2)*x)**(S(7)/S(2))*(S(12923401) + S(11603280)*x)*sqrt(S(3) + S(5)*x) + S(1152712429)/S(1280000000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(1) - S(2)*x)**(S(5)/S(2))*(S(2) + S(3)*x)**S(3)/sqrt(S(3) + S(5)*x), x, S(7), S(368012183)/S(64000000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(3041423)/S(19200000)*(S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x) + S(276493)/S(4800000)*(S(1) - S(2)*x)**(S(5)/S(2))*sqrt(S(3) + S(5)*x) - S(1)/S(20)*(S(1) - S(2)*x)**(S(7)/S(2))*(S(2) + S(3)*x)**S(2)*sqrt(S(3) + S(5)*x) - S(1)/S(160000)*(S(1) - S(2)*x)**(S(7)/S(2))*(S(52951) + S(47280)*x)*sqrt(S(3) + S(5)*x) + S(33455653)/S(64000000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(4)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x), x, S(6), S(1067352517)/S(2560000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) - S(987)/S(4000)*(S(2) + S(3)*x)**S(2)*(S(3) + S(5)*x)**(S(3)/S(2))*sqrt(S(1) - S(2)*x) - S(3)/S(50)*(S(2) + S(3)*x)**S(3)*(S(3) + S(5)*x)**(S(3)/S(2))*sqrt(S(1) - S(2)*x) - S(21)/S(640000)*(S(3) + S(5)*x)**(S(3)/S(2))*(S(194923) + S(92040)*x)*sqrt(S(1) - S(2)*x) - S(97032047)/S(2560000)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x), x, S(5), S(677017)/S(5120)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) - S(3)/S(40)*(S(2) + S(3)*x)**S(2)*(S(3) + S(5)*x)**(S(3)/S(2))*sqrt(S(1) - S(2)*x) - S(3)/S(1280)*(S(3) + S(5)*x)**(S(3)/S(2))*(S(865) + S(408)*x)*sqrt(S(1) - S(2)*x) - S(61547)/S(5120)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(4)/(sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)), x, S(5), S(10866247)/S(128000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) - S(259)/S(800)*(S(2) + S(3)*x)**S(2)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) - S(3)/S(40)*(S(2) + S(3)*x)**S(3)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) - S(7)/S(128000)*(S(187559) + S(77820)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(3)/(sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)), x, S(4), S(44437)/S(1600)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) - S(1)/S(10)*(S(2) + S(3)*x)**S(2)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) - S(1)/S(1600)*(S(5363) + S(2220)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(5)*sqrt(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(3)/S(2)), x, S(7), - S(35439958001)/S(5120000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + (S(2) + S(3)*x)**S(5)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x) + S(847637)/S(32000)*(S(2) + S(3)*x)**S(2)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) + S(10389)/S(1600)*(S(2) + S(3)*x)**S(3)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) + S(33)/S(20)*(S(2) + S(3)*x)**S(4)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) + S(49)/S(5120000)*(S(87394471) + S(36265980)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(4)*sqrt(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(3)/S(2)), x, S(6), - S(92108287)/S(51200)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + (S(2) + S(3)*x)**S(4)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x) + S(2203)/S(320)*(S(2) + S(3)*x)**S(2)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) + S(27)/S(16)*(S(2) + S(3)*x)**S(3)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) + S(1)/S(51200)*(S(11129753) + S(4618500)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(5)/((S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x)), x, S(6), - S(291096141)/S(256000)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(7)/S(11)*(S(2) + S(3)*x)**S(4)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x) + S(76587)/S(17600)*(S(2) + S(3)*x)**S(2)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) + S(939)/S(880)*(S(2) + S(3)*x)**S(3)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) + S(21)/S(2816000)*(S(18424549) + S(7645620)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(4)/((S(1) - S(2)*x)**(S(3)/S(2))*sqrt(S(3) + S(5)*x)), x, S(5), - S(184641)/S(640)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(7)/S(11)*(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x) + S(243)/S(220)*(S(2) + S(3)*x)**S(2)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) + S(9)/S(7040)*(S(27269) + S(11316)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(4)*sqrt(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(5)/S(2)), x, S(6), S(13246251)/S(6400)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(1)/S(3)*(S(2) + S(3)*x)**S(4)*sqrt(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(3)/S(2)) - S(299)/S(66)*(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x) - S(697)/S(88)*(S(2) + S(3)*x)**S(2)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) - S(1)/S(70400)*(S(17606479) + S(7306140)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(5)/S(2)), x, S(5), S(126513)/S(320)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(1)/S(3)*(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(3)/S(2)) - S(233)/S(66)*(S(2) + S(3)*x)**S(2)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x) - S(1)/S(3520)*(S(168157) + S(69780)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(5)/((S(1) - S(2)*x)**(S(5)/S(2))*sqrt(S(3) + S(5)*x)), x, S(6), S(8261577)/S(6400)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(7)/S(33)*(S(2) + S(3)*x)**S(4)*sqrt(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(3)/S(2)) - S(2051)/S(726)*(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x) - S(23909)/S(4840)*(S(2) + S(3)*x)**S(2)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x) - S(1)/S(774400)*(S(120791143) + S(50124540)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(S(2) + S(3)*x)**S(4)/((S(1) - S(2)*x)**(S(5)/S(2))*sqrt(S(3) + S(5)*x)), x, S(5), S(392283)/S(1600)*arcsin(sqrt(S(2)/S(11))*sqrt(S(3) + S(5)*x))/sqrt(S(10)) + S(7)/S(33)*(S(2) + S(3)*x)**S(3)*sqrt(S(3) + S(5)*x)/(S(1) - S(2)*x)**(S(3)/S(2)) - S(1589)/S(726)*(S(2) + S(3)*x)**S(2)*sqrt(S(3) + S(5)*x)/sqrt(S(1) - S(2)*x) - S(1)/S(193600)*(S(5735477) + S(2380020)*x)*sqrt(S(1) - S(2)*x)*sqrt(S(3) + S(5)*x)],
        [(c + d*x)**(S(1)/S(2))/(x**S(2)*(a + b*x)**S(2)), x, S(7), (S(4)*b*c - a*d)*arctanh(sqrt(c + d*x)/sqrt(c))/(a**S(3)*sqrt(c)) - (S(4)*b*c - S(3)*a*d)*arctanh(sqrt(b)*sqrt(c + d*x)/sqrt(b*c - a*d))*sqrt(b)/(a**S(3)*sqrt(b*c - a*d)) - S(2)*b*sqrt(c + d*x)/(a**S(2)*(a + b*x)) - sqrt(c + d*x)/(a*x*(a + b*x))],
        [S(1)/(x**S(2)*(a + b*x)**S(2)*(c + d*x)**(S(1)/S(2))), x, S(7), (S(4)*b*c + a*d)*arctanh(sqrt(c + d*x)/sqrt(c))/(a**S(3)*c**(S(3)/S(2))) - b**(S(3)/S(2))*(S(4)*b*c - S(5)*a*d)*arctanh(sqrt(b)*sqrt(c + d*x)/sqrt(b*c - a*d))/(a**S(3)*(b*c - a*d)**(S(3)/S(2))) - b*(S(2)*b*c - a*d)*sqrt(c + d*x)/(a**S(2)*c*(b*c - a*d)*(a + b*x)) - sqrt(c + d*x)/(a*c*x*(a + b*x))],
        [S(1)/(x**S(2)*(a + b*x)**S(2)*(c + d*x)**(S(3)/S(2))), x, S(8), (S(4)*b*c + S(3)*a*d)*arctanh(sqrt(c + d*x)/sqrt(c))/(a**S(3)*c**(S(5)/S(2))) - b**(S(5)/S(2))*(S(4)*b*c - S(7)*a*d)*arctanh(sqrt(b)*sqrt(c + d*x)/sqrt(b*c - a*d))/(a**S(3)*(b*c - a*d)**(S(5)/S(2))) - d*(S(2)*b**S(2)*c**S(2) - S(2)*a*b*c*d + S(3)*a**S(2)*d**S(2))/(a**S(2)*c**S(2)*(b*c - a*d)**S(2)*sqrt(c + d*x)) - b*(S(2)*b*c - a*d)/(a**S(2)*c*(b*c - a*d)*(a + b*x)*sqrt(c + d*x)) + ( - S(1))/(a*c*x*(a + b*x)*sqrt(c + d*x))],
        [x**S(3)*(c + d*x)**(S(3)/S(2))/(a + b*x)**(S(3)/S(2)), x, S(6), S(3)/S(64)*(b*c - a*d)*(b**S(3)*c**S(3) + S(5)*a*b**S(2)*c**S(2)*d + S(35)*a**S(2)*b*c*d**S(2) - S(105)*a**S(3)*d**S(3))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(11)/S(2))*d**(S(5)/S(2))) - S(2)*x**S(3)*(c + d*x)**(S(3)/S(2))/(b*sqrt(a + b*x)) + S(9)/S(4)*x**S(2)*(c + d*x)**(S(3)/S(2))*sqrt(a + b*x)/b**S(2) - S(1)/S(32)*(c + d*x)**(S(3)/S(2))*(S(3)*b**S(2)*c**S(2) + S(14)*a*b*c*d - S(105)*a**S(2)*d**S(2) - S(4)*b*d*(b*c - S(21)*a*d)*x)*sqrt(a + b*x)/(b**S(4)*d**S(2)) + S(3)/S(64)*(b**S(3)*c**S(3) + S(5)*a*b**S(2)*c**S(2)*d + S(35)*a**S(2)*b*c*d**S(2) - S(105)*a**S(3)*d**S(3))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(5)*d**S(2))],
        [x**S(2)*(c + d*x)**(S(3)/S(2))/(a + b*x)**(S(3)/S(2)), x, S(6), - S(1)/S(8)*(b*c - a*d)*(b**S(2)*c**S(2) + S(10)*a*b*c*d - S(35)*a**S(2)*d**S(2))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(9)/S(2))*d**(S(3)/S(2))) - S(2)*a**S(2)*(c + d*x)**(S(5)/S(2))/(b**S(2)*(b*c - a*d)*sqrt(a + b*x)) - S(1)/S(12)*(S(10)*a*c + b*c**S(2)/d - S(35)*a**S(2)*d/b)*(c + d*x)**(S(3)/S(2))*sqrt(a + b*x)/(b**S(2)*(b*c - a*d)) + S(1)/S(3)*(c + d*x)**(S(5)/S(2))*sqrt(a + b*x)/(b**S(2)*d) - S(1)/S(8)*(b**S(2)*c**S(2) + S(10)*a*b*c*d - S(35)*a**S(2)*d**S(2))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(4)*d)],
        [x**S(3)*(c + d*x)**(S(5)/S(2))/(a + b*x)**(S(5)/S(2)), x, S(7), - S(2)/S(3)*x**S(3)*(c + d*x)**(S(5)/S(2))/(b*(a + b*x)**(S(3)/S(2))) - S(5)/S(64)*(b*c - a*d)*(b**S(3)*c**S(3) + S(21)*a*b**S(2)*c**S(2)*d - S(189)*a**S(2)*b*c*d**S(2) + S(231)*a**S(3)*d**S(3))*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(13)/S(2))*d**(S(3)/S(2))) - S(2)/S(3)*(S(6)*b*c - S(11)*a*d)*x**S(2)*(c + d*x)**(S(5)/S(2))/(b**S(2)*(b*c - a*d)*sqrt(a + b*x)) - S(5)/S(96)*(b**S(3)*c**S(3) + S(21)*a*b**S(2)*c**S(2)*d - S(189)*a**S(2)*b*c*d**S(2) + S(231)*a**S(3)*d**S(3))*(c + d*x)**(S(3)/S(2))*sqrt(a + b*x)/(b**S(5)*d*(b*c - a*d)) + S(1)/S(24)*(c + d*x)**(S(5)/S(2))*(S(5)*b**S(2)*c**S(2) - S(156)*a*b*c*d + S(231)*a**S(2)*d**S(2) + S(2)*b*d*(S(59)*b*c - S(99)*a*d)*x)*sqrt(a + b*x)/(b**S(4)*d*(b*c - a*d)) - S(5)/S(64)*(b**S(3)*c**S(3) + S(21)*a*b**S(2)*c**S(2)*d - S(189)*a**S(2)*b*c*d**S(2) + S(231)*a**S(3)*d**S(3))*sqrt(a + b*x)*sqrt(c + d*x)/(b**S(6)*d)],
        [x**S(2)/((a + b*x)**(S(5)/S(2))*(c + d*x)**(S(1)/S(2))), x, S(4), S(2)*arctanh(sqrt(d)*sqrt(a + b*x)/(sqrt(b)*sqrt(c + d*x)))/(b**(S(5)/S(2))*sqrt(d)) - S(2)/S(3)*a**S(2)*sqrt(c + d*x)/(b**S(2)*(b*c - a*d)*(a + b*x)**(S(3)/S(2))) + S(4)/S(3)*a*(S(3)*b*c - S(2)*a*d)*sqrt(c + d*x)/(b**S(2)*(b*c - a*d)**S(2)*sqrt(a + b*x))],
        [x*sqrt(a + b*x)/sqrt( - a - b*x), x, S(2), S(1)/S(2)*x**S(2)*sqrt(a + b*x)/sqrt( - a - b*x)],
        [(c + d*x)**(S(3)/S(2))/(x*(a + b*x)**S(2)), x, S(6), - S(2)*c**(S(3)/S(2))*arctanh(sqrt(c + d*x)/sqrt(c))/a**S(2) + (S(2)*b*c + a*d)*arctanh(sqrt(b)*sqrt(c + d*x)/sqrt(b*c - a*d))*sqrt(b*c - a*d)/(a**S(2)*b**(S(3)/S(2))) + (b*c - a*d)*sqrt(c + d*x)/(a*b*(a + b*x))],
    ]
    for i in test:
        r = rubi_integrate(i[0], i[1])
        if len(i) == 5:
            assert rubi_test(r, i[1], i[3], expand=True, _diff=True, _numerical=True) or rubi_test(r, i[1], i[4], expand=True, _diff=True, _numerical=True)
        else:
            assert rubi_test(r, i[1], i[3], expand=True, _diff=True, _numerical=True)

def test_simplify():
    test = [
        [x**S(3)*(a + b*x)**S(2)*(c + d*x)**S(16), x, S(2), - S(1)/S(17)*c**S(3)*(b*c - a*d)**S(2)*(c + d*x)**S(17)/d**S(6) + S(1)/S(18)*c**S(2)*(S(5)*b*c - S(3)*a*d)*(b*c - a*d)*(c + d*x)**S(18)/d**S(6) - S(1)/S(19)*c*(S(10)*b**S(2)*c**S(2) - S(12)*a*b*c*d + S(3)*a**S(2)*d**S(2))*(c + d*x)**S(19)/d**S(6) + S(1)/S(20)*(S(10)*b**S(2)*c**S(2) - S(8)*a*b*c*d + a**S(2)*d**S(2))*(c + d*x)**S(20)/d**S(6) - S(1)/S(21)*b*(S(5)*b*c - S(2)*a*d)*(c + d*x)**S(21)/d**S(6) + S(1)/S(22)*b**S(2)*(c + d*x)**S(22)/d**S(6)],
        [x**S(5)/((a + b*x)**S(2)*(c + d*x)**S(2)), x, S(2), - S(2)*(b*c + a*d)*x/(b**S(3)*d**S(3)) + S(1)/S(2)*x**S(2)/(b**S(2)*d**S(2)) + a**S(5)/(b**S(4)*(b*c - a*d)**S(2)*(a + b*x)) + c**S(5)/(d**S(4)*(b*c - a*d)**S(2)*(c + d*x)) + a**S(4)*(S(5)*b*c - S(3)*a*d)*log(a + b*x)/(b**S(4)*(b*c - a*d)**S(3)) + c**S(4)*(S(3)*b*c - S(5)*a*d)*log(c + d*x)/(d**S(4)*(b*c - a*d)**S(3))],
        [x**S(5)/((a + b*x)**S(2)*(c + d*x)**S(2)), x, S(2), - S(2)*(b*c + a*d)*x/(b**S(3)*d**S(3)) + S(1)/S(2)*x**S(2)/(b**S(2)*d**S(2)) + a**S(5)/(b**S(4)*(b*c - a*d)**S(2)*(a + b*x)) + c**S(5)/(d**S(4)*(b*c - a*d)**S(2)*(c + d*x)) + a**S(4)*(S(5)*b*c - S(3)*a*d)*log(a + b*x)/(b**S(4)*(b*c - a*d)**S(3)) + c**S(4)*(S(3)*b*c - S(5)*a*d)*log(c + d*x)/(d**S(4)*(b*c - a*d)**S(3))],
        [x**S(4)/((a + b*x)*(c + d*x)), x, S(2), (b**S(2)*c**S(2) + a*b*c*d + a**S(2)*d**S(2))*x/(b**S(3)*d**S(3)) - S(1)/S(2)*(b*c + a*d)*x**S(2)/(b**S(2)*d**S(2)) + S(1)/S(3)*x**S(3)/(b*d) + a**S(4)*log(a + b*x)/(b**S(4)*(b*c - a*d)) - c**S(4)*log(c + d*x)/(d**S(4)*(b*c - a*d))],
        [(a + b*x)*(A + B*x)*(d + e*x)**S(4), x, S(2), S(1)/S(5)*(b*d - a*e)*(B*d - A*e)*(d + e*x)**S(5)/e**S(3) - S(1)/S(6)*(S(2)*b*B*d - A*b*e - a*B*e)*(d + e*x)**S(6)/e**S(3) + S(1)/S(7)*b*B*(d + e*x)**S(7)/e**S(3)],
        [(a + b*x)**S(3)*(c + d*x)**S(3)*(e + f*x)**S(3), x, S(2), S(1)/S(4)*(b*c - a*d)**S(3)*(b*e - a*f)**S(3)*(a + b*x)**S(4)/b**S(7) + S(3)/S(5)*(b*c - a*d)**S(2)*(b*e - a*f)**S(2)*(b*d*e + b*c*f - S(2)*a*d*f)*(a + b*x)**S(5)/b**S(7) + S(1)/S(2)*(b*c - a*d)*(b*e - a*f)*(S(5)*a**S(2)*d**S(2)*f**S(2) - S(5)*a*b*d*f*(d*e + c*f) + b**S(2)*(d**S(2)*e**S(2) + S(3)*c*d*e*f + c**S(2)*f**S(2)))*(a + b*x)**S(6)/b**S(7) + S(1)/S(7)*(b*d*e + b*c*f - S(2)*a*d*f)*(S(10)*a**S(2)*d**S(2)*f**S(2) - S(10)*a*b*d*f*(d*e + c*f) + b**S(2)*(d**S(2)*e**S(2) + S(8)*c*d*e*f + c**S(2)*f**S(2)))*(a + b*x)**S(7)/b**S(7) + S(3)/S(8)*d*f*(S(5)*a**S(2)*d**S(2)*f**S(2) - S(5)*a*b*d*f*(d*e + c*f) + b**S(2)*(d**S(2)*e**S(2) + S(3)*c*d*e*f + c**S(2)*f**S(2)))*(a + b*x)**S(8)/b**S(7) + S(1)/S(3)*d**S(2)*f**S(2)*(b*d*e + b*c*f - S(2)*a*d*f)*(a + b*x)**S(9)/b**S(7) + S(1)/S(10)*d**S(3)*f**S(3)*(a + b*x)**S(10)/b**S(7)],
        [(a + b*x)*(A + B*x)*(d + e*x)**(S(5)/S(2)), x, S(2), S(2)/S(7)*(b*d - a*e)*(B*d - A*e)*(d + e*x)**(S(7)/S(2))/e**S(3) - S(2)/S(9)*(S(2)*b*B*d - A*b*e - a*B*e)*(d + e*x)**(S(9)/S(2))/e**S(3) + S(2)/S(11)*b*B*(d + e*x)**(S(11)/S(2))/e**S(3)],
        [(S(5) - S(4)*x)**S(4)*(S(2) + S(3)*x)**m/(S(1) + S(2)*x)**m, x, S(4), - S(1)/S(45)*(S(88) - m)*(S(5) - S(4)*x)**S(2)*(S(1) + S(2)*x)**(S(1) - m)*(S(2) + S(3)*x)**(S(1) + m) - S(2)/S(15)*(S(5) - S(4)*x)**S(3)*(S(1) + S(2)*x)**(S(1) - m)*(S(2) + S(3)*x)**(S(1) + m) - S(1)/S(1215)*(S(1) + S(2)*x)**(S(1) - m)*(S(2) + S(3)*x)**(S(1) + m)*(S(386850) - S(25441)*m + S(426)*m**S(2) - S(2)*m**S(3) - S(24)*(S(4359) - S(154)*m + m**S(2))*x) + S(1)/S(1215)*S(2)**( - S(1) - m)*(S(3528363) - S(639760)*m + S(29050)*m**S(2) - S(440)*m**S(3) + S(2)*m**S(4))*(S(1) + S(2)*x)**(S(1) - m)*hypergeom([S(1) - m, - m], [S(2) - m], - S(3)*(S(1) + S(2)*x))/(S(1) - m)],
        [(S(5) - S(4)*x)**S(3)*(S(1) + S(2)*x)**( - S(1) - m)*(S(2) + S(3)*x)**m, x, S(3), - S(2)/S(9)*(S(5) - S(4)*x)**S(2)*(S(2) + S(3)*x)**(S(1) + m)/(S(1) + S(2)*x)**m - S(1)/S(27)*(S(2) + S(3)*x)**(S(1) + m)*(S(9261) - S(512)*m + S(4)*m**S(2) - S(4)*(S(109) - S(2)*m)*m*x)/(m*(S(1) + S(2)*x)**m) + S(1)/S(27)*S(2)**( - S(1) - m)*(S(27783) - S(8324)*m + S(390)*m**S(2) - S(4)*m**S(3))*(S(1) + S(2)*x)**(S(1) - m)*hypergeom([S(1) - m, - m], [S(2) - m], - S(3)*(S(1) + S(2)*x))/((S(1) - m)*m)],
        [(a + b*x)**m*(c + d*x)**n*((b*c*f + a*d*f + a*d*f*m + b*c*f*n)/(b*d*(S(2) + m + n)) + f*x)**( - S(3) - m - n), x, S(1), b*d*(S(2) + m + n)*(a + b*x)**(S(1) + m)*(c + d*x)**(S(1) + n)*(f*(a*d*(S(1) + m) + b*c*(S(1) + n))/(b*d*(S(2) + m + n)) + f*x)**( - S(2) - m - n)/((b*c - a*d)**S(2)*f*(S(1) + m)*(S(1) + n))],
        [x**S(3)*(c + d*x)**S(3)/(a + b*x)**S(3), x, S(2), (b*c - a*d)*(b**S(2)*c**S(2) - S(8)*a*b*c*d + S(10)*a**S(2)*d**S(2))*x/b**S(6) + S(3)/S(2)*d*(b*c - S(2)*a*d)*(b*c - a*d)*x**S(2)/b**S(5) + d**S(2)*(b*c - a*d)*x**S(3)/b**S(4) + S(1)/S(4)*d**S(3)*x**S(4)/b**S(3) + S(1)/S(2)*a**S(3)*(b*c - a*d)**S(3)/(b**S(7)*(a + b*x)**S(2)) - S(3)*a**S(2)*(b*c - S(2)*a*d)*(b*c - a*d)**S(2)/(b**S(7)*(a + b*x)) - S(3)*a*(b*c - a*d)*(b**S(2)*c**S(2) - S(5)*a*b*c*d + S(5)*a**S(2)*d**S(2))*log(a + b*x)/b**S(7)],
        [(S(2) + S(3)*x)**S(8)*(S(3) + S(5)*x)/(S(1) - S(2)*x)**S(3), x, S(2), S(63412811)/S(2048)/(S(1) - S(2)*x)**S(2) + ( - S(246239357)/S(1024))/(S(1) - S(2)*x) - S(120864213)/S(256)*x - S(118841283)/S(512)*x**S(2) - S(16042509)/S(128)*x**S(3) - S(7568235)/S(128)*x**S(4) - S(213597)/S(10)*x**S(5) - S(162567)/S(32)*x**S(6) - S(32805)/S(56)*x**S(7) - S(106237047)/S(256)*log(S(1) - S(2)*x)],
    ]

    for i in test:
        r = rubi_integrate(i[0], i[1])
        if len(i) == 5:
            assert rubi_test(r, i[1], i[3], expand=True) or rubi_test(r, i[1], i[4], expand=True)
        else:
            assert rubi_test(r, i[1], i[3], expand=True)

def test_diff():
    test = [
        [(a + b*x)*(e + f*x)**(S(3)/S(2))/(c + d*x), x, S(5), - S(2)/S(3)*(b*c - a*d)*(e + f*x)**(S(3)/S(2))/d**S(2) + S(2)/S(5)*b*(e + f*x)**(S(5)/S(2))/(d*f) + S(2)*(b*c - a*d)*(d*e - c*f)**(S(3)/S(2))*arctanh(sqrt(d)*sqrt(e + f*x)/sqrt(d*e - c*f))/d**(S(7)/S(2)) - S(2)*(b*c - a*d)*(d*e - c*f)*sqrt(e + f*x)/d**S(3)],
        [x**(S(5)/S(2))*(A + B*x)/(a + b*x), x, S(6), - S(2)/S(3)*a*(A*b - a*B)*x**(S(3)/S(2))/b**S(3) + S(2)/S(5)*(A*b - a*B)*x**(S(5)/S(2))/b**S(2) + S(2)/S(7)*B*x**(S(7)/S(2))/b - S(2)*a**(S(5)/S(2))*(A*b - a*B)*arctan(sqrt(b)*sqrt(x)/sqrt(a))/b**(S(9)/S(2)) + S(2)*a**S(2)*(A*b - a*B)*sqrt(x)/b**S(4)],
        [(a + b*x)**S(2)/((c + d*x)**S(2)*sqrt(e + f*x)), x, S(4), (b*c - a*d)*(S(4)*b*d*e - S(3)*b*c*f - a*d*f)*arctanh(sqrt(d)*sqrt(e + f*x)/sqrt(d*e - c*f))/(d**(S(5)/S(2))*(d*e - c*f)**(S(3)/S(2))) + S(2)*b**S(2)*sqrt(e + f*x)/(d**S(2)*f) - (b*c - a*d)**S(2)*sqrt(e + f*x)/(d**S(2)*(d*e - c*f)*(c + d*x))],
    ]

    for i in test:
        r = rubi_integrate(i[0], i[1])
        if len(i) == 5:
            assert rubi_test(r, i[1], i[3], expand=True, _diff=True) or rubi_test(r, i[1], i[4], expand=True, _diff=True)
        else:
            assert rubi_test(r, i[1], i[3], expand=True, _diff=True)
