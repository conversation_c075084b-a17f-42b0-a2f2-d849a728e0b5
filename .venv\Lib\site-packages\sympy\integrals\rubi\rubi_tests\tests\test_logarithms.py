import sys
from sympy.external import import_module
matchpy = import_module("matchpy")

if not matchpy:
    #bin/test will not execute any tests now
    disabled = True

if sys.version_info[:2] < (3, 6):
    disabled = True

from sympy.integrals.rubi.rubimain import rubi_integrate
from sympy.functions import log, sqrt, exp, cos, sin, tan, sec, csc, cot,cosh, sinh, tanh, coth, csch, csch, sech
from sympy.functions.elementary.hyperbolic import (acosh, acsch, asinh, atanh)
from sympy.functions.elementary.trigonometric import (acos, acsc, asin, atan)

from sympy.integrals.rubi.utility_function import (EllipticE, EllipticF, Int, ArcCsch, ArcCsc, Gamma, Factorial, PolyGamma , LogGamma , Subst ,
    hypergeom, rubi_test, AppellF1, EllipticPi, Log, Sqrt, ArcTan, ArcTanh, ArcSin, Arc<PERSON>inh, ArcCosh, ArcTanh, ArcCos, Hypergeometric2F1,)
from sympy.core.singleton import S
from sympy.core import <PERSON><PERSON><PERSON><PERSON>am<PERSON>
from sympy.core.numbers import (E, I, pi)
from sympy.core.symbol import symbols
from sympy.functions.elementary.exponential import (exp, exp_polar)
from sympy.functions.special.error_functions import (Chi, Ci, Ei, erf, erfi, expint, li, Shi, Si)
from sympy.functions.special.hyper import HypergeometricPFQ as hyper
from sympy.functions.special.zeta_functions import polylog
from sympy.integrals.integrals import Integral
from sympy.simplify.simplify import simplify
from sympy.testing.pytest import SKIP

a, b, c, d, e, f, m, n, x, u , k, p, r, s, t, i, j = symbols('a b c d e f m n x u k p r s t i j')
A, B, C, D, a, b, c, d, e, f, g, h, y, z, m, n, p, q, u, v, w, F = symbols('A B C D a b c d e f g h y z m n p q u v w F', )


def test_1():
    assert rubi_test(rubi_integrate((e + f*x)**(p + S(-1))/log(d*(e + f*x)**p), x), x, li(d*(e + f*x)**p)/(d*f*p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((e*g + f*g*x)**(p + S(-1))/log(d*(e + f*x)**p), x), x, (e + f*x)**(-p + S(1))*(e*g + f*g*x)**(p + S(-1))*li(d*(e + f*x)**p)/(d*f*p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**m, x), x, b*f*p*q*(g + h*x)**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), f*(g + h*x)/(-e*h + f*g))/(h*(m + S(1))*(m + S(2))*(-e*h + f*g)) + (a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(m + S(1))/(h*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(4), x), x, -b*p*q*(g + h*x)**S(5)/(S(25)*h) - b*p*q*(g + h*x)**S(4)*(-e*h + f*g)/(S(20)*f*h) - b*p*q*(g + h*x)**S(3)*(-e*h + f*g)**S(2)/(S(15)*f**S(2)*h) - b*p*q*(g + h*x)**S(2)*(-e*h + f*g)**S(3)/(S(10)*f**S(3)*h) - b*p*q*x*(-e*h + f*g)**S(4)/(S(5)*f**S(4)) - b*p*q*(-e*h + f*g)**S(5)*log(e + f*x)/(S(5)*f**S(5)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(5)/(S(5)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(3), x), x, -b*p*q*(g + h*x)**S(4)/(S(16)*h) - b*p*q*(g + h*x)**S(3)*(-e*h + f*g)/(S(12)*f*h) - b*p*q*(g + h*x)**S(2)*(-e*h + f*g)**S(2)/(S(8)*f**S(2)*h) - b*p*q*x*(-e*h + f*g)**S(3)/(S(4)*f**S(3)) - b*p*q*(-e*h + f*g)**S(4)*log(e + f*x)/(S(4)*f**S(4)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(4)/(S(4)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(2), x), x, -b*p*q*(g + h*x)**S(3)/(S(9)*h) - b*p*q*(g + h*x)**S(2)*(-e*h + f*g)/(S(6)*f*h) - b*p*q*x*(-e*h + f*g)**S(2)/(S(3)*f**S(2)) - b*p*q*(-e*h + f*g)**S(3)*log(e + f*x)/(S(3)*f**S(3)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(3)/(S(3)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x), x), x, -b*p*q*(g + h*x)**S(2)/(S(4)*h) - b*p*q*x*(-e*h + f*g)/(S(2)*f) - b*p*q*(-e*h + f*g)**S(2)*log(e + f*x)/(S(2)*f**S(2)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(2)/(S(2)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a + b*log(c*(d*(e + f*x)**p)**q), x), x, a*x - b*p*q*x + b*(e + f*x)*log(c*(d*(e + f*x)**p)**q)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x), x), x, b*p*q*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h + (a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**S(2), x), x, b*f*p*q*log(e + f*x)/(h*(-e*h + f*g)) - b*f*p*q*log(g + h*x)/(h*(-e*h + f*g)) + (-a - b*log(c*(d*(e + f*x)**p)**q))/(h*(g + h*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**S(3), x), x, b*f**S(2)*p*q*log(e + f*x)/(S(2)*h*(-e*h + f*g)**S(2)) - b*f**S(2)*p*q*log(g + h*x)/(S(2)*h*(-e*h + f*g)**S(2)) + b*f*p*q/(S(2)*h*(g + h*x)*(-e*h + f*g)) + (-a/S(2) - b*log(c*(d*(e + f*x)**p)**q)/S(2))/(h*(g + h*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**S(4), x), x, b*f**S(3)*p*q*log(e + f*x)/(S(3)*h*(-e*h + f*g)**S(3)) - b*f**S(3)*p*q*log(g + h*x)/(S(3)*h*(-e*h + f*g)**S(3)) + b*f**S(2)*p*q/(S(3)*h*(g + h*x)*(-e*h + f*g)**S(2)) + b*f*p*q/(S(6)*h*(g + h*x)**S(2)*(-e*h + f*g)) + (-a/S(3) - b*log(c*(d*(e + f*x)**p)**q)/S(3))/(h*(g + h*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**S(5), x), x, b*f**S(4)*p*q*log(e + f*x)/(S(4)*h*(-e*h + f*g)**S(4)) - b*f**S(4)*p*q*log(g + h*x)/(S(4)*h*(-e*h + f*g)**S(4)) + b*f**S(3)*p*q/(S(4)*h*(g + h*x)*(-e*h + f*g)**S(3)) + b*f**S(2)*p*q/(S(8)*h*(g + h*x)**S(2)*(-e*h + f*g)**S(2)) + b*f*p*q/(S(12)*h*(g + h*x)**S(3)*(-e*h + f*g)) + (-a/S(4) - b*log(c*(d*(e + f*x)**p)**q)/S(4))/(h*(g + h*x)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**m, x), x, Integral((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**S(3), x), x, -a*b*p*q*x*(-e*h + f*g)**S(3)/(S(2)*f**S(3)) + b**S(2)*p**S(2)*q**S(2)*(g + h*x)**S(4)/(S(32)*h) + S(7)*b**S(2)*p**S(2)*q**S(2)*(g + h*x)**S(3)*(-e*h + f*g)/(S(72)*f*h) + S(13)*b**S(2)*p**S(2)*q**S(2)*(g + h*x)**S(2)*(-e*h + f*g)**S(2)/(S(48)*f**S(2)*h) + S(25)*b**S(2)*p**S(2)*q**S(2)*x*(-e*h + f*g)**S(3)/(S(24)*f**S(3)) - b**S(2)*p*q*(e + f*x)*(-e*h + f*g)**S(3)*log(c*(d*(e + f*x)**p)**q)/(S(2)*f**S(4)) + S(13)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**S(4)*log(e + f*x)/(S(24)*f**S(4)*h) - b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(4)/(S(8)*h) - b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(3)*(-e*h + f*g)/(S(6)*f*h) - b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(2)*(-e*h + f*g)**S(2)/(S(4)*f**S(2)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**S(4)/(S(4)*h) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(-e*h + f*g)**S(4)/(S(4)*f**S(4)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**S(2), x), x, -S(2)*a*b*p*q*x*(-e*h + f*g)**S(2)/(S(3)*f**S(2)) + S(2)*b**S(2)*p**S(2)*q**S(2)*(g + h*x)**S(3)/(S(27)*h) + S(5)*b**S(2)*p**S(2)*q**S(2)*(g + h*x)**S(2)*(-e*h + f*g)/(S(18)*f*h) + S(11)*b**S(2)*p**S(2)*q**S(2)*x*(-e*h + f*g)**S(2)/(S(9)*f**S(2)) - S(2)*b**S(2)*p*q*(e + f*x)*(-e*h + f*g)**S(2)*log(c*(d*(e + f*x)**p)**q)/(S(3)*f**S(3)) + S(5)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**S(3)*log(e + f*x)/(S(9)*f**S(3)*h) - S(2)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(3)/(S(9)*h) - b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(2)*(-e*h + f*g)/(S(3)*f*h) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**S(3)/(S(3)*h) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(-e*h + f*g)**S(3)/(S(3)*f**S(3)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x), x), x, -S(2)*a*b*p*q*x*(-e*h + f*g)/f + b**S(2)*e*h*p**S(2)*q**S(2)*x/(S(2)*f) + b**S(2)*h*p**S(2)*q**S(2)*x**S(2)/S(4) + S(2)*b**S(2)*p**S(2)*q**S(2)*x*(-e*h + f*g)/f - S(2)*b**S(2)*p*q*(e + f*x)*(-e*h + f*g)*log(c*(d*(e + f*x)**p)**q)/f**S(2) - b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)/(S(2)*f**S(2)) + h*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(2)/(S(2)*f**S(2)) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-e*h + f*g)/f**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2), x), x, -S(2)*a*b*p*q*x + S(2)*b**S(2)*p**S(2)*q**S(2)*x - S(2)*b**S(2)*p*q*(e + f*x)*log(c*(d*(e + f*x)**p)**q)/f + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(g + h*x), x), x, -S(2)*b**S(2)*p**S(2)*q**S(2)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h + S(2)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(g + h*x)/(-e*h + f*g))/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(g + h*x)**S(2), x), x, -S(2)*b**S(2)*f*p**S(2)*q**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)) - S(2)*b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/(h*(-e*h + f*g)) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/((g + h*x)*(-e*h + f*g)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(g + h*x)**S(3), x), x, -b**S(2)*f**S(2)*p**S(2)*q**S(2)*log(e + f*x)/(h*(-e*h + f*g)**S(2)) + b**S(2)*f**S(2)*p**S(2)*q**S(2)*log(g + h*x)/(h*(-e*h + f*g)**S(2)) - b**S(2)*f**S(2)*p**S(2)*q**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(2)) - b*f**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(2)) + b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))/(h*(g + h*x)*(-e*h + f*g)) + f**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(2)*h*(-e*h + f*g)**S(2)) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(2)*h*(g + h*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(g + h*x)**S(4), x), x, -b**S(2)*f**S(3)*p**S(2)*q**S(2)*log(e + f*x)/(h*(-e*h + f*g)**S(3)) + b**S(2)*f**S(3)*p**S(2)*q**S(2)*log(g + h*x)/(h*(-e*h + f*g)**S(3)) - S(2)*b**S(2)*f**S(3)*p**S(2)*q**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(S(3)*h*(-e*h + f*g)**S(3)) - b**S(2)*f**S(2)*p**S(2)*q**S(2)/(S(3)*h*(g + h*x)*(-e*h + f*g)**S(2)) - S(2)*b*f**S(3)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/(S(3)*h*(-e*h + f*g)**S(3)) + S(2)*b*f**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))/(S(3)*h*(g + h*x)*(-e*h + f*g)**S(2)) + b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))/(S(3)*h*(g + h*x)**S(2)*(-e*h + f*g)) + f**S(3)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(3)*h*(-e*h + f*g)**S(3)) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(3)*h*(g + h*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(g + h*x)**m, x), x, Integral((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(g + h*x)**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(g + h*x)**S(3), x), x, S(6)*a*b**S(2)*p**S(2)*q**S(2)*x*(-e*h + f*g)**S(3)/f**S(3) - S(9)*b**S(3)*e*h*p**S(3)*q**S(3)*x*(-e*h + f*g)**S(2)/(S(4)*f**S(3)) - S(9)*b**S(3)*h*p**S(3)*q**S(3)*x**S(2)*(-e*h + f*g)**S(2)/(S(8)*f**S(2)) - S(6)*b**S(3)*p**S(3)*q**S(3)*x*(-e*h + f*g)**S(3)/f**S(3) - S(3)*b**S(3)*h**S(3)*p**S(3)*q**S(3)*(e + f*x)**S(4)/(S(128)*f**S(4)) - S(2)*b**S(3)*h**S(2)*p**S(3)*q**S(3)*(e + f*x)**S(3)*(-e*h + f*g)/(S(9)*f**S(4)) + S(6)*b**S(3)*p**S(2)*q**S(2)*(e + f*x)*(-e*h + f*g)**S(3)*log(c*(d*(e + f*x)**p)**q)/f**S(4) + S(3)*b**S(2)*h**S(3)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(4)/(S(32)*f**S(4)) + S(2)*b**S(2)*h**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)*(-e*h + f*g)/(S(3)*f**S(4)) + S(9)*b**S(2)*h*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)/(S(4)*f**S(4)) - S(3)*b*h**S(3)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(4)/(S(16)*f**S(4)) - b*h**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(3)*(-e*h + f*g)/f**S(4) - S(9)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(2)*(-e*h + f*g)**S(2)/(S(4)*f**S(4)) - S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-e*h + f*g)**S(3)/f**S(4) + h**S(3)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(4)/(S(4)*f**S(4)) + h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(3)*(-e*h + f*g)/f**S(4) + S(3)*h*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(2)*(-e*h + f*g)**S(2)/(S(2)*f**S(4)) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)*(-e*h + f*g)**S(3)/f**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(g + h*x)**S(2), x), x, S(6)*a*b**S(2)*p**S(2)*q**S(2)*x*(-e*h + f*g)**S(2)/f**S(2) - S(3)*b**S(3)*e*h*p**S(3)*q**S(3)*x*(-e*h + f*g)/(S(2)*f**S(2)) - S(3)*b**S(3)*h*p**S(3)*q**S(3)*x**S(2)*(-e*h + f*g)/(S(4)*f) - S(6)*b**S(3)*p**S(3)*q**S(3)*x*(-e*h + f*g)**S(2)/f**S(2) - S(2)*b**S(3)*h**S(2)*p**S(3)*q**S(3)*(e + f*x)**S(3)/(S(27)*f**S(3)) + S(6)*b**S(3)*p**S(2)*q**S(2)*(e + f*x)*(-e*h + f*g)**S(2)*log(c*(d*(e + f*x)**p)**q)/f**S(3) + S(2)*b**S(2)*h**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)/(S(9)*f**S(3)) + S(3)*b**S(2)*h*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*h + f*g)/(S(2)*f**S(3)) - b*h**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(3)/(S(3)*f**S(3)) - S(3)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(2)*(-e*h + f*g)/(S(2)*f**S(3)) - S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-e*h + f*g)**S(2)/f**S(3) + h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(3)/(S(3)*f**S(3)) + h*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(2)*(-e*h + f*g)/f**S(3) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)*(-e*h + f*g)**S(2)/f**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(g + h*x), x), x, S(6)*a*b**S(2)*p**S(2)*q**S(2)*x*(-e*h + f*g)/f - S(3)*b**S(3)*e*h*p**S(3)*q**S(3)*x/(S(4)*f) - S(3)*b**S(3)*h*p**S(3)*q**S(3)*x**S(2)/S(8) - S(6)*b**S(3)*p**S(3)*q**S(3)*x*(-e*h + f*g)/f + S(6)*b**S(3)*p**S(2)*q**S(2)*(e + f*x)*(-e*h + f*g)*log(c*(d*(e + f*x)**p)**q)/f**S(2) + S(3)*b**S(2)*h*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)/(S(4)*f**S(2)) - S(3)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(2)/(S(4)*f**S(2)) - S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-e*h + f*g)/f**S(2) + h*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(2)/(S(2)*f**S(2)) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)*(-e*h + f*g)/f**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3), x), x, S(6)*a*b**S(2)*p**S(2)*q**S(2)*x - S(6)*b**S(3)*p**S(3)*q**S(3)*x + S(6)*b**S(3)*p**S(2)*q**S(2)*(e + f*x)*log(c*(d*(e + f*x)**p)**q)/f - S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/f + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(g + h*x), x), x, S(6)*b**S(3)*p**S(3)*q**S(3)*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/h - S(6)*b**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h + S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*log(f*(g + h*x)/(-e*h + f*g))/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(g + h*x)**S(2), x), x, S(6)*b**S(3)*f*p**S(3)*q**S(3)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)) - S(6)*b**S(2)*f*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)) - S(3)*b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(g + h*x)/(-e*h + f*g))/(h*(-e*h + f*g)) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)/((g + h*x)*(-e*h + f*g)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(g + h*x)**S(3), x), x, S(3)*b**S(3)*f**S(2)*p**S(3)*q**S(3)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(2)) + S(3)*b**S(3)*f**S(2)*p**S(3)*q**S(3)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(2)) + S(3)*b**S(2)*f**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(2)) - S(3)*b**S(2)*f**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(2)) - S(3)*b*f**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(g + h*x)/(-e*h + f*g))/(S(2)*h*(-e*h + f*g)**S(2)) - S(3)*b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/(S(2)*(g + h*x)*(-e*h + f*g)**S(2)) + f**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(S(2)*h*(-e*h + f*g)**S(2)) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(S(2)*h*(g + h*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(g + h*x)**S(4), x), x, b**S(3)*f**S(3)*p**S(3)*q**S(3)*log(e + f*x)/(h*(-e*h + f*g)**S(3)) - b**S(3)*f**S(3)*p**S(3)*q**S(3)*log(g + h*x)/(h*(-e*h + f*g)**S(3)) + S(3)*b**S(3)*f**S(3)*p**S(3)*q**S(3)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(3)) + S(2)*b**S(3)*f**S(3)*p**S(3)*q**S(3)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(3)) + S(3)*b**S(2)*f**S(3)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(3)) - S(2)*b**S(2)*f**S(3)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(3)) - b**S(2)*f**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))/(h*(g + h*x)*(-e*h + f*g)**S(2)) - b*f**S(3)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(g + h*x)/(-e*h + f*g))/(h*(-e*h + f*g)**S(3)) - b*f**S(3)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(2)*h*(-e*h + f*g)**S(3)) - b*f**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/((g + h*x)*(-e*h + f*g)**S(3)) + b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(2)*h*(g + h*x)**S(2)*(-e*h + f*g)) + f**S(3)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(S(3)*h*(-e*h + f*g)**S(3)) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(S(3)*h*(g + h*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(g + h*x)**S(5), x), x, S(3)*b**S(3)*f**S(4)*p**S(3)*q**S(3)*log(e + f*x)/(S(2)*h*(-e*h + f*g)**S(4)) - S(3)*b**S(3)*f**S(4)*p**S(3)*q**S(3)*log(g + h*x)/(S(2)*h*(-e*h + f*g)**S(4)) + S(11)*b**S(3)*f**S(4)*p**S(3)*q**S(3)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(S(4)*h*(-e*h + f*g)**S(4)) + S(3)*b**S(3)*f**S(4)*p**S(3)*q**S(3)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(S(2)*h*(-e*h + f*g)**S(4)) + b**S(3)*f**S(3)*p**S(3)*q**S(3)/(S(4)*h*(g + h*x)*(-e*h + f*g)**S(3)) + S(11)*b**S(2)*f**S(4)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/(S(4)*h*(-e*h + f*g)**S(4)) - S(3)*b**S(2)*f**S(4)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(S(2)*h*(-e*h + f*g)**S(4)) - S(5)*b**S(2)*f**S(3)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))/(S(4)*h*(g + h*x)*(-e*h + f*g)**S(3)) - b**S(2)*f**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))/(S(4)*h*(g + h*x)**S(2)*(-e*h + f*g)**S(2)) - S(3)*b*f**S(4)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(g + h*x)/(-e*h + f*g))/(S(4)*h*(-e*h + f*g)**S(4)) - S(5)*b*f**S(4)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(8)*h*(-e*h + f*g)**S(4)) - S(3)*b*f**S(3)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/(S(4)*(g + h*x)*(-e*h + f*g)**S(4)) + S(3)*b*f**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(8)*h*(g + h*x)**S(2)*(-e*h + f*g)**S(2)) + b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(4)*h*(g + h*x)**S(3)*(-e*h + f*g)) + f**S(4)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(S(4)*h*(-e*h + f*g)**S(4)) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(S(4)*h*(g + h*x)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(4), x), x, -S(24)*a*b**S(3)*p**S(3)*q**S(3)*x + S(24)*b**S(4)*p**S(4)*q**S(4)*x - S(24)*b**S(4)*p**S(3)*q**S(3)*(e + f*x)*log(c*(d*(e + f*x)**p)**q)/f + S(12)*b**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/f - S(4)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)/f + (a + b*log(c*(d*(e + f*x)**p)**q))**S(4)*(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(4)/(g + h*x), x), x, -S(24)*b**S(4)*p**S(4)*q**S(4)*polylog(S(5), -h*(e + f*x)/(-e*h + f*g))/h + S(24)*b**S(3)*p**S(3)*q**S(3)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/h - S(12)*b**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h + S(4)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h + (a + b*log(c*(d*(e + f*x)**p)**q))**S(4)*log(f*(g + h*x)/(-e*h + f*g))/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(4)/(g + h*x)**S(2), x), x, -S(24)*b**S(4)*f*p**S(4)*q**S(4)*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)) + S(24)*b**S(3)*f*p**S(3)*q**S(3)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)) - S(12)*b**S(2)*f*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(h*(-e*h + f*g)) - S(4)*b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*log(f*(g + h*x)/(-e*h + f*g))/(h*(-e*h + f*g)) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(4)*(e + f*x)/((g + h*x)*(-e*h + f*g)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*x), x), x, -x + (a + b*x)*log(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*x)**S(2), x), x, S(2)*x + (a + b*x)*log(a + b*x)**S(2)/b - (S(2)*a + S(2)*b*x)*log(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*x)**S(3), x), x, -S(6)*x + (a + b*x)*log(a + b*x)**S(3)/b - (S(3)*a + S(3)*b*x)*log(a + b*x)**S(2)/b + (S(6)*a + S(6)*b*x)*log(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*x + c*x), x), x, -x + (a + x*(b + c))*log(a + x*(b + c))/(b + c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*x + c*x)**S(2), x), x, S(2)*x + (a + x*(b + c))*log(a + x*(b + c))**S(2)/(b + c) - (S(2)*a + S(2)*x*(b + c))*log(a + x*(b + c))/(b + c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*x + c*x)**S(3), x), x, -S(6)*x + (a + x*(b + c))*log(a + x*(b + c))**S(3)/(b + c) - (S(3)*a + S(3)*x*(b + c))*log(a + x*(b + c))**S(2)/(b + c) + (S(6)*a + S(6)*x*(b + c))*log(a + x*(b + c))/(b + c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(-g*(d + e*x)/(-d*g + e*f))/(f + g*x), x), x, -polylog(S(2), e*(f + g*x)/(-d*g + e*f))/g, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(b*x + S(1))/x, x), x, -polylog(S(2), -b*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x)**n)**S(2), x), x, -S(5)*a**S(3)*n**S(2)*log(a + b*x)/(S(9)*b**S(3)) + a**S(3)*log(c*(a + b*x)**n)**S(2)/(S(3)*b**S(3)) + S(11)*a**S(2)*n**S(2)*x/(S(9)*b**S(2)) - S(2)*a**S(2)*n*(a + b*x)*log(c*(a + b*x)**n)/(S(3)*b**S(3)) - S(5)*a*n**S(2)*x**S(2)/(S(18)*b) + a*n*x**S(2)*log(c*(a + b*x)**n)/(S(3)*b) + S(2)*n**S(2)*x**S(3)/S(27) - S(2)*n*x**S(3)*log(c*(a + b*x)**n)/S(9) + x**S(3)*log(c*(a + b*x)**n)**S(2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)**S(2)/x**S(4), x), x, -log(c*(a + b*x)**n)**S(2)/(S(3)*x**S(3)) - b*n*log(c*(a + b*x)**n)/(S(3)*a*x**S(2)) - b**S(2)*n**S(2)/(S(3)*a**S(2)*x) + S(2)*b**S(2)*n*log(c*(a + b*x)**n)/(S(3)*a**S(2)*x) - b**S(3)*n**S(2)*log(x)/a**S(3) + b**S(3)*n**S(2)*log(a + b*x)/a**S(3) + S(2)*b**S(3)*n**S(2)*polylog(S(2), (a + b*x)/a)/(S(3)*a**S(3)) + S(2)*b**S(3)*n*log(c*(a + b*x)**n)*log(-b*x/a)/(S(3)*a**S(3)) - b**S(3)*log(c*(a + b*x)**n)**S(2)/(S(3)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x)**n)**S(3), x), x, S(19)*a**S(3)*n**S(3)*log(a + b*x)/(S(18)*b**S(3)) - S(5)*a**S(3)*n*log(c*(a + b*x)**n)**S(2)/(S(6)*b**S(3)) + a**S(3)*log(c*(a + b*x)**n)**S(3)/(S(3)*b**S(3)) - S(85)*a**S(2)*n**S(3)*x/(S(18)*b**S(2)) + S(11)*a**S(2)*n**S(2)*(a + b*x)*log(c*(a + b*x)**n)/(S(3)*b**S(3)) - a**S(2)*n*(a + b*x)*log(c*(a + b*x)**n)**S(2)/b**S(3) + S(19)*a*n**S(3)*x**S(2)/(S(36)*b) - S(5)*a*n**S(2)*x**S(2)*log(c*(a + b*x)**n)/(S(6)*b) + a*n*x**S(2)*log(c*(a + b*x)**n)**S(2)/(S(2)*b) - S(2)*n**S(3)*x**S(3)/S(27) + S(2)*n**S(2)*x**S(3)*log(c*(a + b*x)**n)/S(9) - n*x**S(3)*log(c*(a + b*x)**n)**S(2)/S(3) + x**S(3)*log(c*(a + b*x)**n)**S(3)/S(3), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x)**n)**S(3), x), x, -S(9)*a**S(2)*n**S(3)*x/(S(2)*b**S(2)) + S(6)*a**S(2)*n**S(2)*(a + b*x)*log(c*(a + b*x)**n)/b**S(3) - S(3)*a**S(2)*n*(a + b*x)*log(c*(a + b*x)**n)**S(2)/b**S(3) + a**S(2)*(a + b*x)*log(c*(a + b*x)**n)**S(3)/b**S(3) + S(3)*a*n**S(3)*x**S(2)/(S(4)*b) - S(3)*a*n**S(2)*(a + b*x)**S(2)*log(c*(a + b*x)**n)/(S(2)*b**S(3)) + S(3)*a*n*(a + b*x)**S(2)*log(c*(a + b*x)**n)**S(2)/(S(2)*b**S(3)) - a*(a + b*x)**S(2)*log(c*(a + b*x)**n)**S(3)/b**S(3) - S(2)*n**S(3)*(a + b*x)**S(3)/(S(27)*b**S(3)) + S(2)*n**S(2)*(a + b*x)**S(3)*log(c*(a + b*x)**n)/(S(9)*b**S(3)) - n*(a + b*x)**S(3)*log(c*(a + b*x)**n)**S(2)/(S(3)*b**S(3)) + (a + b*x)**S(3)*log(c*(a + b*x)**n)**S(3)/(S(3)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, Integral((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**S(3)/(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, h**S(3)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*exp(-S(4)*a/(b*p*q))*Ei((S(4)*a + S(4)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f**S(4)*p*q) + S(3)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)*exp(-S(3)*a/(b*p*q))*Ei((S(3)*a + S(3)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f**S(4)*p*q) + S(3)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*exp(-S(2)*a/(b*p*q))*Ei((S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f**S(4)*p*q) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(3)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f**S(4)*p*q), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**S(2)/(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*exp(-S(3)*a/(b*p*q))*Ei((S(3)*a + S(3)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f**S(3)*p*q) + S(2)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)*exp(-S(2)*a/(b*p*q))*Ei((S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f**S(3)*p*q) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(2)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f**S(3)*p*q), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)/(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*exp(-S(2)*a/(b*p*q))*Ei((S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f**S(2)*p*q) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f**S(2)*p*q), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b*f*p*q), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(2)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q))**S(2), x), x, Integral((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q))**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**S(3)/(a + b*log(c*(d*(e + f*x)**p)**q))**S(2), x), x, -(e + f*x)*(g + h*x)**S(3)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))) + S(4)*h**S(3)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*exp(-S(4)*a/(b*p*q))*Ei((S(4)*a + S(4)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f**S(4)*p**S(2)*q**S(2)) + S(9)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)*exp(-S(3)*a/(b*p*q))*Ei((S(3)*a + S(3)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f**S(4)*p**S(2)*q**S(2)) + S(6)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*exp(-S(2)*a/(b*p*q))*Ei((S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f**S(4)*p**S(2)*q**S(2)) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(3)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f**S(4)*p**S(2)*q**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**S(2)/(a + b*log(c*(d*(e + f*x)**p)**q))**S(2), x), x, -(e + f*x)*(g + h*x)**S(2)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))) + S(3)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*exp(-S(3)*a/(b*p*q))*Ei((S(3)*a + S(3)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f**S(3)*p**S(2)*q**S(2)) + S(4)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)*exp(-S(2)*a/(b*p*q))*Ei((S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f**S(3)*p**S(2)*q**S(2)) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(2)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f**S(3)*p**S(2)*q**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)/(a + b*log(c*(d*(e + f*x)**p)**q))**S(2), x), x, -(e + f*x)*(g + h*x)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))) + S(2)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*exp(-S(2)*a/(b*p*q))*Ei((S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f**S(2)*p**S(2)*q**S(2)) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f**S(2)*p**S(2)*q**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(-2)), x), x, (-e - f*x)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(2)*f*p**S(2)*q**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**S(2)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q))**S(3), x), x, Integral((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q))**S(3), x), expand=True, _diff=True, _numerical=True)

    # long time in rubi_test assert rubi_test(rubi_integrate((g + h*x)**S(3)/(a + b*log(c*(d*(e + f*x)**p)**q))**S(3), x), x, -(e/S(2) + f*x/S(2))*(g + h*x)**S(3)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)) - (S(2)*e + S(2)*f*x)*(g + h*x)**S(3)/(b**S(2)*f*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))) + (e + f*x)*(g + h*x)**S(2)*(-S(3)*e*h/S(2) + S(3)*f*g/S(2))/(b**S(2)*f**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))) + S(8)*h**S(3)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*exp(-S(4)*a/(b*p*q))*Ei((S(4)*a + S(4)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(3)*f**S(4)*p**S(3)*q**S(3)) + S(27)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)*exp(-S(3)*a/(b*p*q))*Ei((S(3)*a + S(3)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(S(2)*b**S(3)*f**S(4)*p**S(3)*q**S(3)) + S(6)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*exp(-S(2)*a/(b*p*q))*Ei((S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(3)*f**S(4)*p**S(3)*q**S(3)) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(3)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(S(2)*b**S(3)*f**S(4)*p**S(3)*q**S(3)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((g + h*x)**S(2)/(a + b*log(c*(d*(e + f*x)**p)**q))**S(3), x), x, -(e/S(2) + f*x/S(2))*(g + h*x)**S(2)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)) - (S(3)*e/S(2) + S(3)*f*x/S(2))*(g + h*x)**S(2)/(b**S(2)*f*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))) + (e + f*x)*(g + h*x)*(-e*h + f*g)/(b**S(2)*f**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))) + S(9)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*exp(-S(3)*a/(b*p*q))*Ei((S(3)*a + S(3)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(S(2)*b**S(3)*f**S(3)*p**S(3)*q**S(3)) + S(4)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)*exp(-S(2)*a/(b*p*q))*Ei((S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(3)*f**S(3)*p**S(3)*q**S(3)) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(2)*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(S(2)*b**S(3)*f**S(3)*p**S(3)*q**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)/(a + b*log(c*(d*(e + f*x)**p)**q))**S(3), x), x, -(e/S(2) + f*x/S(2))*(g + h*x)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)) - (e + f*x)*(g + h*x)/(b**S(2)*f*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))) + (e + f*x)*(-e*h/S(2) + f*g/S(2))/(b**S(2)*f**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))) + S(2)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*exp(-S(2)*a/(b*p*q))*Ei((S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(3)*f**S(2)*p**S(3)*q**S(3)) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h/S(2) + f*g/S(2))*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(3)*f**S(2)*p**S(3)*q**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(-3)), x), x, (-e/S(2) - f*x/S(2))/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)) + (-e/S(2) - f*x/S(2))/(b**S(2)*f*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e/S(2) + f*x/S(2))*exp(-a/(b*p*q))*Ei((a + b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))/(b**S(3)*f*p**S(3)*q**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(g + h*x)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(g + h*x)**S(2)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(g + h*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**m, x), x, Integral(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(4), x), x, -sqrt(S(5))*sqrt(pi)*sqrt(b)*h**S(4)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(5)/(p*q))*(e + f*x)**S(5)*exp(-S(5)*a/(b*p*q))*erfi(sqrt(S(5))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(50)*f**S(5)) - sqrt(pi)*sqrt(b)*h**S(3)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*(-e*h + f*g)*exp(-S(4)*a/(b*p*q))*erfi(S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(4)*f**S(5)) - sqrt(S(3))*sqrt(pi)*sqrt(b)*h**S(2)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)**S(2)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(3)*f**S(5)) - sqrt(S(2))*sqrt(pi)*sqrt(b)*h*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(3)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(2)*f**S(5)) - sqrt(pi)*sqrt(b)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(4)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(2)*f**S(5)) + h**S(4)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(5)/(S(5)*f**S(5)) + h**S(3)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(4)*(-e*h + f*g)/f**S(5) + S(2)*h**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)*(-e*h + f*g)**S(2)/f**S(5) + S(2)*h*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*h + f*g)**S(3)/f**S(5) + sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)**S(4)/f**S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(3), x), x, -sqrt(pi)*sqrt(b)*h**S(3)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*exp(-S(4)*a/(b*p*q))*erfi(S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(16)*f**S(4)) - sqrt(S(3))*sqrt(pi)*sqrt(b)*h**S(2)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(6)*f**S(4)) - S(3)*sqrt(S(2))*sqrt(pi)*sqrt(b)*h*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(8)*f**S(4)) - sqrt(pi)*sqrt(b)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(3)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(2)*f**S(4)) + h**S(3)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(4)/(S(4)*f**S(4)) + h**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)*(-e*h + f*g)/f**S(4) + S(3)*h*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)/(S(2)*f**S(4)) + sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)**S(3)/f**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**S(2), x), x, -sqrt(S(3))*sqrt(pi)*sqrt(b)*h**S(2)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(18)*f**S(3)) - sqrt(S(2))*sqrt(pi)*sqrt(b)*h*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(4)*f**S(3)) - sqrt(pi)*sqrt(b)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(2)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(2)*f**S(3)) + h**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)/(S(3)*f**S(3)) + h*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*h + f*g)/f**S(3) + sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)**S(2)/f**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x), x), x, -sqrt(S(2))*sqrt(pi)*sqrt(b)*h*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(8)*f**S(2)) - sqrt(pi)*sqrt(b)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h/S(2) + f*g/S(2))*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/f**S(2) + h*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)/(S(2)*f**S(2)) + sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)/f**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, sqrt(pi)*sqrt(b)*sqrt(p)*sqrt(q)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(-e/S(2) - f*x/S(2))*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/f + sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x), x), x, Integral(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**S(2), x), x, -b*f*p*q*Integral(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)), x)/(S(2)*(-e*h + f*g)) + sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)/((g + h*x)*(-e*h + f*g)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**S(3), x), x, b*f*p*q*Integral(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(g + h*x)**S(2)), x)/(S(4)*h) - sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(S(2)*h*(g + h*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**S(4), x), x, b*f*p*q*Integral(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(g + h*x)**S(3)), x)/(S(6)*h) - sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(S(3)*h*(g + h*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**S(5), x), x, b*f*p*q*Integral(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(g + h*x)**S(4)), x)/(S(8)*h) - sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(S(4)*h*(g + h*x)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(g + h*x)**m, x), x, Integral((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(g + h*x)**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(g + h*x)**S(3), x), x, S(3)*sqrt(pi)*b**(S(3)/2)*h**S(3)*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*exp(-S(4)*a/(b*p*q))*erfi(S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(128)*f**S(4)) + sqrt(S(3))*sqrt(pi)*b**(S(3)/2)*h**S(2)*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(12)*f**S(4)) + S(9)*sqrt(S(2))*sqrt(pi)*b**(S(3)/2)*h*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(32)*f**S(4)) + S(3)*sqrt(pi)*b**(S(3)/2)*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(3)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(4)*f**S(4)) - S(3)*b*h**S(3)*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(4)/(S(32)*f**S(4)) - b*h**S(2)*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)*(-e*h + f*g)/(S(2)*f**S(4)) - S(9)*b*h*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)/(S(8)*f**S(4)) - S(3)*b*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)**S(3)/(S(2)*f**S(4)) + h**S(3)*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(4)/(S(4)*f**S(4)) + h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(3)*(-e*h + f*g)/f**S(4) + S(3)*h*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(2)*(-e*h + f*g)**S(2)/(S(2)*f**S(4)) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)*(-e*h + f*g)**S(3)/f**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(g + h*x)**S(2), x), x, sqrt(S(3))*sqrt(pi)*b**(S(3)/2)*h**S(2)*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(36)*f**S(3)) + S(3)*sqrt(S(2))*sqrt(pi)*b**(S(3)/2)*h*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(16)*f**S(3)) + S(3)*sqrt(pi)*b**(S(3)/2)*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(2)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(4)*f**S(3)) - b*h**S(2)*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)/(S(6)*f**S(3)) - S(3)*b*h*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*h + f*g)/(S(4)*f**S(3)) - S(3)*b*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)**S(2)/(S(2)*f**S(3)) + h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(3)/(S(3)*f**S(3)) + h*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(2)*(-e*h + f*g)/f**S(3) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)*(-e*h + f*g)**S(2)/f**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(g + h*x), x), x, S(3)*sqrt(S(2))*sqrt(pi)*b**(S(3)/2)*h*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(32)*f**S(2)) + S(3)*sqrt(pi)*b**(S(3)/2)*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(4)*f**S(2)) - S(3)*b*h*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)/(S(8)*f**S(2)) - S(3)*b*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)/(S(2)*f**S(2)) + h*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(2)/(S(2)*f**S(2)) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)*(-e*h + f*g)/f**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2), x), x, S(3)*sqrt(pi)*b**(S(3)/2)*p**(S(3)/2)*q**(S(3)/2)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(4)*f) - S(3)*b*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)/(S(2)*f) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/(g + h*x), x), x, Integral((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/(g + h*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/(g + h*x)**S(2), x), x, -S(3)*b*f*p*q*Integral(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x), x)/(S(2)*(-e*h + f*g)) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)/((g + h*x)*(-e*h + f*g)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/(g + h*x)**S(3), x), x, S(3)*b*f*p*q*Integral(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/((e + f*x)*(g + h*x)**S(2)), x)/(S(4)*h) - (a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/(S(2)*h*(g + h*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/(g + h*x)**S(4), x), x, b*f*p*q*Integral(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/((e + f*x)*(g + h*x)**S(3)), x)/(S(2)*h) - (a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/(S(3)*h*(g + h*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(g + h*x)**m, x), x, Integral((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(g + h*x)**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(g + h*x)**S(3), x), x, -S(15)*sqrt(pi)*b**(S(5)/2)*h**S(3)*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*exp(-S(4)*a/(b*p*q))*erfi(S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(1024)*f**S(4)) - S(5)*sqrt(S(3))*sqrt(pi)*b**(S(5)/2)*h**S(2)*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(72)*f**S(4)) - S(45)*sqrt(S(2))*sqrt(pi)*b**(S(5)/2)*h*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(128)*f**S(4)) - S(15)*sqrt(pi)*b**(S(5)/2)*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(3)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(8)*f**S(4)) + S(15)*b**S(2)*h**S(3)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(4)/(S(256)*f**S(4)) + S(5)*b**S(2)*h**S(2)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)*(-e*h + f*g)/(S(12)*f**S(4)) + S(45)*b**S(2)*h*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)/(S(32)*f**S(4)) + S(15)*b**S(2)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)**S(3)/(S(4)*f**S(4)) - S(5)*b*h**S(3)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(4)/(S(32)*f**S(4)) - S(5)*b*h**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(3)*(-e*h + f*g)/(S(6)*f**S(4)) - S(15)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(2)*(-e*h + f*g)**S(2)/(S(8)*f**S(4)) - S(5)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)*(-e*h + f*g)**S(3)/(S(2)*f**S(4)) + h**S(3)*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)**S(4)/(S(4)*f**S(4)) + h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)**S(3)*(-e*h + f*g)/f**S(4) + S(3)*h*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)**S(2)*(-e*h + f*g)**S(2)/(S(2)*f**S(4)) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)*(-e*h + f*g)**S(3)/f**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(g + h*x)**S(2), x), x, -S(5)*sqrt(S(3))*sqrt(pi)*b**(S(5)/2)*h**S(2)*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(216)*f**S(3)) - S(15)*sqrt(S(2))*sqrt(pi)*b**(S(5)/2)*h*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(64)*f**S(3)) - S(15)*sqrt(pi)*b**(S(5)/2)*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(2)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(8)*f**S(3)) + S(5)*b**S(2)*h**S(2)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)/(S(36)*f**S(3)) + S(15)*b**S(2)*h*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*h + f*g)/(S(16)*f**S(3)) + S(15)*b**S(2)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)**S(2)/(S(4)*f**S(3)) - S(5)*b*h**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(3)/(S(18)*f**S(3)) - S(5)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(2)*(-e*h + f*g)/(S(4)*f**S(3)) - S(5)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)*(-e*h + f*g)**S(2)/(S(2)*f**S(3)) + h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)**S(3)/(S(3)*f**S(3)) + h*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)**S(2)*(-e*h + f*g)/f**S(3) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)*(-e*h + f*g)**S(2)/f**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(g + h*x), x), x, -S(15)*sqrt(S(2))*sqrt(pi)*b**(S(5)/2)*h*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(128)*f**S(2)) - S(15)*sqrt(pi)*b**(S(5)/2)*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(8)*f**S(2)) + S(15)*b**S(2)*h*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)/(S(32)*f**S(2)) + S(15)*b**S(2)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*(-e*h + f*g)/(S(4)*f**S(2)) - S(5)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)**S(2)/(S(8)*f**S(2)) - S(5)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)*(-e*h + f*g)/(S(2)*f**S(2)) + h*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)**S(2)/(S(2)*f**S(2)) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)*(-e*h + f*g)/f**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2), x), x, -S(15)*sqrt(pi)*b**(S(5)/2)*p**(S(5)/2)*q**(S(5)/2)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(8)*f) + S(15)*b**S(2)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)/(S(4)*f) - S(5)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(e + f*x)/(S(2)*f) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)/(g + h*x), x), x, Integral((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)/(g + h*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)/(g + h*x)**S(2), x), x, -S(5)*b*f*p*q*Integral((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/(g + h*x), x)/(S(2)*(-e*h + f*g)) + (a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(e + f*x)/((g + h*x)*(-e*h + f*g)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)/(g + h*x)**S(3), x), x, S(5)*b*f*p*q*Integral((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/((e + f*x)*(g + h*x)**S(2)), x)/(S(4)*h) - (a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)/(S(2)*h*(g + h*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)/(g + h*x)**S(4), x), x, S(5)*b*f*p*q*Integral((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/((e + f*x)*(g + h*x)**S(3)), x)/(S(6)*h) - (a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)/(S(3)*h*(g + h*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)/(g + h*x)**S(5), x), x, S(5)*b*f*p*q*Integral((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)/((e + f*x)*(g + h*x)**S(4)), x)/(S(8)*h) - (a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)/(S(4)*h*(g + h*x)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**m/sqrt(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, Integral((g + h*x)**m/sqrt(a + b*log(c*(d*(e + f*x)**p)**q)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**S(3)/sqrt(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, sqrt(pi)*h**S(3)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*exp(-S(4)*a/(b*p*q))*erfi(S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(2)*sqrt(b)*f**S(4)*sqrt(p)*sqrt(q)) + sqrt(S(3))*sqrt(pi)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(sqrt(b)*f**S(4)*sqrt(p)*sqrt(q)) + S(3)*sqrt(S(2))*sqrt(pi)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(2)*sqrt(b)*f**S(4)*sqrt(p)*sqrt(q)) + sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(3)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(sqrt(b)*f**S(4)*sqrt(p)*sqrt(q)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**S(2)/sqrt(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, sqrt(S(3))*sqrt(pi)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(3)*sqrt(b)*f**S(3)*sqrt(p)*sqrt(q)) + sqrt(S(2))*sqrt(pi)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(sqrt(b)*f**S(3)*sqrt(p)*sqrt(q)) + sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(2)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(sqrt(b)*f**S(3)*sqrt(p)*sqrt(q)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)/sqrt(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, sqrt(S(2))*sqrt(pi)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(2)*sqrt(b)*f**S(2)*sqrt(p)*sqrt(q)) + sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(sqrt(b)*f**S(2)*sqrt(p)*sqrt(q)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(sqrt(b)*f*sqrt(p)*sqrt(q)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)), x), x, Integral(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2), x), x, Integral((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**S(3)/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2), x), x, -(S(2)*e + S(2)*f*x)*(g + h*x)**S(3)/(b*f*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + S(4)*sqrt(pi)*h**S(3)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*exp(-S(4)*a/(b*p*q))*erfi(S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f**S(4)*p**(S(3)/2)*q**(S(3)/2)) + S(6)*sqrt(S(3))*sqrt(pi)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f**S(4)*p**(S(3)/2)*q**(S(3)/2)) + S(6)*sqrt(S(2))*sqrt(pi)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f**S(4)*p**(S(3)/2)*q**(S(3)/2)) + S(2)*sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(3)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f**S(4)*p**(S(3)/2)*q**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**S(2)/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2), x), x, -(S(2)*e + S(2)*f*x)*(g + h*x)**S(2)/(b*f*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + S(2)*sqrt(S(3))*sqrt(pi)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f**S(3)*p**(S(3)/2)*q**(S(3)/2)) + S(4)*sqrt(S(2))*sqrt(pi)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f**S(3)*p**(S(3)/2)*q**(S(3)/2)) + S(2)*sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(2)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f**S(3)*p**(S(3)/2)*q**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2), x), x, -(S(2)*e + S(2)*f*x)*(g + h*x)/(b*f*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + S(2)*sqrt(S(2))*sqrt(pi)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f**S(2)*p**(S(3)/2)*q**(S(3)/2)) + sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-S(2)*e*h + S(2)*f*g)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f**S(2)*p**(S(3)/2)*q**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(-3)/2), x), x, -(S(2)*e + S(2)*f*x)/(b*f*p*q*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(S(2)*e + S(2)*f*x)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(3)/2)*f*p**(S(3)/2)*q**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(g + h*x)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2), x), x, Integral((g + h*x)**m/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2), x), expand=True, _diff=True, _numerical=True)

    ''' long time in rubi test
    assert rubi_test(rubi_integrate((g + h*x)**S(3)/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2), x), x, (-S(2)*e/S(3) - S(2)*f*x/S(3))*(g + h*x)**S(3)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)) - (S(16)*e/S(3) + S(16)*f*x/S(3))*(g + h*x)**S(3)/(b**S(2)*f*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + (e + f*x)*(g + h*x)**S(2)*(-S(4)*e*h + S(4)*f*g)/(b**S(2)*f**S(2)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + S(32)*sqrt(pi)*h**S(3)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*(e + f*x)**S(4)*exp(-S(4)*a/(b*p*q))*erfi(S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(3)*b**(S(5)/2)*f**S(4)*p**(S(5)/2)*q**(S(5)/2)) + S(12)*sqrt(S(3))*sqrt(pi)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*(-e*h + f*g)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(5)/2)*f**S(4)*p**(S(5)/2)*q**(S(5)/2)) + S(8)*sqrt(S(2))*sqrt(pi)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(5)/2)*f**S(4)*p**(S(5)/2)*q**(S(5)/2)) + S(4)*sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(3)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(3)*b**(S(5)/2)*f**S(4)*p**(S(5)/2)*q**(S(5)/2)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((g + h*x)**S(2)/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2), x), x, (-S(2)*e/S(3) - S(2)*f*x/S(3))*(g + h*x)**S(2)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)) - (S(4)*e + S(4)*f*x)*(g + h*x)**S(2)/(b**S(2)*f*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + (e + f*x)*(g + h*x)*(-S(8)*e*h/S(3) + S(8)*f*g/S(3))/(b**S(2)*f**S(2)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + S(4)*sqrt(S(3))*sqrt(pi)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*(e + f*x)**S(3)*exp(-S(3)*a/(b*p*q))*erfi(sqrt(S(3))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(5)/2)*f**S(3)*p**(S(5)/2)*q**(S(5)/2)) + S(16)*sqrt(S(2))*sqrt(pi)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*(-e*h + f*g)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(3)*b**(S(5)/2)*f**S(3)*p**(S(5)/2)*q**(S(5)/2)) + S(4)*sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-e*h + f*g)**S(2)*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(3)*b**(S(5)/2)*f**S(3)*p**(S(5)/2)*q**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((g + h*x)/(a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2), x), x, (-S(2)*e/S(3) - S(2)*f*x/S(3))*(g + h*x)/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)) - (S(8)*e/S(3) + S(8)*f*x/S(3))*(g + h*x)/(b**S(2)*f*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + (e + f*x)*(-S(4)*e*h/S(3) + S(4)*f*g/S(3))/(b**S(2)*f**S(2)*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + S(8)*sqrt(S(2))*sqrt(pi)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*(e + f*x)**S(2)*exp(-S(2)*a/(b*p*q))*erfi(sqrt(S(2))*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(S(3)*b**(S(5)/2)*f**S(2)*p**(S(5)/2)*q**(S(5)/2)) + sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(e + f*x)*(-S(4)*e*h/S(3) + S(4)*f*g/S(3))*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(5)/2)*f**S(2)*p**(S(5)/2)*q**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    '''
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**(S(-5)/2), x), x, (-S(2)*e/S(3) - S(2)*f*x/S(3))/(b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**(S(3)/2)) - (S(4)*e/S(3) + S(4)*f*x/S(3))/(b**S(2)*f*p**S(2)*q**S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))) + sqrt(pi)*(c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*(S(4)*e/S(3) + S(4)*f*x/S(3))*exp(-a/(b*p*q))*erfi(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(b)*sqrt(p)*sqrt(q)))/(b**(S(5)/2)*f*p**(S(5)/2)*q**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(g + h*x)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**(S(5)/2)*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(S(3)/2), x), x, -S(4)*b*p*q*(g + h*x)**(S(5)/2)/(S(25)*h) - S(4)*b*p*q*(g + h*x)**(S(3)/2)*(-e*h + f*g)/(S(15)*f*h) - S(4)*b*p*q*sqrt(g + h*x)*(-e*h + f*g)**S(2)/(S(5)*f**S(2)*h) + S(4)*b*p*q*(-e*h + f*g)**(S(5)/2)*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(5)*f**(S(5)/2)*h) + S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(S(5)/2)/(S(5)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*sqrt(g + h*x), x), x, -S(4)*b*p*q*(g + h*x)**(S(3)/2)/(S(9)*h) - S(4)*b*p*q*sqrt(g + h*x)*(-e*h + f*g)/(S(3)*f*h) + S(4)*b*p*q*(-e*h + f*g)**(S(3)/2)*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(3)*f**(S(3)/2)*h) + S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(S(3)/2)/(S(3)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/sqrt(g + h*x), x), x, -S(4)*b*p*q*sqrt(g + h*x)/h + S(4)*b*p*q*sqrt(-e*h + f*g)*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(sqrt(f)*h) + (S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))*sqrt(g + h*x)/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**(S(3)/2), x), x, -S(4)*b*sqrt(f)*p*q*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(h*sqrt(-e*h + f*g)) - (S(2)*a + S(2)*b*log(c*(d*(e + f*x)**p)**q))/(h*sqrt(g + h*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**(S(5)/2), x), x, -S(4)*b*f**(S(3)/2)*p*q*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(3)*h*(-e*h + f*g)**(S(3)/2)) + S(4)*b*f*p*q/(S(3)*h*sqrt(g + h*x)*(-e*h + f*g)) - (S(2)*a/S(3) + S(2)*b*log(c*(d*(e + f*x)**p)**q)/S(3))/(h*(g + h*x)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**(S(7)/2), x), x, -S(4)*b*f**(S(5)/2)*p*q*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(5)*h*(-e*h + f*g)**(S(5)/2)) + S(4)*b*f**S(2)*p*q/(S(5)*h*sqrt(g + h*x)*(-e*h + f*g)**S(2)) + S(4)*b*f*p*q/(S(15)*h*(g + h*x)**(S(3)/2)*(-e*h + f*g)) - (S(2)*a/S(5) + S(2)*b*log(c*(d*(e + f*x)**p)**q)/S(5))/(h*(g + h*x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**(S(9)/2), x), x, -S(4)*b*f**(S(7)/2)*p*q*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(7)*h*(-e*h + f*g)**(S(7)/2)) + S(4)*b*f**S(3)*p*q/(S(7)*h*sqrt(g + h*x)*(-e*h + f*g)**S(3)) + S(4)*b*f**S(2)*p*q/(S(21)*h*(g + h*x)**(S(3)/2)*(-e*h + f*g)**S(2)) + S(4)*b*f*p*q/(S(35)*h*(g + h*x)**(S(5)/2)*(-e*h + f*g)) - (S(2)*a/S(7) + S(2)*b*log(c*(d*(e + f*x)**p)**q)/S(7))/(h*(g + h*x)**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**(S(3)/2), x), x, S(16)*b**S(2)*p**S(2)*q**S(2)*(g + h*x)**(S(5)/2)/(S(125)*h) + S(128)*b**S(2)*p**S(2)*q**S(2)*(g + h*x)**(S(3)/2)*(-e*h + f*g)/(S(225)*f*h) + S(368)*b**S(2)*p**S(2)*q**S(2)*sqrt(g + h*x)*(-e*h + f*g)**S(2)/(S(75)*f**S(2)*h) + S(16)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**(S(5)/2)*log(S(2)/(-sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g) + S(1)))*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(5)*f**(S(5)/2)*h) - S(8)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**(S(5)/2)*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))**S(2)/(S(5)*f**(S(5)/2)*h) - S(368)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**(S(5)/2)*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(75)*f**(S(5)/2)*h) + S(8)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**(S(5)/2)*polylog(S(2), (-sqrt(f)*sqrt(g + h*x) - sqrt(-e*h + f*g))/(-sqrt(f)*sqrt(g + h*x) + sqrt(-e*h + f*g)))/(S(5)*f**(S(5)/2)*h) - S(8)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(S(5)/2)/(S(25)*h) - S(8)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(S(3)/2)*(-e*h + f*g)/(S(15)*f*h) - S(8)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*sqrt(g + h*x)*(-e*h + f*g)**S(2)/(S(5)*f**S(2)*h) + S(8)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(-e*h + f*g)**(S(5)/2)*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(5)*f**(S(5)/2)*h) + S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**(S(5)/2)/(S(5)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*sqrt(g + h*x), x), x, S(16)*b**S(2)*p**S(2)*q**S(2)*(g + h*x)**(S(3)/2)/(S(27)*h) + S(64)*b**S(2)*p**S(2)*q**S(2)*sqrt(g + h*x)*(-e*h + f*g)/(S(9)*f*h) + S(16)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**(S(3)/2)*log(S(2)/(-sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g) + S(1)))*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(3)*f**(S(3)/2)*h) - S(8)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**(S(3)/2)*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))**S(2)/(S(3)*f**(S(3)/2)*h) - S(64)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**(S(3)/2)*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(9)*f**(S(3)/2)*h) + S(8)*b**S(2)*p**S(2)*q**S(2)*(-e*h + f*g)**(S(3)/2)*polylog(S(2), (-sqrt(f)*sqrt(g + h*x) - sqrt(-e*h + f*g))/(-sqrt(f)*sqrt(g + h*x) + sqrt(-e*h + f*g)))/(S(3)*f**(S(3)/2)*h) - S(8)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(S(3)/2)/(S(9)*h) - S(8)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*sqrt(g + h*x)*(-e*h + f*g)/(S(3)*f*h) + S(8)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(-e*h + f*g)**(S(3)/2)*atanh(sqrt(f)*sqrt(g + h*x)/sqrt(-e*h + f*g))/(S(3)*f**(S(3)/2)*h) + S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)**(S(3)/2)/(S(3)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*sqrt(g + h*x), x), x, -b*f*p*q*Integral((g + h*x)**(S(3)/2)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)), x)/(S(3)*h) + S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(S(3)/2)/(S(3)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/sqrt(g + h*x), x), x, -b*f*p*q*Integral(sqrt(g + h*x)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)), x)/h + S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*sqrt(g + h*x)/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x)**(S(3)/2), x), x, b*f*p*q*Integral(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)*sqrt(g + h*x)), x)/h - S(2)*sqrt(a + b*log(c*(d*(e + f*x)**p)**q))/(h*sqrt(g + h*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(g + h*x)/sqrt(a + b*log(c*(d*(e + f*x)**p)**q)), x), x, Integral(sqrt(g + h*x)/sqrt(a + b*log(c*(d*(e + f*x)**p)**q)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*sqrt(g + h*x)), x), x, Integral(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*sqrt(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(S(3)/2)), x), x, Integral(S(1)/(sqrt(a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)**(S(3)/2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**n*(g + h*x)**m, x), x, Integral((a + b*log(c*(d*(e + f*x)**p)**q))**n*(g + h*x)**m, x), expand=True, _diff=True, _numerical=True)

    '''long time
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**n*(g + h*x)**S(3), x), x, S(3)*S(2)**(-n + S(-1))*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)**S(2)*(-e*h + f*g)**S(2)*Gamma(n + S(1), (-S(2)*a - S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-S(2)*a/(b*p*q))/f**S(4) + S(4)**(-n + S(-1))*h**S(3)*(c*(d*(e + f*x)**p)**q)**(-S(4)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)**S(4)*Gamma(n + S(1), (-S(4)*a - S(4)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-S(4)*a/(b*p*q))/f**S(4) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)*(-e*h + f*g)**S(3)*Gamma(n + S(1), (-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-a/(b*p*q))/f**S(4) + S(3)**(-n)*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)**S(3)*(-e*h + f*g)*Gamma(n + S(1), (-S(3)*a - S(3)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-S(3)*a/(b*p*q))/f**S(4), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**n*(g + h*x)**S(2), x), x, S(3)**(-n + S(-1))*h**S(2)*(c*(d*(e + f*x)**p)**q)**(-S(3)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)**S(3)*Gamma(n + S(1), (-S(3)*a - S(3)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-S(3)*a/(b*p*q))/f**S(3) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)*(-e*h + f*g)**S(2)*Gamma(n + S(1), (-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-a/(b*p*q))/f**S(3) + S(2)**(-n)*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)**S(2)*(-e*h + f*g)*Gamma(n + S(1), (-S(2)*a - S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-S(2)*a/(b*p*q))/f**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**n*(g + h*x), x), x, S(2)**(-n + S(-1))*h*(c*(d*(e + f*x)**p)**q)**(-S(2)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)**S(2)*Gamma(n + S(1), (-S(2)*a - S(2)*b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-S(2)*a/(b*p*q))/f**S(2) + (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)*(-e*h + f*g)*Gamma(n + S(1), (-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-a/(b*p*q))/f**S(2), expand=True, _diff=True, _numerical=True)
    '''
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**n, x), x, (c*(d*(e + f*x)**p)**q)**(-S(1)/(p*q))*((-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))**(-n)*(a + b*log(c*(d*(e + f*x)**p)**q))**n*(e + f*x)*Gamma(n + S(1), (-a - b*log(c*(d*(e + f*x)**p)**q))/(b*p*q))*exp(-a/(b*p*q))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**n/(g + h*x), x), x, Integral((a + b*log(c*(d*(e + f*x)**p)**q))**n/(g + h*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))*(i + j*x)**S(4)/(d*e + d*f*x), x), x, -S(4)*b*j*x*(-e*j + f*i)**S(3)/(d*f**S(4)) - b*j**S(4)*(e + f*x)**S(4)/(S(16)*d*f**S(5)) - S(4)*b*j**S(3)*(e + f*x)**S(3)*(-e*j + f*i)/(S(9)*d*f**S(5)) - S(3)*b*j**S(2)*(e + f*x)**S(2)*(-e*j + f*i)**S(2)/(S(2)*d*f**S(5)) + j**S(4)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(4)/(S(4)*d*f**S(5)) + S(4)*j**S(3)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(3)*(-e*j + f*i)/(S(3)*d*f**S(5)) + S(3)*j**S(2)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(2)*(-e*j + f*i)**S(2)/(d*f**S(5)) + S(4)*j*(a + b*log(c*(e + f*x)))*(e + f*x)*(-e*j + f*i)**S(3)/(d*f**S(5)) + (a + b*log(c*(e + f*x)))**S(2)*(-e*j + f*i)**S(4)/(S(2)*b*d*f**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))*(i + j*x)**S(3)/(d*e + d*f*x), x), x, -S(3)*b*j*x*(-e*j + f*i)**S(2)/(d*f**S(3)) - b*j**S(3)*(e + f*x)**S(3)/(S(9)*d*f**S(4)) - S(3)*b*j**S(2)*(e + f*x)**S(2)*(-e*j + f*i)/(S(4)*d*f**S(4)) + j**S(3)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(3)/(S(3)*d*f**S(4)) + S(3)*j**S(2)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(2)*(-e*j + f*i)/(S(2)*d*f**S(4)) + S(3)*j*(a + b*log(c*(e + f*x)))*(e + f*x)*(-e*j + f*i)**S(2)/(d*f**S(4)) + (a + b*log(c*(e + f*x)))**S(2)*(-e*j + f*i)**S(3)/(S(2)*b*d*f**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))*(i + j*x)**S(2)/(d*e + d*f*x), x), x, -S(2)*b*j*x*(-e*j + f*i)/(d*f**S(2)) - b*j**S(2)*(e + f*x)**S(2)/(S(4)*d*f**S(3)) + j**S(2)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(2)/(S(2)*d*f**S(3)) + S(2)*j*(a + b*log(c*(e + f*x)))*(e + f*x)*(-e*j + f*i)/(d*f**S(3)) + (a + b*log(c*(e + f*x)))**S(2)*(-e*j + f*i)**S(2)/(S(2)*b*d*f**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))*(i + j*x)/(d*e + d*f*x), x), x, -b*j*x/(d*f) + j*(a + b*log(c*(e + f*x)))*(e + f*x)/(d*f**S(2)) + (a + b*log(c*(e + f*x)))**S(2)*(-e*j/S(2) + f*i/S(2))/(b*d*f**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))/(d*e + d*f*x), x), x, (a + b*log(c*(e + f*x)))**S(2)/(S(2)*b*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))/((i + j*x)*(d*e + d*f*x)), x), x, -b*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)) - (a + b*log(c*(e + f*x)))*log(f*(i + j*x)/(-e*j + f*i))/(d*(-e*j + f*i)) + (a + b*log(c*(e + f*x)))**S(2)/(S(2)*b*d*(-e*j + f*i)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))/((i + j*x)**S(2)*(d*e + d*f*x)), x), x, -b*f*log(e + f*x)/(d*(-e*j + f*i)**S(2)) + b*f*log(i + j*x)/(d*(-e*j + f*i)**S(2)) - b*f*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(2)) - f*(a + b*log(c*(e + f*x)))*log(f*(i + j*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(2)) + (a + b*log(c*(e + f*x)))/(d*(i + j*x)*(-e*j + f*i)) + f*(a + b*log(c*(e + f*x)))**S(2)/(S(2)*b*d*(-e*j + f*i)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))/((i + j*x)**S(3)*(d*e + d*f*x)), x), x, -S(3)*b*f**S(2)*log(e + f*x)/(S(2)*d*(-e*j + f*i)**S(3)) + S(3)*b*f**S(2)*log(i + j*x)/(S(2)*d*(-e*j + f*i)**S(3)) - b*f**S(2)*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(3)) - b*f/(S(2)*d*(i + j*x)*(-e*j + f*i)**S(2)) - f**S(2)*(a + b*log(c*(e + f*x)))*log(f*(i + j*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(3)) + f*(a + b*log(c*(e + f*x)))/(d*(i + j*x)*(-e*j + f*i)**S(2)) + (a/S(2) + b*log(c*(e + f*x))/S(2))/(d*(i + j*x)**S(2)*(-e*j + f*i)) + f**S(2)*(a + b*log(c*(e + f*x)))**S(2)/(S(2)*b*d*(-e*j + f*i)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**S(2)*(i + j*x)**S(4)/(d*e + d*f*x), x), x, S(8)*b**S(2)*j*x*(-e*j + f*i)**S(3)/(d*f**S(4)) + b**S(2)*j**S(4)*(e + f*x)**S(4)/(S(32)*d*f**S(5)) + S(8)*b**S(2)*j**S(3)*(e + f*x)**S(3)*(-e*j + f*i)/(S(27)*d*f**S(5)) + S(3)*b**S(2)*j**S(2)*(e + f*x)**S(2)*(-e*j + f*i)**S(2)/(S(2)*d*f**S(5)) - b*j**S(4)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(4)/(S(8)*d*f**S(5)) - S(8)*b*j**S(3)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(3)*(-e*j + f*i)/(S(9)*d*f**S(5)) - S(3)*b*j**S(2)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(2)*(-e*j + f*i)**S(2)/(d*f**S(5)) - S(8)*b*j*(a + b*log(c*(e + f*x)))*(e + f*x)*(-e*j + f*i)**S(3)/(d*f**S(5)) + j**S(4)*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)**S(4)/(S(4)*d*f**S(5)) + S(4)*j**S(3)*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)**S(3)*(-e*j + f*i)/(S(3)*d*f**S(5)) + S(3)*j**S(2)*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)**S(2)*(-e*j + f*i)**S(2)/(d*f**S(5)) + S(4)*j*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)*(-e*j + f*i)**S(3)/(d*f**S(5)) + (a + b*log(c*(e + f*x)))**S(3)*(-e*j + f*i)**S(4)/(S(3)*b*d*f**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**S(2)*(i + j*x)**S(3)/(d*e + d*f*x), x), x, S(6)*b**S(2)*j*x*(-e*j + f*i)**S(2)/(d*f**S(3)) + S(2)*b**S(2)*j**S(3)*(e + f*x)**S(3)/(S(27)*d*f**S(4)) + S(3)*b**S(2)*j**S(2)*(e + f*x)**S(2)*(-e*j + f*i)/(S(4)*d*f**S(4)) - S(2)*b*j**S(3)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(3)/(S(9)*d*f**S(4)) - S(3)*b*j**S(2)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(2)*(-e*j + f*i)/(S(2)*d*f**S(4)) - S(6)*b*j*(a + b*log(c*(e + f*x)))*(e + f*x)*(-e*j + f*i)**S(2)/(d*f**S(4)) + j**S(3)*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)**S(3)/(S(3)*d*f**S(4)) + S(3)*j**S(2)*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)**S(2)*(-e*j + f*i)/(S(2)*d*f**S(4)) + S(3)*j*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)*(-e*j + f*i)**S(2)/(d*f**S(4)) + (a + b*log(c*(e + f*x)))**S(3)*(-e*j + f*i)**S(3)/(S(3)*b*d*f**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**S(2)*(i + j*x)**S(2)/(d*e + d*f*x), x), x, S(4)*b**S(2)*j*x*(-e*j + f*i)/(d*f**S(2)) + b**S(2)*j**S(2)*(e + f*x)**S(2)/(S(4)*d*f**S(3)) - b*j**S(2)*(a + b*log(c*(e + f*x)))*(e + f*x)**S(2)/(S(2)*d*f**S(3)) - S(4)*b*j*(a + b*log(c*(e + f*x)))*(e + f*x)*(-e*j + f*i)/(d*f**S(3)) + j**S(2)*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)**S(2)/(S(2)*d*f**S(3)) + S(2)*j*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)*(-e*j + f*i)/(d*f**S(3)) + (a + b*log(c*(e + f*x)))**S(3)*(-e*j + f*i)**S(2)/(S(3)*b*d*f**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**S(2)*(i + j*x)/(d*e + d*f*x), x), x, S(2)*b**S(2)*j*x/(d*f) - S(2)*b*j*(a + b*log(c*(e + f*x)))*(e + f*x)/(d*f**S(2)) + j*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)/(d*f**S(2)) + (a + b*log(c*(e + f*x)))**S(3)*(-e*j/S(3) + f*i/S(3))/(b*d*f**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**S(2)/(d*e + d*f*x), x), x, (a + b*log(c*(e + f*x)))**S(3)/(S(3)*b*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**S(2)/((i + j*x)*(d*e + d*f*x)), x), x, S(2)*b**S(2)*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)) - S(2)*b*(a + b*log(c*(e + f*x)))*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)) - (a + b*log(c*(e + f*x)))**S(2)*log(f*(i + j*x)/(-e*j + f*i))/(d*(-e*j + f*i)) + (a + b*log(c*(e + f*x)))**S(3)/(S(3)*b*d*(-e*j + f*i)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**S(2)/((i + j*x)**S(2)*(d*e + d*f*x)), x), x, S(2)*b**S(2)*f*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(2)) + S(2)*b**S(2)*f*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(2)) + S(2)*b*f*(a + b*log(c*(e + f*x)))*log(f*(i + j*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(2)) - S(2)*b*f*(a + b*log(c*(e + f*x)))*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(2)) - f*(a + b*log(c*(e + f*x)))**S(2)*log(f*(i + j*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(2)) - j*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)/(d*(i + j*x)*(-e*j + f*i)**S(2)) + f*(a + b*log(c*(e + f*x)))**S(3)/(S(3)*b*d*(-e*j + f*i)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**S(2)/((i + j*x)**S(3)*(d*e + d*f*x)), x), x, b**S(2)*f**S(2)*log(e + f*x)/(d*(-e*j + f*i)**S(3)) - b**S(2)*f**S(2)*log(i + j*x)/(d*(-e*j + f*i)**S(3)) + S(3)*b**S(2)*f**S(2)*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(3)) + S(2)*b**S(2)*f**S(2)*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(3)) + S(3)*b*f**S(2)*(a + b*log(c*(e + f*x)))*log(f*(i + j*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(3)) - S(2)*b*f**S(2)*(a + b*log(c*(e + f*x)))*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(3)) - b*f*(a + b*log(c*(e + f*x)))/(d*(i + j*x)*(-e*j + f*i)**S(2)) - f**S(2)*(a + b*log(c*(e + f*x)))**S(2)*log(f*(i + j*x)/(-e*j + f*i))/(d*(-e*j + f*i)**S(3)) - f**S(2)*(a + b*log(c*(e + f*x)))**S(2)/(S(2)*d*(-e*j + f*i)**S(3)) - f*j*(a + b*log(c*(e + f*x)))**S(2)*(e + f*x)/(d*(i + j*x)*(-e*j + f*i)**S(3)) + (a + b*log(c*(e + f*x)))**S(2)/(S(2)*d*(i + j*x)**S(2)*(-e*j + f*i)) + f**S(2)*(a + b*log(c*(e + f*x)))**S(3)/(S(3)*b*d*(-e*j + f*i)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((i + j*x)**S(4)/((a + b*log(c*(e + f*x)))*(d*e + d*f*x)), x), x, (-e*j + f*i)**S(4)*log(a + b*log(c*(e + f*x)))/(b*d*f**S(5)) + S(4)*j*(-e*j + f*i)**S(3)*exp(-a/b)*Ei((a + b*log(c*(e + f*x)))/b)/(b*c*d*f**S(5)) + S(6)*j**S(2)*(-e*j + f*i)**S(2)*exp(-S(2)*a/b)*Ei((S(2)*a + S(2)*b*log(c*(e + f*x)))/b)/(b*c**S(2)*d*f**S(5)) + S(4)*j**S(3)*(-e*j + f*i)*exp(-S(3)*a/b)*Ei((S(3)*a + S(3)*b*log(c*(e + f*x)))/b)/(b*c**S(3)*d*f**S(5)) + j**S(4)*exp(-S(4)*a/b)*Ei((S(4)*a + S(4)*b*log(c*(e + f*x)))/b)/(b*c**S(4)*d*f**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((i + j*x)**S(3)/((a + b*log(c*(e + f*x)))*(d*e + d*f*x)), x), x, (-e*j + f*i)**S(3)*log(a + b*log(c*(e + f*x)))/(b*d*f**S(4)) + S(3)*j*(-e*j + f*i)**S(2)*exp(-a/b)*Ei((a + b*log(c*(e + f*x)))/b)/(b*c*d*f**S(4)) + S(3)*j**S(2)*(-e*j + f*i)*exp(-S(2)*a/b)*Ei((S(2)*a + S(2)*b*log(c*(e + f*x)))/b)/(b*c**S(2)*d*f**S(4)) + j**S(3)*exp(-S(3)*a/b)*Ei((S(3)*a + S(3)*b*log(c*(e + f*x)))/b)/(b*c**S(3)*d*f**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((i + j*x)**S(2)/((a + b*log(c*(e + f*x)))*(d*e + d*f*x)), x), x, (-e*j + f*i)**S(2)*log(a + b*log(c*(e + f*x)))/(b*d*f**S(3)) + S(2)*j*(-e*j + f*i)*exp(-a/b)*Ei((a + b*log(c*(e + f*x)))/b)/(b*c*d*f**S(3)) + j**S(2)*exp(-S(2)*a/b)*Ei((S(2)*a + S(2)*b*log(c*(e + f*x)))/b)/(b*c**S(2)*d*f**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((i + j*x)/((a + b*log(c*(e + f*x)))*(d*e + d*f*x)), x), x, (-e*j + f*i)*log(a + b*log(c*(e + f*x)))/(b*d*f**S(2)) + j*exp(-a/b)*Ei((a + b*log(c*(e + f*x)))/b)/(b*c*d*f**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(e + f*x)))*(d*e + d*f*x)), x), x, log(a + b*log(c*(e + f*x)))/(b*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(e + f*x)))*(i + j*x)*(d*e + d*f*x)), x), x, Integral(S(1)/((a + b*log(c*(e + f*x)))*(i + j*x)*(d*e + d*f*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(e + f*x)))*(i + j*x)**S(2)*(d*e + d*f*x)), x), x, Integral(S(1)/((a + b*log(c*(e + f*x)))*(i + j*x)**S(2)*(d*e + d*f*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d + e*x)**n))*(f + g*x)**(S(5)/2)/(d + e*x), x), x, -S(4)*b*n*(f + g*x)**(S(5)/2)/(S(25)*e) - S(32)*b*n*(f + g*x)**(S(3)/2)*(-d*g + e*f)/(S(45)*e**S(2)) - S(92)*b*n*sqrt(f + g*x)*(-d*g + e*f)**S(2)/(S(15)*e**S(3)) - S(4)*b*n*(-d*g + e*f)**(S(5)/2)*log(S(2)/(-sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f) + S(1)))*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))/e**(S(7)/2) + S(2)*b*n*(-d*g + e*f)**(S(5)/2)*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))**S(2)/e**(S(7)/2) + S(92)*b*n*(-d*g + e*f)**(S(5)/2)*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))/(S(15)*e**(S(7)/2)) - S(2)*b*n*(-d*g + e*f)**(S(5)/2)*polylog(S(2), (-sqrt(e)*sqrt(f + g*x) - sqrt(-d*g + e*f))/(-sqrt(e)*sqrt(f + g*x) + sqrt(-d*g + e*f)))/e**(S(7)/2) + S(2)*(a + b*log(c*(d + e*x)**n))*(f + g*x)**(S(5)/2)/(S(5)*e) + (a + b*log(c*(d + e*x)**n))*(f + g*x)**(S(3)/2)*(-S(2)*d*g/S(3) + S(2)*e*f/S(3))/e**S(2) + S(2)*(a + b*log(c*(d + e*x)**n))*sqrt(f + g*x)*(-d*g + e*f)**S(2)/e**S(3) - S(2)*(a + b*log(c*(d + e*x)**n))*(-d*g + e*f)**(S(5)/2)*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))/e**(S(7)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d + e*x)**n))*(f + g*x)**(S(3)/2)/(d + e*x), x), x, -S(4)*b*n*(f + g*x)**(S(3)/2)/(S(9)*e) - S(16)*b*n*sqrt(f + g*x)*(-d*g + e*f)/(S(3)*e**S(2)) - S(4)*b*n*(-d*g + e*f)**(S(3)/2)*log(S(2)/(-sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f) + S(1)))*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))/e**(S(5)/2) + S(2)*b*n*(-d*g + e*f)**(S(3)/2)*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))**S(2)/e**(S(5)/2) + S(16)*b*n*(-d*g + e*f)**(S(3)/2)*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))/(S(3)*e**(S(5)/2)) - S(2)*b*n*(-d*g + e*f)**(S(3)/2)*polylog(S(2), (-sqrt(e)*sqrt(f + g*x) - sqrt(-d*g + e*f))/(-sqrt(e)*sqrt(f + g*x) + sqrt(-d*g + e*f)))/e**(S(5)/2) + S(2)*(a + b*log(c*(d + e*x)**n))*(f + g*x)**(S(3)/2)/(S(3)*e) + (a + b*log(c*(d + e*x)**n))*sqrt(f + g*x)*(-S(2)*d*g + S(2)*e*f)/e**S(2) - S(2)*(a + b*log(c*(d + e*x)**n))*(-d*g + e*f)**(S(3)/2)*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))/e**(S(5)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d + e*x)**n))*sqrt(f + g*x)/(d + e*x), x), x, -S(4)*b*n*sqrt(f + g*x)/e - S(4)*b*n*sqrt(-d*g + e*f)*log(S(2)/(-sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f) + S(1)))*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))/e**(S(3)/2) + S(2)*b*n*sqrt(-d*g + e*f)*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))**S(2)/e**(S(3)/2) + S(4)*b*n*sqrt(-d*g + e*f)*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))/e**(S(3)/2) - S(2)*b*n*sqrt(-d*g + e*f)*polylog(S(2), (-sqrt(e)*sqrt(f + g*x) - sqrt(-d*g + e*f))/(-sqrt(e)*sqrt(f + g*x) + sqrt(-d*g + e*f)))/e**(S(3)/2) + (S(2)*a + S(2)*b*log(c*(d + e*x)**n))*sqrt(f + g*x)/e - S(2)*(a + b*log(c*(d + e*x)**n))*sqrt(-d*g + e*f)*atanh(sqrt(e)*sqrt(f + g*x)/sqrt(-d*g + e*f))/e**(S(3)/2), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(sqrt(d + e*x)*log(a + b*x)/(a + b*x), x), x, S(2)*sqrt(d + e*x)*log(a + b*x)/b - S(4)*sqrt(d + e*x)/b - S(2)*sqrt(-a*e + b*d)*log(a + b*x)*atanh(sqrt(b)*sqrt(d + e*x)/sqrt(-a*e + b*d))/b**(S(3)/2) - S(4)*sqrt(-a*e + b*d)*log(S(2)/(-sqrt(b)*sqrt(d + e*x)/sqrt(-a*e + b*d) + S(1)))*atanh(sqrt(b)*sqrt(d + e*x)/sqrt(-a*e + b*d))/b**(S(3)/2) + S(2)*sqrt(-a*e + b*d)*atanh(sqrt(b)*sqrt(d + e*x)/sqrt(-a*e + b*d))**S(2)/b**(S(3)/2) + S(4)*sqrt(-a*e + b*d)*atanh(sqrt(b)*sqrt(d + e*x)/sqrt(-a*e + b*d))/b**(S(3)/2) - S(2)*sqrt(-a*e + b*d)*polylog(S(2), (-sqrt(b)*sqrt(d + e*x) - sqrt(-a*e + b*d))/(-sqrt(b)*sqrt(d + e*x) + sqrt(-a*e + b*d)))/b**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**S(2)*(i + j*x)**m/(d*e + d*f*x), x), x, Integral((a + b*log(c*(e + f*x)))**S(2)*(i + j*x)**m/(d*e + d*f*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))*(i + j*x)**m/(d*e + d*f*x), x), x, Integral((a + b*log(c*(e + f*x)))*(i + j*x)**m/(d*e + d*f*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**n*(i + j*x)**m/(d*e + d*f*x), x), x, Integral((a + b*log(c*(e + f*x)))**n*(i + j*x)**m/(d*e + d*f*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**n*(i + j*x)**S(4)/(d*e + d*f*x), x), x, S(4)*S(3)**(-n + S(-1))*j**S(3)*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*(-e*j + f*i)*Gamma(n + S(1), (-S(3)*a - S(3)*b*log(c*(e + f*x)))/b)*exp(-S(3)*a/b)/(c**S(3)*d*f**S(5)) + S(4)**(-n + S(-1))*j**S(4)*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*Gamma(n + S(1), (-S(4)*a - S(4)*b*log(c*(e + f*x)))/b)*exp(-S(4)*a/b)/(c**S(4)*d*f**S(5)) + S(4)*j*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*(-e*j + f*i)**S(3)*Gamma(n + S(1), (-a - b*log(c*(e + f*x)))/b)*exp(-a/b)/(c*d*f**S(5)) + (a + b*log(c*(e + f*x)))**(n + S(1))*(-e*j + f*i)**S(4)/(b*d*f**S(5)*(n + S(1))) + S(3)*S(2)**(-n)*j**S(2)*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*(-e*j + f*i)**S(2)*Gamma(n + S(1), (-S(2)*a - S(2)*b*log(c*(e + f*x)))/b)*exp(-S(2)*a/b)/(c**S(2)*d*f**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**n*(i + j*x)**S(3)/(d*e + d*f*x), x), x, S(3)*S(2)**(-n + S(-1))*j**S(2)*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*(-e*j + f*i)*Gamma(n + S(1), (-S(2)*a - S(2)*b*log(c*(e + f*x)))/b)*exp(-S(2)*a/b)/(c**S(2)*d*f**S(4)) + S(3)**(-n + S(-1))*j**S(3)*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*Gamma(n + S(1), (-S(3)*a - S(3)*b*log(c*(e + f*x)))/b)*exp(-S(3)*a/b)/(c**S(3)*d*f**S(4)) + S(3)*j*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*(-e*j + f*i)**S(2)*Gamma(n + S(1), (-a - b*log(c*(e + f*x)))/b)*exp(-a/b)/(c*d*f**S(4)) + (a + b*log(c*(e + f*x)))**(n + S(1))*(-e*j + f*i)**S(3)/(b*d*f**S(4)*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**n*(i + j*x)**S(2)/(d*e + d*f*x), x), x, S(2)**(-n + S(-1))*j**S(2)*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*Gamma(n + S(1), (-S(2)*a - S(2)*b*log(c*(e + f*x)))/b)*exp(-S(2)*a/b)/(c**S(2)*d*f**S(3)) + S(2)*j*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*(-e*j + f*i)*Gamma(n + S(1), (-a - b*log(c*(e + f*x)))/b)*exp(-a/b)/(c*d*f**S(3)) + (a + b*log(c*(e + f*x)))**(n + S(1))*(-e*j + f*i)**S(2)/(b*d*f**S(3)*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**n*(i + j*x)/(d*e + d*f*x), x), x, j*((-a - b*log(c*(e + f*x)))/b)**(-n)*(a + b*log(c*(e + f*x)))**n*Gamma(n + S(1), (-a - b*log(c*(e + f*x)))/b)*exp(-a/b)/(c*d*f**S(2)) + (a + b*log(c*(e + f*x)))**(n + S(1))*(-e*j + f*i)/(b*d*f**S(2)*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**n/(d*e + d*f*x), x), x, (a + b*log(c*(e + f*x)))**(n + S(1))/(b*d*f*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**n/((i + j*x)*(d*e + d*f*x)), x), x, Integral((a + b*log(c*(e + f*x)))**n/((i + j*x)*(d*e + d*f*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**n/((i + j*x)**S(2)*(d*e + d*f*x)), x), x, Integral((a + b*log(c*(e + f*x)))**n/((i + j*x)**S(2)*(d*e + d*f*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(e + f*x)))**n/((i + j*x)**S(3)*(d*e + d*f*x)), x), x, Integral((a + b*log(c*(e + f*x)))**n/((i + j*x)**S(3)*(d*e + d*f*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*(i + j*x)**S(3)/(g + h*x), x), x, a*j*x*(-g*j + h*i)**S(2)/h**S(3) - b*p*q*(i + j*x)**S(3)/(S(9)*h) - b*p*q*(i + j*x)**S(2)*(-g*j + h*i)/(S(4)*h**S(2)) - b*j*p*q*x*(-g*j + h*i)**S(2)/h**S(3) + b*p*q*(-g*j + h*i)**S(3)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h**S(4) - b*p*q*(i + j*x)**S(2)*(-e*j + f*i)/(S(6)*f*h) - b*j*p*q*x*(-e*j + f*i)*(-g*j + h*i)/(S(2)*f*h**S(2)) + b*j*(e + f*x)*(-g*j + h*i)**S(2)*log(c*(d*(e + f*x)**p)**q)/(f*h**S(3)) - b*j*p*q*x*(-e*j + f*i)**S(2)/(S(3)*f**S(2)*h) - b*p*q*(-e*j + f*i)**S(2)*(-g*j + h*i)*log(e + f*x)/(S(2)*f**S(2)*h**S(2)) - b*p*q*(-e*j + f*i)**S(3)*log(e + f*x)/(S(3)*f**S(3)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))*(i + j*x)**S(3)/(S(3)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))*(i + j*x)**S(2)*(-g*j/S(2) + h*i/S(2))/h**S(2) + (a + b*log(c*(d*(e + f*x)**p)**q))*(-g*j + h*i)**S(3)*log(f*(g + h*x)/(-e*h + f*g))/h**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*(i + j*x)**S(2)/(g + h*x), x), x, a*j*x*(-g*j + h*i)/h**S(2) - b*p*q*(i + j*x)**S(2)/(S(4)*h) - b*j*p*q*x*(-g*j + h*i)/h**S(2) + b*p*q*(-g*j + h*i)**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h**S(3) - b*j*p*q*x*(-e*j + f*i)/(S(2)*f*h) + b*j*(e + f*x)*(-g*j + h*i)*log(c*(d*(e + f*x)**p)**q)/(f*h**S(2)) - b*p*q*(-e*j + f*i)**S(2)*log(e + f*x)/(S(2)*f**S(2)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))*(i + j*x)**S(2)/(S(2)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))*(-g*j + h*i)**S(2)*log(f*(g + h*x)/(-e*h + f*g))/h**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))*(i + j*x)/(g + h*x), x), x, a*j*x/h - b*j*p*q*x/h + b*p*q*(-g*j + h*i)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h**S(2) + b*j*(e + f*x)*log(c*(d*(e + f*x)**p)**q)/(f*h) + (a + b*log(c*(d*(e + f*x)**p)**q))*(-g*j + h*i)*log(f*(g + h*x)/(-e*h + f*g))/h**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x), x), x, b*p*q*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h + (a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/((g + h*x)*(i + j*x)), x), x, b*p*q*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i) - b*p*q*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i) + (a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/(-g*j + h*i) - (a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(i + j*x)/(-e*j + f*i))/(-g*j + h*i), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/((g + h*x)*(i + j*x)**S(2)), x), x, -b*f*p*q*log(e + f*x)/((-e*j + f*i)*(-g*j + h*i)) + b*f*p*q*log(i + j*x)/((-e*j + f*i)*(-g*j + h*i)) + b*h*p*q*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(2) - b*h*p*q*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(2) + h*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/(-g*j + h*i)**S(2) - h*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(i + j*x)/(-e*j + f*i))/(-g*j + h*i)**S(2) + (a + b*log(c*(d*(e + f*x)**p)**q))/((i + j*x)*(-g*j + h*i)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/((g + h*x)*(i + j*x)**S(3)), x), x, -b*f**S(2)*p*q*log(e + f*x)/(S(2)*(-e*j + f*i)**S(2)*(-g*j + h*i)) + b*f**S(2)*p*q*log(i + j*x)/(S(2)*(-e*j + f*i)**S(2)*(-g*j + h*i)) - b*f*h*p*q*log(e + f*x)/((-e*j + f*i)*(-g*j + h*i)**S(2)) + b*f*h*p*q*log(i + j*x)/((-e*j + f*i)*(-g*j + h*i)**S(2)) - b*f*p*q/(S(2)*(i + j*x)*(-e*j + f*i)*(-g*j + h*i)) + b*h**S(2)*p*q*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(3) - b*h**S(2)*p*q*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(3) + h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(g + h*x)/(-e*h + f*g))/(-g*j + h*i)**S(3) - h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(i + j*x)/(-e*j + f*i))/(-g*j + h*i)**S(3) + h*(a + b*log(c*(d*(e + f*x)**p)**q))/((i + j*x)*(-g*j + h*i)**S(2)) + (a/S(2) + b*log(c*(d*(e + f*x)**p)**q)/S(2))/((i + j*x)**S(2)*(-g*j + h*i)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(i + j*x)**S(3)/(g + h*x), x), x, -S(2)*a*b*j*p*q*x*(-g*j + h*i)**S(2)/h**S(3) - S(2)*a*b*j*p*q*x*(-e*j + f*i)*(-g*j + h*i)/(f*h**S(2)) - S(2)*a*b*j*p*q*x*(-e*j + f*i)**S(2)/(S(3)*f**S(2)*h) + b**S(2)*e*j**S(2)*p**S(2)*q**S(2)*x*(-g*j + h*i)/(S(2)*f*h**S(2)) + S(2)*b**S(2)*p**S(2)*q**S(2)*(i + j*x)**S(3)/(S(27)*h) + b**S(2)*j**S(2)*p**S(2)*q**S(2)*x**S(2)*(-g*j + h*i)/(S(4)*h**S(2)) + S(2)*b**S(2)*j*p**S(2)*q**S(2)*x*(-g*j + h*i)**S(2)/h**S(3) - S(2)*b**S(2)*p**S(2)*q**S(2)*(-g*j + h*i)**S(3)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h**S(4) + S(5)*b**S(2)*p**S(2)*q**S(2)*(i + j*x)**S(2)*(-e*j + f*i)/(S(18)*f*h) + S(2)*b**S(2)*j*p**S(2)*q**S(2)*x*(-e*j + f*i)*(-g*j + h*i)/(f*h**S(2)) - S(2)*b**S(2)*j*p*q*(e + f*x)*(-g*j + h*i)**S(2)*log(c*(d*(e + f*x)**p)**q)/(f*h**S(3)) + S(11)*b**S(2)*j*p**S(2)*q**S(2)*x*(-e*j + f*i)**S(2)/(S(9)*f**S(2)*h) - S(2)*b**S(2)*j*p*q*(e + f*x)*(-e*j + f*i)*(-g*j + h*i)*log(c*(d*(e + f*x)**p)**q)/(f**S(2)*h**S(2)) - S(2)*b**S(2)*j*p*q*(e + f*x)*(-e*j + f*i)**S(2)*log(c*(d*(e + f*x)**p)**q)/(S(3)*f**S(3)*h) + S(5)*b**S(2)*p**S(2)*q**S(2)*(-e*j + f*i)**S(3)*log(e + f*x)/(S(9)*f**S(3)*h) - S(2)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(i + j*x)**S(3)/(S(9)*h) + S(2)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(-g*j + h*i)**S(3)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h**S(4) - b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(i + j*x)**S(2)*(-e*j + f*i)/(S(3)*f*h) - b*j**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-g*j + h*i)/(S(2)*f**S(2)*h**S(2)) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(i + j*x)**S(3)/(S(3)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(-g*j + h*i)**S(3)*log(f*(g + h*x)/(-e*h + f*g))/h**S(4) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-g*j + h*i)**S(2)/(f*h**S(3)) + j**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(2)*(-g*j + h*i)/(S(2)*f**S(2)*h**S(2)) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-e*j + f*i)*(-g*j + h*i)/(f**S(2)*h**S(2)) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(-e*j + f*i)**S(3)/(S(3)*f**S(3)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(i + j*x)**S(2)/(g + h*x), x), x, -S(2)*a*b*j*p*q*x*(-g*j + h*i)/h**S(2) - S(2)*a*b*j*p*q*x*(-e*j + f*i)/(f*h) + b**S(2)*e*j**S(2)*p**S(2)*q**S(2)*x/(S(2)*f*h) + b**S(2)*j**S(2)*p**S(2)*q**S(2)*x**S(2)/(S(4)*h) + S(2)*b**S(2)*j*p**S(2)*q**S(2)*x*(-g*j + h*i)/h**S(2) - S(2)*b**S(2)*p**S(2)*q**S(2)*(-g*j + h*i)**S(2)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h**S(3) + S(2)*b**S(2)*j*p**S(2)*q**S(2)*x*(-e*j + f*i)/(f*h) - S(2)*b**S(2)*j*p*q*(e + f*x)*(-g*j + h*i)*log(c*(d*(e + f*x)**p)**q)/(f*h**S(2)) - S(2)*b**S(2)*j*p*q*(e + f*x)*(-e*j + f*i)*log(c*(d*(e + f*x)**p)**q)/(f**S(2)*h) + S(2)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(-g*j + h*i)**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h**S(3) - b*j**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)/(S(2)*f**S(2)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(-g*j + h*i)**S(2)*log(f*(g + h*x)/(-e*h + f*g))/h**S(3) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-g*j + h*i)/(f*h**S(2)) + j**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(2)/(S(2)*f**S(2)*h) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-e*j + f*i)/(f**S(2)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(i + j*x)/(g + h*x), x), x, -S(2)*a*b*j*p*q*x/h + S(2)*b**S(2)*j*p**S(2)*q**S(2)*x/h - S(2)*b**S(2)*p**S(2)*q**S(2)*(-g*j + h*i)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h**S(2) - S(2)*b**S(2)*j*p*q*(e + f*x)*log(c*(d*(e + f*x)**p)**q)/(f*h) + S(2)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*(-g*j + h*i)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h**S(2) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(-g*j + h*i)*log(f*(g + h*x)/(-e*h + f*g))/h**S(2) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/(f*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(g + h*x), x), x, -S(2)*b**S(2)*p**S(2)*q**S(2)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h + S(2)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(g + h*x)/(-e*h + f*g))/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/((g + h*x)*(i + j*x)), x), x, -S(2)*b**S(2)*p**S(2)*q**S(2)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i) + S(2)*b**S(2)*p**S(2)*q**S(2)*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i) + S(2)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i) - S(2)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(g + h*x)/(-e*h + f*g))/(-g*j + h*i) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(i + j*x)/(-e*j + f*i))/(-g*j + h*i), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/((g + h*x)*(i + j*x)**S(2)), x), x, S(2)*b**S(2)*f*p**S(2)*q**S(2)*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)) - S(2)*b**S(2)*h*p**S(2)*q**S(2)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(2) + S(2)*b**S(2)*h*p**S(2)*q**S(2)*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(2) + S(2)*b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(i + j*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)) + S(2)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(2) - S(2)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(2) + h*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(g + h*x)/(-e*h + f*g))/(-g*j + h*i)**S(2) - h*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(i + j*x)/(-e*j + f*i))/(-g*j + h*i)**S(2) - j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/((i + j*x)*(-e*j + f*i)*(-g*j + h*i)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/((g + h*x)*(i + j*x)**S(3)), x), x, b**S(2)*f**S(2)*p**S(2)*q**S(2)*log(e + f*x)/((-e*j + f*i)**S(2)*(-g*j + h*i)) - b**S(2)*f**S(2)*p**S(2)*q**S(2)*log(i + j*x)/((-e*j + f*i)**S(2)*(-g*j + h*i)) + b**S(2)*f**S(2)*p**S(2)*q**S(2)*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)**S(2)*(-g*j + h*i)) + S(2)*b**S(2)*f*h*p**S(2)*q**S(2)*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)**S(2)) - S(2)*b**S(2)*h**S(2)*p**S(2)*q**S(2)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(3) + S(2)*b**S(2)*h**S(2)*p**S(2)*q**S(2)*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(3) + b*f**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(i + j*x)/(-e*j + f*i))/((-e*j + f*i)**S(2)*(-g*j + h*i)) + S(2)*b*f*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(i + j*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)**S(2)) - b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))/((i + j*x)*(-e*j + f*i)*(-g*j + h*i)) + S(2)*b*h**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(3) - S(2)*b*h**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(3) - f**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(2)*(-e*j + f*i)**S(2)*(-g*j + h*i)) + h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(g + h*x)/(-e*h + f*g))/(-g*j + h*i)**S(3) - h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(i + j*x)/(-e*j + f*i))/(-g*j + h*i)**S(3) - h*j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/((i + j*x)*(-e*j + f*i)*(-g*j + h*i)**S(2)) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(2)/(S(2)*(i + j*x)**S(2)*(-g*j + h*i)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(i + j*x)**S(3)/(g + h*x), x), x, S(6)*a*b**S(2)*j*p**S(2)*q**S(2)*x*(-g*j + h*i)**S(2)/h**S(3) + S(6)*a*b**S(2)*j*p**S(2)*q**S(2)*x*(-e*j + f*i)*(-g*j + h*i)/(f*h**S(2)) + S(6)*a*b**S(2)*j*p**S(2)*q**S(2)*x*(-e*j + f*i)**S(2)/(f**S(2)*h) - S(3)*b**S(3)*e*j**S(2)*p**S(3)*q**S(3)*x*(-g*j + h*i)/(S(4)*f*h**S(2)) - S(3)*b**S(3)*e*j**S(2)*p**S(3)*q**S(3)*x*(-e*j + f*i)/(S(2)*f**S(2)*h) - S(3)*b**S(3)*j**S(2)*p**S(3)*q**S(3)*x**S(2)*(-g*j + h*i)/(S(8)*h**S(2)) - S(6)*b**S(3)*j*p**S(3)*q**S(3)*x*(-g*j + h*i)**S(2)/h**S(3) + S(6)*b**S(3)*p**S(3)*q**S(3)*(-g*j + h*i)**S(3)*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/h**S(4) - S(3)*b**S(3)*j**S(2)*p**S(3)*q**S(3)*x**S(2)*(-e*j + f*i)/(S(4)*f*h) - S(6)*b**S(3)*j*p**S(3)*q**S(3)*x*(-e*j + f*i)*(-g*j + h*i)/(f*h**S(2)) + S(6)*b**S(3)*j*p**S(2)*q**S(2)*(e + f*x)*(-g*j + h*i)**S(2)*log(c*(d*(e + f*x)**p)**q)/(f*h**S(3)) - S(6)*b**S(3)*j*p**S(3)*q**S(3)*x*(-e*j + f*i)**S(2)/(f**S(2)*h) + S(6)*b**S(3)*j*p**S(2)*q**S(2)*(e + f*x)*(-e*j + f*i)*(-g*j + h*i)*log(c*(d*(e + f*x)**p)**q)/(f**S(2)*h**S(2)) - S(2)*b**S(3)*j**S(3)*p**S(3)*q**S(3)*(e + f*x)**S(3)/(S(27)*f**S(3)*h) + S(6)*b**S(3)*j*p**S(2)*q**S(2)*(e + f*x)*(-e*j + f*i)**S(2)*log(c*(d*(e + f*x)**p)**q)/(f**S(3)*h) - S(6)*b**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(-g*j + h*i)**S(3)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h**S(4) + S(3)*b**S(2)*j**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-g*j + h*i)/(S(4)*f**S(2)*h**S(2)) + S(2)*b**S(2)*j**S(3)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(3)/(S(9)*f**S(3)*h) + S(3)*b**S(2)*j**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)*(-e*j + f*i)/(S(2)*f**S(3)*h) + S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(-g*j + h*i)**S(3)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h**S(4) - S(3)*b*j*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-g*j + h*i)**S(2)/(f*h**S(3)) - S(3)*b*j**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(2)*(-g*j + h*i)/(S(4)*f**S(2)*h**S(2)) - S(3)*b*j*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-e*j + f*i)*(-g*j + h*i)/(f**S(2)*h**S(2)) - b*j**S(3)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(3)/(S(3)*f**S(3)*h) - S(3)*b*j**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(2)*(-e*j + f*i)/(S(2)*f**S(3)*h) - S(3)*b*j*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-e*j + f*i)**S(2)/(f**S(3)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(-g*j + h*i)**S(3)*log(f*(g + h*x)/(-e*h + f*g))/h**S(4) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)*(-g*j + h*i)**S(2)/(f*h**S(3)) + j**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(2)*(-g*j + h*i)/(S(2)*f**S(2)*h**S(2)) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)*(-e*j + f*i)*(-g*j + h*i)/(f**S(2)*h**S(2)) + j**S(3)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(3)/(S(3)*f**S(3)*h) + j**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(2)*(-e*j + f*i)/(f**S(3)*h) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)*(-e*j + f*i)**S(2)/(f**S(3)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(i + j*x)**S(2)/(g + h*x), x), x, S(6)*a*b**S(2)*j*p**S(2)*q**S(2)*x*(-g*j + h*i)/h**S(2) + S(6)*a*b**S(2)*j*p**S(2)*q**S(2)*x*(-e*j + f*i)/(f*h) - S(3)*b**S(3)*e*j**S(2)*p**S(3)*q**S(3)*x/(S(4)*f*h) - S(3)*b**S(3)*j**S(2)*p**S(3)*q**S(3)*x**S(2)/(S(8)*h) - S(6)*b**S(3)*j*p**S(3)*q**S(3)*x*(-g*j + h*i)/h**S(2) + S(6)*b**S(3)*p**S(3)*q**S(3)*(-g*j + h*i)**S(2)*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/h**S(3) - S(6)*b**S(3)*j*p**S(3)*q**S(3)*x*(-e*j + f*i)/(f*h) + S(6)*b**S(3)*j*p**S(2)*q**S(2)*(e + f*x)*(-g*j + h*i)*log(c*(d*(e + f*x)**p)**q)/(f*h**S(2)) + S(6)*b**S(3)*j*p**S(2)*q**S(2)*(e + f*x)*(-e*j + f*i)*log(c*(d*(e + f*x)**p)**q)/(f**S(2)*h) - S(6)*b**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(-g*j + h*i)**S(2)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h**S(3) + S(3)*b**S(2)*j**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(e + f*x)**S(2)/(S(4)*f**S(2)*h) + S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(-g*j + h*i)**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h**S(3) - S(3)*b*j*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-g*j + h*i)/(f*h**S(2)) - S(3)*b*j**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)**S(2)/(S(4)*f**S(2)*h) - S(3)*b*j*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)*(-e*j + f*i)/(f**S(2)*h) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(-g*j + h*i)**S(2)*log(f*(g + h*x)/(-e*h + f*g))/h**S(3) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)*(-g*j + h*i)/(f*h**S(2)) + j**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)**S(2)/(S(2)*f**S(2)*h) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)*(-e*j + f*i)/(f**S(2)*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(i + j*x)/(g + h*x), x), x, S(6)*a*b**S(2)*j*p**S(2)*q**S(2)*x/h - S(6)*b**S(3)*j*p**S(3)*q**S(3)*x/h + S(6)*b**S(3)*p**S(3)*q**S(3)*(-g*j + h*i)*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/h**S(2) + S(6)*b**S(3)*j*p**S(2)*q**S(2)*(e + f*x)*log(c*(d*(e + f*x)**p)**q)/(f*h) - S(6)*b**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*(-g*j + h*i)*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h**S(2) + S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(-g*j + h*i)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h**S(2) - S(3)*b*j*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/(f*h) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(-g*j + h*i)*log(f*(g + h*x)/(-e*h + f*g))/h**S(2) + j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)/(f*h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(g + h*x), x), x, S(6)*b**S(3)*p**S(3)*q**S(3)*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/h - S(6)*b**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/h + S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/h + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*log(f*(g + h*x)/(-e*h + f*g))/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/((g + h*x)*(i + j*x)), x), x, S(6)*b**S(3)*p**S(3)*q**S(3)*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i) - S(6)*b**S(3)*p**S(3)*q**S(3)*polylog(S(4), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i) - S(6)*b**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i) + S(6)*b**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i) + S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i) - S(3)*b*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*log(f*(g + h*x)/(-e*h + f*g))/(-g*j + h*i) - (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*log(f*(i + j*x)/(-e*j + f*i))/(-g*j + h*i), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/((g + h*x)*(i + j*x)**S(2)), x), x, -S(6)*b**S(3)*f*p**S(3)*q**S(3)*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)) + S(6)*b**S(3)*h*p**S(3)*q**S(3)*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(2) - S(6)*b**S(3)*h*p**S(3)*q**S(3)*polylog(S(4), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(2) + S(6)*b**S(2)*f*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)) - S(6)*b**S(2)*h*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(2) + S(6)*b**S(2)*h*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(2) + S(3)*b*f*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(i + j*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)) + S(3)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(2) - S(3)*b*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(2) + h*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*log(f*(g + h*x)/(-e*h + f*g))/(-g*j + h*i)**S(2) - h*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*log(f*(i + j*x)/(-e*j + f*i))/(-g*j + h*i)**S(2) - j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)/((i + j*x)*(-e*j + f*i)*(-g*j + h*i)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/((g + h*x)*(i + j*x)**S(3)), x), x, -S(3)*b**S(3)*f**S(2)*p**S(3)*q**S(3)*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)**S(2)*(-g*j + h*i)) - S(3)*b**S(3)*f**S(2)*p**S(3)*q**S(3)*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)**S(2)*(-g*j + h*i)) - S(6)*b**S(3)*f*h*p**S(3)*q**S(3)*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)**S(2)) + S(6)*b**S(3)*h**S(2)*p**S(3)*q**S(3)*polylog(S(4), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(3) - S(6)*b**S(3)*h**S(2)*p**S(3)*q**S(3)*polylog(S(4), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(3) - S(3)*b**S(2)*f**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*log(f*(i + j*x)/(-e*j + f*i))/((-e*j + f*i)**S(2)*(-g*j + h*i)) + S(3)*b**S(2)*f**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)**S(2)*(-g*j + h*i)) + S(6)*b**S(2)*f*h*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)**S(2)) - S(6)*b**S(2)*h**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(3), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(3) + S(6)*b**S(2)*h**S(2)*p**S(2)*q**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))*polylog(S(3), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(3) + S(3)*b*f**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(i + j*x)/(-e*j + f*i))/(S(2)*(-e*j + f*i)**S(2)*(-g*j + h*i)) + S(3)*b*f*h*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*log(f*(i + j*x)/(-e*j + f*i))/((-e*j + f*i)*(-g*j + h*i)**S(2)) + S(3)*b*f*j*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(e + f*x)/(S(2)*(i + j*x)*(-e*j + f*i)**S(2)*(-g*j + h*i)) + S(3)*b*h**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(2), -h*(e + f*x)/(-e*h + f*g))/(-g*j + h*i)**S(3) - S(3)*b*h**S(2)*p*q*(a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*polylog(S(2), -j*(e + f*x)/(-e*j + f*i))/(-g*j + h*i)**S(3) - f**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(S(2)*(-e*j + f*i)**S(2)*(-g*j + h*i)) + h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*log(f*(g + h*x)/(-e*h + f*g))/(-g*j + h*i)**S(3) - h**S(2)*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*log(f*(i + j*x)/(-e*j + f*i))/(-g*j + h*i)**S(3) - h*j*(a + b*log(c*(d*(e + f*x)**p)**q))**S(3)*(e + f*x)/((i + j*x)*(-e*j + f*i)*(-g*j + h*i)**S(2)) + (a + b*log(c*(d*(e + f*x)**p)**q))**S(3)/(S(2)*(i + j*x)**S(2)*(-g*j + h*i)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((i + j*x)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)), x), x, Integral((i + j*x)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)*(i + j*x)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)*(i + j*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)*(i + j*x)**S(2)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))*(g + h*x)*(i + j*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((i + j*x)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)), x), x, Integral((i + j*x)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)*(i + j*x)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)*(i + j*x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)*(i + j*x)**S(2)), x), x, Integral(S(1)/((a + b*log(c*(d*(e + f*x)**p)**q))**S(2)*(g + h*x)*(i + j*x)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(g + h*x**S(2)), x), x, -b*p*q*polylog(S(2), sqrt(h)*(-e - f*x)/(-e*sqrt(h) + f*sqrt(-g)))/(S(2)*sqrt(h)*sqrt(-g)) + b*p*q*polylog(S(2), sqrt(h)*(e + f*x)/(e*sqrt(h) + f*sqrt(-g)))/(S(2)*sqrt(h)*sqrt(-g)) - (a/S(2) + b*log(c*(d*(e + f*x)**p)**q)/S(2))*log(f*(sqrt(h)*x + sqrt(-g))/(-e*sqrt(h) + f*sqrt(-g)))/(sqrt(h)*sqrt(-g)) + (a/S(2) + b*log(c*(d*(e + f*x)**p)**q)/S(2))*log(f*(-sqrt(h)*x + sqrt(-g))/(e*sqrt(h) + f*sqrt(-g)))/(sqrt(h)*sqrt(-g)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/sqrt(h*x**S(2) + S(2)), x), x, -b*p*q*log(sqrt(S(2))*f*exp(asinh(sqrt(S(2))*sqrt(h)*x/S(2)))/(e*sqrt(h) - sqrt(e**S(2)*h + S(2)*f**S(2))) + S(1))*asinh(sqrt(S(2))*sqrt(h)*x/S(2))/sqrt(h) - b*p*q*log(sqrt(S(2))*f*exp(asinh(sqrt(S(2))*sqrt(h)*x/S(2)))/(e*sqrt(h) + sqrt(e**S(2)*h + S(2)*f**S(2))) + S(1))*asinh(sqrt(S(2))*sqrt(h)*x/S(2))/sqrt(h) + b*p*q*asinh(sqrt(S(2))*sqrt(h)*x/S(2))**S(2)/(S(2)*sqrt(h)) - b*p*q*polylog(S(2), -sqrt(S(2))*f*exp(asinh(sqrt(S(2))*sqrt(h)*x/S(2)))/(e*sqrt(h) - sqrt(e**S(2)*h + S(2)*f**S(2))))/sqrt(h) - b*p*q*polylog(S(2), -sqrt(S(2))*f*exp(asinh(sqrt(S(2))*sqrt(h)*x/S(2)))/(e*sqrt(h) + sqrt(e**S(2)*h + S(2)*f**S(2))))/sqrt(h) + (a + b*log(c*(d*(e + f*x)**p)**q))*asinh(sqrt(S(2))*sqrt(h)*x/S(2))/sqrt(h), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/sqrt(g + h*x**S(2)), x), x, -b*sqrt(g)*p*q*sqrt(S(1) + h*x**S(2)/g)*log(f*sqrt(g)*exp(asinh(sqrt(h)*x/sqrt(g)))/(e*sqrt(h) - sqrt(e**S(2)*h + f**S(2)*g)) + S(1))*asinh(sqrt(h)*x/sqrt(g))/(sqrt(h)*sqrt(g + h*x**S(2))) - b*sqrt(g)*p*q*sqrt(S(1) + h*x**S(2)/g)*log(f*sqrt(g)*exp(asinh(sqrt(h)*x/sqrt(g)))/(e*sqrt(h) + sqrt(e**S(2)*h + f**S(2)*g)) + S(1))*asinh(sqrt(h)*x/sqrt(g))/(sqrt(h)*sqrt(g + h*x**S(2))) + b*sqrt(g)*p*q*sqrt(S(1) + h*x**S(2)/g)*asinh(sqrt(h)*x/sqrt(g))**S(2)/(S(2)*sqrt(h)*sqrt(g + h*x**S(2))) - b*sqrt(g)*p*q*sqrt(S(1) + h*x**S(2)/g)*polylog(S(2), -f*sqrt(g)*exp(asinh(sqrt(h)*x/sqrt(g)))/(e*sqrt(h) - sqrt(e**S(2)*h + f**S(2)*g)))/(sqrt(h)*sqrt(g + h*x**S(2))) - b*sqrt(g)*p*q*sqrt(S(1) + h*x**S(2)/g)*polylog(S(2), -f*sqrt(g)*exp(asinh(sqrt(h)*x/sqrt(g)))/(e*sqrt(h) + sqrt(e**S(2)*h + f**S(2)*g)))/(sqrt(h)*sqrt(g + h*x**S(2))) + sqrt(g)*sqrt(S(1) + h*x**S(2)/g)*(a + b*log(c*(d*(e + f*x)**p)**q))*asinh(sqrt(h)*x/sqrt(g))/(sqrt(h)*sqrt(g + h*x**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(-h*x + S(2))*sqrt(h*x + S(2))), x), x, -b*p*q*log(S(2)*f*exp(I*asin(h*x/S(2)))/(I*e*h - sqrt(-e**S(2)*h**S(2) + S(4)*f**S(2))) + S(1))*asin(h*x/S(2))/h - b*p*q*log(S(2)*f*exp(I*asin(h*x/S(2)))/(I*e*h + sqrt(-e**S(2)*h**S(2) + S(4)*f**S(2))) + S(1))*asin(h*x/S(2))/h + I*b*p*q*asin(h*x/S(2))**S(2)/(S(2)*h) + I*b*p*q*polylog(S(2), -S(2)*f*exp(I*asin(h*x/S(2)))/(I*e*h - sqrt(-e**S(2)*h**S(2) + S(4)*f**S(2))))/h + I*b*p*q*polylog(S(2), -S(2)*f*exp(I*asin(h*x/S(2)))/(I*e*h + sqrt(-e**S(2)*h**S(2) + S(4)*f**S(2))))/h + (a + b*log(c*(d*(e + f*x)**p)**q))*asin(h*x/S(2))/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d*(e + f*x)**p)**q))/(sqrt(g - h*x)*sqrt(g + h*x)), x), x, -b*g*p*q*sqrt(S(1) - h**S(2)*x**S(2)/g**S(2))*log(f*g*exp(I*asin(h*x/g))/(I*e*h - sqrt(-e**S(2)*h**S(2) + f**S(2)*g**S(2))) + S(1))*asin(h*x/g)/(h*sqrt(g - h*x)*sqrt(g + h*x)) - b*g*p*q*sqrt(S(1) - h**S(2)*x**S(2)/g**S(2))*log(f*g*exp(I*asin(h*x/g))/(I*e*h + sqrt(-e**S(2)*h**S(2) + f**S(2)*g**S(2))) + S(1))*asin(h*x/g)/(h*sqrt(g - h*x)*sqrt(g + h*x)) + I*b*g*p*q*sqrt(S(1) - h**S(2)*x**S(2)/g**S(2))*asin(h*x/g)**S(2)/(S(2)*h*sqrt(g - h*x)*sqrt(g + h*x)) + I*b*g*p*q*sqrt(S(1) - h**S(2)*x**S(2)/g**S(2))*polylog(S(2), -f*g*exp(I*asin(h*x/g))/(I*e*h - sqrt(-e**S(2)*h**S(2) + f**S(2)*g**S(2))))/(h*sqrt(g - h*x)*sqrt(g + h*x)) + I*b*g*p*q*sqrt(S(1) - h**S(2)*x**S(2)/g**S(2))*polylog(S(2), -f*g*exp(I*asin(h*x/g))/(I*e*h + sqrt(-e**S(2)*h**S(2) + f**S(2)*g**S(2))))/(h*sqrt(g - h*x)*sqrt(g + h*x)) + g*sqrt(S(1) - h**S(2)*x**S(2)/g**S(2))*(a + b*log(c*(d*(e + f*x)**p)**q))*asin(h*x/g)/(h*sqrt(g - h*x)*sqrt(g + h*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(2)*e/(e + f*x))/(e**S(2) - f**S(2)*x**S(2)), x), x, polylog(S(2), (-e + f*x)/(e + f*x))/(S(2)*e*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(S(2)*e/(e + f*x)))/(e**S(2) - f**S(2)*x**S(2)), x), x, a*atanh(f*x/e)/(e*f) + b*polylog(S(2), (-e + f*x)/(e + f*x))/(S(2)*e*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e/(e + f*x))/(e**S(2) - f**S(2)*x**S(2)), x), x, -log(S(2))*atanh(f*x/e)/(e*f) + polylog(S(2), (-e + f*x)/(e + f*x))/(S(2)*e*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(e/(e + f*x)))/(e**S(2) - f**S(2)*x**S(2)), x), x, b*polylog(S(2), (-e + f*x)/(e + f*x))/(S(2)*e*f) + (a - b*log(S(2)))*atanh(f*x/e)/(e*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*x)/(c + d/x**S(2)), x), x, -sqrt(d)*log(b*(sqrt(d) - x*sqrt(-c))/(a*sqrt(-c) + b*sqrt(d)))*log(a + b*x)/(S(2)*(-c)**(S(3)/2)) + sqrt(d)*log(-b*(sqrt(d) + x*sqrt(-c))/(a*sqrt(-c) - b*sqrt(d)))*log(a + b*x)/(S(2)*(-c)**(S(3)/2)) + sqrt(d)*polylog(S(2), sqrt(-c)*(a + b*x)/(a*sqrt(-c) - b*sqrt(d)))/(S(2)*(-c)**(S(3)/2)) - sqrt(d)*polylog(S(2), sqrt(-c)*(a + b*x)/(a*sqrt(-c) + b*sqrt(d)))/(S(2)*(-c)**(S(3)/2)) - x/c + (a + b*x)*log(a + b*x)/(b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)**S(3)/(d + e*x**S(2)), x), x, -S(3)*n**S(3)*polylog(S(4), sqrt(e)*(-a - b*x)/(-a*sqrt(e) + b*sqrt(-d)))/(sqrt(e)*sqrt(-d)) + S(3)*n**S(3)*polylog(S(4), sqrt(e)*(a + b*x)/(a*sqrt(e) + b*sqrt(-d)))/(sqrt(e)*sqrt(-d)) + S(3)*n**S(2)*log(c*(a + b*x)**n)*polylog(S(3), sqrt(e)*(-a - b*x)/(-a*sqrt(e) + b*sqrt(-d)))/(sqrt(e)*sqrt(-d)) - S(3)*n**S(2)*log(c*(a + b*x)**n)*polylog(S(3), sqrt(e)*(a + b*x)/(a*sqrt(e) + b*sqrt(-d)))/(sqrt(e)*sqrt(-d)) - S(3)*n*log(c*(a + b*x)**n)**S(2)*polylog(S(2), sqrt(e)*(-a - b*x)/(-a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)) + S(3)*n*log(c*(a + b*x)**n)**S(2)*polylog(S(2), sqrt(e)*(a + b*x)/(a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)) - log(c*(a + b*x)**n)**S(3)*log(b*(sqrt(e)*x + sqrt(-d))/(-a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)) + log(c*(a + b*x)**n)**S(3)*log(b*(-sqrt(e)*x + sqrt(-d))/(a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)**S(2)/(d + e*x**S(2)), x), x, n**S(2)*polylog(S(3), sqrt(e)*(-a - b*x)/(-a*sqrt(e) + b*sqrt(-d)))/(sqrt(e)*sqrt(-d)) - n**S(2)*polylog(S(3), sqrt(e)*(a + b*x)/(a*sqrt(e) + b*sqrt(-d)))/(sqrt(e)*sqrt(-d)) - n*log(c*(a + b*x)**n)*polylog(S(2), sqrt(e)*(-a - b*x)/(-a*sqrt(e) + b*sqrt(-d)))/(sqrt(e)*sqrt(-d)) + n*log(c*(a + b*x)**n)*polylog(S(2), sqrt(e)*(a + b*x)/(a*sqrt(e) + b*sqrt(-d)))/(sqrt(e)*sqrt(-d)) - log(c*(a + b*x)**n)**S(2)*log(b*(sqrt(e)*x + sqrt(-d))/(-a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)) + log(c*(a + b*x)**n)**S(2)*log(b*(-sqrt(e)*x + sqrt(-d))/(a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)/(d + e*x**S(2)), x), x, -n*polylog(S(2), sqrt(e)*(-a - b*x)/(-a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)) + n*polylog(S(2), sqrt(e)*(a + b*x)/(a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)) - log(c*(a + b*x)**n)*log(b*(sqrt(e)*x + sqrt(-d))/(-a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)) + log(c*(a + b*x)**n)*log(b*(-sqrt(e)*x + sqrt(-d))/(a*sqrt(e) + b*sqrt(-d)))/(S(2)*sqrt(e)*sqrt(-d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((d + e*x**S(2))*log(c*(a + b*x)**n)), x), x, Integral(S(1)/((d + e*x**S(2))*log(c*(a + b*x)**n)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)*log(c + d*x)/(a + b*x**S(2)), x), x, a**S(2)*log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/(S(2)*b**S(3)) + a**S(2)*log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/(S(2)*b**S(3)) + a**S(2)*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/(S(2)*b**S(3)) + a**S(2)*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/(S(2)*b**S(3)) + a*c**S(2)*log(c + d*x)/(S(2)*b**S(2)*d**S(2)) - a*c*x/(S(2)*b**S(2)*d) - a*x**S(2)*log(c + d*x)/(S(2)*b**S(2)) + a*x**S(2)/(S(4)*b**S(2)) - c**S(4)*log(c + d*x)/(S(4)*b*d**S(4)) + c**S(3)*x/(S(4)*b*d**S(3)) - c**S(2)*x**S(2)/(S(8)*b*d**S(2)) + c*x**S(3)/(S(12)*b*d) + x**S(4)*log(c + d*x)/(S(4)*b) - x**S(4)/(S(16)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(c + d*x)/(a + b*x**S(2)), x), x, a*x/b**S(2) - a*(c + d*x)*log(c + d*x)/(b**S(2)*d) + c**S(3)*log(c + d*x)/(S(3)*b*d**S(3)) - c**S(2)*x/(S(3)*b*d**S(2)) + c*x**S(2)/(S(6)*b*d) + x**S(3)*log(c + d*x)/(S(3)*b) - x**S(3)/(S(9)*b) - (-a)**(S(3)/2)*log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/(S(2)*b**(S(5)/2)) + (-a)**(S(3)/2)*log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/(S(2)*b**(S(5)/2)) - (-a)**(S(3)/2)*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/(S(2)*b**(S(5)/2)) + (-a)**(S(3)/2)*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/(S(2)*b**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c + d*x)/(a + b*x**S(2)), x), x, -a*log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/(S(2)*b**S(2)) - a*log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/(S(2)*b**S(2)) - a*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/(S(2)*b**S(2)) - a*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/(S(2)*b**S(2)) - c**S(2)*log(c + d*x)/(S(2)*b*d**S(2)) + c*x/(S(2)*b*d) + x**S(2)*log(c + d*x)/(S(2)*b) - x**S(2)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c + d*x)/(a + b*x**S(2)), x), x, -x/b + (c + d*x)*log(c + d*x)/(b*d) - sqrt(-a)*log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/(S(2)*b**(S(3)/2)) + sqrt(-a)*log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/(S(2)*b**(S(3)/2)) - sqrt(-a)*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/(S(2)*b**(S(3)/2)) + sqrt(-a)*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/(S(2)*b**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c + d*x)/(a + b*x**S(2)), x), x, log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/(S(2)*b) + log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/(S(2)*b) + polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/(S(2)*b) + polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(a + b*x**S(2)), x), x, -log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/(S(2)*sqrt(b)*sqrt(-a)) + log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/(S(2)*sqrt(b)*sqrt(-a)) - polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/(S(2)*sqrt(b)*sqrt(-a)) + polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/(S(2)*sqrt(b)*sqrt(-a)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(x*(a + b*x**S(2))), x), x, log(-d*x/c)*log(c + d*x)/a - log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/(S(2)*a) - log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/(S(2)*a) + polylog(S(2), (c + d*x)/c)/a - polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/(S(2)*a) - polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(x**S(2)*(a + b*x**S(2))), x), x, -sqrt(b)*log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/(S(2)*(-a)**(S(3)/2)) + sqrt(b)*log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/(S(2)*(-a)**(S(3)/2)) - sqrt(b)*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/(S(2)*(-a)**(S(3)/2)) + sqrt(b)*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/(S(2)*(-a)**(S(3)/2)) - log(c + d*x)/(a*x) + d*log(x)/(a*c) - d*log(c + d*x)/(a*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(x**S(3)*(a + b*x**S(2))), x), x, -log(c + d*x)/(S(2)*a*x**S(2)) - d/(S(2)*a*c*x) - d**S(2)*log(x)/(S(2)*a*c**S(2)) + d**S(2)*log(c + d*x)/(S(2)*a*c**S(2)) - b*log(-d*x/c)*log(c + d*x)/a**S(2) + b*log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/(S(2)*a**S(2)) + b*log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/(S(2)*a**S(2)) - b*polylog(S(2), (c + d*x)/c)/a**S(2) + b*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/(S(2)*a**S(2)) + b*polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/(S(2)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)*log(c + d*x)/(a + b*x**S(3)), x), x, -a*log(-d*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b**S(2)) - a*log(-d*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b**S(2)) - a*log((S(-1))**(S(1)/3)*d*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b**S(2)) - a*polylog(S(2), b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*b**S(2)) - a*polylog(S(2), b**(S(1)/3)*(c + d*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*b**S(2)) - a*polylog(S(2), b**(S(1)/3)*(c + d*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*b**S(2)) + c**S(3)*log(c + d*x)/(S(3)*b*d**S(3)) - c**S(2)*x/(S(3)*b*d**S(2)) + c*x**S(2)/(S(6)*b*d) + x**S(3)*log(c + d*x)/(S(3)*b) - x**S(3)/(S(9)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(c + d*x)/(a + b*x**S(3)), x), x, a**(S(2)/3)*log(-d*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b**(S(5)/3)) - (S(-1))**(S(1)/3)*a**(S(2)/3)*log(d*(a**(S(1)/3) - (S(-1))**(S(1)/3)*b**(S(1)/3)*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b**(S(5)/3)) + (S(-1))**(S(2)/3)*a**(S(2)/3)*log(-d*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b**(S(5)/3)) + a**(S(2)/3)*polylog(S(2), b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*b**(S(5)/3)) - (S(-1))**(S(1)/3)*a**(S(2)/3)*polylog(S(2), (S(-1))**(S(1)/3)*b**(S(1)/3)*(c + d*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))/(S(3)*b**(S(5)/3)) + (S(-1))**(S(2)/3)*a**(S(2)/3)*polylog(S(2), (S(-1))**(S(2)/3)*b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))/(S(3)*b**(S(5)/3)) - c**S(2)*log(c + d*x)/(S(2)*b*d**S(2)) + c*x/(S(2)*b*d) + x**S(2)*log(c + d*x)/(S(2)*b) - x**S(2)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c + d*x)/(a + b*x**S(3)), x), x, -a**(S(1)/3)*log(-d*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b**(S(4)/3)) - (S(-1))**(S(2)/3)*a**(S(1)/3)*log(d*(a**(S(1)/3) - (S(-1))**(S(1)/3)*b**(S(1)/3)*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b**(S(4)/3)) + (S(-1))**(S(1)/3)*a**(S(1)/3)*log(-d*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b**(S(4)/3)) - a**(S(1)/3)*polylog(S(2), b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*b**(S(4)/3)) - (S(-1))**(S(2)/3)*a**(S(1)/3)*polylog(S(2), (S(-1))**(S(1)/3)*b**(S(1)/3)*(c + d*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))/(S(3)*b**(S(4)/3)) + (S(-1))**(S(1)/3)*a**(S(1)/3)*polylog(S(2), (S(-1))**(S(2)/3)*b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))/(S(3)*b**(S(4)/3)) - x/b + (c + d*x)*log(c + d*x)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c + d*x)/(a + b*x**S(3)), x), x, log(-d*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b) + log(-d*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b) + log((S(-1))**(S(1)/3)*d*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*b) + polylog(S(2), b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*b) + polylog(S(2), b**(S(1)/3)*(c + d*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*b) + polylog(S(2), b**(S(1)/3)*(c + d*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c + d*x)/(a + b*x**S(3)), x), x, -log(-d*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(1)/3)*b**(S(2)/3)) + (S(-1))**(S(1)/3)*log(d*(a**(S(1)/3) - (S(-1))**(S(1)/3)*b**(S(1)/3)*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(1)/3)*b**(S(2)/3)) - (S(-1))**(S(2)/3)*log(-d*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(1)/3)*b**(S(2)/3)) - polylog(S(2), b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*a**(S(1)/3)*b**(S(2)/3)) + (S(-1))**(S(1)/3)*polylog(S(2), (S(-1))**(S(1)/3)*b**(S(1)/3)*(c + d*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))/(S(3)*a**(S(1)/3)*b**(S(2)/3)) - (S(-1))**(S(2)/3)*polylog(S(2), (S(-1))**(S(2)/3)*b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))/(S(3)*a**(S(1)/3)*b**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(a + b*x**S(3)), x), x, log(-d*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(2)/3)*b**(S(1)/3)) + (S(-1))**(S(2)/3)*log(d*(a**(S(1)/3) - (S(-1))**(S(1)/3)*b**(S(1)/3)*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(2)/3)*b**(S(1)/3)) - (S(-1))**(S(1)/3)*log(-d*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(2)/3)*b**(S(1)/3)) + polylog(S(2), b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*a**(S(2)/3)*b**(S(1)/3)) + (S(-1))**(S(2)/3)*polylog(S(2), (S(-1))**(S(1)/3)*b**(S(1)/3)*(c + d*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))/(S(3)*a**(S(2)/3)*b**(S(1)/3)) - (S(-1))**(S(1)/3)*polylog(S(2), (S(-1))**(S(2)/3)*b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))/(S(3)*a**(S(2)/3)*b**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(x*(a + b*x**S(3))), x), x, log(-d*x/c)*log(c + d*x)/a - log(-d*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a) - log(-d*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a) - log((S(-1))**(S(1)/3)*d*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a) + polylog(S(2), (c + d*x)/c)/a - polylog(S(2), b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*a) - polylog(S(2), b**(S(1)/3)*(c + d*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*a) - polylog(S(2), b**(S(1)/3)*(c + d*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(x**S(2)*(a + b*x**S(3))), x), x, -log(c + d*x)/(a*x) + d*log(x)/(a*c) - d*log(c + d*x)/(a*c) + b**(S(1)/3)*log(-d*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(4)/3)) - (S(-1))**(S(1)/3)*b**(S(1)/3)*log(d*(a**(S(1)/3) - (S(-1))**(S(1)/3)*b**(S(1)/3)*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(4)/3)) + (S(-1))**(S(2)/3)*b**(S(1)/3)*log(-d*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(4)/3)) + b**(S(1)/3)*polylog(S(2), b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*a**(S(4)/3)) - (S(-1))**(S(1)/3)*b**(S(1)/3)*polylog(S(2), (S(-1))**(S(1)/3)*b**(S(1)/3)*(c + d*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))/(S(3)*a**(S(4)/3)) + (S(-1))**(S(2)/3)*b**(S(1)/3)*polylog(S(2), (S(-1))**(S(2)/3)*b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))/(S(3)*a**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(x**S(3)*(a + b*x**S(3))), x), x, -log(c + d*x)/(S(2)*a*x**S(2)) - d/(S(2)*a*c*x) - d**S(2)*log(x)/(S(2)*a*c**S(2)) + d**S(2)*log(c + d*x)/(S(2)*a*c**S(2)) - b**(S(2)/3)*log(-d*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(5)/3)) - (S(-1))**(S(2)/3)*b**(S(2)/3)*log(d*(a**(S(1)/3) - (S(-1))**(S(1)/3)*b**(S(1)/3)*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(5)/3)) + (S(-1))**(S(1)/3)*b**(S(2)/3)*log(-d*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))*log(c + d*x)/(S(3)*a**(S(5)/3)) - b**(S(2)/3)*polylog(S(2), b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + b**(S(1)/3)*c))/(S(3)*a**(S(5)/3)) - (S(-1))**(S(2)/3)*b**(S(2)/3)*polylog(S(2), (S(-1))**(S(1)/3)*b**(S(1)/3)*(c + d*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*c))/(S(3)*a**(S(5)/3)) + (S(-1))**(S(1)/3)*b**(S(2)/3)*polylog(S(2), (S(-1))**(S(2)/3)*b**(S(1)/3)*(c + d*x)/(-a**(S(1)/3)*d + (S(-1))**(S(2)/3)*b**(S(1)/3)*c))/(S(3)*a**(S(5)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(c + d*x)/(a + b*x**S(4)), x), x, -x/b + (c + d*x)*log(c + d*x)/(b*d) - (-a)**(S(1)/4)*log(-d*(b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b**(S(5)/4)) + (-a)**(S(1)/4)*log(d*(-b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b**(S(5)/4)) - (-a)**(S(1)/4)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))/(S(4)*b**(S(5)/4)) + (-a)**(S(1)/4)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))/(S(4)*b**(S(5)/4)) - sqrt(-sqrt(-a))*log(-d*(b**(S(1)/4)*x + sqrt(-sqrt(-a)))/(b**(S(1)/4)*c - d*sqrt(-sqrt(-a))))*log(c + d*x)/(S(4)*b**(S(5)/4)) + sqrt(-sqrt(-a))*log(d*(-b**(S(1)/4)*x + sqrt(-sqrt(-a)))/(b**(S(1)/4)*c + d*sqrt(-sqrt(-a))))*log(c + d*x)/(S(4)*b**(S(5)/4)) - sqrt(-sqrt(-a))*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*sqrt(-sqrt(-a))))/(S(4)*b**(S(5)/4)) + sqrt(-sqrt(-a))*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*sqrt(-sqrt(-a))))/(S(4)*b**(S(5)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c + d*x)/(a + b*x**S(4)), x), x, log(-d*(b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b) + log(d*(-b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b) + log(-d*(b**(S(1)/4)*x + I*(-a)**(S(1)/4))/(b**(S(1)/4)*c - I*d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b) + log(d*(-b**(S(1)/4)*x + I*(-a)**(S(1)/4))/(b**(S(1)/4)*c + I*d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))/(S(4)*b) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))/(S(4)*b) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - I*d*(-a)**(S(1)/4)))/(S(4)*b) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + I*d*(-a)**(S(1)/4)))/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c + d*x)/(a + b*x**S(4)), x), x, -log(-d*(b**(S(1)/4)*x + sqrt(-sqrt(-a)))/(b**(S(1)/4)*c - d*sqrt(-sqrt(-a))))*log(c + d*x)/(S(4)*b**(S(3)/4)*sqrt(-sqrt(-a))) + log(d*(-b**(S(1)/4)*x + sqrt(-sqrt(-a)))/(b**(S(1)/4)*c + d*sqrt(-sqrt(-a))))*log(c + d*x)/(S(4)*b**(S(3)/4)*sqrt(-sqrt(-a))) - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*sqrt(-sqrt(-a))))/(S(4)*b**(S(3)/4)*sqrt(-sqrt(-a))) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*sqrt(-sqrt(-a))))/(S(4)*b**(S(3)/4)*sqrt(-sqrt(-a))) - log(-d*(b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b**(S(3)/4)*(-a)**(S(1)/4)) + log(d*(-b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b**(S(3)/4)*(-a)**(S(1)/4)) - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))/(S(4)*b**(S(3)/4)*(-a)**(S(1)/4)) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))/(S(4)*b**(S(3)/4)*(-a)**(S(1)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c + d*x)/(a + b*x**S(4)), x), x, log(-d*(b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*sqrt(b)*sqrt(-a)) + log(d*(-b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*sqrt(b)*sqrt(-a)) - log(-d*(b**(S(1)/4)*x + I*(-a)**(S(1)/4))/(b**(S(1)/4)*c - I*d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*sqrt(b)*sqrt(-a)) - log(d*(-b**(S(1)/4)*x + I*(-a)**(S(1)/4))/(b**(S(1)/4)*c + I*d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*sqrt(b)*sqrt(-a)) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))/(S(4)*sqrt(b)*sqrt(-a)) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))/(S(4)*sqrt(b)*sqrt(-a)) - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - I*d*(-a)**(S(1)/4)))/(S(4)*sqrt(b)*sqrt(-a)) - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + I*d*(-a)**(S(1)/4)))/(S(4)*sqrt(b)*sqrt(-a)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(a + b*x**S(4)), x), x, -log(-d*(b**(S(1)/4)*x + sqrt(-sqrt(-a)))/(b**(S(1)/4)*c - d*sqrt(-sqrt(-a))))*log(c + d*x)/(S(4)*b**(S(1)/4)*(-sqrt(-a))**(S(3)/2)) + log(d*(-b**(S(1)/4)*x + sqrt(-sqrt(-a)))/(b**(S(1)/4)*c + d*sqrt(-sqrt(-a))))*log(c + d*x)/(S(4)*b**(S(1)/4)*(-sqrt(-a))**(S(3)/2)) - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*sqrt(-sqrt(-a))))/(S(4)*b**(S(1)/4)*(-sqrt(-a))**(S(3)/2)) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*sqrt(-sqrt(-a))))/(S(4)*b**(S(1)/4)*(-sqrt(-a))**(S(3)/2)) - log(-d*(b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b**(S(1)/4)*(-a)**(S(3)/4)) + log(d*(-b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*b**(S(1)/4)*(-a)**(S(3)/4)) - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))/(S(4)*b**(S(1)/4)*(-a)**(S(3)/4)) + polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))/(S(4)*b**(S(1)/4)*(-a)**(S(3)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(x*(a + b*x**S(4))), x), x, log(-d*x/c)*log(c + d*x)/a - log(-d*(b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*a) - log(d*(-b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*a) - log(-d*(b**(S(1)/4)*x + I*(-a)**(S(1)/4))/(b**(S(1)/4)*c - I*d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*a) - log(d*(-b**(S(1)/4)*x + I*(-a)**(S(1)/4))/(b**(S(1)/4)*c + I*d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*a) + polylog(S(2), (c + d*x)/c)/a - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))/(S(4)*a) - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))/(S(4)*a) - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - I*d*(-a)**(S(1)/4)))/(S(4)*a) - polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + I*d*(-a)**(S(1)/4)))/(S(4)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(x**S(2)*(a + b*x**S(4))), x), x, -b**(S(1)/4)*log(-d*(b**(S(1)/4)*x + sqrt(-sqrt(-a)))/(b**(S(1)/4)*c - d*sqrt(-sqrt(-a))))*log(c + d*x)/(S(4)*(-sqrt(-a))**(S(5)/2)) + b**(S(1)/4)*log(d*(-b**(S(1)/4)*x + sqrt(-sqrt(-a)))/(b**(S(1)/4)*c + d*sqrt(-sqrt(-a))))*log(c + d*x)/(S(4)*(-sqrt(-a))**(S(5)/2)) - b**(S(1)/4)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*sqrt(-sqrt(-a))))/(S(4)*(-sqrt(-a))**(S(5)/2)) + b**(S(1)/4)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*sqrt(-sqrt(-a))))/(S(4)*(-sqrt(-a))**(S(5)/2)) - b**(S(1)/4)*log(-d*(b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*(-a)**(S(5)/4)) + b**(S(1)/4)*log(d*(-b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*(-a)**(S(5)/4)) - b**(S(1)/4)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))/(S(4)*(-a)**(S(5)/4)) + b**(S(1)/4)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))/(S(4)*(-a)**(S(5)/4)) - log(c + d*x)/(a*x) + d*log(x)/(a*c) - d*log(c + d*x)/(a*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c + d*x)/(x**S(3)*(a + b*x**S(4))), x), x, sqrt(b)*log(-d*(b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*(-a)**(S(3)/2)) + sqrt(b)*log(d*(-b**(S(1)/4)*x + (-a)**(S(1)/4))/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*(-a)**(S(3)/2)) - sqrt(b)*log(-d*(b**(S(1)/4)*x + I*(-a)**(S(1)/4))/(b**(S(1)/4)*c - I*d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*(-a)**(S(3)/2)) - sqrt(b)*log(d*(-b**(S(1)/4)*x + I*(-a)**(S(1)/4))/(b**(S(1)/4)*c + I*d*(-a)**(S(1)/4)))*log(c + d*x)/(S(4)*(-a)**(S(3)/2)) + sqrt(b)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - d*(-a)**(S(1)/4)))/(S(4)*(-a)**(S(3)/2)) + sqrt(b)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + d*(-a)**(S(1)/4)))/(S(4)*(-a)**(S(3)/2)) - sqrt(b)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c - I*d*(-a)**(S(1)/4)))/(S(4)*(-a)**(S(3)/2)) - sqrt(b)*polylog(S(2), b**(S(1)/4)*(c + d*x)/(b**(S(1)/4)*c + I*d*(-a)**(S(1)/4)))/(S(4)*(-a)**(S(3)/2)) - log(c + d*x)/(S(2)*a*x**S(2)) - d/(S(2)*a*c*x) - d**S(2)*log(x)/(S(2)*a*c**S(2)) + d**S(2)*log(c + d*x)/(S(2)*a*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)**S(3)/(d*x + e*x**S(2)), x), x, S(6)*n**S(3)*polylog(S(4), (a + b*x)/a)/d - S(6)*n**S(3)*polylog(S(4), -e*(a + b*x)/(-a*e + b*d))/d - S(6)*n**S(2)*log(c*(a + b*x)**n)*polylog(S(3), (a + b*x)/a)/d + S(6)*n**S(2)*log(c*(a + b*x)**n)*polylog(S(3), -e*(a + b*x)/(-a*e + b*d))/d + S(3)*n*log(c*(a + b*x)**n)**S(2)*polylog(S(2), (a + b*x)/a)/d - S(3)*n*log(c*(a + b*x)**n)**S(2)*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/d + log(c*(a + b*x)**n)**S(3)*log(-b*x/a)/d - log(c*(a + b*x)**n)**S(3)*log(b*(d + e*x)/(-a*e + b*d))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)**S(2)/(d*x + e*x**S(2)), x), x, -S(2)*n**S(2)*polylog(S(3), (a + b*x)/a)/d + S(2)*n**S(2)*polylog(S(3), -e*(a + b*x)/(-a*e + b*d))/d + S(2)*n*log(c*(a + b*x)**n)*polylog(S(2), (a + b*x)/a)/d - S(2)*n*log(c*(a + b*x)**n)*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/d + log(c*(a + b*x)**n)**S(2)*log(-b*x/a)/d - log(c*(a + b*x)**n)**S(2)*log(b*(d + e*x)/(-a*e + b*d))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)/(d*x + e*x**S(2)), x), x, n*polylog(S(2), (a + b*x)/a)/d - n*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/d + log(c*(a + b*x)**n)*log(-b*x/a)/d - log(c*(a + b*x)**n)*log(b*(d + e*x)/(-a*e + b*d))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((d*x + e*x**S(2))*log(c*(a + b*x)**n)), x), x, Integral(S(1)/(x*(d + e*x)*log(c*(a + b*x)**n)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)**S(3)/(d + e*x + f*x**S(2)), x), x, S(6)*n**S(3)*polylog(S(4), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e - sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) - S(6)*n**S(3)*polylog(S(4), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e + sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) - S(6)*n**S(2)*log(c*(a + b*x)**n)*polylog(S(3), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e - sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + S(6)*n**S(2)*log(c*(a + b*x)**n)*polylog(S(3), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e + sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + S(3)*n*log(c*(a + b*x)**n)**S(2)*polylog(S(2), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e - sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) - S(3)*n*log(c*(a + b*x)**n)**S(2)*polylog(S(2), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e + sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + log(c*(a + b*x)**n)**S(3)*log(-b*(e + S(2)*f*x - sqrt(-S(4)*d*f + e**S(2)))/(S(2)*a*f - b*(e - sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) - log(c*(a + b*x)**n)**S(3)*log(-b*(e + S(2)*f*x + sqrt(-S(4)*d*f + e**S(2)))/(S(2)*a*f - b*(e + sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)**S(2)/(d + e*x + f*x**S(2)), x), x, -S(2)*n**S(2)*polylog(S(3), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e - sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + S(2)*n**S(2)*polylog(S(3), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e + sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + S(2)*n*log(c*(a + b*x)**n)*polylog(S(2), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e - sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) - S(2)*n*log(c*(a + b*x)**n)*polylog(S(2), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e + sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + log(c*(a + b*x)**n)**S(2)*log(-b*(e + S(2)*f*x - sqrt(-S(4)*d*f + e**S(2)))/(S(2)*a*f - b*(e - sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) - log(c*(a + b*x)**n)**S(2)*log(-b*(e + S(2)*f*x + sqrt(-S(4)*d*f + e**S(2)))/(S(2)*a*f - b*(e + sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**n)/(d + e*x + f*x**S(2)), x), x, n*polylog(S(2), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e - sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) - n*polylog(S(2), S(2)*f*(a + b*x)/(S(2)*a*f - b*(e + sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + log(c*(a + b*x)**n)*log(-b*(e + S(2)*f*x - sqrt(-S(4)*d*f + e**S(2)))/(S(2)*a*f - b*(e - sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) - log(c*(a + b*x)**n)*log(-b*(e + S(2)*f*x + sqrt(-S(4)*d*f + e**S(2)))/(S(2)*a*f - b*(e + sqrt(-S(4)*d*f + e**S(2)))))/sqrt(-S(4)*d*f + e**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((d + e*x + f*x**S(2))*log(c*(a + b*x)**n)), x), x, Integral(S(1)/((d + e*x + f*x**S(2))*log(c*(a + b*x)**n)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(x)/(a + b*x + c*x**S(2)), x), x, -b*x*log(x)/c**S(2) + b*x/c**S(2) + x**S(2)*log(x)/(S(2)*c) - x**S(2)/(S(4)*c) + (-a*c + b**S(2) - b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(x)*log((b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c**S(3)) + (-a*c + b**S(2) - b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -S(2)*c*x/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c**S(3)) + (-a*c + b**S(2) + b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(x)*log((b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c**S(3)) + (-a*c + b**S(2) + b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -S(2)*c*x/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(x)/(a + b*x + c*x**S(2)), x), x, x*log(x)/c - x/c - (b + (-S(2)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(x)*log((b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c**S(2)) - (b + (-S(2)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -S(2)*c*x/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c**S(2)) - (b + (S(2)*a*c - b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(x)*log((b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c**S(2)) - (b + (S(2)*a*c - b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -S(2)*c*x/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(x)/(a + b*x + c*x**S(2)), x), x, (-b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*log(x)*log((b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c) + (-b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*polylog(S(2), -S(2)*c*x/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c) + (b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*log(x)*log((b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c) + (b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*polylog(S(2), -S(2)*c*x/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(a + b*x + c*x**S(2)), x), x, log(x)*log((b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(b - sqrt(-S(4)*a*c + b**S(2))))/sqrt(-S(4)*a*c + b**S(2)) - log(x)*log((b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(b + sqrt(-S(4)*a*c + b**S(2))))/sqrt(-S(4)*a*c + b**S(2)) + polylog(S(2), -S(2)*c*x/(b - sqrt(-S(4)*a*c + b**S(2))))/sqrt(-S(4)*a*c + b**S(2)) - polylog(S(2), -S(2)*c*x/(b + sqrt(-S(4)*a*c + b**S(2))))/sqrt(-S(4)*a*c + b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(x*(a + b*x + c*x**S(2))), x), x, -(-b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*log(x)*log((b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a) - (-b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*polylog(S(2), -S(2)*c*x/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a) - (b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*log(x)*log((b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a) - (b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*polylog(S(2), -S(2)*c*x/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a) + log(x)**S(2)/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(x**S(2)*(a + b*x + c*x**S(2))), x), x, -log(x)/(a*x) - S(1)/(a*x) - b*log(x)**S(2)/(S(2)*a**S(2)) + (b + (-S(2)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(x)*log((b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a**S(2)) + (b + (-S(2)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -S(2)*c*x/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a**S(2)) + (b + (S(2)*a*c - b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(x)*log((b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a**S(2)) + (b + (S(2)*a*c - b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -S(2)*c*x/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(x**S(3)*(a + b*x + c*x**S(2))), x), x, -log(x)/(S(2)*a*x**S(2)) - S(1)/(S(4)*a*x**S(2)) + b*log(x)/(a**S(2)*x) + b/(a**S(2)*x) + (-a*c/S(2) + b**S(2)/S(2))*log(x)**S(2)/a**S(3) - (-a*c + b**S(2) - b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(x)*log((b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a**S(3)) - (-a*c + b**S(2) - b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -S(2)*c*x/(b + sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a**S(3)) - (-a*c + b**S(2) + b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(x)*log((b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a**S(3)) - (-a*c + b**S(2) + b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -S(2)*c*x/(b - sqrt(-S(4)*a*c + b**S(2))))/(S(2)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d + e/(f + g*x))**p))**S(4), x), x, -S(24)*b**S(4)*e*p**S(4)*polylog(S(4), (d + e/(f + g*x))/d)/(d*g) + S(24)*b**S(3)*e*p**S(3)*(a + b*log(c*(d + e/(f + g*x))**p))*polylog(S(3), (d + e/(f + g*x))/d)/(d*g) - S(12)*b**S(2)*e*p**S(2)*(a + b*log(c*(d + e/(f + g*x))**p))**S(2)*polylog(S(2), (d + e/(f + g*x))/d)/(d*g) - S(4)*b*e*p*(a + b*log(c*(d + e/(f + g*x))**p))**S(3)*log(-e/(d*(f + g*x)))/(d*g) + (a + b*log(c*(d + e/(f + g*x))**p))**S(4)*(d*(f + g*x) + e)/(d*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d + e/(f + g*x))**p))**S(3), x), x, S(6)*b**S(3)*e*p**S(3)*polylog(S(3), (d + e/(f + g*x))/d)/(d*g) - S(6)*b**S(2)*e*p**S(2)*(a + b*log(c*(d + e/(f + g*x))**p))*polylog(S(2), (d + e/(f + g*x))/d)/(d*g) - S(3)*b*e*p*(a + b*log(c*(d + e/(f + g*x))**p))**S(2)*log(-e/(d*(f + g*x)))/(d*g) + (a + b*log(c*(d + e/(f + g*x))**p))**S(3)*(d*(f + g*x) + e)/(d*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d + e/(f + g*x))**p))**S(2), x), x, -S(2)*b**S(2)*e*p**S(2)*polylog(S(2), (d + e/(f + g*x))/d)/(d*g) - S(2)*b*e*p*(a + b*log(c*(d + e/(f + g*x))**p))*log(-e/(d*(f + g*x)))/(d*g) + (a + b*log(c*(d + e/(f + g*x))**p))**S(2)*(d*(f + g*x) + e)/(d*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a + b*log(c*(d + e/(f + g*x))**p), x), x, a*x + b*(f + g*x)*log(c*(d + e/(f + g*x))**p)/g + b*e*p*log(d*(f + g*x) + e)/(d*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*log(c*(d + e/(f + g*x))**p)), x), x, Integral(S(1)/(a + b*log(c*(d + e/x)**p)), (x, f + g*x))/g, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*(d + e/(f + g*x))**p))**(S(-2)), x), x, Integral((a + b*log(c*(d + e/x)**p))**(S(-2)), (x, f + g*x))/g, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(e*(f + g*x)**p)**q), x), x, -p*q*x + (f + g*x)*log(c*(e*(f + g*x)**p)**q)/g, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(d + e*(f + g*x)**p)**q), x), x, -p*q*x + p*q*(f + g*x)*hyper((S(1), S(1)/p), (S(1) + S(1)/p,), -e*(f + g*x)**p/d)/g + (f + g*x)*log(c*(d + e*(f + g*x)**p)**q)/g, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(d + e*(f + g*x)**S(3))**q), x), x, d**(S(1)/3)*q*log(d**(S(1)/3) + e**(S(1)/3)*(f + g*x))/(e**(S(1)/3)*g) - d**(S(1)/3)*q*log(d**(S(2)/3) - d**(S(1)/3)*e**(S(1)/3)*(f + g*x) + e**(S(2)/3)*(f + g*x)**S(2))/(S(2)*e**(S(1)/3)*g) - sqrt(S(3))*d**(S(1)/3)*q*atan(sqrt(S(3))*(d**(S(1)/3) - S(2)*e**(S(1)/3)*(f + g*x))/(S(3)*d**(S(1)/3)))/(e**(S(1)/3)*g) - S(3)*q*x + (f + g*x)*log(c*(d + e*(f + g*x)**S(3))**q)/g, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(d + e*(f + g*x)**S(2))**q), x), x, S(2)*sqrt(d)*q*atan(sqrt(e)*(f + g*x)/sqrt(d))/(sqrt(e)*g) - S(2)*q*x + (f + g*x)*log(c*(d + e*(f + g*x)**S(2))**q)/g, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(d + e*(f + g*x))**q), x), x, -q*x + (d + e*f + e*g*x)*log(c*(d + e*f + e*g*x)**q)/(e*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(d + e/(f + g*x))**q), x), x, (f + g*x)*log(c*(d + e/(f + g*x))**q)/g + e*q*log(d*(f + g*x) + e)/(d*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(d + e/(f + g*x)**S(2))**q), x), x, (f + g*x)*log(c*(d + e/(f + g*x)**S(2))**q)/g + S(2)*sqrt(e)*q*atan(sqrt(d)*(f + g*x)/sqrt(e))/(sqrt(d)*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(d + e/(f + g*x)**S(3))**q), x), x, (f + g*x)*log(c*(d + e/(f + g*x)**S(3))**q)/g + e**(S(1)/3)*q*log(d**(S(1)/3)*(f + g*x) + e**(S(1)/3))/(d**(S(1)/3)*g) - e**(S(1)/3)*q*log(d**(S(2)/3)*(f + g*x)**S(2) - d**(S(1)/3)*e**(S(1)/3)*(f + g*x) + e**(S(2)/3))/(S(2)*d**(S(1)/3)*g) - sqrt(S(3))*e**(S(1)/3)*q*atan(sqrt(S(3))*(-S(2)*d**(S(1)/3)*(f + g*x) + e**(S(1)/3))/(S(3)*e**(S(1)/3)))/(d**(S(1)/3)*g), expand=True, _diff=True, _numerical=True)


def test_2():
    assert rubi_test(rubi_integrate(x**m*log(a*x**n), x), x, -n*x**(m + S(1))/(m + S(1))**S(2) + x**(m + S(1))*log(a*x**n)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))*log(a*x**n), x), x, x**n*log(a*x**n)/n - x**n/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(a*x**n), x), x, -n*x**S(4)/S(16) + x**S(4)*log(a*x**n)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(a*x**n), x), x, -n*x**S(3)/S(9) + x**S(3)*log(a*x**n)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(a*x**n), x), x, -n*x**S(2)/S(4) + x**S(2)*log(a*x**n)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n), x), x, -n*x + x*log(a*x**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)/x, x), x, log(a*x**n)**S(2)/(S(2)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)/x**S(2), x), x, -n/x - log(a*x**n)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)/x**S(3), x), x, -n/(S(4)*x**S(2)) - log(a*x**n)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(a*x**n)**S(2), x), x, S(2)*n**S(2)*x**(m + S(1))/(m + S(1))**S(3) - S(2)*n*x**(m + S(1))*log(a*x**n)/(m + S(1))**S(2) + x**(m + S(1))*log(a*x**n)**S(2)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))*log(a*x**n)**S(2), x), x, x**n*log(a*x**n)**S(2)/n - S(2)*x**n*log(a*x**n)/n + S(2)*x**n/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(a*x**n)**S(2), x), x, n**S(2)*x**S(4)/S(32) - n*x**S(4)*log(a*x**n)/S(8) + x**S(4)*log(a*x**n)**S(2)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(a*x**n)**S(2), x), x, S(2)*n**S(2)*x**S(3)/S(27) - S(2)*n*x**S(3)*log(a*x**n)/S(9) + x**S(3)*log(a*x**n)**S(2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(a*x**n)**S(2), x), x, n**S(2)*x**S(2)/S(4) - n*x**S(2)*log(a*x**n)/S(2) + x**S(2)*log(a*x**n)**S(2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**S(2), x), x, S(2)*n**S(2)*x - S(2)*n*x*log(a*x**n) + x*log(a*x**n)**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**S(2)/x, x), x, log(a*x**n)**S(3)/(S(3)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**S(2)/x**S(2), x), x, -S(2)*n**S(2)/x - S(2)*n*log(a*x**n)/x - log(a*x**n)**S(2)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**S(2)/x**S(3), x), x, -n**S(2)/(S(4)*x**S(2)) - n*log(a*x**n)/(S(2)*x**S(2)) - log(a*x**n)**S(2)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(a*x**n)**S(3), x), x, -S(6)*n**S(3)*x**(m + S(1))/(m + S(1))**S(4) + S(6)*n**S(2)*x**(m + S(1))*log(a*x**n)/(m + S(1))**S(3) - S(3)*n*x**(m + S(1))*log(a*x**n)**S(2)/(m + S(1))**S(2) + x**(m + S(1))*log(a*x**n)**S(3)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))*log(a*x**n)**S(3), x), x, x**n*log(a*x**n)**S(3)/n - S(3)*x**n*log(a*x**n)**S(2)/n + S(6)*x**n*log(a*x**n)/n - S(6)*x**n/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(a*x**n)**S(3), x), x, -S(3)*n**S(3)*x**S(4)/S(128) + S(3)*n**S(2)*x**S(4)*log(a*x**n)/S(32) - S(3)*n*x**S(4)*log(a*x**n)**S(2)/S(16) + x**S(4)*log(a*x**n)**S(3)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(a*x**n)**S(3), x), x, -S(2)*n**S(3)*x**S(3)/S(27) + S(2)*n**S(2)*x**S(3)*log(a*x**n)/S(9) - n*x**S(3)*log(a*x**n)**S(2)/S(3) + x**S(3)*log(a*x**n)**S(3)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(a*x**n)**S(3), x), x, -S(3)*n**S(3)*x**S(2)/S(8) + S(3)*n**S(2)*x**S(2)*log(a*x**n)/S(4) - S(3)*n*x**S(2)*log(a*x**n)**S(2)/S(4) + x**S(2)*log(a*x**n)**S(3)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**S(3), x), x, -S(6)*n**S(3)*x + S(6)*n**S(2)*x*log(a*x**n) - S(3)*n*x*log(a*x**n)**S(2) + x*log(a*x**n)**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**S(3)/x, x), x, log(a*x**n)**S(4)/(S(4)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**S(3)/x**S(2), x), x, -S(6)*n**S(3)/x - S(6)*n**S(2)*log(a*x**n)/x - S(3)*n*log(a*x**n)**S(2)/x - log(a*x**n)**S(3)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**S(3)/x**S(3), x), x, -S(3)*n**S(3)/(S(8)*x**S(2)) - S(3)*n**S(2)*log(a*x**n)/(S(4)*x**S(2)) - S(3)*n*log(a*x**n)**S(2)/(S(4)*x**S(2)) - log(a*x**n)**S(3)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(S(5)/2)*log(a*x), x), x, S(2)*x**(S(7)/2)*log(a*x)/S(7) - S(4)*x**(S(7)/2)/S(49), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(S(3)/2)*log(a*x), x), x, S(2)*x**(S(5)/2)*log(a*x)/S(5) - S(4)*x**(S(5)/2)/S(25), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(x)*log(a*x), x), x, S(2)*x**(S(3)/2)*log(a*x)/S(3) - S(4)*x**(S(3)/2)/S(9), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)/sqrt(x), x), x, S(2)*sqrt(x)*log(a*x) - S(4)*sqrt(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)/x**(S(3)/2), x), x, -S(2)*log(a*x)/sqrt(x) - S(4)/sqrt(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)/x**(S(5)/2), x), x, -S(2)*log(a*x)/(S(3)*x**(S(3)/2)) - S(4)/(S(9)*x**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(a*x**n), x), x, x**(m + S(1))*(a*x**n)**(-(m + S(1))/n)*Ei((m + S(1))*log(a*x**n)/n)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))/log(a*x**n), x), x, li(a*x**n)/(a*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(a*x**n), x), x, x**S(4)*(a*x**n)**(-S(4)/n)*Ei(S(4)*log(a*x**n)/n)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(a*x**n), x), x, x**S(3)*(a*x**n)**(-S(3)/n)*Ei(S(3)*log(a*x**n)/n)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(a*x**n), x), x, x**S(2)*(a*x**n)**(-S(2)/n)*Ei(S(2)*log(a*x**n)/n)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/log(a*x**n), x), x, x*(a*x**n)**(-S(1)/n)*Ei(log(a*x**n)/n)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(a*x**n)), x), x, log(log(a*x**n))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(a*x**n)), x), x, (a*x**n)**(S(1)/n)*Ei(-log(a*x**n)/n)/(n*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(a*x**n)), x), x, (a*x**n)**(S(2)/n)*Ei(-S(2)*log(a*x**n)/n)/(n*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(a*x**n)**S(2), x), x, -x**(m + S(1))/(n*log(a*x**n)) + x**(m + S(1))*(a*x**n)**(-(m + S(1))/n)*(m + S(1))*Ei((m + S(1))*log(a*x**n)/n)/n**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))/log(a*x**n)**S(2), x), x, -x**n/(n*log(a*x**n)) + li(a*x**n)/(a*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(a*x**n)**S(2), x), x, -x**S(4)/(n*log(a*x**n)) + S(4)*x**S(4)*(a*x**n)**(-S(4)/n)*Ei(S(4)*log(a*x**n)/n)/n**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(a*x**n)**S(2), x), x, -x**S(3)/(n*log(a*x**n)) + S(3)*x**S(3)*(a*x**n)**(-S(3)/n)*Ei(S(3)*log(a*x**n)/n)/n**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(a*x**n)**S(2), x), x, -x**S(2)/(n*log(a*x**n)) + S(2)*x**S(2)*(a*x**n)**(-S(2)/n)*Ei(S(2)*log(a*x**n)/n)/n**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**(S(-2)), x), x, -x/(n*log(a*x**n)) + x*(a*x**n)**(-S(1)/n)*Ei(log(a*x**n)/n)/n**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(a*x**n)**S(2)), x), x, -S(1)/(n*log(a*x**n)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(a*x**n)**S(2)), x), x, -S(1)/(n*x*log(a*x**n)) - (a*x**n)**(S(1)/n)*Ei(-log(a*x**n)/n)/(n**S(2)*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(a*x**n)**S(2)), x), x, -S(1)/(n*x**S(2)*log(a*x**n)) - S(2)*(a*x**n)**(S(2)/n)*Ei(-S(2)*log(a*x**n)/n)/(n**S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(a*x**n)**S(3), x), x, -x**(m + S(1))/(S(2)*n*log(a*x**n)**S(2)) - x**(m + S(1))*(m/S(2) + S(1)/2)/(n**S(2)*log(a*x**n)) + x**(m + S(1))*(a*x**n)**(-(m + S(1))/n)*(m + S(1))**S(2)*Ei((m + S(1))*log(a*x**n)/n)/(S(2)*n**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))/log(a*x**n)**S(3), x), x, -x**n/(S(2)*n*log(a*x**n)) - x**n/(S(2)*n*log(a*x**n)**S(2)) + li(a*x**n)/(S(2)*a*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(a*x**n)**S(3), x), x, -x**S(4)/(S(2)*n*log(a*x**n)**S(2)) - S(2)*x**S(4)/(n**S(2)*log(a*x**n)) + S(8)*x**S(4)*(a*x**n)**(-S(4)/n)*Ei(S(4)*log(a*x**n)/n)/n**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(a*x**n)**S(3), x), x, -x**S(3)/(S(2)*n*log(a*x**n)**S(2)) - S(3)*x**S(3)/(S(2)*n**S(2)*log(a*x**n)) + S(9)*x**S(3)*(a*x**n)**(-S(3)/n)*Ei(S(3)*log(a*x**n)/n)/(S(2)*n**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(a*x**n)**S(3), x), x, -x**S(2)/(S(2)*n*log(a*x**n)**S(2)) - x**S(2)/(n**S(2)*log(a*x**n)) + S(2)*x**S(2)*(a*x**n)**(-S(2)/n)*Ei(S(2)*log(a*x**n)/n)/n**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**(S(-3)), x), x, -x/(S(2)*n*log(a*x**n)**S(2)) - x/(S(2)*n**S(2)*log(a*x**n)) + x*(a*x**n)**(-S(1)/n)*Ei(log(a*x**n)/n)/(S(2)*n**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(a*x**n)**S(3)), x), x, -S(1)/(S(2)*n*log(a*x**n)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(a*x**n)**S(3)), x), x, -S(1)/(S(2)*n*x*log(a*x**n)**S(2)) + S(1)/(S(2)*n**S(2)*x*log(a*x**n)) + (a*x**n)**(S(1)/n)*Ei(-log(a*x**n)/n)/(S(2)*n**S(3)*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(a*x**n)**S(3)), x), x, -S(1)/(S(2)*n*x**S(2)*log(a*x**n)**S(2)) + S(1)/(n**S(2)*x**S(2)*log(a*x**n)) + S(2)*(a*x**n)**(S(2)/n)*Ei(-S(2)*log(a*x**n)/n)/(n**S(3)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(a*x), x), x, x**(m + S(1))*(a*x)**(-m + S(-1))*Ei((m + S(1))*log(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(a*x), x), x, Ei(S(4)*log(a*x))/a**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(a*x), x), x, Ei(S(3)*log(a*x))/a**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(a*x), x), x, Ei(S(2)*log(a*x))/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/log(a*x), x), x, li(a*x)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(a*x)), x), x, log(log(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(a*x)), x), x, a*Ei(-log(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(a*x)), x), x, a**S(2)*Ei(-S(2)*log(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(a*x)**S(2), x), x, x**(m + S(1))*(a*x)**(-m + S(-1))*(m + S(1))*Ei((m + S(1))*log(a*x)) - x**(m + S(1))/log(a*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(a*x)**S(2), x), x, -x**S(4)/log(a*x) + S(4)*Ei(S(4)*log(a*x))/a**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(a*x)**S(2), x), x, -x**S(3)/log(a*x) + S(3)*Ei(S(3)*log(a*x))/a**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(a*x)**S(2), x), x, -x**S(2)/log(a*x) + S(2)*Ei(S(2)*log(a*x))/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)**(S(-2)), x), x, -x/log(a*x) + li(a*x)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(a*x)**S(2)), x), x, -S(1)/log(a*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(a*x)**S(2)), x), x, -a*Ei(-log(a*x)) - S(1)/(x*log(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(a*x)**S(2)), x), x, -S(2)*a**S(2)*Ei(-S(2)*log(a*x)) - S(1)/(x**S(2)*log(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(a*x)**S(3), x), x, x**(m + S(1))*(a*x)**(-m + S(-1))*(m + S(1))**S(2)*Ei((m + S(1))*log(a*x))/S(2) - x**(m + S(1))*(m/S(2) + S(1)/2)/log(a*x) - x**(m + S(1))/(S(2)*log(a*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(a*x)**S(3), x), x, -S(2)*x**S(4)/log(a*x) - x**S(4)/(S(2)*log(a*x)**S(2)) + S(8)*Ei(S(4)*log(a*x))/a**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(a*x)**S(3), x), x, -S(3)*x**S(3)/(S(2)*log(a*x)) - x**S(3)/(S(2)*log(a*x)**S(2)) + S(9)*Ei(S(3)*log(a*x))/(S(2)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(a*x)**S(3), x), x, -x**S(2)/log(a*x) - x**S(2)/(S(2)*log(a*x)**S(2)) + S(2)*Ei(S(2)*log(a*x))/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)**(S(-3)), x), x, -x/(S(2)*log(a*x)) - x/(S(2)*log(a*x)**S(2)) + li(a*x)/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(a*x)**S(3)), x), x, -S(1)/(S(2)*log(a*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(a*x)**S(3)), x), x, a*Ei(-log(a*x))/S(2) + S(1)/(S(2)*x*log(a*x)) - S(1)/(S(2)*x*log(a*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(a*x)**S(3)), x), x, S(2)*a**S(2)*Ei(-S(2)*log(a*x)) + S(1)/(x**S(2)*log(a*x)) - S(1)/(S(2)*x**S(2)*log(a*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*sqrt(log(a*x**n)), x), x, -sqrt(pi)*sqrt(n)*x**(m + S(1))*(a*x**n)**(-(m + S(1))/n)*erfi(sqrt(m + S(1))*sqrt(log(a*x**n))/sqrt(n))/(S(2)*(m + S(1))**(S(3)/2)) + x**(m + S(1))*sqrt(log(a*x**n))/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*sqrt(log(a*x**n)), x), x, -sqrt(pi)*sqrt(n)*x**S(4)*(a*x**n)**(-S(4)/n)*erfi(S(2)*sqrt(log(a*x**n))/sqrt(n))/S(16) + x**S(4)*sqrt(log(a*x**n))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*sqrt(log(a*x**n)), x), x, -sqrt(S(3))*sqrt(pi)*sqrt(n)*x**S(3)*(a*x**n)**(-S(3)/n)*erfi(sqrt(S(3))*sqrt(log(a*x**n))/sqrt(n))/S(18) + x**S(3)*sqrt(log(a*x**n))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*sqrt(log(a*x**n)), x), x, -sqrt(S(2))*sqrt(pi)*sqrt(n)*x**S(2)*(a*x**n)**(-S(2)/n)*erfi(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/S(8) + x**S(2)*sqrt(log(a*x**n))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(log(a*x**n)), x), x, -sqrt(pi)*sqrt(n)*x*(a*x**n)**(-S(1)/n)*erfi(sqrt(log(a*x**n))/sqrt(n))/S(2) + x*sqrt(log(a*x**n)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(log(a*x**n))/x, x), x, S(2)*log(a*x**n)**(S(3)/2)/(S(3)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(log(a*x**n))/x**S(2), x), x, sqrt(pi)*sqrt(n)*(a*x**n)**(S(1)/n)*erf(sqrt(log(a*x**n))/sqrt(n))/(S(2)*x) - sqrt(log(a*x**n))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(log(a*x**n))/x**S(3), x), x, sqrt(S(2))*sqrt(pi)*sqrt(n)*(a*x**n)**(S(2)/n)*erf(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/(S(8)*x**S(2)) - sqrt(log(a*x**n))/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(a*x**n)**(S(3)/2), x), x, S(3)*sqrt(pi)*n**(S(3)/2)*x**(m + S(1))*(a*x**n)**(-(m + S(1))/n)*erfi(sqrt(m + S(1))*sqrt(log(a*x**n))/sqrt(n))/(S(4)*(m + S(1))**(S(5)/2)) - S(3)*n*x**(m + S(1))*sqrt(log(a*x**n))/(S(2)*(m + S(1))**S(2)) + x**(m + S(1))*log(a*x**n)**(S(3)/2)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(a*x**n)**(S(3)/2), x), x, S(3)*sqrt(pi)*n**(S(3)/2)*x**S(4)*(a*x**n)**(-S(4)/n)*erfi(S(2)*sqrt(log(a*x**n))/sqrt(n))/S(128) - S(3)*n*x**S(4)*sqrt(log(a*x**n))/S(32) + x**S(4)*log(a*x**n)**(S(3)/2)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(a*x**n)**(S(3)/2), x), x, sqrt(S(3))*sqrt(pi)*n**(S(3)/2)*x**S(3)*(a*x**n)**(-S(3)/n)*erfi(sqrt(S(3))*sqrt(log(a*x**n))/sqrt(n))/S(36) - n*x**S(3)*sqrt(log(a*x**n))/S(6) + x**S(3)*log(a*x**n)**(S(3)/2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(a*x**n)**(S(3)/2), x), x, S(3)*sqrt(S(2))*sqrt(pi)*n**(S(3)/2)*x**S(2)*(a*x**n)**(-S(2)/n)*erfi(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/S(32) - S(3)*n*x**S(2)*sqrt(log(a*x**n))/S(8) + x**S(2)*log(a*x**n)**(S(3)/2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**(S(3)/2), x), x, S(3)*sqrt(pi)*n**(S(3)/2)*x*(a*x**n)**(-S(1)/n)*erfi(sqrt(log(a*x**n))/sqrt(n))/S(4) - S(3)*n*x*sqrt(log(a*x**n))/S(2) + x*log(a*x**n)**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**(S(3)/2)/x, x), x, S(2)*log(a*x**n)**(S(5)/2)/(S(5)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**(S(3)/2)/x**S(2), x), x, S(3)*sqrt(pi)*n**(S(3)/2)*(a*x**n)**(S(1)/n)*erf(sqrt(log(a*x**n))/sqrt(n))/(S(4)*x) - S(3)*n*sqrt(log(a*x**n))/(S(2)*x) - log(a*x**n)**(S(3)/2)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**(S(3)/2)/x**S(3), x), x, S(3)*sqrt(S(2))*sqrt(pi)*n**(S(3)/2)*(a*x**n)**(S(2)/n)*erf(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/(S(32)*x**S(2)) - S(3)*n*sqrt(log(a*x**n))/(S(8)*x**S(2)) - log(a*x**n)**(S(3)/2)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/sqrt(log(a*x**n)), x), x, sqrt(pi)*x**(m + S(1))*(a*x**n)**(-(m + S(1))/n)*erfi(sqrt(m + S(1))*sqrt(log(a*x**n))/sqrt(n))/(sqrt(n)*sqrt(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/sqrt(log(a*x**n)), x), x, sqrt(pi)*x**S(4)*(a*x**n)**(-S(4)/n)*erfi(S(2)*sqrt(log(a*x**n))/sqrt(n))/(S(2)*sqrt(n)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/sqrt(log(a*x**n)), x), x, sqrt(S(3))*sqrt(pi)*x**S(3)*(a*x**n)**(-S(3)/n)*erfi(sqrt(S(3))*sqrt(log(a*x**n))/sqrt(n))/(S(3)*sqrt(n)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/sqrt(log(a*x**n)), x), x, sqrt(S(2))*sqrt(pi)*x**S(2)*(a*x**n)**(-S(2)/n)*erfi(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/(S(2)*sqrt(n)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(log(a*x**n)), x), x, sqrt(pi)*x*(a*x**n)**(-S(1)/n)*erfi(sqrt(log(a*x**n))/sqrt(n))/sqrt(n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(log(a*x**n))), x), x, S(2)*sqrt(log(a*x**n))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*sqrt(log(a*x**n))), x), x, sqrt(pi)*(a*x**n)**(S(1)/n)*erf(sqrt(log(a*x**n))/sqrt(n))/(sqrt(n)*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*sqrt(log(a*x**n))), x), x, sqrt(S(2))*sqrt(pi)*(a*x**n)**(S(2)/n)*erf(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/(S(2)*sqrt(n)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(a*x**n)**(S(3)/2), x), x, -S(2)*x**(m + S(1))/(n*sqrt(log(a*x**n))) + S(2)*sqrt(pi)*x**(m + S(1))*(a*x**n)**(-(m + S(1))/n)*sqrt(m + S(1))*erfi(sqrt(m + S(1))*sqrt(log(a*x**n))/sqrt(n))/n**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(a*x**n)**(S(3)/2), x), x, -S(2)*x**S(4)/(n*sqrt(log(a*x**n))) + S(4)*sqrt(pi)*x**S(4)*(a*x**n)**(-S(4)/n)*erfi(S(2)*sqrt(log(a*x**n))/sqrt(n))/n**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(a*x**n)**(S(3)/2), x), x, -S(2)*x**S(3)/(n*sqrt(log(a*x**n))) + S(2)*sqrt(S(3))*sqrt(pi)*x**S(3)*(a*x**n)**(-S(3)/n)*erfi(sqrt(S(3))*sqrt(log(a*x**n))/sqrt(n))/n**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(a*x**n)**(S(3)/2), x), x, -S(2)*x**S(2)/(n*sqrt(log(a*x**n))) + S(2)*sqrt(S(2))*sqrt(pi)*x**S(2)*(a*x**n)**(-S(2)/n)*erfi(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/n**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**(S(-3)/2), x), x, -S(2)*x/(n*sqrt(log(a*x**n))) + S(2)*sqrt(pi)*x*(a*x**n)**(-S(1)/n)*erfi(sqrt(log(a*x**n))/sqrt(n))/n**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(a*x**n)**(S(3)/2)), x), x, -S(2)/(n*sqrt(log(a*x**n))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(a*x**n)**(S(3)/2)), x), x, -S(2)/(n*x*sqrt(log(a*x**n))) - S(2)*sqrt(pi)*(a*x**n)**(S(1)/n)*erf(sqrt(log(a*x**n))/sqrt(n))/(n**(S(3)/2)*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(a*x**n)**(S(3)/2)), x), x, -S(2)/(n*x**S(2)*sqrt(log(a*x**n))) - S(2)*sqrt(S(2))*sqrt(pi)*(a*x**n)**(S(2)/n)*erf(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/(n**(S(3)/2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(a*x**n)**(S(5)/2), x), x, -S(2)*x**(m + S(1))/(S(3)*n*log(a*x**n)**(S(3)/2)) - x**(m + S(1))*(S(4)*m/S(3) + S(4)/3)/(n**S(2)*sqrt(log(a*x**n))) + S(4)*sqrt(pi)*x**(m + S(1))*(a*x**n)**(-(m + S(1))/n)*(m + S(1))**(S(3)/2)*erfi(sqrt(m + S(1))*sqrt(log(a*x**n))/sqrt(n))/(S(3)*n**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(a*x**n)**(S(5)/2), x), x, -S(2)*x**S(4)/(S(3)*n*log(a*x**n)**(S(3)/2)) - S(16)*x**S(4)/(S(3)*n**S(2)*sqrt(log(a*x**n))) + S(32)*sqrt(pi)*x**S(4)*(a*x**n)**(-S(4)/n)*erfi(S(2)*sqrt(log(a*x**n))/sqrt(n))/(S(3)*n**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(a*x**n)**(S(5)/2), x), x, -S(2)*x**S(3)/(S(3)*n*log(a*x**n)**(S(3)/2)) - S(4)*x**S(3)/(n**S(2)*sqrt(log(a*x**n))) + S(4)*sqrt(S(3))*sqrt(pi)*x**S(3)*(a*x**n)**(-S(3)/n)*erfi(sqrt(S(3))*sqrt(log(a*x**n))/sqrt(n))/n**(S(5)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(a*x**n)**(S(5)/2), x), x, -S(2)*x**S(2)/(S(3)*n*log(a*x**n)**(S(3)/2)) - S(8)*x**S(2)/(S(3)*n**S(2)*sqrt(log(a*x**n))) + S(8)*sqrt(S(2))*sqrt(pi)*x**S(2)*(a*x**n)**(-S(2)/n)*erfi(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/(S(3)*n**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**(S(-5)/2), x), x, -S(2)*x/(S(3)*n*log(a*x**n)**(S(3)/2)) - S(4)*x/(S(3)*n**S(2)*sqrt(log(a*x**n))) + S(4)*sqrt(pi)*x*(a*x**n)**(-S(1)/n)*erfi(sqrt(log(a*x**n))/sqrt(n))/(S(3)*n**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(a*x**n)**(S(5)/2)), x), x, -S(2)/(S(3)*n*log(a*x**n)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(a*x**n)**(S(5)/2)), x), x, -S(2)/(S(3)*n*x*log(a*x**n)**(S(3)/2)) + S(4)/(S(3)*n**S(2)*x*sqrt(log(a*x**n))) + S(4)*sqrt(pi)*(a*x**n)**(S(1)/n)*erf(sqrt(log(a*x**n))/sqrt(n))/(S(3)*n**(S(5)/2)*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(a*x**n)**(S(5)/2)), x), x, -S(2)/(S(3)*n*x**S(2)*log(a*x**n)**(S(3)/2)) + S(8)/(S(3)*n**S(2)*x**S(2)*sqrt(log(a*x**n))) + S(8)*sqrt(S(2))*sqrt(pi)*(a*x**n)**(S(2)/n)*erf(sqrt(S(2))*sqrt(log(a*x**n))/sqrt(n))/(S(3)*n**(S(5)/2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(a*x)**p, x), x, x**(m + S(1))*(a*x)**(-m + S(-1))*((-m + S(-1))*log(a*x))**(-p)*Gamma(p + S(1), (-m + S(-1))*log(a*x))*log(a*x)**p/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(a*x)**p, x), x, S(4)**(-p + S(-1))*(-log(a*x))**(-p)*Gamma(p + S(1), -S(4)*log(a*x))*log(a*x)**p/a**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(a*x)**p, x), x, S(3)**(-p + S(-1))*(-log(a*x))**(-p)*Gamma(p + S(1), -S(3)*log(a*x))*log(a*x)**p/a**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(a*x)**p, x), x, S(2)**(-p + S(-1))*(-log(a*x))**(-p)*Gamma(p + S(1), -S(2)*log(a*x))*log(a*x)**p/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)**p, x), x, (-log(a*x))**(-p)*Gamma(p + S(1), -log(a*x))*log(a*x)**p/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)**p/x, x), x, log(a*x)**(p + S(1))/(p + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)**p/x**S(2), x), x, -a*Gamma(p + S(1), log(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)**p/x**S(3), x), x, -S(2)**(-p + S(-1))*a**S(2)*Gamma(p + S(1), S(2)*log(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(a*x**n)**p, x), x, x**(m + S(1))*(a*x**n)**(-(m + S(1))/n)*((-m + S(-1))*log(a*x**n)/n)**(-p)*Gamma(p + S(1), (-m + S(-1))*log(a*x**n)/n)*log(a*x**n)**p/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))*log(a*x**n)**p, x), x, (-log(a*x**n))**(-p)*Gamma(p + S(1), -log(a*x**n))*log(a*x**n)**p/(a*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(a*x**n)**p, x), x, S(4)**(-p + S(-1))*x**S(4)*(a*x**n)**(-S(4)/n)*(-log(a*x**n)/n)**(-p)*Gamma(p + S(1), -S(4)*log(a*x**n)/n)*log(a*x**n)**p, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(a*x**n)**p, x), x, S(3)**(-p + S(-1))*x**S(3)*(a*x**n)**(-S(3)/n)*(-log(a*x**n)/n)**(-p)*Gamma(p + S(1), -S(3)*log(a*x**n)/n)*log(a*x**n)**p, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(a*x**n)**p, x), x, S(2)**(-p + S(-1))*x**S(2)*(a*x**n)**(-S(2)/n)*(-log(a*x**n)/n)**(-p)*Gamma(p + S(1), -S(2)*log(a*x**n)/n)*log(a*x**n)**p, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**p, x), x, x*(a*x**n)**(-S(1)/n)*(-log(a*x**n)/n)**(-p)*Gamma(p + S(1), -log(a*x**n)/n)*log(a*x**n)**p, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**p/x, x), x, log(a*x**n)**(p + S(1))/(n*(p + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**p/x**S(2), x), x, -(a*x**n)**(S(1)/n)*(log(a*x**n)/n)**(-p)*Gamma(p + S(1), log(a*x**n)/n)*log(a*x**n)**p/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)**p/x**S(3), x), x, -S(2)**(-p + S(-1))*(a*x**n)**(S(2)/n)*(log(a*x**n)/n)**(-p)*Gamma(p + S(1), S(2)*log(a*x**n)/n)*log(a*x**n)**p/x**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(c*(b*x**n)**p), x), x, -n*p*x**(m + S(1))/(m + S(1))**S(2) + x**(m + S(1))*log(c*(b*x**n)**p)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(b*x**n)**p), x), x, -n*p*x**S(3)/S(9) + x**S(3)*log(c*(b*x**n)**p)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(b*x**n)**p), x), x, -n*p*x**S(2)/S(4) + x**S(2)*log(c*(b*x**n)**p)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p), x), x, -n*p*x + x*log(c*(b*x**n)**p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p)/x, x), x, log(c*(b*x**n)**p)**S(2)/(S(2)*n*p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p)/x**S(2), x), x, -n*p/x - log(c*(b*x**n)**p)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p)/x**S(3), x), x, -n*p/(S(4)*x**S(2)) - log(c*(b*x**n)**p)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p)/x**S(4), x), x, -n*p/(S(9)*x**S(3)) - log(c*(b*x**n)**p)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(c*(b*x**n)**p)**S(2), x), x, S(2)*n**S(2)*p**S(2)*x**(m + S(1))/(m + S(1))**S(3) - S(2)*n*p*x**(m + S(1))*log(c*(b*x**n)**p)/(m + S(1))**S(2) + x**(m + S(1))*log(c*(b*x**n)**p)**S(2)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(b*x**n)**p)**S(2), x), x, S(2)*n**S(2)*p**S(2)*x**S(3)/S(27) - S(2)*n*p*x**S(3)*log(c*(b*x**n)**p)/S(9) + x**S(3)*log(c*(b*x**n)**p)**S(2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(b*x**n)**p)**S(2), x), x, n**S(2)*p**S(2)*x**S(2)/S(4) - n*p*x**S(2)*log(c*(b*x**n)**p)/S(2) + x**S(2)*log(c*(b*x**n)**p)**S(2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p)**S(2), x), x, S(2)*n**S(2)*p**S(2)*x - S(2)*n*p*x*log(c*(b*x**n)**p) + x*log(c*(b*x**n)**p)**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p)**S(2)/x, x), x, log(c*(b*x**n)**p)**S(3)/(S(3)*n*p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p)**S(2)/x**S(2), x), x, -S(2)*n**S(2)*p**S(2)/x - S(2)*n*p*log(c*(b*x**n)**p)/x - log(c*(b*x**n)**p)**S(2)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p)**S(2)/x**S(3), x), x, -n**S(2)*p**S(2)/(S(4)*x**S(2)) - n*p*log(c*(b*x**n)**p)/(S(2)*x**S(2)) - log(c*(b*x**n)**p)**S(2)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(b*x**n)**p)**S(2)/x**S(4), x), x, -S(2)*n**S(2)*p**S(2)/(S(27)*x**S(3)) - S(2)*n*p*log(c*(b*x**n)**p)/(S(9)*x**S(3)) - log(c*(b*x**n)**p)**S(2)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(c*(b*x**n)**p), x), x, x**(m + S(1))*(c*(b*x**n)**p)**(-(m + S(1))/(n*p))*Ei((m + S(1))*log(c*(b*x**n)**p)/(n*p))/(n*p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(c*(b*x**n)**p)**S(2), x), x, -x**(m + S(1))/(n*p*log(c*(b*x**n)**p)) + x**(m + S(1))*(c*(b*x**n)**p)**(-(m + S(1))/(n*p))*(m + S(1))*Ei((m + S(1))*log(c*(b*x**n)**p)/(n*p))/(n**S(2)*p**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(c*(b*x**n)**p)**q, x), x, x**(m + S(1))*(c*(b*x**n)**p)**(-(m + S(1))/(n*p))*((-m + S(-1))*log(c*(b*x**n)**p)/(n*p))**(-q)*Gamma(q + S(1), (-m + S(-1))*log(c*(b*x**n)**p)/(n*p))*log(c*(b*x**n)**p)**q/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**m*log(c*x), x), x, (a + b*x)**(m + S(1))*log(c*x)/(b*(m + S(1))) + (a + b*x)**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), S(1) + b*x/a)/(a*b*(m**S(2) + S(3)*m + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*log(c*x), x), x, -a**S(4)*log(x)/(S(4)*b) - a**S(3)*x - S(3)*a**S(2)*b*x**S(2)/S(4) - a*b**S(2)*x**S(3)/S(3) - b**S(3)*x**S(4)/S(16) + (a + b*x)**S(4)*log(c*x)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*log(c*x), x), x, -a**S(3)*log(x)/(S(3)*b) - a**S(2)*x - a*b*x**S(2)/S(2) - b**S(2)*x**S(3)/S(9) + (a + b*x)**S(3)*log(c*x)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*log(c*x), x), x, -a*x - b*x**S(2)/S(4) + x*(S(2)*a + b*x)*log(c*x)/S(2), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((a + b*x)*log(c*x), x), x, -a**S(2)*log(x)/(S(2)*b) - a*x - b*x**S(2)/S(4) + (a + b*x)**S(2)*log(c*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x)/(a + b*x), x), x, log((a + b*x)/a)*log(c*x)/b + polylog(S(2), -b*x/a)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x)/(a + b*x)**S(2), x), x, -log(c*x)/(b*(a + b*x)) + log(x)/(a*b) - log(a + b*x)/(a*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x)/(a + b*x)**S(3), x), x, -log(c*x)/(S(2)*b*(a + b*x)**S(2)) + S(1)/(S(2)*a*b*(a + b*x)) + log(x)/(S(2)*a**S(2)*b) - log(a + b*x)/(S(2)*a**S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x)/(a + b*x)**S(4), x), x, -log(c*x)/(S(3)*b*(a + b*x)**S(3)) + S(1)/(S(6)*a*b*(a + b*x)**S(2)) + S(1)/(S(3)*a**S(2)*b*(a + b*x)) + log(x)/(S(3)*a**S(3)*b) - log(a + b*x)/(S(3)*a**S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**m*log(c*x**n), x), x, (a + b*x)**(m + S(1))*log(c*x**n)/(b*(m + S(1))) + n*(a + b*x)**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), S(1) + b*x/a)/(a*b*(m**S(2) + S(3)*m + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*log(c*x**n), x), x, -a**S(4)*n*log(x)/(S(4)*b) - a**S(3)*n*x - S(3)*a**S(2)*b*n*x**S(2)/S(4) - a*b**S(2)*n*x**S(3)/S(3) - b**S(3)*n*x**S(4)/S(16) + (a + b*x)**S(4)*log(c*x**n)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*log(c*x**n), x), x, -a**S(3)*n*log(x)/(S(3)*b) - a**S(2)*n*x - a*b*n*x**S(2)/S(2) - b**S(2)*n*x**S(3)/S(9) + (a + b*x)**S(3)*log(c*x**n)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*log(c*x**n), x), x, -a**S(2)*n*log(x)/(S(2)*b) - a*n*x - b*n*x**S(2)/S(4) + (a + b*x)**S(2)*log(c*x**n)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**n)/(a + b*x), x), x, n*polylog(S(2), -b*x/a)/b + log((a + b*x)/a)*log(c*x**n)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**n)/(a + b*x)**S(2), x), x, -log(c*x**n)/(b*(a + b*x)) + n*log(x)/(a*b) - n*log(a + b*x)/(a*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**n)/(a + b*x)**S(3), x), x, -log(c*x**n)/(S(2)*b*(a + b*x)**S(2)) + n/(S(2)*a*b*(a + b*x)) + n*log(x)/(S(2)*a**S(2)*b) - n*log(a + b*x)/(S(2)*a**S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**n)/(a + b*x)**S(4), x), x, -log(c*x**n)/(S(3)*b*(a + b*x)**S(3)) + n/(S(6)*a*b*(a + b*x)**S(2)) + n/(S(3)*a**S(2)*b*(a + b*x)) + n*log(x)/(S(3)*a**S(3)*b) - n*log(a + b*x)/(S(3)*a**S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**n)/(S(4)*x + S(2))**S(2), x), x, n*log(x)/S(8) - n*log(S(2)*x + S(1))/S(8) - log(c*x**n)/(S(8)*(S(2)*x + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x)/(-a*x + S(1)), x), x, polylog(S(2), -a*x + S(1))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x/a)/(a - x), x), x, polylog(S(2), (a - x)/a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(a*x**S(2))/(-a*x**S(2) + S(1)), x), x, polylog(S(2), -a*x**S(2) + S(1))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(x**S(2)/a)/(a - x**S(2)), x), x, polylog(S(2), (a - x**S(2))/a)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))*log(a*x**n)/(-a*x**n + S(1)), x), x, polylog(S(2), -a*x**n + S(1))/(a*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))*log(x**n/a)/(a - x**n), x), x, polylog(S(2), (a - x**n)/a)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a/x)/(a*x - x**S(2)), x), x, polylog(S(2), -a/x + S(1))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a/x**S(2))/(a*x - x**S(3)), x), x, polylog(S(2), (-a + x**S(2))/x**S(2))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**(-n + S(1)))/(a*x - x**n), x), x, -polylog(S(2), -a*x**(-n + S(1)) + S(1))/(a*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(-a*x**(-m)*(-c + S(1))/b + c)/(x*(a + b*x**m)), x), x, polylog(S(2), x**(-m)*(a + b*x**m)*(-c + S(1))/b)/(a*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**(-m)*(a*c - a + b*c*x**m)/b)/(x*(a + b*x**m)), x), x, polylog(S(2), x**(-m)*(a + b*x**m)*(-c + S(1))/b)/(a*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + x**(-m)*(a*c*d - d)/(c*e)))/(x*(d + e*x**m)), x), x, polylog(S(2), x**(-m)*(d + e*x**m)*(-a*c + S(1))/e)/(d*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**(-m)*(a*c*d + a*c*e*x**m - d)/e)/(x*(d + e*x**m)), x), x, polylog(S(2), x**(-m)*(d + e*x**m)*(-a*c + S(1))/e)/(d*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(2)*a/(a + b*x))/(a**S(2) - b**S(2)*x**S(2)), x), x, polylog(S(2), (-a + b*x)/(a + b*x))/(S(2)*a*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(2)*a/(a + b*x))/((a - b*x)*(a + b*x)), x), x, polylog(S(2), (-a + b*x)/(a + b*x))/(S(2)*a*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((a*(-c + S(1)) + b*x*(c + S(1)))/(a + b*x))/(a**S(2) - b**S(2)*x**S(2)), x), x, polylog(S(2), c*(a - b*x)/(a + b*x))/(S(2)*a*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((a*(-c + S(1)) + b*x*(c + S(1)))/(a + b*x))/((a - b*x)*(a + b*x)), x), x, polylog(S(2), c*(a - b*x)/(a + b*x))/(S(2)*a*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(-c*(a - b*x)/(a + b*x) + S(1))/(a**S(2) - b**S(2)*x**S(2)), x), x, polylog(S(2), c*(a - b*x)/(a + b*x))/(S(2)*a*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(-c*(a - b*x)/(a + b*x) + S(1))/((a - b*x)*(a + b*x)), x), x, polylog(S(2), c*(a - b*x)/(a + b*x))/(S(2)*a*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*x**n))/(d + e*x**S(2)), x), x, -I*b*n*polylog(S(2), -I*sqrt(e)*x/sqrt(d))/(S(2)*sqrt(d)*sqrt(e)) + I*b*n*polylog(S(2), I*sqrt(e)*x/sqrt(d))/(S(2)*sqrt(d)*sqrt(e)) + (a + b*log(c*x**n))*atan(sqrt(e)*x/sqrt(d))/(sqrt(d)*sqrt(e)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*x**n))/(d + e*x + f*x**S(2)), x), x, b*n*polylog(S(2), -S(2)*f*x/(e - sqrt(-S(4)*d*f + e**S(2))))/sqrt(-S(4)*d*f + e**S(2)) - b*n*polylog(S(2), -S(2)*f*x/(e + sqrt(-S(4)*d*f + e**S(2))))/sqrt(-S(4)*d*f + e**S(2)) + (a + b*log(c*x**n))*log((e + S(2)*f*x - sqrt(-S(4)*d*f + e**S(2)))/(e - sqrt(-S(4)*d*f + e**S(2))))/sqrt(-S(4)*d*f + e**S(2)) - (a + b*log(c*x**n))*log((e + S(2)*f*x + sqrt(-S(4)*d*f + e**S(2)))/(e + sqrt(-S(4)*d*f + e**S(2))))/sqrt(-S(4)*d*f + e**S(2)), expand=True, _diff=True, _numerical=True)

    # same result as in mathematica but fails assert rubi_test(rubi_integrate((d + e*x)**m*log(c*x)/x, x), x, (d + e*x)**m*(d/(e*x) + S(1))**(-m)*log(c*x)*hyper((-m, -m), (-m + S(1),), -d/(e*x))/m - (d + e*x)**m*(d/(e*x) + S(1))**(-m)*hyper((-m, -m, -m), (-m + S(1), -m + S(1)), -d/(e*x))/m**S(2), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((a + b*log(c*x**n))**S(3), x), x, S(6)*a*b**S(2)*n**S(2)*x - S(6)*b**S(3)*n**S(3)*x + S(6)*b**S(3)*n**S(2)*x*log(c*x**n) - S(3)*b*n*x*(a + b*log(c*x**n))**S(2) + x*(a + b*log(c*x**n))**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*x**n))**S(2), x), x, -S(2)*a*b*n*x + S(2)*b**S(2)*n**S(2)*x - S(2)*b**S(2)*n*x*log(c*x**n) + x*(a + b*log(c*x**n))**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a + b*log(c*x**n), x), x, a*x - b*n*x + b*x*log(c*x**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*log(c*x**n)), x), x, x*(c*x**n)**(-S(1)/n)*exp(-a/(b*n))*Ei((a + b*log(c*x**n))/(b*n))/(b*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*x**n))**(S(-2)), x), x, -x/(b*n*(a + b*log(c*x**n))) + x*(c*x**n)**(-S(1)/n)*exp(-a/(b*n))*Ei((a + b*log(c*x**n))/(b*n))/(b**S(2)*n**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*x**n))**(S(-3)), x), x, -x/(S(2)*b*n*(a + b*log(c*x**n))**S(2)) - x/(S(2)*b**S(2)*n**S(2)*(a + b*log(c*x**n))) + x*(c*x**n)**(-S(1)/n)*exp(-a/(b*n))*Ei((a + b*log(c*x**n))/(b*n))/(S(2)*b**S(3)*n**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(c*x**n))**m, x), x, x*(c*x**n)**(-S(1)/n)*((-a - b*log(c*x**n))/(b*n))**(-m)*(a + b*log(c*x**n))**m*Gamma(m + S(1), (-a - b*log(c*x**n))/(b*n))*exp(-a/(b*n)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/(a + b*log(c*x**n)), x), x, x**(m + S(1))*(c*x**n)**(-(m + S(1))/n)*exp(-a*(m + S(1))/(b*n))*Ei((a + b*log(c*x**n))*(m + S(1))/(b*n))/(b*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/(a + b*log(c*x**n))**S(2), x), x, -x**(m + S(1))/(b*n*(a + b*log(c*x**n))) + x**(m + S(1))*(c*x**n)**(-(m + S(1))/n)*(m + S(1))*exp(-a*(m + S(1))/(b*n))*Ei((a + b*log(c*x**n))*(m + S(1))/(b*n))/(b**S(2)*n**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*(a + b*log(c*x**n))**p, x), x, x**(m + S(1))*(c*x**n)**(-(m + S(1))/n)*((a + b*log(c*x**n))*(-m + S(-1))/(b*n))**(-p)*(a + b*log(c*x**n))**p*Gamma(p + S(1), (a + b*log(c*x**n))*(-m + S(-1))/(b*n))*exp(-a*(m + S(1))/(b*n))/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(n + S(-1))*log(-b*x**n/a)/(a + b*x**n), x), x, -polylog(S(2), (a + b*x**n)/a)/(b*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(c*(a + b*x**S(2))**p), x), x, x**(m + S(1))*log(c*(a + b*x**S(2))**p)/(m + S(1)) - S(2)*b*p*x**(m + S(3))*hyper((S(1), m/S(2) + S(3)/2), (m/S(2) + S(5)/2,), -b*x**S(2)/a)/(a*(m**S(2) + S(4)*m + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(c*(a + b*x**S(2))**p), x), x, S(2)*a**(S(5)/2)*p*atan(sqrt(b)*x/sqrt(a))/(S(5)*b**(S(5)/2)) - S(2)*a**S(2)*p*x/(S(5)*b**S(2)) + S(2)*a*p*x**S(3)/(S(15)*b) - S(2)*p*x**S(5)/S(25) + x**S(5)*log(c*(a + b*x**S(2))**p)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b*x**S(2))**p), x), x, -a**S(2)*p*log(a + b*x**S(2))/(S(4)*b**S(2)) + a*p*x**S(2)/(S(4)*b) - p*x**S(4)/S(8) + x**S(4)*log(c*(a + b*x**S(2))**p)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x**S(2))**p), x), x, -S(2)*a**(S(3)/2)*p*atan(sqrt(b)*x/sqrt(a))/(S(3)*b**(S(3)/2)) + S(2)*a*p*x/(S(3)*b) - S(2)*p*x**S(3)/S(9) + x**S(3)*log(c*(a + b*x**S(2))**p)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b*x**S(2))**p), x), x, -p*x**S(2)/S(2) + (a/S(2) + b*x**S(2)/S(2))*log(c*(a + b*x**S(2))**p)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p), x), x, S(2)*sqrt(a)*p*atan(sqrt(b)*x/sqrt(a))/sqrt(b) - S(2)*p*x + x*log(c*(a + b*x**S(2))**p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/x, x), x, p*polylog(S(2), (a + b*x**S(2))/a)/S(2) + log(c*(a + b*x**S(2))**p)*log(-b*x**S(2)/a)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/x**S(2), x), x, -log(c*(a + b*x**S(2))**p)/x + S(2)*sqrt(b)*p*atan(sqrt(b)*x/sqrt(a))/sqrt(a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/x**S(3), x), x, b*p*log(x)/a - (a/S(2) + b*x**S(2)/S(2))*log(c*(a + b*x**S(2))**p)/(a*x**S(2)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/x**S(3), x), x, -log(c*(a + b*x**S(2))**p)/(S(2)*x**S(2)) + b*p*log(x)/a - b*p*log(a + b*x**S(2))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/x**S(4), x), x, -log(c*(a + b*x**S(2))**p)/(S(3)*x**S(3)) - S(2)*b*p/(S(3)*a*x) - S(2)*b**(S(3)/2)*p*atan(sqrt(b)*x/sqrt(a))/(S(3)*a**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/x**S(5), x), x, -log(c*(a + b*x**S(2))**p)/(S(4)*x**S(4)) - b*p/(S(4)*a*x**S(2)) - b**S(2)*p*log(x)/(S(2)*a**S(2)) + b**S(2)*p*log(a + b*x**S(2))/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/x**S(6), x), x, -log(c*(a + b*x**S(2))**p)/(S(5)*x**S(5)) - S(2)*b*p/(S(15)*a*x**S(3)) + S(2)*b**S(2)*p/(S(5)*a**S(2)*x) + S(2)*b**(S(5)/2)*p*atan(sqrt(b)*x/sqrt(a))/(S(5)*a**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/x**S(7), x), x, -log(c*(a + b*x**S(2))**p)/(S(6)*x**S(6)) - b*p/(S(12)*a*x**S(4)) + b**S(2)*p/(S(6)*a**S(2)*x**S(2)) + b**S(3)*p*log(x)/(S(3)*a**S(3)) - b**S(3)*p*log(a + b*x**S(2))/(S(6)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(c*(a + b*x**S(3))**p), x), x, x**(m + S(1))*log(c*(a + b*x**S(3))**p)/(m + S(1)) - S(3)*b*p*x**(m + S(4))*hyper((S(1), m/S(3) + S(4)/3), (m/S(3) + S(7)/3,), -b*x**S(3)/a)/(a*(m**S(2) + S(5)*m + S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)*log(c*(a + b*x**S(3))**p), x), x, -a**S(2)*p*log(a + b*x**S(3))/(S(6)*b**S(2)) + a*p*x**S(3)/(S(6)*b) - p*x**S(6)/S(12) + x**S(6)*log(c*(a + b*x**S(3))**p)/S(6), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(c*(a + b*x**S(3))**p), x), x, a**(S(5)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(5)*b**(S(5)/3)) - a**(S(5)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(10)*b**(S(5)/3)) + sqrt(S(3))*a**(S(5)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(5)*b**(S(5)/3)) + S(3)*a*p*x**S(2)/(S(10)*b) - S(3)*p*x**S(5)/S(25) + x**S(5)*log(c*(a + b*x**S(3))**p)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b*x**S(3))**p), x), x, -a**(S(4)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(4)*b**(S(4)/3)) + a**(S(4)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(8)*b**(S(4)/3)) + sqrt(S(3))*a**(S(4)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(4)*b**(S(4)/3)) + S(3)*a*p*x/(S(4)*b) - S(3)*p*x**S(4)/S(16) + x**S(4)*log(c*(a + b*x**S(3))**p)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x**S(3))**p), x), x, -p*x**S(3)/S(3) + (a/S(3) + b*x**S(3)/S(3))*log(c*(a + b*x**S(3))**p)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b*x**S(3))**p), x), x, -a**(S(2)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(2)*b**(S(2)/3)) + a**(S(2)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(4)*b**(S(2)/3)) - sqrt(S(3))*a**(S(2)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(2)*b**(S(2)/3)) - S(3)*p*x**S(2)/S(4) + x**S(2)*log(c*(a + b*x**S(3))**p)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p), x), x, a**(S(1)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/b**(S(1)/3) - a**(S(1)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*b**(S(1)/3)) - sqrt(S(3))*a**(S(1)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/b**(S(1)/3) - S(3)*p*x + x*log(c*(a + b*x**S(3))**p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/x, x), x, p*polylog(S(2), (a + b*x**S(3))/a)/S(3) + log(c*(a + b*x**S(3))**p)*log(-b*x**S(3)/a)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/x**S(2), x), x, -log(c*(a + b*x**S(3))**p)/x - b**(S(1)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/a**(S(1)/3) + b**(S(1)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*a**(S(1)/3)) - sqrt(S(3))*b**(S(1)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/a**(S(1)/3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/x**S(3), x), x, -log(c*(a + b*x**S(3))**p)/(S(2)*x**S(2)) + b**(S(2)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(2)*a**(S(2)/3)) - b**(S(2)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(4)*a**(S(2)/3)) - sqrt(S(3))*b**(S(2)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(2)*a**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/x**S(4), x), x, -log(c*(a + b*x**S(3))**p)/(S(3)*x**S(3)) + b*p*log(x)/a - b*p*log(a + b*x**S(3))/(S(3)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/x**S(5), x), x, -log(c*(a + b*x**S(3))**p)/(S(4)*x**S(4)) - S(3)*b*p/(S(4)*a*x) + b**(S(4)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(4)*a**(S(4)/3)) - b**(S(4)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(8)*a**(S(4)/3)) + sqrt(S(3))*b**(S(4)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(4)*a**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/x**S(6), x), x, -log(c*(a + b*x**S(3))**p)/(S(5)*x**S(5)) - S(3)*b*p/(S(10)*a*x**S(2)) - b**(S(5)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(5)*a**(S(5)/3)) + b**(S(5)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(10)*a**(S(5)/3)) + sqrt(S(3))*b**(S(5)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(5)*a**(S(5)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/x**S(7), x), x, -log(c*(a + b*x**S(3))**p)/(S(6)*x**S(6)) - b*p/(S(6)*a*x**S(3)) - b**S(2)*p*log(x)/(S(2)*a**S(2)) + b**S(2)*p*log(a + b*x**S(3))/(S(6)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(c*(a + b*sqrt(x))**p), x), x, x**(m + S(1))*log(c*(a + b*sqrt(x))**p)/(m + S(1)) - b*p*x**(m + S(3)/2)*hyper((S(1), S(2)*m + S(3)), (S(2)*m + S(4),), -b*sqrt(x)/a)/(a*(S(2)*m**S(2) + S(5)*m + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b*sqrt(x))**p), x), x, -a**S(8)*p*log(a + b*sqrt(x))/(S(4)*b**S(8)) + a**S(7)*p*sqrt(x)/(S(4)*b**S(7)) - a**S(6)*p*x/(S(8)*b**S(6)) + a**S(5)*p*x**(S(3)/2)/(S(12)*b**S(5)) - a**S(4)*p*x**S(2)/(S(16)*b**S(4)) + a**S(3)*p*x**(S(5)/2)/(S(20)*b**S(3)) - a**S(2)*p*x**S(3)/(S(24)*b**S(2)) + a*p*x**(S(7)/2)/(S(28)*b) - p*x**S(4)/S(32) + x**S(4)*log(c*(a + b*sqrt(x))**p)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*sqrt(x))**p), x), x, -a**S(6)*p*log(a + b*sqrt(x))/(S(3)*b**S(6)) + a**S(5)*p*sqrt(x)/(S(3)*b**S(5)) - a**S(4)*p*x/(S(6)*b**S(4)) + a**S(3)*p*x**(S(3)/2)/(S(9)*b**S(3)) - a**S(2)*p*x**S(2)/(S(12)*b**S(2)) + a*p*x**(S(5)/2)/(S(15)*b) - p*x**S(3)/S(18) + x**S(3)*log(c*(a + b*sqrt(x))**p)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b*sqrt(x))**p), x), x, -a**S(4)*p*log(a + b*sqrt(x))/(S(2)*b**S(4)) + a**S(3)*p*sqrt(x)/(S(2)*b**S(3)) - a**S(2)*p*x/(S(4)*b**S(2)) + a*p*x**(S(3)/2)/(S(6)*b) - p*x**S(2)/S(8) + x**S(2)*log(c*(a + b*sqrt(x))**p)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*sqrt(x))**p), x), x, -a**S(2)*p*log(a + b*sqrt(x))/b**S(2) + a*p*sqrt(x)/b - p*x/S(2) + x*log(c*(a + b*sqrt(x))**p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*sqrt(x))**p)/x, x), x, S(2)*p*polylog(S(2), (a + b*sqrt(x))/a) + S(2)*log(c*(a + b*sqrt(x))**p)*log(-b*sqrt(x)/a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*sqrt(x))**p)/x**S(2), x), x, -log(c*(a + b*sqrt(x))**p)/x - b*p/(a*sqrt(x)) - b**S(2)*p*log(x)/(S(2)*a**S(2)) + b**S(2)*p*log(a + b*sqrt(x))/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*sqrt(x))**p)/x**S(3), x), x, -log(c*(a + b*sqrt(x))**p)/(S(2)*x**S(2)) - b*p/(S(6)*a*x**(S(3)/2)) + b**S(2)*p/(S(4)*a**S(2)*x) - b**S(3)*p/(S(2)*a**S(3)*sqrt(x)) - b**S(4)*p*log(x)/(S(4)*a**S(4)) + b**S(4)*p*log(a + b*sqrt(x))/(S(2)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*sqrt(x))**p)/x**S(4), x), x, -log(c*(a + b*sqrt(x))**p)/(S(3)*x**S(3)) - b*p/(S(15)*a*x**(S(5)/2)) + b**S(2)*p/(S(12)*a**S(2)*x**S(2)) - b**S(3)*p/(S(9)*a**S(3)*x**(S(3)/2)) + b**S(4)*p/(S(6)*a**S(4)*x) - b**S(5)*p/(S(3)*a**S(5)*sqrt(x)) - b**S(6)*p*log(x)/(S(6)*a**S(6)) + b**S(6)*p*log(a + b*sqrt(x))/(S(3)*a**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*sqrt(x))/sqrt(x), x), x, -S(2)*sqrt(x) + S(2)*(a + b*sqrt(x))*log(a + b*sqrt(x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(c*(a + b/x)**p), x), x, p*x**(m + S(1))*hyper((S(1), m + S(1)), (m + S(2),), -a*x/b)/(m + S(1))**S(2) + x**(m + S(1))*log(c*(a + b/x)**p)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(c*(a + b/x)**p), x), x, x**S(5)*log(c*(a + b/x)**p)/S(5) + b*p*x**S(4)/(S(20)*a) - b**S(2)*p*x**S(3)/(S(15)*a**S(2)) + b**S(3)*p*x**S(2)/(S(10)*a**S(3)) - b**S(4)*p*x/(S(5)*a**S(4)) + b**S(5)*p*log(a*x + b)/(S(5)*a**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b/x)**p), x), x, x**S(4)*log(c*(a + b/x)**p)/S(4) + b*p*x**S(3)/(S(12)*a) - b**S(2)*p*x**S(2)/(S(8)*a**S(2)) + b**S(3)*p*x/(S(4)*a**S(3)) - b**S(4)*p*log(a*x + b)/(S(4)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b/x)**p), x), x, x**S(3)*log(c*(a + b/x)**p)/S(3) + b*p*x**S(2)/(S(6)*a) - b**S(2)*p*x/(S(3)*a**S(2)) + b**S(3)*p*log(a*x + b)/(S(3)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b/x)**p), x), x, x**S(2)*log(c*(a + b/x)**p)/S(2) + b*p*x/(S(2)*a) - b**S(2)*p*log(a*x + b)/(S(2)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p), x), x, x*log(c*(a + b/x)**p) + b*p*log(a*x + b)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p)/x, x), x, -p*polylog(S(2), (a + b/x)/a) - log(c*(a + b/x)**p)*log(-b/(a*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p)/x**S(2), x), x, p/x - (a + b/x)*log(c*(a + b/x)**p)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p)/x**S(3), x), x, -a**S(2)*p*log(x)/(S(2)*b**S(2)) + a**S(2)*p*log(a*x + b)/(S(2)*b**S(2)) - a*p/(S(2)*b*x) + p/(S(4)*x**S(2)) - log(c*(a + b/x)**p)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p)/x**S(4), x), x, a**S(3)*p*log(x)/(S(3)*b**S(3)) - a**S(3)*p*log(a*x + b)/(S(3)*b**S(3)) + a**S(2)*p/(S(3)*b**S(2)*x) - a*p/(S(6)*b*x**S(2)) + p/(S(9)*x**S(3)) - log(c*(a + b/x)**p)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p)/x**S(5), x), x, -a**S(4)*p*log(x)/(S(4)*b**S(4)) + a**S(4)*p*log(a*x + b)/(S(4)*b**S(4)) - a**S(3)*p/(S(4)*b**S(3)*x) + a**S(2)*p/(S(8)*b**S(2)*x**S(2)) - a*p/(S(12)*b*x**S(3)) + p/(S(16)*x**S(4)) - log(c*(a + b/x)**p)/(S(4)*x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(b/x + S(1))/x, x), x, polylog(S(2), -b/x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(c*(a + b/x**S(2))**p), x), x, S(2)*p*x**(m + S(1))*hyper((S(1), m/S(2) + S(1)/2), (m/S(2) + S(3)/2,), -a*x**S(2)/b)/(m + S(1))**S(2) + x**(m + S(1))*log(c*(a + b/x**S(2))**p)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(c*(a + b/x**S(2))**p), x), x, x**S(5)*log(c*(a + b/x**S(2))**p)/S(5) + S(2)*b*p*x**S(3)/(S(15)*a) - S(2)*b**S(2)*p*x/(S(5)*a**S(2)) + S(2)*b**(S(5)/2)*p*atan(sqrt(a)*x/sqrt(b))/(S(5)*a**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b/x**S(2))**p), x), x, x**S(4)*log(c*(a + b/x**S(2))**p)/S(4) + b*p*x**S(2)/(S(4)*a) - b**S(2)*p*log(a*x**S(2) + b)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b/x**S(2))**p), x), x, x**S(3)*log(c*(a + b/x**S(2))**p)/S(3) + S(2)*b*p*x/(S(3)*a) - S(2)*b**(S(3)/2)*p*atan(sqrt(a)*x/sqrt(b))/(S(3)*a**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b/x**S(2))**p), x), x, x**S(2)*log(c*(a + b/x**S(2))**p)/S(2) + b*p*log(a*x**S(2) + b)/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(2))**p), x), x, x*log(c*(a + b/x**S(2))**p) + S(2)*sqrt(b)*p*atan(sqrt(a)*x/sqrt(b))/sqrt(a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(2))**p)/x, x), x, -p*polylog(S(2), (a + b/x**S(2))/a)/S(2) - log(c*(a + b/x**S(2))**p)*log(-b/(a*x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(2))**p)/x**S(2), x), x, S(2)*sqrt(a)*p*atan(sqrt(a)*x/sqrt(b))/sqrt(b) + S(2)*p/x - log(c*(a + b/x**S(2))**p)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(2))**p)/x**S(3), x), x, p/(S(2)*x**S(2)) - (a/S(2) + b/(S(2)*x**S(2)))*log(c*(a + b/x**S(2))**p)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(2))**p)/x**S(4), x), x, -S(2)*a**(S(3)/2)*p*atan(sqrt(a)*x/sqrt(b))/(S(3)*b**(S(3)/2)) - S(2)*a*p/(S(3)*b*x) + S(2)*p/(S(9)*x**S(3)) - log(c*(a + b/x**S(2))**p)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(c*(a + b*x**n)**p), x), x, x**(m + S(1))*log(c*(a + b*x**n)**p)/(m + S(1)) - b*n*p*x**(m + n + S(1))*hyper((S(1), (m + n + S(1))/n), ((m + S(2)*n + S(1))/n,), -b*x**n/a)/(a*(m + S(1))*(m + n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x**n)**p), x), x, x**S(3)*log(c*(a + b*x**n)**p)/S(3) - b*n*p*x**(n + S(3))*hyper((S(1), (n + S(3))/n), (S(2) + S(3)/n,), -b*x**n/a)/(S(3)*a*(n + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b*x**n)**p), x), x, x**S(2)*log(c*(a + b*x**n)**p)/S(2) - b*n*p*x**(n + S(2))*hyper((S(1), (n + S(2))/n), (S(2) + S(2)/n,), -b*x**n/a)/(S(2)*a*(n + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**n)**p), x), x, x*log(c*(a + b*x**n)**p) - b*n*p*x**(n + S(1))*hyper((S(1), S(1) + S(1)/n), (S(2) + S(1)/n,), -b*x**n/a)/(a*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**n)**p)/x, x), x, p*polylog(S(2), (a + b*x**n)/a)/n + log(c*(a + b*x**n)**p)*log(-b*x**n/a)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**n)**p)/x**S(2), x), x, -log(c*(a + b*x**n)**p)/x - b*n*p*x**(n + S(-1))*hyper((S(1), (n + S(-1))/n), (S(2) - S(1)/n,), -b*x**n/a)/(a*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**n)**p)/x**S(3), x), x, -log(c*(a + b*x**n)**p)/(S(2)*x**S(2)) - b*n*p*x**(n + S(-2))*hyper((S(1), (n + S(-2))/n), (S(2) - S(2)/n,), -b*x**n/a)/(S(2)*a*(-n + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**n)**p)/x**S(4), x), x, -log(c*(a + b*x**n)**p)/(S(3)*x**S(3)) - b*n*p*x**(n + S(-3))*hyper((S(1), (n + S(-3))/n), (S(2) - S(3)/n,), -b*x**n/a)/(S(3)*a*(-n + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**m*log(c*(a + b*x)**p), x), x, b*p*(d + e*x)**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), b*(d + e*x)/(-a*e + b*d))/(e*(m + S(1))*(m + S(2))*(-a*e + b*d)) + (d + e*x)**(m + S(1))*log(c*(a + b*x)**p)/(e*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**S(3)*log(c*(a + b*x)**p), x), x, -p*(d + e*x)**S(4)/(S(16)*e) + (d + e*x)**S(4)*log(c*(a + b*x)**p)/(S(4)*e) - p*(d + e*x)**S(3)*(-a*e/S(12) + b*d/S(12))/(b*e) - p*(d + e*x)**S(2)*(-a*e + b*d)**S(2)/(S(8)*b**S(2)*e) - p*x*(-a*e + b*d)**S(3)/(S(4)*b**S(3)) - p*(-a*e + b*d)**S(4)*log(a + b*x)/(S(4)*b**S(4)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**S(2)*log(c*(a + b*x)**p), x), x, -p*(d + e*x)**S(3)/(S(9)*e) + (d + e*x)**S(3)*log(c*(a + b*x)**p)/(S(3)*e) - p*(d + e*x)**S(2)*(-a*e/S(6) + b*d/S(6))/(b*e) - p*x*(-a*e + b*d)**S(2)/(S(3)*b**S(2)) - p*(-a*e + b*d)**S(3)*log(a + b*x)/(S(3)*b**S(3)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)*log(c*(a + b*x)**p), x), x, -p*(d + e*x)**S(2)/(S(4)*e) + (d + e*x)**S(2)*log(c*(a + b*x)**p)/(S(2)*e) + p*x*(a*e/S(2) - b*d/S(2))/b - p*(-a*e + b*d)**S(2)*log(a + b*x)/(S(2)*b**S(2)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**p), x), x, -p*x + (a + b*x)*log(c*(a + b*x)**p)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**p)/(d + e*x), x), x, p*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/e + log(c*(a + b*x)**p)*log(b*(d + e*x)/(-a*e + b*d))/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**p)/(d + e*x)**S(2), x), x, b*p*log(a + b*x)/(e*(-a*e + b*d)) - b*p*log(d + e*x)/(e*(-a*e + b*d)) - log(c*(a + b*x)**p)/(e*(d + e*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**p)/(d + e*x)**S(3), x), x, b**S(2)*p*log(a + b*x)/(S(2)*e*(-a*e + b*d)**S(2)) - b**S(2)*p*log(d + e*x)/(S(2)*e*(-a*e + b*d)**S(2)) + b*p/(S(2)*e*(d + e*x)*(-a*e + b*d)) - log(c*(a + b*x)**p)/(S(2)*e*(d + e*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**p)/(d + e*x)**S(4), x), x, b**S(3)*p*log(a + b*x)/(S(3)*e*(-a*e + b*d)**S(3)) - b**S(3)*p*log(d + e*x)/(S(3)*e*(-a*e + b*d)**S(3)) + b**S(2)*p/(S(3)*e*(d + e*x)*(-a*e + b*d)**S(2)) + b*p/(S(6)*e*(d + e*x)**S(2)*(-a*e + b*d)) - log(c*(a + b*x)**p)/(S(3)*e*(d + e*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**m*log(c*(a + b*x**S(2))**p), x), x, sqrt(b)*p*(d + e*x)**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), sqrt(b)*(d + e*x)/(sqrt(b)*d + e*sqrt(-a)))/(e*(m + S(1))*(m + S(2))*(sqrt(b)*d + e*sqrt(-a))) + sqrt(b)*p*(d + e*x)**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), sqrt(b)*(d + e*x)/(sqrt(b)*d - e*sqrt(-a)))/(e*(m + S(1))*(m + S(2))*(sqrt(b)*d - e*sqrt(-a))) + (d + e*x)**(m + S(1))*log(c*(a + b*x**S(2))**p)/(e*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**S(3)*log(c*(a + b*x**S(2))**p), x), x, S(2)*sqrt(a)*d*p*(-a*e**S(2) + b*d**S(2))*atan(sqrt(b)*x/sqrt(a))/b**(S(3)/2) - S(2)*d*e**S(2)*p*x**S(3)/S(3) - e**S(3)*p*x**S(4)/S(8) + (d + e*x)**S(4)*log(c*(a + b*x**S(2))**p)/(S(4)*e) - S(2)*d*p*x*(-a*e**S(2) + b*d**S(2))/b - e*p*x**S(2)*(-a*e**S(2) + S(6)*b*d**S(2))/(S(4)*b) - p*(a**S(2)*e**S(4)/S(4) - S(3)*a*b*d**S(2)*e**S(2)/S(2) + b**S(2)*d**S(4)/S(4))*log(a + b*x**S(2))/(b**S(2)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**S(2)*log(c*(a + b*x**S(2))**p), x), x, sqrt(a)*p*(-S(2)*a*e**S(2)/S(3) + S(2)*b*d**S(2))*atan(sqrt(b)*x/sqrt(a))/b**(S(3)/2) - d*e*p*x**S(2) - S(2)*e**S(2)*p*x**S(3)/S(9) + (d + e*x)**S(3)*log(c*(a + b*x**S(2))**p)/(S(3)*e) - d*p*(-S(3)*a*e**S(2) + b*d**S(2))*log(a + b*x**S(2))/(S(3)*b*e) + p*x*(S(2)*a*e**S(2)/S(3) - S(2)*b*d**S(2))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)*log(c*(a + b*x**S(2))**p), x), x, S(2)*sqrt(a)*d*p*atan(sqrt(b)*x/sqrt(a))/sqrt(b) - S(2)*d*p*x - e*p*x**S(2)/S(2) + (d + e*x)**S(2)*log(c*(a + b*x**S(2))**p)/(S(2)*e) - p*(-a*e**S(2)/S(2) + b*d**S(2)/S(2))*log(a + b*x**S(2))/(b*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p), x), x, S(2)*sqrt(a)*p*atan(sqrt(b)*x/sqrt(a))/sqrt(b) - S(2)*p*x + x*log(c*(a + b*x**S(2))**p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/(d + e*x), x), x, -p*log(-e*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*d - e*sqrt(-a)))*log(d + e*x)/e - p*log(e*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*d + e*sqrt(-a)))*log(d + e*x)/e - p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d - e*sqrt(-a)))/e - p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d + e*sqrt(-a)))/e + log(c*(a + b*x**S(2))**p)*log(d + e*x)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/(d + e*x)**S(2), x), x, S(2)*sqrt(a)*sqrt(b)*p*atan(sqrt(b)*x/sqrt(a))/(a*e**S(2) + b*d**S(2)) + b*d*p*log(a + b*x**S(2))/(e*(a*e**S(2) + b*d**S(2))) - S(2)*b*d*p*log(d + e*x)/(e*(a*e**S(2) + b*d**S(2))) - log(c*(a + b*x**S(2))**p)/(e*(d + e*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/(d + e*x)**S(3), x), x, S(2)*sqrt(a)*b**(S(3)/2)*d*p*atan(sqrt(b)*x/sqrt(a))/(a*e**S(2) + b*d**S(2))**S(2) + b*d*p/(e*(d + e*x)*(a*e**S(2) + b*d**S(2))) + b*p*(-a*e**S(2) + b*d**S(2))*log(a + b*x**S(2))/(S(2)*e*(a*e**S(2) + b*d**S(2))**S(2)) - b*p*(-a*e**S(2) + b*d**S(2))*log(d + e*x)/(e*(a*e**S(2) + b*d**S(2))**S(2)) - log(c*(a + b*x**S(2))**p)/(S(2)*e*(d + e*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**m*log(c*(a + b*x**S(3))**p), x), x, b**(S(1)/3)*p*(d + e*x)**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), b**(S(1)/3)*(d + e*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/(e*(m + S(1))*(m + S(2))*(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d)) + b**(S(1)/3)*p*(d + e*x)**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), b**(S(1)/3)*(d + e*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/(e*(m + S(1))*(m + S(2))*((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d)) + b**(S(1)/3)*p*(d + e*x)**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), b**(S(1)/3)*(d + e*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))/(e*(m + S(1))*(m + S(2))*(-a**(S(1)/3)*e + b**(S(1)/3)*d)) + (d + e*x)**(m + S(1))*log(c*(a + b*x**S(3))**p)/(e*(m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**S(3)*log(c*(a + b*x**S(3))**p), x), x, a**(S(1)/3)*p*(-S(6)*a**(S(1)/3)*b**(S(2)/3)*d**S(2)*e - a*e**S(3) + S(4)*b*d**S(3))*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(4)*b**(S(4)/3)) - a**(S(1)/3)*p*(-S(6)*a**(S(1)/3)*b**(S(2)/3)*d**S(2)*e - a*e**S(3) + S(4)*b*d**S(3))*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(8)*b**(S(4)/3)) - sqrt(S(3))*a**(S(1)/3)*p*(S(6)*a**(S(1)/3)*b**(S(2)/3)*d**S(2)*e - a*e**S(3) + S(4)*b*d**S(3))*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(4)*b**(S(4)/3)) - S(9)*d**S(2)*e*p*x**S(2)/S(4) - d*e**S(2)*p*x**S(3) - S(3)*e**S(3)*p*x**S(4)/S(16) + (d + e*x)**S(4)*log(c*(a + b*x**S(3))**p)/(S(4)*e) - d*p*(-S(4)*a*e**S(3) + b*d**S(3))*log(a + b*x**S(3))/(S(4)*b*e) + p*x*(S(3)*a*e**S(3)/S(4) - S(3)*b*d**S(3))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**S(2)*log(c*(a + b*x**S(3))**p), x), x, a**(S(1)/3)*d*p*(-a**(S(1)/3)*e + b**(S(1)/3)*d)*log(a**(S(1)/3) + b**(S(1)/3)*x)/b**(S(2)/3) - a**(S(1)/3)*d*p*(-a**(S(1)/3)*e + b**(S(1)/3)*d)*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*b**(S(2)/3)) - sqrt(S(3))*a**(S(1)/3)*d*p*(a**(S(1)/3)*e + b**(S(1)/3)*d)*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/b**(S(2)/3) - S(3)*d**S(2)*p*x - S(3)*d*e*p*x**S(2)/S(2) - e**S(2)*p*x**S(3)/S(3) + (d + e*x)**S(3)*log(c*(a + b*x**S(3))**p)/(S(3)*e) - p*(-a*e**S(3)/S(3) + b*d**S(3)/S(3))*log(a + b*x**S(3))/(b*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)*log(c*(a + b*x**S(3))**p), x), x, a**(S(1)/3)*p*(-a**(S(1)/3)*e + S(2)*b**(S(1)/3)*d)*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(2)*b**(S(2)/3)) - a**(S(1)/3)*p*(-a**(S(1)/3)*e + S(2)*b**(S(1)/3)*d)*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(4)*b**(S(2)/3)) - sqrt(S(3))*a**(S(1)/3)*p*(a**(S(1)/3)*e + S(2)*b**(S(1)/3)*d)*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(2)*b**(S(2)/3)) - d**S(2)*p*log(a + b*x**S(3))/(S(2)*e) - S(3)*d*p*x - S(3)*e*p*x**S(2)/S(4) + (d + e*x)**S(2)*log(c*(a + b*x**S(3))**p)/(S(2)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p), x), x, a**(S(1)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/b**(S(1)/3) - a**(S(1)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*b**(S(1)/3)) - sqrt(S(3))*a**(S(1)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/b**(S(1)/3) - S(3)*p*x + x*log(c*(a + b*x**S(3))**p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/(d + e*x), x), x, -p*log(-e*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e - p*log(-e*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e - p*log((S(-1))**(S(1)/3)*e*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e - p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))/e - p*polylog(S(2), b**(S(1)/3)*(d + e*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e - p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e + log(c*(a + b*x**S(3))**p)*log(d + e*x)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/(d + e*x)**S(2), x), x, a**(S(1)/3)*b**(S(1)/3)*p*(a**(S(1)/3)*e + b**(S(1)/3)*d)*log(a**(S(1)/3) + b**(S(1)/3)*x)/(-a*e**S(3) + b*d**S(3)) - a**(S(1)/3)*b**(S(1)/3)*p*(a**(S(1)/3)*e + b**(S(1)/3)*d)*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*(-a*e**S(3) + b*d**S(3))) - sqrt(S(3))*a**(S(1)/3)*b**(S(1)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(a**(S(2)/3)*e**S(2) + a**(S(1)/3)*b**(S(1)/3)*d*e + b**(S(2)/3)*d**S(2)) + b*d**S(2)*p*log(a + b*x**S(3))/(e*(-a*e**S(3) + b*d**S(3))) - S(3)*b*d**S(2)*p*log(d + e*x)/(e*(-a*e**S(3) + b*d**S(3))) - log(c*(a + b*x**S(3))**p)/(e*(d + e*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/(d + e*x)**S(3), x), x, -sqrt(S(3))*a**(S(1)/3)*b**(S(2)/3)*p*(-S(3)*a**(S(1)/3)*b**(S(2)/3)*d**S(2)*e + a*e**S(3) + S(2)*b*d**S(3))*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(2)*(-a*e**S(3) + b*d**S(3))**S(2)) + a**(S(1)/3)*b**(S(2)/3)*p*(S(3)*a**(S(1)/3)*b**(S(2)/3)*d**S(2)*e + a*e**S(3) + S(2)*b*d**S(3))*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(2)*(-a*e**S(3) + b*d**S(3))**S(2)) - a**(S(1)/3)*b**(S(2)/3)*p*(S(3)*a**(S(1)/3)*b**(S(2)/3)*d**S(2)*e + a*e**S(3) + S(2)*b*d**S(3))*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(4)*(-a*e**S(3) + b*d**S(3))**S(2)) + S(3)*b*d**S(2)*p/(S(2)*e*(d + e*x)*(-a*e**S(3) + b*d**S(3))) + b*d*p*(S(2)*a*e**S(3) + b*d**S(3))*log(a + b*x**S(3))/(S(2)*e*(-a*e**S(3) + b*d**S(3))**S(2)) - S(3)*b*d*p*(S(2)*a*e**S(3) + b*d**S(3))*log(d + e*x)/(S(2)*e*(-a*e**S(3) + b*d**S(3))**S(2)) - log(c*(a + b*x**S(3))**p)/(S(2)*e*(d + e*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b/x)/(c + d*x), x), x, log(-d*x/c)*log(c + d*x)/d - log(-d*(a*x + b)/(a*c - b*d))*log(c + d*x)/d + log(a + b/x)*log(c + d*x)/d + polylog(S(2), (c + d*x)/c)/d - polylog(S(2), a*(c + d*x)/(a*c - b*d))/d, expand=True, _diff=True, _numerical=True)

    # recursion sympy and mathematica assert rubi_test(rubi_integrate(log(a + b*x**n)/(c + d*x), x), x, -b*n*Integral(x**(n + S(-1))*log(c + d*x)/(a + b*x**n), x)/d + log(a + b*x**n)*log(c + d*x)/d, expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(a*x)/(c + d*x), x), x, log(a*x)*log((c + d*x)/c)/d + polylog(S(2), -d*x/c)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a/x)/(c + d*x), x), x, log(a/x)*log((c + d*x)/c)/d - polylog(S(2), -d*x/c)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*x**n)/(c + d*x), x), x, n*polylog(S(2), -d*x/c)/d + log(a*x**n)*log((c + d*x)/c)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**n)/(a + b*x), x), x, n*polylog(S(2), -b*x/a)/b + log(x**n)*log((a + b*x)/a)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b*x)**p)/(d + e*x), x), x, a**S(3)*p*log(a + b*x)/(S(3)*b**S(3)*e) + a**S(2)*d*p*log(a + b*x)/(S(2)*b**S(2)*e**S(2)) - a**S(2)*p*x/(S(3)*b**S(2)*e) - a*d*p*x/(S(2)*b*e**S(2)) + a*p*x**S(2)/(S(6)*b*e) - d**S(3)*p*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/e**S(4) - d**S(3)*log(c*(a + b*x)**p)*log(b*(d + e*x)/(-a*e + b*d))/e**S(4) - d**S(2)*p*x/e**S(3) + d*p*x**S(2)/(S(4)*e**S(2)) - d*x**S(2)*log(c*(a + b*x)**p)/(S(2)*e**S(2)) - p*x**S(3)/(S(9)*e) + x**S(3)*log(c*(a + b*x)**p)/(S(3)*e) + d**S(2)*(a + b*x)*log(c*(a + b*x)**p)/(b*e**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x)**p)/(d + e*x), x), x, -a**S(2)*p*log(a + b*x)/(S(2)*b**S(2)*e) + a*p*x/(S(2)*b*e) + d**S(2)*p*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/e**S(3) + d**S(2)*log(c*(a + b*x)**p)*log(b*(d + e*x)/(-a*e + b*d))/e**S(3) + d*p*x/e**S(2) - p*x**S(2)/(S(4)*e) + x**S(2)*log(c*(a + b*x)**p)/(S(2)*e) - d*(a + b*x)*log(c*(a + b*x)**p)/(b*e**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b*x)**p)/(d + e*x), x), x, -d*p*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/e**S(2) - d*log(c*(a + b*x)**p)*log(b*(d + e*x)/(-a*e + b*d))/e**S(2) - p*x/e + (a + b*x)*log(c*(a + b*x)**p)/(b*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**p)/(d + e*x), x), x, p*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/e + log(c*(a + b*x)**p)*log(b*(d + e*x)/(-a*e + b*d))/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**p)/(x*(d + e*x)), x), x, p*polylog(S(2), (a + b*x)/a)/d - p*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/d + log(c*(a + b*x)**p)*log(-b*x/a)/d - log(c*(a + b*x)**p)*log(b*(d + e*x)/(-a*e + b*d))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**p)/(x**S(2)*(d + e*x)), x), x, -log(c*(a + b*x)**p)/(d*x) - e*p*polylog(S(2), (a + b*x)/a)/d**S(2) + e*p*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/d**S(2) - e*log(c*(a + b*x)**p)*log(-b*x/a)/d**S(2) + e*log(c*(a + b*x)**p)*log(b*(d + e*x)/(-a*e + b*d))/d**S(2) + b*p*log(x)/(a*d) - b*p*log(a + b*x)/(a*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x)**p)/(x**S(3)*(d + e*x)), x), x, -log(c*(a + b*x)**p)/(S(2)*d*x**S(2)) + e*log(c*(a + b*x)**p)/(d**S(2)*x) + e**S(2)*p*polylog(S(2), (a + b*x)/a)/d**S(3) - e**S(2)*p*polylog(S(2), -e*(a + b*x)/(-a*e + b*d))/d**S(3) + e**S(2)*log(c*(a + b*x)**p)*log(-b*x/a)/d**S(3) - e**S(2)*log(c*(a + b*x)**p)*log(b*(d + e*x)/(-a*e + b*d))/d**S(3) - b*p/(S(2)*a*d*x) - b*e*p*log(x)/(a*d**S(2)) + b*e*p*log(a + b*x)/(a*d**S(2)) - b**S(2)*p*log(x)/(S(2)*a**S(2)*d) + b**S(2)*p*log(a + b*x)/(S(2)*a**S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b*x**S(2))**p)/(d + e*x), x), x, -S(2)*a**(S(3)/2)*p*atan(sqrt(b)*x/sqrt(a))/(S(3)*b**(S(3)/2)*e) + S(2)*sqrt(a)*d**S(2)*p*atan(sqrt(b)*x/sqrt(a))/(sqrt(b)*e**S(3)) + S(2)*a*p*x/(S(3)*b*e) + d**S(3)*p*log(-e*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*d - e*sqrt(-a)))*log(d + e*x)/e**S(4) + d**S(3)*p*log(e*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*d + e*sqrt(-a)))*log(d + e*x)/e**S(4) + d**S(3)*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d - e*sqrt(-a)))/e**S(4) + d**S(3)*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d + e*sqrt(-a)))/e**S(4) - d**S(3)*log(c*(a + b*x**S(2))**p)*log(d + e*x)/e**S(4) - S(2)*d**S(2)*p*x/e**S(3) + d**S(2)*x*log(c*(a + b*x**S(2))**p)/e**S(3) + d*p*x**S(2)/(S(2)*e**S(2)) - S(2)*p*x**S(3)/(S(9)*e) + x**S(3)*log(c*(a + b*x**S(2))**p)/(S(3)*e) - d*(a + b*x**S(2))*log(c*(a + b*x**S(2))**p)/(S(2)*b*e**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x**S(2))**p)/(d + e*x), x), x, -S(2)*sqrt(a)*d*p*atan(sqrt(b)*x/sqrt(a))/(sqrt(b)*e**S(2)) - d**S(2)*p*log(-e*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*d - e*sqrt(-a)))*log(d + e*x)/e**S(3) - d**S(2)*p*log(e*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*d + e*sqrt(-a)))*log(d + e*x)/e**S(3) - d**S(2)*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d - e*sqrt(-a)))/e**S(3) - d**S(2)*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d + e*sqrt(-a)))/e**S(3) + d**S(2)*log(c*(a + b*x**S(2))**p)*log(d + e*x)/e**S(3) + S(2)*d*p*x/e**S(2) - d*x*log(c*(a + b*x**S(2))**p)/e**S(2) - p*x**S(2)/(S(2)*e) + (a/S(2) + b*x**S(2)/S(2))*log(c*(a + b*x**S(2))**p)/(b*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b*x**S(2))**p)/(d + e*x), x), x, S(2)*sqrt(a)*p*atan(sqrt(b)*x/sqrt(a))/(sqrt(b)*e) + d*p*log(-e*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*d - e*sqrt(-a)))*log(d + e*x)/e**S(2) + d*p*log(e*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*d + e*sqrt(-a)))*log(d + e*x)/e**S(2) + d*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d - e*sqrt(-a)))/e**S(2) + d*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d + e*sqrt(-a)))/e**S(2) - d*log(c*(a + b*x**S(2))**p)*log(d + e*x)/e**S(2) - S(2)*p*x/e + x*log(c*(a + b*x**S(2))**p)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/(d + e*x), x), x, -p*log(-e*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*d - e*sqrt(-a)))*log(d + e*x)/e - p*log(e*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*d + e*sqrt(-a)))*log(d + e*x)/e - p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d - e*sqrt(-a)))/e - p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d + e*sqrt(-a)))/e + log(c*(a + b*x**S(2))**p)*log(d + e*x)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/(x*(d + e*x)), x), x, p*log(-e*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*d - e*sqrt(-a)))*log(d + e*x)/d + p*log(e*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*d + e*sqrt(-a)))*log(d + e*x)/d + p*polylog(S(2), (a + b*x**S(2))/a)/(S(2)*d) + p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d - e*sqrt(-a)))/d + p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d + e*sqrt(-a)))/d + log(c*(a + b*x**S(2))**p)*log(-b*x**S(2)/a)/(S(2)*d) - log(c*(a + b*x**S(2))**p)*log(d + e*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/(x**S(2)*(d + e*x)), x), x, -log(c*(a + b*x**S(2))**p)/(d*x) - e*p*log(-e*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*d - e*sqrt(-a)))*log(d + e*x)/d**S(2) - e*p*log(e*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*d + e*sqrt(-a)))*log(d + e*x)/d**S(2) - e*p*polylog(S(2), (a + b*x**S(2))/a)/(S(2)*d**S(2)) - e*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d - e*sqrt(-a)))/d**S(2) - e*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d + e*sqrt(-a)))/d**S(2) - e*log(c*(a + b*x**S(2))**p)*log(-b*x**S(2)/a)/(S(2)*d**S(2)) + e*log(c*(a + b*x**S(2))**p)*log(d + e*x)/d**S(2) + S(2)*sqrt(b)*p*atan(sqrt(b)*x/sqrt(a))/(sqrt(a)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**p)/(x**S(3)*(d + e*x)), x), x, -log(c*(a + b*x**S(2))**p)/(S(2)*d*x**S(2)) + e*log(c*(a + b*x**S(2))**p)/(d**S(2)*x) + e**S(2)*p*log(-e*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*d - e*sqrt(-a)))*log(d + e*x)/d**S(3) + e**S(2)*p*log(e*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*d + e*sqrt(-a)))*log(d + e*x)/d**S(3) + e**S(2)*p*polylog(S(2), (a + b*x**S(2))/a)/(S(2)*d**S(3)) + e**S(2)*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d - e*sqrt(-a)))/d**S(3) + e**S(2)*p*polylog(S(2), sqrt(b)*(d + e*x)/(sqrt(b)*d + e*sqrt(-a)))/d**S(3) + e**S(2)*log(c*(a + b*x**S(2))**p)*log(-b*x**S(2)/a)/(S(2)*d**S(3)) - e**S(2)*log(c*(a + b*x**S(2))**p)*log(d + e*x)/d**S(3) + b*p*log(x)/(a*d) - b*p*log(a + b*x**S(2))/(S(2)*a*d) - S(2)*sqrt(b)*e*p*atan(sqrt(b)*x/sqrt(a))/(sqrt(a)*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b*x**S(3))**p)/(d + e*x), x), x, a**(S(2)/3)*d*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(2)*b**(S(2)/3)*e**S(2)) - a**(S(2)/3)*d*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(4)*b**(S(2)/3)*e**S(2)) + sqrt(S(3))*a**(S(2)/3)*d*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(2)*b**(S(2)/3)*e**S(2)) + a**(S(1)/3)*d**S(2)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(b**(S(1)/3)*e**S(3)) - a**(S(1)/3)*d**S(2)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*b**(S(1)/3)*e**S(3)) - sqrt(S(3))*a**(S(1)/3)*d**S(2)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(b**(S(1)/3)*e**S(3)) + d**S(3)*p*log(-e*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e**S(4) + d**S(3)*p*log(-e*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e**S(4) + d**S(3)*p*log((S(-1))**(S(1)/3)*e*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e**S(4) + d**S(3)*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))/e**S(4) + d**S(3)*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e**S(4) + d**S(3)*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e**S(4) - d**S(3)*log(c*(a + b*x**S(3))**p)*log(d + e*x)/e**S(4) - S(3)*d**S(2)*p*x/e**S(3) + d**S(2)*x*log(c*(a + b*x**S(3))**p)/e**S(3) + S(3)*d*p*x**S(2)/(S(4)*e**S(2)) - d*x**S(2)*log(c*(a + b*x**S(3))**p)/(S(2)*e**S(2)) - p*x**S(3)/(S(3)*e) + (a/S(3) + b*x**S(3)/S(3))*log(c*(a + b*x**S(3))**p)/(b*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x**S(3))**p)/(d + e*x), x), x, -a**(S(2)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(2)*b**(S(2)/3)*e) + a**(S(2)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(4)*b**(S(2)/3)*e) - sqrt(S(3))*a**(S(2)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(2)*b**(S(2)/3)*e) - a**(S(1)/3)*d*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(b**(S(1)/3)*e**S(2)) + a**(S(1)/3)*d*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*b**(S(1)/3)*e**S(2)) + sqrt(S(3))*a**(S(1)/3)*d*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(b**(S(1)/3)*e**S(2)) - d**S(2)*p*log(-e*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e**S(3) - d**S(2)*p*log(-e*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e**S(3) - d**S(2)*p*log((S(-1))**(S(1)/3)*e*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e**S(3) - d**S(2)*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))/e**S(3) - d**S(2)*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e**S(3) - d**S(2)*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e**S(3) + d**S(2)*log(c*(a + b*x**S(3))**p)*log(d + e*x)/e**S(3) + S(3)*d*p*x/e**S(2) - d*x*log(c*(a + b*x**S(3))**p)/e**S(2) - S(3)*p*x**S(2)/(S(4)*e) + x**S(2)*log(c*(a + b*x**S(3))**p)/(S(2)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b*x**S(3))**p)/(d + e*x), x), x, a**(S(1)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(b**(S(1)/3)*e) - a**(S(1)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*b**(S(1)/3)*e) - sqrt(S(3))*a**(S(1)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(b**(S(1)/3)*e) + d*p*log(-e*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e**S(2) + d*p*log(-e*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e**S(2) + d*p*log((S(-1))**(S(1)/3)*e*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e**S(2) + d*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))/e**S(2) + d*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e**S(2) + d*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e**S(2) - d*log(c*(a + b*x**S(3))**p)*log(d + e*x)/e**S(2) - S(3)*p*x/e + x*log(c*(a + b*x**S(3))**p)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/(d + e*x), x), x, -p*log(-e*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e - p*log(-e*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e - p*log((S(-1))**(S(1)/3)*e*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/e - p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))/e - p*polylog(S(2), b**(S(1)/3)*(d + e*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e - p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/e + log(c*(a + b*x**S(3))**p)*log(d + e*x)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/(x*(d + e*x)), x), x, p*log(-e*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/d + p*log(-e*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/d + p*log((S(-1))**(S(1)/3)*e*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/d + p*polylog(S(2), (a + b*x**S(3))/a)/(S(3)*d) + p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))/d + p*polylog(S(2), b**(S(1)/3)*(d + e*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/d + p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/d + log(c*(a + b*x**S(3))**p)*log(-b*x**S(3)/a)/(S(3)*d) - log(c*(a + b*x**S(3))**p)*log(d + e*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/(x**S(2)*(d + e*x)), x), x, -log(c*(a + b*x**S(3))**p)/(d*x) - e*p*log(-e*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/d**S(2) - e*p*log(-e*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/d**S(2) - e*p*log((S(-1))**(S(1)/3)*e*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/d**S(2) - e*p*polylog(S(2), (a + b*x**S(3))/a)/(S(3)*d**S(2)) - e*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))/d**S(2) - e*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/d**S(2) - e*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/d**S(2) - e*log(c*(a + b*x**S(3))**p)*log(-b*x**S(3)/a)/(S(3)*d**S(2)) + e*log(c*(a + b*x**S(3))**p)*log(d + e*x)/d**S(2) - b**(S(1)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(a**(S(1)/3)*d) + b**(S(1)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*a**(S(1)/3)*d) - sqrt(S(3))*b**(S(1)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(a**(S(1)/3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(3))**p)/(x**S(3)*(d + e*x)), x), x, -log(c*(a + b*x**S(3))**p)/(S(2)*d*x**S(2)) + e*log(c*(a + b*x**S(3))**p)/(d**S(2)*x) + e**S(2)*p*log(-e*(a**(S(1)/3) + b**(S(1)/3)*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/d**S(3) + e**S(2)*p*log(-e*((S(-1))**(S(2)/3)*a**(S(1)/3) + b**(S(1)/3)*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/d**S(3) + e**S(2)*p*log((S(-1))**(S(1)/3)*e*(a**(S(1)/3) + (S(-1))**(S(2)/3)*b**(S(1)/3)*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))*log(d + e*x)/d**S(3) + e**S(2)*p*polylog(S(2), (a + b*x**S(3))/a)/(S(3)*d**S(3)) + e**S(2)*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-a**(S(1)/3)*e + b**(S(1)/3)*d))/d**S(3) + e**S(2)*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/((S(-1))**(S(1)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/d**S(3) + e**S(2)*p*polylog(S(2), b**(S(1)/3)*(d + e*x)/(-(S(-1))**(S(2)/3)*a**(S(1)/3)*e + b**(S(1)/3)*d))/d**S(3) + e**S(2)*log(c*(a + b*x**S(3))**p)*log(-b*x**S(3)/a)/(S(3)*d**S(3)) - e**S(2)*log(c*(a + b*x**S(3))**p)*log(d + e*x)/d**S(3) + b**(S(1)/3)*e*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(a**(S(1)/3)*d**S(2)) - b**(S(1)/3)*e*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(2)*a**(S(1)/3)*d**S(2)) + sqrt(S(3))*b**(S(1)/3)*e*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(a**(S(1)/3)*d**S(2)) + b**(S(2)/3)*p*log(a**(S(1)/3) + b**(S(1)/3)*x)/(S(2)*a**(S(2)/3)*d) - b**(S(2)/3)*p*log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3)*x**S(2))/(S(4)*a**(S(2)/3)*d) - sqrt(S(3))*b**(S(2)/3)*p*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*x)/(S(3)*a**(S(1)/3)))/(S(2)*a**(S(2)/3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b/x)**p)/(d + e*x), x), x, -d**S(3)*p*log(-e*x/d)*log(d + e*x)/e**S(4) + d**S(3)*p*log(-e*(a*x + b)/(a*d - b*e))*log(d + e*x)/e**S(4) - d**S(3)*p*polylog(S(2), (d + e*x)/d)/e**S(4) + d**S(3)*p*polylog(S(2), a*(d + e*x)/(a*d - b*e))/e**S(4) - d**S(3)*log(c*(a + b/x)**p)*log(d + e*x)/e**S(4) + d**S(2)*x*log(c*(a + b/x)**p)/e**S(3) - d*x**S(2)*log(c*(a + b/x)**p)/(S(2)*e**S(2)) + x**S(3)*log(c*(a + b/x)**p)/(S(3)*e) + b*d**S(2)*p*log(a*x + b)/(a*e**S(3)) - b*d*p*x/(S(2)*a*e**S(2)) + b*p*x**S(2)/(S(6)*a*e) + b**S(2)*d*p*log(a*x + b)/(S(2)*a**S(2)*e**S(2)) - b**S(2)*p*x/(S(3)*a**S(2)*e) + b**S(3)*p*log(a*x + b)/(S(3)*a**S(3)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b/x)**p)/(d + e*x), x), x, d**S(2)*p*log(-e*x/d)*log(d + e*x)/e**S(3) - d**S(2)*p*log(-e*(a*x + b)/(a*d - b*e))*log(d + e*x)/e**S(3) + d**S(2)*p*polylog(S(2), (d + e*x)/d)/e**S(3) - d**S(2)*p*polylog(S(2), a*(d + e*x)/(a*d - b*e))/e**S(3) + d**S(2)*log(c*(a + b/x)**p)*log(d + e*x)/e**S(3) - d*x*log(c*(a + b/x)**p)/e**S(2) + x**S(2)*log(c*(a + b/x)**p)/(S(2)*e) - b*d*p*log(a*x + b)/(a*e**S(2)) + b*p*x/(S(2)*a*e) - b**S(2)*p*log(a*x + b)/(S(2)*a**S(2)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b/x)**p)/(d + e*x), x), x, -d*p*log(-e*x/d)*log(d + e*x)/e**S(2) + d*p*log(-e*(a*x + b)/(a*d - b*e))*log(d + e*x)/e**S(2) - d*p*polylog(S(2), (d + e*x)/d)/e**S(2) + d*p*polylog(S(2), a*(d + e*x)/(a*d - b*e))/e**S(2) - d*log(c*(a + b/x)**p)*log(d + e*x)/e**S(2) + x*log(c*(a + b/x)**p)/e + b*p*log(a*x + b)/(a*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p)/(d + e*x), x), x, p*log(-e*x/d)*log(d + e*x)/e - p*log(-e*(a*x + b)/(a*d - b*e))*log(d + e*x)/e + p*polylog(S(2), (d + e*x)/d)/e - p*polylog(S(2), a*(d + e*x)/(a*d - b*e))/e + log(c*(a + b/x)**p)*log(d + e*x)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p)/(x*(d + e*x)), x), x, -p*log(-e*x/d)*log(d + e*x)/d + p*log(-e*(a*x + b)/(a*d - b*e))*log(d + e*x)/d - p*polylog(S(2), (a + b/x)/a)/d - p*polylog(S(2), (d + e*x)/d)/d + p*polylog(S(2), a*(d + e*x)/(a*d - b*e))/d - log(c*(a + b/x)**p)*log(-b/(a*x))/d - log(c*(a + b/x)**p)*log(d + e*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p)/(x**S(2)*(d + e*x)), x), x, p/(d*x) + e*p*log(-e*x/d)*log(d + e*x)/d**S(2) - e*p*log(-e*(a*x + b)/(a*d - b*e))*log(d + e*x)/d**S(2) + e*p*polylog(S(2), (a + b/x)/a)/d**S(2) + e*p*polylog(S(2), (d + e*x)/d)/d**S(2) - e*p*polylog(S(2), a*(d + e*x)/(a*d - b*e))/d**S(2) + e*log(c*(a + b/x)**p)*log(-b/(a*x))/d**S(2) + e*log(c*(a + b/x)**p)*log(d + e*x)/d**S(2) - (a + b/x)*log(c*(a + b/x)**p)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x)**p)/(x**S(3)*(d + e*x)), x), x, -a**S(2)*p*log(x)/(S(2)*b**S(2)*d) + a**S(2)*p*log(a*x + b)/(S(2)*b**S(2)*d) - a*p/(S(2)*b*d*x) + p/(S(4)*d*x**S(2)) - log(c*(a + b/x)**p)/(S(2)*d*x**S(2)) - e*p/(d**S(2)*x) - e**S(2)*p*log(-e*x/d)*log(d + e*x)/d**S(3) + e**S(2)*p*log(-e*(a*x + b)/(a*d - b*e))*log(d + e*x)/d**S(3) - e**S(2)*p*polylog(S(2), (a + b/x)/a)/d**S(3) - e**S(2)*p*polylog(S(2), (d + e*x)/d)/d**S(3) + e**S(2)*p*polylog(S(2), a*(d + e*x)/(a*d - b*e))/d**S(3) - e**S(2)*log(c*(a + b/x)**p)*log(-b/(a*x))/d**S(3) - e**S(2)*log(c*(a + b/x)**p)*log(d + e*x)/d**S(3) + e*(a + b/x)*log(c*(a + b/x)**p)/(b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b/x**S(2))**p)/(d + e*x), x), x, -S(2)*d**S(3)*p*log(-e*x/d)*log(d + e*x)/e**S(4) + d**S(3)*p*log(e*(sqrt(b) - x*sqrt(-a))/(sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/e**S(4) + d**S(3)*p*log(-e*(sqrt(b) + x*sqrt(-a))/(-sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/e**S(4) - S(2)*d**S(3)*p*polylog(S(2), (d + e*x)/d)/e**S(4) + d**S(3)*p*polylog(S(2), sqrt(-a)*(d + e*x)/(-sqrt(b)*e + d*sqrt(-a)))/e**S(4) + d**S(3)*p*polylog(S(2), sqrt(-a)*(d + e*x)/(sqrt(b)*e + d*sqrt(-a)))/e**S(4) - d**S(3)*log(c*(a + b/x**S(2))**p)*log(d + e*x)/e**S(4) + d**S(2)*x*log(c*(a + b/x**S(2))**p)/e**S(3) - d*x**S(2)*log(c*(a + b/x**S(2))**p)/(S(2)*e**S(2)) + x**S(3)*log(c*(a + b/x**S(2))**p)/(S(3)*e) - b*d*p*log(a*x**S(2) + b)/(S(2)*a*e**S(2)) + S(2)*b*p*x/(S(3)*a*e) + S(2)*sqrt(b)*d**S(2)*p*atan(sqrt(a)*x/sqrt(b))/(sqrt(a)*e**S(3)) - S(2)*b**(S(3)/2)*p*atan(sqrt(a)*x/sqrt(b))/(S(3)*a**(S(3)/2)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b/x**S(2))**p)/(d + e*x), x), x, S(2)*d**S(2)*p*log(-e*x/d)*log(d + e*x)/e**S(3) - d**S(2)*p*log(e*(sqrt(b) - x*sqrt(-a))/(sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/e**S(3) - d**S(2)*p*log(-e*(sqrt(b) + x*sqrt(-a))/(-sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/e**S(3) + S(2)*d**S(2)*p*polylog(S(2), (d + e*x)/d)/e**S(3) - d**S(2)*p*polylog(S(2), sqrt(-a)*(d + e*x)/(-sqrt(b)*e + d*sqrt(-a)))/e**S(3) - d**S(2)*p*polylog(S(2), sqrt(-a)*(d + e*x)/(sqrt(b)*e + d*sqrt(-a)))/e**S(3) + d**S(2)*log(c*(a + b/x**S(2))**p)*log(d + e*x)/e**S(3) - d*x*log(c*(a + b/x**S(2))**p)/e**S(2) + x**S(2)*log(c*(a + b/x**S(2))**p)/(S(2)*e) + b*p*log(a*x**S(2) + b)/(S(2)*a*e) - S(2)*sqrt(b)*d*p*atan(sqrt(a)*x/sqrt(b))/(sqrt(a)*e**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b/x**S(2))**p)/(d + e*x), x), x, -S(2)*d*p*log(-e*x/d)*log(d + e*x)/e**S(2) + d*p*log(e*(sqrt(b) - x*sqrt(-a))/(sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/e**S(2) + d*p*log(-e*(sqrt(b) + x*sqrt(-a))/(-sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/e**S(2) - S(2)*d*p*polylog(S(2), (d + e*x)/d)/e**S(2) + d*p*polylog(S(2), sqrt(-a)*(d + e*x)/(-sqrt(b)*e + d*sqrt(-a)))/e**S(2) + d*p*polylog(S(2), sqrt(-a)*(d + e*x)/(sqrt(b)*e + d*sqrt(-a)))/e**S(2) - d*log(c*(a + b/x**S(2))**p)*log(d + e*x)/e**S(2) + x*log(c*(a + b/x**S(2))**p)/e + S(2)*sqrt(b)*p*atan(sqrt(a)*x/sqrt(b))/(sqrt(a)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(2))**p)/(d + e*x), x), x, S(2)*p*log(-e*x/d)*log(d + e*x)/e - p*log(e*(sqrt(b) - x*sqrt(-a))/(sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/e - p*log(-e*(sqrt(b) + x*sqrt(-a))/(-sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/e + S(2)*p*polylog(S(2), (d + e*x)/d)/e - p*polylog(S(2), sqrt(-a)*(d + e*x)/(-sqrt(b)*e + d*sqrt(-a)))/e - p*polylog(S(2), sqrt(-a)*(d + e*x)/(sqrt(b)*e + d*sqrt(-a)))/e + log(c*(a + b/x**S(2))**p)*log(d + e*x)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(2))**p)/(x*(d + e*x)), x), x, -S(2)*p*log(-e*x/d)*log(d + e*x)/d + p*log(e*(sqrt(b) - x*sqrt(-a))/(sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/d + p*log(-e*(sqrt(b) + x*sqrt(-a))/(-sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/d - p*polylog(S(2), (a + b/x**S(2))/a)/(S(2)*d) - S(2)*p*polylog(S(2), (d + e*x)/d)/d + p*polylog(S(2), sqrt(-a)*(d + e*x)/(-sqrt(b)*e + d*sqrt(-a)))/d + p*polylog(S(2), sqrt(-a)*(d + e*x)/(sqrt(b)*e + d*sqrt(-a)))/d - log(c*(a + b/x**S(2))**p)*log(-b/(a*x**S(2)))/(S(2)*d) - log(c*(a + b/x**S(2))**p)*log(d + e*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(2))**p)/(x**S(2)*(d + e*x)), x), x, S(2)*sqrt(a)*p*atan(sqrt(a)*x/sqrt(b))/(sqrt(b)*d) + S(2)*p/(d*x) - log(c*(a + b/x**S(2))**p)/(d*x) + S(2)*e*p*log(-e*x/d)*log(d + e*x)/d**S(2) - e*p*log(e*(sqrt(b) - x*sqrt(-a))/(sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/d**S(2) - e*p*log(-e*(sqrt(b) + x*sqrt(-a))/(-sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/d**S(2) + e*p*polylog(S(2), (a + b/x**S(2))/a)/(S(2)*d**S(2)) + S(2)*e*p*polylog(S(2), (d + e*x)/d)/d**S(2) - e*p*polylog(S(2), sqrt(-a)*(d + e*x)/(-sqrt(b)*e + d*sqrt(-a)))/d**S(2) - e*p*polylog(S(2), sqrt(-a)*(d + e*x)/(sqrt(b)*e + d*sqrt(-a)))/d**S(2) + e*log(c*(a + b/x**S(2))**p)*log(-b/(a*x**S(2)))/(S(2)*d**S(2)) + e*log(c*(a + b/x**S(2))**p)*log(d + e*x)/d**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(2))**p)/(x**S(3)*(d + e*x)), x), x, -S(2)*sqrt(a)*e*p*atan(sqrt(a)*x/sqrt(b))/(sqrt(b)*d**S(2)) + p/(S(2)*d*x**S(2)) - S(2)*e*p/(d**S(2)*x) + e*log(c*(a + b/x**S(2))**p)/(d**S(2)*x) - S(2)*e**S(2)*p*log(-e*x/d)*log(d + e*x)/d**S(3) + e**S(2)*p*log(e*(sqrt(b) - x*sqrt(-a))/(sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/d**S(3) + e**S(2)*p*log(-e*(sqrt(b) + x*sqrt(-a))/(-sqrt(b)*e + d*sqrt(-a)))*log(d + e*x)/d**S(3) - e**S(2)*p*polylog(S(2), (a + b/x**S(2))/a)/(S(2)*d**S(3)) - S(2)*e**S(2)*p*polylog(S(2), (d + e*x)/d)/d**S(3) + e**S(2)*p*polylog(S(2), sqrt(-a)*(d + e*x)/(-sqrt(b)*e + d*sqrt(-a)))/d**S(3) + e**S(2)*p*polylog(S(2), sqrt(-a)*(d + e*x)/(sqrt(b)*e + d*sqrt(-a)))/d**S(3) - e**S(2)*log(c*(a + b/x**S(2))**p)*log(-b/(a*x**S(2)))/(S(2)*d**S(3)) - e**S(2)*log(c*(a + b/x**S(2))**p)*log(d + e*x)/d**S(3) - (a/S(2) + b/(S(2)*x**S(2)))*log(c*(a + b/x**S(2))**p)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b/x**S(3))**p)/(d + e*x), x), x, -S(3)*d**S(3)*p*log(-e*x/d)*log(d + e*x)/e**S(4) + d**S(3)*p*log(-e*(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d - b**(S(1)/3)*e))*log(d + e*x)/e**S(4) + d**S(3)*p*log(-e*(a**(S(1)/3)*x + (S(-1))**(S(2)/3)*b**(S(1)/3))/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))*log(d + e*x)/e**S(4) + d**S(3)*p*log((S(-1))**(S(1)/3)*e*((S(-1))**(S(2)/3)*a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))*log(d + e*x)/e**S(4) - S(3)*d**S(3)*p*polylog(S(2), (d + e*x)/d)/e**S(4) + d**S(3)*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - b**(S(1)/3)*e))/e**S(4) + d**S(3)*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))/e**S(4) + d**S(3)*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))/e**S(4) - d**S(3)*log(c*(a + b/x**S(3))**p)*log(d + e*x)/e**S(4) + d**S(2)*x*log(c*(a + b/x**S(3))**p)/e**S(3) - d*x**S(2)*log(c*(a + b/x**S(3))**p)/(S(2)*e**S(2)) + x**S(3)*log(c*(a + b/x**S(3))**p)/(S(3)*e) + b*p*log(a*x**S(3) + b)/(S(3)*a*e) + b**(S(1)/3)*d**S(2)*p*log(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*e**S(3)) - b**(S(1)/3)*d**S(2)*p*log(a**(S(2)/3)*x**S(2) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3))/(S(2)*a**(S(1)/3)*e**S(3)) - sqrt(S(3))*b**(S(1)/3)*d**S(2)*p*atan(sqrt(S(3))*(-S(2)*a**(S(1)/3)*x + b**(S(1)/3))/(S(3)*b**(S(1)/3)))/(a**(S(1)/3)*e**S(3)) + b**(S(2)/3)*d*p*log(a**(S(1)/3)*x + b**(S(1)/3))/(S(2)*a**(S(2)/3)*e**S(2)) - b**(S(2)/3)*d*p*log(a**(S(2)/3)*x**S(2) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3))/(S(4)*a**(S(2)/3)*e**S(2)) + sqrt(S(3))*b**(S(2)/3)*d*p*atan(sqrt(S(3))*(-S(2)*a**(S(1)/3)*x + b**(S(1)/3))/(S(3)*b**(S(1)/3)))/(S(2)*a**(S(2)/3)*e**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b/x**S(3))**p)/(d + e*x), x), x, S(3)*d**S(2)*p*log(-e*x/d)*log(d + e*x)/e**S(3) - d**S(2)*p*log(-e*(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d - b**(S(1)/3)*e))*log(d + e*x)/e**S(3) - d**S(2)*p*log(-e*(a**(S(1)/3)*x + (S(-1))**(S(2)/3)*b**(S(1)/3))/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))*log(d + e*x)/e**S(3) - d**S(2)*p*log((S(-1))**(S(1)/3)*e*((S(-1))**(S(2)/3)*a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))*log(d + e*x)/e**S(3) + S(3)*d**S(2)*p*polylog(S(2), (d + e*x)/d)/e**S(3) - d**S(2)*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - b**(S(1)/3)*e))/e**S(3) - d**S(2)*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))/e**S(3) - d**S(2)*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))/e**S(3) + d**S(2)*log(c*(a + b/x**S(3))**p)*log(d + e*x)/e**S(3) - d*x*log(c*(a + b/x**S(3))**p)/e**S(2) + x**S(2)*log(c*(a + b/x**S(3))**p)/(S(2)*e) - b**(S(1)/3)*d*p*log(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*e**S(2)) + b**(S(1)/3)*d*p*log(a**(S(2)/3)*x**S(2) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3))/(S(2)*a**(S(1)/3)*e**S(2)) + sqrt(S(3))*b**(S(1)/3)*d*p*atan(sqrt(S(3))*(-S(2)*a**(S(1)/3)*x + b**(S(1)/3))/(S(3)*b**(S(1)/3)))/(a**(S(1)/3)*e**S(2)) - b**(S(2)/3)*p*log(a**(S(1)/3)*x + b**(S(1)/3))/(S(2)*a**(S(2)/3)*e) + b**(S(2)/3)*p*log(a**(S(2)/3)*x**S(2) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3))/(S(4)*a**(S(2)/3)*e) - sqrt(S(3))*b**(S(2)/3)*p*atan(sqrt(S(3))*(-S(2)*a**(S(1)/3)*x + b**(S(1)/3))/(S(3)*b**(S(1)/3)))/(S(2)*a**(S(2)/3)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b/x**S(3))**p)/(d + e*x), x), x, -S(3)*d*p*log(-e*x/d)*log(d + e*x)/e**S(2) + d*p*log(-e*(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d - b**(S(1)/3)*e))*log(d + e*x)/e**S(2) + d*p*log(-e*(a**(S(1)/3)*x + (S(-1))**(S(2)/3)*b**(S(1)/3))/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))*log(d + e*x)/e**S(2) + d*p*log((S(-1))**(S(1)/3)*e*((S(-1))**(S(2)/3)*a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))*log(d + e*x)/e**S(2) - S(3)*d*p*polylog(S(2), (d + e*x)/d)/e**S(2) + d*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - b**(S(1)/3)*e))/e**S(2) + d*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))/e**S(2) + d*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))/e**S(2) - d*log(c*(a + b/x**S(3))**p)*log(d + e*x)/e**S(2) + x*log(c*(a + b/x**S(3))**p)/e + b**(S(1)/3)*p*log(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*e) - b**(S(1)/3)*p*log(a**(S(2)/3)*x**S(2) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3))/(S(2)*a**(S(1)/3)*e) - sqrt(S(3))*b**(S(1)/3)*p*atan(sqrt(S(3))*(-S(2)*a**(S(1)/3)*x + b**(S(1)/3))/(S(3)*b**(S(1)/3)))/(a**(S(1)/3)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(3))**p)/(d + e*x), x), x, S(3)*p*log(-e*x/d)*log(d + e*x)/e - p*log(-e*(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d - b**(S(1)/3)*e))*log(d + e*x)/e - p*log(-e*(a**(S(1)/3)*x + (S(-1))**(S(2)/3)*b**(S(1)/3))/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))*log(d + e*x)/e - p*log((S(-1))**(S(1)/3)*e*((S(-1))**(S(2)/3)*a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))*log(d + e*x)/e + S(3)*p*polylog(S(2), (d + e*x)/d)/e - p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - b**(S(1)/3)*e))/e - p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))/e - p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))/e + log(c*(a + b/x**S(3))**p)*log(d + e*x)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(3))**p)/(x*(d + e*x)), x), x, -S(3)*p*log(-e*x/d)*log(d + e*x)/d + p*log(-e*(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d - b**(S(1)/3)*e))*log(d + e*x)/d + p*log(-e*(a**(S(1)/3)*x + (S(-1))**(S(2)/3)*b**(S(1)/3))/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))*log(d + e*x)/d + p*log((S(-1))**(S(1)/3)*e*((S(-1))**(S(2)/3)*a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))*log(d + e*x)/d - p*polylog(S(2), (a + b/x**S(3))/a)/(S(3)*d) - S(3)*p*polylog(S(2), (d + e*x)/d)/d + p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - b**(S(1)/3)*e))/d + p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))/d + p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))/d - log(c*(a + b/x**S(3))**p)*log(-b/(a*x**S(3)))/(S(3)*d) - log(c*(a + b/x**S(3))**p)*log(d + e*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(3))**p)/(x**S(2)*(d + e*x)), x), x, -a**(S(1)/3)*p*log(a**(S(1)/3)*x + b**(S(1)/3))/(b**(S(1)/3)*d) + a**(S(1)/3)*p*log(a**(S(2)/3)*x**S(2) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3))/(S(2)*b**(S(1)/3)*d) - sqrt(S(3))*a**(S(1)/3)*p*atan(sqrt(S(3))*(-S(2)*a**(S(1)/3)*x + b**(S(1)/3))/(S(3)*b**(S(1)/3)))/(b**(S(1)/3)*d) + S(3)*p/(d*x) - log(c*(a + b/x**S(3))**p)/(d*x) + S(3)*e*p*log(-e*x/d)*log(d + e*x)/d**S(2) - e*p*log(-e*(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d - b**(S(1)/3)*e))*log(d + e*x)/d**S(2) - e*p*log(-e*(a**(S(1)/3)*x + (S(-1))**(S(2)/3)*b**(S(1)/3))/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))*log(d + e*x)/d**S(2) - e*p*log((S(-1))**(S(1)/3)*e*((S(-1))**(S(2)/3)*a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))*log(d + e*x)/d**S(2) + e*p*polylog(S(2), (a + b/x**S(3))/a)/(S(3)*d**S(2)) + S(3)*e*p*polylog(S(2), (d + e*x)/d)/d**S(2) - e*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - b**(S(1)/3)*e))/d**S(2) - e*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))/d**S(2) - e*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))/d**S(2) + e*log(c*(a + b/x**S(3))**p)*log(-b/(a*x**S(3)))/(S(3)*d**S(2)) + e*log(c*(a + b/x**S(3))**p)*log(d + e*x)/d**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b/x**S(3))**p)/(x**S(3)*(d + e*x)), x), x, a**(S(2)/3)*p*log(a**(S(1)/3)*x + b**(S(1)/3))/(S(2)*b**(S(2)/3)*d) - a**(S(2)/3)*p*log(a**(S(2)/3)*x**S(2) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3))/(S(4)*b**(S(2)/3)*d) - sqrt(S(3))*a**(S(2)/3)*p*atan(sqrt(S(3))*(-S(2)*a**(S(1)/3)*x + b**(S(1)/3))/(S(3)*b**(S(1)/3)))/(S(2)*b**(S(2)/3)*d) + a**(S(1)/3)*e*p*log(a**(S(1)/3)*x + b**(S(1)/3))/(b**(S(1)/3)*d**S(2)) - a**(S(1)/3)*e*p*log(a**(S(2)/3)*x**S(2) - a**(S(1)/3)*b**(S(1)/3)*x + b**(S(2)/3))/(S(2)*b**(S(1)/3)*d**S(2)) + sqrt(S(3))*a**(S(1)/3)*e*p*atan(sqrt(S(3))*(-S(2)*a**(S(1)/3)*x + b**(S(1)/3))/(S(3)*b**(S(1)/3)))/(b**(S(1)/3)*d**S(2)) + S(3)*p/(S(4)*d*x**S(2)) - log(c*(a + b/x**S(3))**p)/(S(2)*d*x**S(2)) - S(3)*e*p/(d**S(2)*x) + e*log(c*(a + b/x**S(3))**p)/(d**S(2)*x) - S(3)*e**S(2)*p*log(-e*x/d)*log(d + e*x)/d**S(3) + e**S(2)*p*log(-e*(a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d - b**(S(1)/3)*e))*log(d + e*x)/d**S(3) + e**S(2)*p*log(-e*(a**(S(1)/3)*x + (S(-1))**(S(2)/3)*b**(S(1)/3))/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))*log(d + e*x)/d**S(3) + e**S(2)*p*log((S(-1))**(S(1)/3)*e*((S(-1))**(S(2)/3)*a**(S(1)/3)*x + b**(S(1)/3))/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))*log(d + e*x)/d**S(3) - e**S(2)*p*polylog(S(2), (a + b/x**S(3))/a)/(S(3)*d**S(3)) - S(3)*e**S(2)*p*polylog(S(2), (d + e*x)/d)/d**S(3) + e**S(2)*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - b**(S(1)/3)*e))/d**S(3) + e**S(2)*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d + (S(-1))**(S(1)/3)*b**(S(1)/3)*e))/d**S(3) + e**S(2)*p*polylog(S(2), a**(S(1)/3)*(d + e*x)/(a**(S(1)/3)*d - (S(-1))**(S(2)/3)*b**(S(1)/3)*e))/d**S(3) - e**S(2)*log(c*(a + b/x**S(3))**p)*log(-b/(a*x**S(3)))/(S(3)*d**S(3)) - e**S(2)*log(c*(a + b/x**S(3))**p)*log(d + e*x)/d**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d + e*x**S(2))/(-x**S(2) + S(1)), x), x, log((-sqrt(e)*x + sqrt(-d))/(-sqrt(e) + sqrt(-d)))*log(-x + S(1))/S(2) - log((sqrt(e)*x + sqrt(-d))/(-sqrt(e) + sqrt(-d)))*log(x + S(1))/S(2) - log((-sqrt(e)*x + sqrt(-d))/(sqrt(e) + sqrt(-d)))*log(x + S(1))/S(2) + log((sqrt(e)*x + sqrt(-d))/(sqrt(e) + sqrt(-d)))*log(-x + S(1))/S(2) + log(d + e*x**S(2))*atanh(x) - polylog(S(2), sqrt(e)*(-x + S(-1))/(-sqrt(e) + sqrt(-d)))/S(2) + polylog(S(2), sqrt(e)*(x + S(-1))/(-sqrt(e) + sqrt(-d)))/S(2) + polylog(S(2), sqrt(e)*(-x + S(1))/(sqrt(e) + sqrt(-d)))/S(2) - polylog(S(2), sqrt(e)*(x + S(1))/(sqrt(e) + sqrt(-d)))/S(2), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(d + e*x**S(2))/(a + b*x**S(2)), x), x, I*log(sqrt(b)*(-sqrt(e)*x + sqrt(-d))/(-I*sqrt(a)*sqrt(e) + sqrt(b)*sqrt(-d)))*log(S(1) + I*sqrt(b)*x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)) - I*log(sqrt(b)*(-sqrt(e)*x + sqrt(-d))/(I*sqrt(a)*sqrt(e) + sqrt(b)*sqrt(-d)))*log(S(1) - I*sqrt(b)*x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)) - I*log(sqrt(b)*(sqrt(e)*x + sqrt(-d))/(-I*sqrt(a)*sqrt(e) + sqrt(b)*sqrt(-d)))*log(S(1) - I*sqrt(b)*x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)) + I*log(sqrt(b)*(sqrt(e)*x + sqrt(-d))/(I*sqrt(a)*sqrt(e) + sqrt(b)*sqrt(-d)))*log(S(1) + I*sqrt(b)*x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)) + log(d + e*x**S(2))*atan(sqrt(b)*x/sqrt(a))/(sqrt(a)*sqrt(b)) + I*polylog(S(2), sqrt(e)*(-sqrt(a) - I*sqrt(b)*x)/(-sqrt(a)*sqrt(e) + I*sqrt(b)*sqrt(-d)))/(S(2)*sqrt(a)*sqrt(b)) - I*polylog(S(2), sqrt(e)*(-sqrt(a) + I*sqrt(b)*x)/(-sqrt(a)*sqrt(e) + I*sqrt(b)*sqrt(-d)))/(S(2)*sqrt(a)*sqrt(b)) - I*polylog(S(2), sqrt(e)*(sqrt(a) - I*sqrt(b)*x)/(sqrt(a)*sqrt(e) + I*sqrt(b)*sqrt(-d)))/(S(2)*sqrt(a)*sqrt(b)) + I*polylog(S(2), sqrt(e)*(sqrt(a) + I*sqrt(b)*x)/(sqrt(a)*sqrt(e) + I*sqrt(b)*sqrt(-d)))/(S(2)*sqrt(a)*sqrt(b)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(-x**S(2) + S(1))/(-x**S(2) + S(2)), x), x, sqrt(S(2))*log(-x**S(2) + S(1))*atanh(sqrt(S(2))*x/S(2))/S(2) - sqrt(S(2))*log(-S(2)*sqrt(S(2)) + S(3))*atanh(x)/S(2) + sqrt(S(2))*polylog(S(2), sqrt(S(2))*(-x + S(-1))/(-sqrt(S(2)) + S(2)))/S(4) - sqrt(S(2))*polylog(S(2), sqrt(S(2))*(x + S(-1))/(-sqrt(S(2)) + S(2)))/S(4) + sqrt(S(2))*polylog(S(2), sqrt(S(2))*(-x + S(1))/(sqrt(S(2)) + S(2)))/S(4) - sqrt(S(2))*polylog(S(2), sqrt(S(2))*(x + S(1))/(sqrt(S(2)) + S(2)))/S(4), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(log(-x**S(2) + S(1))/(-x**S(2) + S(2)), x), x, -sqrt(S(2))*log(-x + S(1))*atanh(sqrt(S(2))/S(2))/S(2) + sqrt(S(2))*log(x + S(1))*atanh(sqrt(S(2))/S(2))/S(2) + sqrt(S(2))*log(-x**S(2) + S(1))*atanh(sqrt(S(2))*x/S(2))/S(2) + sqrt(S(2))*polylog(S(2), sqrt(S(2))*(-x + S(-1))/(-sqrt(S(2)) + S(2)))/S(4) - sqrt(S(2))*polylog(S(2), sqrt(S(2))*(x + S(-1))/(-sqrt(S(2)) + S(2)))/S(4) + sqrt(S(2))*polylog(S(2), sqrt(S(2))*(-x + S(1))/(sqrt(S(2)) + S(2)))/S(4) - sqrt(S(2))*polylog(S(2), sqrt(S(2))*(x + S(1))/(sqrt(S(2)) + S(2)))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + c*x**S(2))*log(d + e*x**S(2))/x**S(2), x), x, -a*log(d + e*x**S(2))/x + c*x*log(d + e*x**S(2)) - S(2)*c*x + (S(2)*a*e + S(2)*c*d)*atan(sqrt(e)*x/sqrt(d))/(sqrt(d)*sqrt(e)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((a + c*x**S(2))*log(d + e*x**S(2))/x**S(2), x), x, -a*log(d + e*x**S(2))/x + S(2)*a*sqrt(e)*atan(sqrt(e)*x/sqrt(d))/sqrt(d) + S(2)*c*sqrt(d)*atan(sqrt(e)*x/sqrt(d))/sqrt(e) + c*x*log(d + e*x**S(2)) - S(2)*c*x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)*log(c*(a + b*x**S(2))**n)**S(2), x), x, -S(5)*a**S(3)*n**S(2)*log(a + b*x**S(2))/(S(18)*b**S(3)) + a**S(3)*log(c*(a + b*x**S(2))**n)**S(2)/(S(6)*b**S(3)) + S(11)*a**S(2)*n**S(2)*x**S(2)/(S(18)*b**S(2)) - a**S(2)*n*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)/(S(3)*b**S(3)) - S(5)*a*n**S(2)*x**S(4)/(S(36)*b) + a*n*x**S(4)*log(c*(a + b*x**S(2))**n)/(S(6)*b) + n**S(2)*x**S(6)/S(27) - n*x**S(6)*log(c*(a + b*x**S(2))**n)/S(9) + x**S(6)*log(c*(a + b*x**S(2))**n)**S(2)/S(6), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b*x**S(2))**n)**S(2), x), x, -S(3)*a*n**S(2)*x**S(2)/(S(4)*b) + a*n*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)/b**S(2) - a*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)**S(2)/(S(2)*b**S(2)) + n**S(2)*x**S(4)/S(8) - n*(a + b*x**S(2))**S(2)*log(c*(a + b*x**S(2))**n)/(S(4)*b**S(2)) + (a + b*x**S(2))**S(2)*log(c*(a + b*x**S(2))**n)**S(2)/(S(4)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b*x**S(2))**n)**S(2), x), x, n**S(2)*x**S(2) - n*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)/b + (a/S(2) + b*x**S(2)/S(2))*log(c*(a + b*x**S(2))**n)**S(2)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(2)/x, x), x, -n**S(2)*polylog(S(3), (a + b*x**S(2))/a) + n*log(c*(a + b*x**S(2))**n)*polylog(S(2), (a + b*x**S(2))/a) + log(c*(a + b*x**S(2))**n)**S(2)*log(-b*x**S(2)/a)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(2)/x**S(3), x), x, b*n**S(2)*polylog(S(2), (a + b*x**S(2))/a)/a + b*n*log(c*(a + b*x**S(2))**n)*log(-b*x**S(2)/a)/a - (a/S(2) + b*x**S(2)/S(2))*log(c*(a + b*x**S(2))**n)**S(2)/(a*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(2)/x**S(5), x), x, -log(c*(a + b*x**S(2))**n)**S(2)/(S(4)*x**S(4)) - b*n*log(c*(a + b*x**S(2))**n)/(S(2)*a*x**S(2)) + b**S(2)*n**S(2)*log(x)/a**S(2) - b**S(2)*n**S(2)*log(a + b*x**S(2))/(S(2)*a**S(2)) - b**S(2)*n**S(2)*polylog(S(2), (a + b*x**S(2))/a)/(S(2)*a**S(2)) - b**S(2)*n*log(c*(a + b*x**S(2))**n)*log(-b*x**S(2)/a)/(S(2)*a**S(2)) + b**S(2)*log(c*(a + b*x**S(2))**n)**S(2)/(S(4)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(2)/x**S(7), x), x, -log(c*(a + b*x**S(2))**n)**S(2)/(S(6)*x**S(6)) - b*n*log(c*(a + b*x**S(2))**n)/(S(6)*a*x**S(4)) - b**S(2)*n**S(2)/(S(6)*a**S(2)*x**S(2)) + b**S(2)*n*log(c*(a + b*x**S(2))**n)/(S(3)*a**S(2)*x**S(2)) - b**S(3)*n**S(2)*log(x)/a**S(3) + b**S(3)*n**S(2)*log(a + b*x**S(2))/(S(2)*a**S(3)) + b**S(3)*n**S(2)*polylog(S(2), (a + b*x**S(2))/a)/(S(3)*a**S(3)) + b**S(3)*n*log(c*(a + b*x**S(2))**n)*log(-b*x**S(2)/a)/(S(3)*a**S(3)) - b**S(3)*log(c*(a + b*x**S(2))**n)**S(2)/(S(6)*a**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(c*(a + b*x**S(2))**n)**S(2), x), x, S(8)*a**(S(5)/2)*n**S(2)*log(S(2)*I*sqrt(a)/(I*sqrt(a) - sqrt(b)*x))*atan(sqrt(b)*x/sqrt(a))/(S(5)*b**(S(5)/2)) + S(4)*I*a**(S(5)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))**S(2)/(S(5)*b**(S(5)/2)) - S(184)*a**(S(5)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))/(S(75)*b**(S(5)/2)) + S(4)*I*a**(S(5)/2)*n**S(2)*polylog(S(2), (-sqrt(a) + I*sqrt(b)*x)/(sqrt(a) + I*sqrt(b)*x))/(S(5)*b**(S(5)/2)) + S(4)*a**(S(5)/2)*n*log(c*(a + b*x**S(2))**n)*atan(sqrt(b)*x/sqrt(a))/(S(5)*b**(S(5)/2)) + S(184)*a**S(2)*n**S(2)*x/(S(75)*b**S(2)) - S(4)*a**S(2)*n*x*log(c*(a + b*x**S(2))**n)/(S(5)*b**S(2)) - S(64)*a*n**S(2)*x**S(3)/(S(225)*b) + S(4)*a*n*x**S(3)*log(c*(a + b*x**S(2))**n)/(S(15)*b) + S(8)*n**S(2)*x**S(5)/S(125) - S(4)*n*x**S(5)*log(c*(a + b*x**S(2))**n)/S(25) + x**S(5)*log(c*(a + b*x**S(2))**n)**S(2)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(c*(a + b*x**S(2))**n)**S(2), x), x, -S(8)*a**(S(3)/2)*n**S(2)*log(S(2)*I*sqrt(a)/(I*sqrt(a) - sqrt(b)*x))*atan(sqrt(b)*x/sqrt(a))/(S(3)*b**(S(3)/2)) - S(4)*I*a**(S(3)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))**S(2)/(S(3)*b**(S(3)/2)) + S(32)*a**(S(3)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))/(S(9)*b**(S(3)/2)) - S(4)*I*a**(S(3)/2)*n**S(2)*polylog(S(2), (-sqrt(a) + I*sqrt(b)*x)/(sqrt(a) + I*sqrt(b)*x))/(S(3)*b**(S(3)/2)) - S(4)*a**(S(3)/2)*n*log(c*(a + b*x**S(2))**n)*atan(sqrt(b)*x/sqrt(a))/(S(3)*b**(S(3)/2)) - S(32)*a*n**S(2)*x/(S(9)*b) + S(4)*a*n*x*log(c*(a + b*x**S(2))**n)/(S(3)*b) + S(8)*n**S(2)*x**S(3)/S(27) - S(4)*n*x**S(3)*log(c*(a + b*x**S(2))**n)/S(9) + x**S(3)*log(c*(a + b*x**S(2))**n)**S(2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(2), x), x, S(8)*sqrt(a)*n**S(2)*log(S(2)*I*sqrt(a)/(I*sqrt(a) - sqrt(b)*x))*atan(sqrt(b)*x/sqrt(a))/sqrt(b) + S(4)*I*sqrt(a)*n**S(2)*atan(sqrt(b)*x/sqrt(a))**S(2)/sqrt(b) - S(8)*sqrt(a)*n**S(2)*atan(sqrt(b)*x/sqrt(a))/sqrt(b) + S(4)*I*sqrt(a)*n**S(2)*polylog(S(2), (-sqrt(a) + I*sqrt(b)*x)/(sqrt(a) + I*sqrt(b)*x))/sqrt(b) + S(4)*sqrt(a)*n*log(c*(a + b*x**S(2))**n)*atan(sqrt(b)*x/sqrt(a))/sqrt(b) + S(8)*n**S(2)*x - S(4)*n*x*log(c*(a + b*x**S(2))**n) + x*log(c*(a + b*x**S(2))**n)**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(2)/x**S(2), x), x, -log(c*(a + b*x**S(2))**n)**S(2)/x + S(8)*sqrt(b)*n**S(2)*log(S(2)*I*sqrt(a)/(I*sqrt(a) - sqrt(b)*x))*atan(sqrt(b)*x/sqrt(a))/sqrt(a) + S(4)*I*sqrt(b)*n**S(2)*atan(sqrt(b)*x/sqrt(a))**S(2)/sqrt(a) + S(4)*I*sqrt(b)*n**S(2)*polylog(S(2), (-sqrt(a) + I*sqrt(b)*x)/(sqrt(a) + I*sqrt(b)*x))/sqrt(a) + S(4)*sqrt(b)*n*log(c*(a + b*x**S(2))**n)*atan(sqrt(b)*x/sqrt(a))/sqrt(a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(2)/x**S(4), x), x, -log(c*(a + b*x**S(2))**n)**S(2)/(S(3)*x**S(3)) - S(4)*b*n*log(c*(a + b*x**S(2))**n)/(S(3)*a*x) - S(8)*b**(S(3)/2)*n**S(2)*log(S(2)*I*sqrt(a)/(I*sqrt(a) - sqrt(b)*x))*atan(sqrt(b)*x/sqrt(a))/(S(3)*a**(S(3)/2)) - S(4)*I*b**(S(3)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))**S(2)/(S(3)*a**(S(3)/2)) + S(8)*b**(S(3)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))/(S(3)*a**(S(3)/2)) - S(4)*I*b**(S(3)/2)*n**S(2)*polylog(S(2), (-sqrt(a) + I*sqrt(b)*x)/(sqrt(a) + I*sqrt(b)*x))/(S(3)*a**(S(3)/2)) - S(4)*b**(S(3)/2)*n*log(c*(a + b*x**S(2))**n)*atan(sqrt(b)*x/sqrt(a))/(S(3)*a**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(2)/x**S(6), x), x, -log(c*(a + b*x**S(2))**n)**S(2)/(S(5)*x**S(5)) - S(4)*b*n*log(c*(a + b*x**S(2))**n)/(S(15)*a*x**S(3)) - S(8)*b**S(2)*n**S(2)/(S(15)*a**S(2)*x) + S(4)*b**S(2)*n*log(c*(a + b*x**S(2))**n)/(S(5)*a**S(2)*x) + S(8)*b**(S(5)/2)*n**S(2)*log(S(2)*I*sqrt(a)/(I*sqrt(a) - sqrt(b)*x))*atan(sqrt(b)*x/sqrt(a))/(S(5)*a**(S(5)/2)) + S(4)*I*b**(S(5)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))**S(2)/(S(5)*a**(S(5)/2)) - S(32)*b**(S(5)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))/(S(15)*a**(S(5)/2)) + S(4)*I*b**(S(5)/2)*n**S(2)*polylog(S(2), (-sqrt(a) + I*sqrt(b)*x)/(sqrt(a) + I*sqrt(b)*x))/(S(5)*a**(S(5)/2)) + S(4)*b**(S(5)/2)*n*log(c*(a + b*x**S(2))**n)*atan(sqrt(b)*x/sqrt(a))/(S(5)*a**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(2)/x**S(8), x), x, -log(c*(a + b*x**S(2))**n)**S(2)/(S(7)*x**S(7)) - S(4)*b*n*log(c*(a + b*x**S(2))**n)/(S(35)*a*x**S(5)) - S(8)*b**S(2)*n**S(2)/(S(105)*a**S(2)*x**S(3)) + S(4)*b**S(2)*n*log(c*(a + b*x**S(2))**n)/(S(21)*a**S(2)*x**S(3)) + S(64)*b**S(3)*n**S(2)/(S(105)*a**S(3)*x) - S(4)*b**S(3)*n*log(c*(a + b*x**S(2))**n)/(S(7)*a**S(3)*x) - S(8)*b**(S(7)/2)*n**S(2)*log(S(2)*I*sqrt(a)/(I*sqrt(a) - sqrt(b)*x))*atan(sqrt(b)*x/sqrt(a))/(S(7)*a**(S(7)/2)) - S(4)*I*b**(S(7)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))**S(2)/(S(7)*a**(S(7)/2)) + S(184)*b**(S(7)/2)*n**S(2)*atan(sqrt(b)*x/sqrt(a))/(S(105)*a**(S(7)/2)) - S(4)*I*b**(S(7)/2)*n**S(2)*polylog(S(2), (-sqrt(a) + I*sqrt(b)*x)/(sqrt(a) + I*sqrt(b)*x))/(S(7)*a**(S(7)/2)) - S(4)*b**(S(7)/2)*n*log(c*(a + b*x**S(2))**n)*atan(sqrt(b)*x/sqrt(a))/(S(7)*a**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(5)*log(c*(a + b*x**S(2))**n)**S(3), x), x, -S(9)*a**S(2)*n**S(3)*x**S(2)/(S(4)*b**S(2)) + S(3)*a**S(2)*n**S(2)*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)/b**S(3) - S(3)*a**S(2)*n*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)**S(2)/(S(2)*b**S(3)) + a**S(2)*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)**S(3)/(S(2)*b**S(3)) + S(3)*a*n**S(3)*x**S(4)/(S(8)*b) - S(3)*a*n**S(2)*(a + b*x**S(2))**S(2)*log(c*(a + b*x**S(2))**n)/(S(4)*b**S(3)) + S(3)*a*n*(a + b*x**S(2))**S(2)*log(c*(a + b*x**S(2))**n)**S(2)/(S(4)*b**S(3)) - a*(a + b*x**S(2))**S(2)*log(c*(a + b*x**S(2))**n)**S(3)/(S(2)*b**S(3)) - n**S(3)*(a + b*x**S(2))**S(3)/(S(27)*b**S(3)) + n**S(2)*(a + b*x**S(2))**S(3)*log(c*(a + b*x**S(2))**n)/(S(9)*b**S(3)) - n*(a + b*x**S(2))**S(3)*log(c*(a + b*x**S(2))**n)**S(2)/(S(6)*b**S(3)) + (a + b*x**S(2))**S(3)*log(c*(a + b*x**S(2))**n)**S(3)/(S(6)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(c*(a + b*x**S(2))**n)**S(3), x), x, S(21)*a*n**S(3)*x**S(2)/(S(8)*b) - S(3)*a*n**S(2)*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)/b**S(2) + S(3)*a*n*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)**S(2)/(S(2)*b**S(2)) - a*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)**S(3)/(S(2)*b**S(2)) - S(3)*n**S(3)*x**S(4)/S(16) + S(3)*n**S(2)*(a + b*x**S(2))**S(2)*log(c*(a + b*x**S(2))**n)/(S(8)*b**S(2)) - S(3)*n*(a + b*x**S(2))**S(2)*log(c*(a + b*x**S(2))**n)**S(2)/(S(8)*b**S(2)) + (a + b*x**S(2))**S(2)*log(c*(a + b*x**S(2))**n)**S(3)/(S(4)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c*(a + b*x**S(2))**n)**S(3), x), x, -S(3)*n**S(3)*x**S(2) + S(3)*n**S(2)*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)/b - S(3)*n*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)**S(2)/(S(2)*b) + (a/S(2) + b*x**S(2)/S(2))*log(c*(a + b*x**S(2))**n)**S(3)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(3)/x, x), x, S(3)*n**S(3)*polylog(S(4), (a + b*x**S(2))/a) - S(3)*n**S(2)*log(c*(a + b*x**S(2))**n)*polylog(S(3), (a + b*x**S(2))/a) + S(3)*n*log(c*(a + b*x**S(2))**n)**S(2)*polylog(S(2), (a + b*x**S(2))/a)/S(2) + log(c*(a + b*x**S(2))**n)**S(3)*log(-b*x**S(2)/a)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(3)/x**S(3), x), x, -S(3)*b*n**S(3)*polylog(S(3), (a + b*x**S(2))/a)/a + S(3)*b*n**S(2)*log(c*(a + b*x**S(2))**n)*polylog(S(2), (a + b*x**S(2))/a)/a + S(3)*b*n*log(c*(a + b*x**S(2))**n)**S(2)*log(-b*x**S(2)/a)/(S(2)*a) - (a/S(2) + b*x**S(2)/S(2))*log(c*(a + b*x**S(2))**n)**S(3)/(a*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(3)/x**S(5), x), x, -log(c*(a + b*x**S(2))**n)**S(3)/(S(4)*x**S(4)) + S(3)*b**S(2)*n**S(3)*polylog(S(2), (a + b*x**S(2))/a)/(S(2)*a**S(2)) + S(3)*b**S(2)*n**S(3)*polylog(S(3), (a + b*x**S(2))/a)/(S(2)*a**S(2)) + S(3)*b**S(2)*n**S(2)*log(c*(a + b*x**S(2))**n)*log(-b*x**S(2)/a)/(S(2)*a**S(2)) - S(3)*b**S(2)*n**S(2)*log(c*(a + b*x**S(2))**n)*polylog(S(2), (a + b*x**S(2))/a)/(S(2)*a**S(2)) - S(3)*b**S(2)*n*log(c*(a + b*x**S(2))**n)**S(2)*log(-b*x**S(2)/a)/(S(4)*a**S(2)) + b**S(2)*log(c*(a + b*x**S(2))**n)**S(3)/(S(4)*a**S(2)) - S(3)*b*n*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)**S(2)/(S(4)*a**S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)**S(3)/x**S(7), x), x, -log(c*(a + b*x**S(2))**n)**S(3)/(S(6)*x**S(6)) - b*n*log(c*(a + b*x**S(2))**n)**S(2)/(S(4)*a*x**S(4)) - b**S(2)*n**S(2)*log(c*(a + b*x**S(2))**n)/(S(2)*a**S(2)*x**S(2)) + b**S(3)*n**S(3)*log(x)/a**S(3) - b**S(3)*n**S(3)*log(a + b*x**S(2))/(S(2)*a**S(3)) - S(3)*b**S(3)*n**S(3)*polylog(S(2), (a + b*x**S(2))/a)/(S(2)*a**S(3)) - b**S(3)*n**S(3)*polylog(S(3), (a + b*x**S(2))/a)/a**S(3) - S(3)*b**S(3)*n**S(2)*log(c*(a + b*x**S(2))**n)*log(-b*x**S(2)/a)/(S(2)*a**S(3)) + b**S(3)*n**S(2)*log(c*(a + b*x**S(2))**n)*polylog(S(2), (a + b*x**S(2))/a)/a**S(3) + b**S(3)*n*log(c*(a + b*x**S(2))**n)**S(2)*log(-b*x**S(2)/a)/(S(2)*a**S(3)) + b**S(3)*n*log(c*(a + b*x**S(2))**n)**S(2)/(S(4)*a**S(3)) - b**S(3)*log(c*(a + b*x**S(2))**n)**S(3)/(S(6)*a**S(3)) + b**S(2)*n*(a + b*x**S(2))*log(c*(a + b*x**S(2))**n)**S(2)/(S(2)*a**S(3)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(c*(a + b*x**S(2))**n), x), x, -a*(c*(a + b*x**S(2))**n)**(-S(1)/n)*(a + b*x**S(2))*Ei(log(c*(a + b*x**S(2))**n)/n)/(S(2)*b**S(2)*n) + (c*(a + b*x**S(2))**n)**(-S(2)/n)*(a + b*x**S(2))**S(2)*Ei(S(2)*log(c*(a + b*x**S(2))**n)/n)/(S(2)*b**S(2)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(c*(a + b*x**S(2))**n), x), x, (c*(a + b*x**S(2))**n)**(-S(1)/n)*(a/S(2) + b*x**S(2)/S(2))*Ei(log(c*(a + b*x**S(2))**n)/n)/(b*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(c*(a + b*x**S(2))**n)), x), x, Integral(S(1)/(x*log(c*(a + b*x)**n)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(c*(a + b*x**S(2))**n)), x), x, Integral(S(1)/(x**S(2)*log(c*(a + b*x)**n)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(c*(a + b*x**S(2))**n)**S(2), x), x, -a*(c*(a + b*x**S(2))**n)**(-S(1)/n)*(a + b*x**S(2))*Ei(log(c*(a + b*x**S(2))**n)/n)/(S(2)*b**S(2)*n**S(2)) - x**S(2)*(a + b*x**S(2))/(S(2)*b*n*log(c*(a + b*x**S(2))**n)) + (c*(a + b*x**S(2))**n)**(-S(2)/n)*(a + b*x**S(2))**S(2)*Ei(S(2)*log(c*(a + b*x**S(2))**n)/n)/(b**S(2)*n**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(c*(a + b*x**S(2))**n)**S(2), x), x, (-a/S(2) - b*x**S(2)/S(2))/(b*n*log(c*(a + b*x**S(2))**n)) + (c*(a + b*x**S(2))**n)**(-S(1)/n)*(a/S(2) + b*x**S(2)/S(2))*Ei(log(c*(a + b*x**S(2))**n)/n)/(b*n**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(c*(a + b*x**S(2))**n)**S(2)), x), x, Integral(S(1)/(x*log(c*(a + b*x)**n)**S(2)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(c*(a + b*x**S(2))**n)**S(2)), x), x, Integral(S(1)/(x**S(2)*log(c*(a + b*x)**n)**S(2)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(c*(a + b*x**S(2))**n)**S(3), x), x, -a*(a + b*x**S(2))/(S(4)*b**S(2)*n**S(2)*log(c*(a + b*x**S(2))**n)) - a*(c*(a + b*x**S(2))**n)**(-S(1)/n)*(a + b*x**S(2))*Ei(log(c*(a + b*x**S(2))**n)/n)/(S(4)*b**S(2)*n**S(3)) - x**S(2)*(a + b*x**S(2))/(S(4)*b*n*log(c*(a + b*x**S(2))**n)**S(2)) - x**S(2)*(a + b*x**S(2))/(S(2)*b*n**S(2)*log(c*(a + b*x**S(2))**n)) + (c*(a + b*x**S(2))**n)**(-S(2)/n)*(a + b*x**S(2))**S(2)*Ei(S(2)*log(c*(a + b*x**S(2))**n)/n)/(b**S(2)*n**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(c*(a + b*x**S(2))**n)**S(3), x), x, (-a/S(4) - b*x**S(2)/S(4))/(b*n*log(c*(a + b*x**S(2))**n)**S(2)) + (-a/S(4) - b*x**S(2)/S(4))/(b*n**S(2)*log(c*(a + b*x**S(2))**n)) + (c*(a + b*x**S(2))**n)**(-S(1)/n)*(a/S(4) + b*x**S(2)/S(4))*Ei(log(c*(a + b*x**S(2))**n)/n)/(b*n**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(c*(a + b*x**S(2))**n)**S(3)), x), x, Integral(S(1)/(x*log(c*(a + b*x)**n)**S(3)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(c*(a + b*x**S(2))**n)**S(3)), x), x, Integral(S(1)/(x**S(2)*log(c*(a + b*x)**n)**S(3)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(c*(a + b*x**S(2))), x), x, Integral(x**m/log(c*(a + b*x**S(2))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(c*(a + b*x**S(2))), x), x, -a*li(a*c + b*c*x**S(2))/(S(2)*b**S(2)*c) + Ei(S(2)*log(a*c + b*c*x**S(2)))/(S(2)*b**S(2)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(c*(a + b*x**S(2))), x), x, Integral(x**S(2)/log(c*(a + b*x**S(2))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(c*(a + b*x**S(2))), x), x, li(c*(a + b*x**S(2)))/(S(2)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/log(c*(a + b*x**S(2))), x), x, Integral(S(1)/log(c*(a + b*x**S(2))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(c*(a + b*x**S(2)))), x), x, Integral(S(1)/(x*log(a*c + b*c*x)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(c*(a + b*x**S(2)))), x), x, Integral(S(1)/(x**S(2)*log(c*(a + b*x**S(2)))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(c*(a + b*x**S(2)))), x), x, Integral(S(1)/(x**S(2)*log(a*c + b*c*x)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(c*(a + b*x**S(2)))**S(2), x), x, Integral(x**m/log(c*(a + b*x**S(2)))**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(c*(a + b*x**S(2)))**S(2), x), x, -a*li(a*c + b*c*x**S(2))/(S(2)*b**S(2)*c) - x**S(2)*(a + b*x**S(2))/(S(2)*b*log(a*c + b*c*x**S(2))) + Ei(S(2)*log(a*c + b*c*x**S(2)))/(b**S(2)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(c*(a + b*x**S(2)))**S(2), x), x, Integral(x**S(2)/log(c*(a + b*x**S(2)))**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(c*(a + b*x**S(2)))**S(2), x), x, (-a/S(2) - b*x**S(2)/S(2))/(b*log(c*(a + b*x**S(2)))) + li(c*(a + b*x**S(2)))/(S(2)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2)))**(S(-2)), x), x, Integral(log(c*(a + b*x**S(2)))**(S(-2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(c*(a + b*x**S(2)))**S(2)), x), x, Integral(S(1)/(x*log(a*c + b*c*x)**S(2)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(c*(a + b*x**S(2)))**S(2)), x), x, Integral(S(1)/(x**S(2)*log(c*(a + b*x**S(2)))**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(c*(a + b*x**S(2)))**S(2)), x), x, Integral(S(1)/(x**S(2)*log(a*c + b*c*x)**S(2)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m/log(c*(a + b*x**S(2)))**S(3), x), x, Integral(x**m/log(c*(a + b*x**S(2)))**S(3), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/log(c*(a + b*x**S(2)))**S(3), x), x, -a*(a + b*x**S(2))/(S(4)*b**S(2)*log(a*c + b*c*x**S(2))) - a*li(a*c + b*c*x**S(2))/(S(4)*b**S(2)*c) - x**S(2)*(a + b*x**S(2))/(S(2)*b*log(a*c + b*c*x**S(2))) - x**S(2)*(a + b*x**S(2))/(S(4)*b*log(a*c + b*c*x**S(2))**S(2)) + Ei(S(2)*log(a*c + b*c*x**S(2)))/(b**S(2)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/log(c*(a + b*x**S(2)))**S(3), x), x, Integral(x**S(2)/log(c*(a + b*x**S(2)))**S(3), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/log(c*(a + b*x**S(2)))**S(3), x), x, (-a/S(4) - b*x**S(2)/S(4))/(b*log(c*(a + b*x**S(2)))) + (-a/S(4) - b*x**S(2)/S(4))/(b*log(c*(a + b*x**S(2)))**S(2)) + li(c*(a + b*x**S(2)))/(S(4)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2)))**(S(-3)), x), x, Integral(log(c*(a + b*x**S(2)))**(S(-3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(c*(a + b*x**S(2)))**S(3)), x), x, Integral(S(1)/(x*log(a*c + b*c*x)**S(3)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*log(c*(a + b*x**S(2)))**S(3)), x), x, Integral(S(1)/(x**S(2)*log(c*(a + b*x**S(2)))**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(3)*log(c*(a + b*x**S(2)))**S(3)), x), x, Integral(S(1)/(x**S(2)*log(a*c + b*c*x)**S(3)), (x, x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(b*x**m + S(1))/x, x), x, -polylog(S(2), -b*x**m)/m, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(b*x**m + S(2))/x, x), x, log(S(2))*log(x) - polylog(S(2), -b*x**m/S(2))/m, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(2)*b*x**m + S(6))/x, x), x, log(S(6))*log(x) - polylog(S(2), -b*x**m/S(3))/m, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**m))/x, x), x, log(c*(a + b*x**m))*log(-b*x**m/a)/m + polylog(S(2), (a + b*x**m)/a)/m, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**m)**n)/x, x), x, n*polylog(S(2), (a + b*x**m)/a)/m + log(c*(a + b*x**m)**n)*log(-b*x**m/a)/m, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**m)**n)**S(2)/x, x), x, -S(2)*n**S(2)*polylog(S(3), (a + b*x**m)/a)/m + S(2)*n*log(c*(a + b*x**m)**n)*polylog(S(2), (a + b*x**m)/a)/m + log(c*(a + b*x**m)**n)**S(2)*log(-b*x**m/a)/m, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**m)**n)**S(3)/x, x), x, S(6)*n**S(3)*polylog(S(4), (a + b*x**m)/a)/m - S(6)*n**S(2)*log(c*(a + b*x**m)**n)*polylog(S(3), (a + b*x**m)/a)/m + S(3)*n*log(c*(a + b*x**m)**n)**S(2)*polylog(S(2), (a + b*x**m)/a)/m + log(c*(a + b*x**m)**n)**S(3)*log(-b*x**m/a)/m, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(d*(b*x + c*x**S(2))**n), x), x, n*x**(m + S(1))*hyper((S(1), m + S(1)), (m + S(2),), -c*x/b)/(m + S(1))**S(2) - S(2)*n*x**(m + S(1))/(m + S(1))**S(2) + x**(m + S(1))*log(d*(b*x + c*x**S(2))**n)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(d*(b*x + c*x**S(2))**n), x), x, b**S(5)*n*log(b + c*x)/(S(5)*c**S(5)) - b**S(4)*n*x/(S(5)*c**S(4)) + b**S(3)*n*x**S(2)/(S(10)*c**S(3)) - b**S(2)*n*x**S(3)/(S(15)*c**S(2)) + b*n*x**S(4)/(S(20)*c) - S(2)*n*x**S(5)/S(25) + x**S(5)*log(d*(b*x + c*x**S(2))**n)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(d*(b*x + c*x**S(2))**n), x), x, -b**S(4)*n*log(b + c*x)/(S(4)*c**S(4)) + b**S(3)*n*x/(S(4)*c**S(3)) - b**S(2)*n*x**S(2)/(S(8)*c**S(2)) + b*n*x**S(3)/(S(12)*c) - n*x**S(4)/S(8) + x**S(4)*log(d*(b*x + c*x**S(2))**n)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(d*(b*x + c*x**S(2))**n), x), x, b**S(3)*n*log(b + c*x)/(S(3)*c**S(3)) - b**S(2)*n*x/(S(3)*c**S(2)) + b*n*x**S(2)/(S(6)*c) - S(2)*n*x**S(3)/S(9) + x**S(3)*log(d*(b*x + c*x**S(2))**n)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(d*(b*x + c*x**S(2))**n), x), x, -b**S(2)*n*log(b + c*x)/(S(2)*c**S(2)) + b*n*x/(S(2)*c) - n*x**S(2)/S(2) + x**S(2)*log(d*(b*x + c*x**S(2))**n)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(b*x + c*x**S(2))**n), x), x, b*n*log(b + c*x)/c - S(2)*n*x + x*log(d*(b*x + c*x**S(2))**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(b*x + c*x**S(2))**n)/x, x), x, -n*log(x)**S(2)/S(2) - n*log(x)*log((b + c*x)/b) - n*polylog(S(2), -c*x/b) + log(x)*log(d*(b*x + c*x**S(2))**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(b*x + c*x**S(2))**n)/x**S(2), x), x, -n/x - log(d*(b*x + c*x**S(2))**n)/x + c*n*log(x)/b - c*n*log(b + c*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(b*x + c*x**S(2))**n)/x**S(3), x), x, -n/(S(4)*x**S(2)) - log(d*(b*x + c*x**S(2))**n)/(S(2)*x**S(2)) - c*n/(S(2)*b*x) - c**S(2)*n*log(x)/(S(2)*b**S(2)) + c**S(2)*n*log(b + c*x)/(S(2)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(b*x + c*x**S(2))**n)/x**S(4), x), x, -n/(S(9)*x**S(3)) - log(d*(b*x + c*x**S(2))**n)/(S(3)*x**S(3)) - c*n/(S(6)*b*x**S(2)) + c**S(2)*n/(S(3)*b**S(2)*x) + c**S(3)*n*log(x)/(S(3)*b**S(3)) - c**S(3)*n*log(b + c*x)/(S(3)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(b*x + c*x**S(2))**n)/x**S(5), x), x, -n/(S(16)*x**S(4)) - log(d*(b*x + c*x**S(2))**n)/(S(4)*x**S(4)) - c*n/(S(12)*b*x**S(3)) + c**S(2)*n/(S(8)*b**S(2)*x**S(2)) - c**S(3)*n/(S(4)*b**S(3)*x) - c**S(4)*n*log(x)/(S(4)*b**S(4)) + c**S(4)*n*log(b + c*x)/(S(4)*b**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(d*(a + b*x + c*x**S(2))**n), x), x, -S(2)*c*n*x**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), -S(2)*c*x/(b + sqrt(-S(4)*a*c + b**S(2))))/((b + sqrt(-S(4)*a*c + b**S(2)))*(m + S(1))*(m + S(2))) - S(2)*c*n*x**(m + S(2))*hyper((S(1), m + S(2)), (m + S(3),), -S(2)*c*x/(b - sqrt(-S(4)*a*c + b**S(2))))/((b - sqrt(-S(4)*a*c + b**S(2)))*(m + S(1))*(m + S(2))) + x**(m + S(1))*log(d*(a + b*x + c*x**S(2))**n)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*log(d*(a + b*x + c*x**S(2))**n), x), x, b*n*x**S(4)/(S(20)*c) + b*n*x**S(2)*(-S(3)*a*c + b**S(2))/(S(10)*c**S(3)) + b*n*(S(5)*a**S(2)*c**S(2) - S(5)*a*b**S(2)*c + b**S(4))*log(a + b*x + c*x**S(2))/(S(10)*c**S(5)) - S(2)*n*x**S(5)/S(25) + x**S(5)*log(d*(a + b*x + c*x**S(2))**n)/S(5) - n*x**S(3)*(-S(2)*a*c/S(15) + b**S(2)/S(15))/c**S(2) + n*x*(-S(2)*a**S(2)*c**S(2)/S(5) + S(4)*a*b**S(2)*c/S(5) - b**S(4)/S(5))/c**S(4) + n*sqrt(-S(4)*a*c + b**S(2))*(a**S(2)*c**S(2)/S(5) - S(3)*a*b**S(2)*c/S(5) + b**S(4)/S(5))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/c**S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(d*(a + b*x + c*x**S(2))**n), x), x, b*n*x**S(3)/(S(12)*c) + b*n*x*(-S(3)*a*c + b**S(2))/(S(4)*c**S(3)) - b*n*sqrt(-S(4)*a*c + b**S(2))*(-S(2)*a*c + b**S(2))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/(S(4)*c**S(4)) - n*x**S(4)/S(8) + x**S(4)*log(d*(a + b*x + c*x**S(2))**n)/S(4) - n*x**S(2)*(-a*c/S(4) + b**S(2)/S(8))/c**S(2) - n*(a**S(2)*c**S(2)/S(4) - a*b**S(2)*c/S(2) + b**S(4)/S(8))*log(a + b*x + c*x**S(2))/c**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(d*(a + b*x + c*x**S(2))**n), x), x, b*n*x**S(2)/(S(6)*c) + b*n*(-S(3)*a*c + b**S(2))*log(a + b*x + c*x**S(2))/(S(6)*c**S(3)) - S(2)*n*x**S(3)/S(9) + x**S(3)*log(d*(a + b*x + c*x**S(2))**n)/S(3) + n*x*(S(2)*a*c/S(3) - b**S(2)/S(3))/c**S(2) + n*sqrt(-S(4)*a*c + b**S(2))*(-a*c/S(3) + b**S(2)/S(3))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/c**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(d*(a + b*x + c*x**S(2))**n), x), x, b*n*x/(S(2)*c) - b*n*sqrt(-S(4)*a*c + b**S(2))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c**S(2)) - n*x**S(2)/S(2) + x**S(2)*log(d*(a + b*x + c*x**S(2))**n)/S(2) - n*(-a*c/S(2) + b**S(2)/S(4))*log(a + b*x + c*x**S(2))/c**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n), x), x, b*n*log(a + b*x + c*x**S(2))/(S(2)*c) - S(2)*n*x + x*log(d*(a + b*x + c*x**S(2))**n) + n*sqrt(-S(4)*a*c + b**S(2))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/x, x), x, -n*log(x)*log((b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(b - sqrt(-S(4)*a*c + b**S(2)))) - n*log(x)*log((b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(b + sqrt(-S(4)*a*c + b**S(2)))) - n*polylog(S(2), -S(2)*c*x/(b - sqrt(-S(4)*a*c + b**S(2)))) - n*polylog(S(2), -S(2)*c*x/(b + sqrt(-S(4)*a*c + b**S(2)))) + log(x)*log(d*(a + b*x + c*x**S(2))**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/x**S(2), x), x, -log(d*(a + b*x + c*x**S(2))**n)/x + b*n*log(x)/a - b*n*log(a + b*x + c*x**S(2))/(S(2)*a) + n*sqrt(-S(4)*a*c + b**S(2))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/x**S(3), x), x, -log(d*(a + b*x + c*x**S(2))**n)/(S(2)*x**S(2)) - b*n/(S(2)*a*x) - b*n*sqrt(-S(4)*a*c + b**S(2))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/(S(2)*a**S(2)) - n*(-a*c + b**S(2)/S(2))*log(x)/a**S(2) + n*(-a*c/S(2) + b**S(2)/S(4))*log(a + b*x + c*x**S(2))/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/x**S(4), x), x, -log(d*(a + b*x + c*x**S(2))**n)/(S(3)*x**S(3)) - b*n/(S(6)*a*x**S(2)) + n*(-S(2)*a*c/S(3) + b**S(2)/S(3))/(a**S(2)*x) + b*n*(-S(3)*a*c + b**S(2))*log(x)/(S(3)*a**S(3)) - b*n*(-S(3)*a*c + b**S(2))*log(a + b*x + c*x**S(2))/(S(6)*a**S(3)) + n*sqrt(-S(4)*a*c + b**S(2))*(-a*c/S(3) + b**S(2)/S(3))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/a**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/x**S(5), x), x, -log(d*(a + b*x + c*x**S(2))**n)/(S(4)*x**S(4)) - b*n/(S(12)*a*x**S(3)) + n*(-a*c/S(4) + b**S(2)/S(8))/(a**S(2)*x**S(2)) - b*n*(-S(3)*a*c + b**S(2))/(S(4)*a**S(3)*x) - b*n*sqrt(-S(4)*a*c + b**S(2))*(-S(2)*a*c + b**S(2))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/(S(4)*a**S(4)) + n*(a**S(2)*c**S(2)/S(4) - a*b**S(2)*c/S(2) + b**S(4)/S(8))*log(a + b*x + c*x**S(2))/a**S(4) - n*(a**S(2)*c**S(2)/S(2) - a*b**S(2)*c + b**S(4)/S(4))*log(x)/a**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**S(2) + x + S(1)), x), x, x*log(x**S(2) + x + S(1)) - S(2)*x + log(x**S(2) + x + S(1))/S(2) + sqrt(S(3))*atan(sqrt(S(3))*(S(2)*x + S(1))/S(3)), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate((d + e*x)**S(4)*log(d*(a + b*x + c*x**S(2))**n), x), x, -S(2)*e**S(4)*n*x**S(5)/S(25) + (d + e*x)**S(5)*log(d*(a + b*x + c*x**S(2))**n)/(S(5)*e) - e**S(3)*n*x**S(4)*(-b*e + S(10)*c*d)/(S(20)*c) - e**S(2)*n*x**S(3)*(b**S(2)*e**S(2) + S(20)*c**S(2)*d**S(2) - c*e*(S(2)*a*e + S(5)*b*d))/(S(15)*c**S(2)) - e*n*x**S(2)*(-b**S(3)*e**S(3) + b*c*e**S(2)*(S(3)*a*e + S(5)*b*d) + S(20)*c**S(3)*d**S(3) - S(10)*c**S(2)*d*e*(a*e + b*d))/(S(10)*c**S(3)) + n*x*(-b**S(4)*e**S(4)/S(5) + b**S(2)*c*e**S(3)*(S(4)*a*e + S(5)*b*d)/S(5) - S(2)*c**S(4)*d**S(4) + S(2)*c**S(3)*d**S(2)*e*(S(2)*a*e + b*d) - c**S(2)*e**S(2)*(S(2)*a**S(2)*e**S(2) + S(15)*a*b*d*e + S(10)*b**S(2)*d**S(2))/S(5))/c**S(4) + n*sqrt(-S(4)*a*c + b**S(2))*(b**S(4)*e**S(4)/S(5) - b**S(2)*c*e**S(3)*(S(3)*a*e + S(5)*b*d)/S(5) + c**S(4)*d**S(4) - S(2)*c**S(3)*d**S(2)*e*(a*e + b*d) + c**S(2)*e**S(2)*(a**S(2)*e**S(2) + S(10)*a*b*d*e + S(10)*b**S(2)*d**S(2))/S(5))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/c**S(5) - n*(-b*e/S(10) + c*d/S(5))*(b**S(4)*e**S(4) - b**S(2)*c*e**S(3)*(S(5)*a*e + S(3)*b*d) + c**S(4)*d**S(4) - S(2)*c**S(3)*d**S(2)*e*(S(5)*a*e + b*d) + c**S(2)*e**S(2)*(S(5)*a**S(2)*e**S(2) + S(10)*a*b*d*e + S(4)*b**S(2)*d**S(2)))*log(a + b*x + c*x**S(2))/(c**S(5)*e), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((d + e*x)**S(3)*log(d*(a + b*x + c*x**S(2))**n), x), x, -e**S(3)*n*x**S(4)/S(8) + (d + e*x)**S(4)*log(d*(a + b*x + c*x**S(2))**n)/(S(4)*e) - e**S(2)*n*x**S(3)*(-b*e + S(8)*c*d)/(S(12)*c) - e*n*x**S(2)*(b**S(2)*e**S(2) + S(12)*c**S(2)*d**S(2) - S(2)*c*e*(a*e + S(2)*b*d))/(S(8)*c**S(2)) + n*x*(b**S(3)*e**S(3)/S(4) - b*c*e**S(2)*(S(3)*a*e + S(4)*b*d)/S(4) - S(2)*c**S(3)*d**S(3) + c**S(2)*d*e*(S(4)*a*e + S(3)*b*d)/S(2))/c**S(3) + n*sqrt(-S(4)*a*c + b**S(2))*(-b*e/S(4) + c*d/S(2))*(b**S(2)*e**S(2) + S(2)*c**S(2)*d**S(2) - S(2)*c*e*(a*e + b*d))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/c**S(4) - n*(b**S(4)*e**S(4)/S(8) - b**S(2)*c*e**S(3)*(a*e + b*d)/S(2) + c**S(4)*d**S(4)/S(4) - c**S(3)*d**S(2)*e*(S(3)*a*e + b*d)/S(2) + c**S(2)*e**S(2)*(a**S(2)*e**S(2) + S(6)*a*b*d*e + S(3)*b**S(2)*d**S(2))/S(4))*log(a + b*x + c*x**S(2))/(c**S(4)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**S(2)*log(d*(a + b*x + c*x**S(2))**n), x), x, -S(2)*e**S(2)*n*x**S(3)/S(9) + (d + e*x)**S(3)*log(d*(a + b*x + c*x**S(2))**n)/(S(3)*e) - e*n*x**S(2)*(-b*e + S(6)*c*d)/(S(6)*c) + n*x*(-b**S(2)*e**S(2)/S(3) - S(2)*c**S(2)*d**S(2) + c*e*(S(2)*a*e + S(3)*b*d)/S(3))/c**S(2) + n*sqrt(-S(4)*a*c + b**S(2))*(b**S(2)*e**S(2)/S(3) + c**S(2)*d**S(2) - c*e*(a*e + S(3)*b*d)/S(3))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/c**S(3) - n*(-b*e/S(6) + c*d/S(3))*(b**S(2)*e**S(2) + c**S(2)*d**S(2) - c*e*(S(3)*a*e + b*d))*log(a + b*x + c*x**S(2))/(c**S(3)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)*log(d*(a + b*x + c*x**S(2))**n), x), x, -e*n*x**S(2)/S(2) + n*x*(b*e/(S(2)*c) - S(2)*d) + (d + e*x)**S(2)*log(d*(a + b*x + c*x**S(2))**n)/(S(2)*e) + n*sqrt(-S(4)*a*c + b**S(2))*(-b*e/S(2) + c*d)*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/c**S(2) - n*(b**S(2)*e**S(2)/S(4) + c**S(2)*d**S(2)/S(2) - c*e*(a*e + b*d)/S(2))*log(a + b*x + c*x**S(2))/(c**S(2)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n), x), x, b*n*log(a + b*x + c*x**S(2))/(S(2)*c) - S(2)*n*x + x*log(d*(a + b*x + c*x**S(2))**n) + n*sqrt(-S(4)*a*c + b**S(2))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/(d + e*x), x), x, -n*log(-e*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c*d - e*(b - sqrt(-S(4)*a*c + b**S(2)))))*log(d + e*x)/e - n*log(-e*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c*d - e*(b + sqrt(-S(4)*a*c + b**S(2)))))*log(d + e*x)/e - n*polylog(S(2), S(2)*c*(d + e*x)/(S(2)*c*d - e*(b + sqrt(-S(4)*a*c + b**S(2)))))/e - n*polylog(S(2), S(2)*c*(d + e*x)/(-b*e + S(2)*c*d + e*sqrt(-S(4)*a*c + b**S(2))))/e + log(d*(a + b*x + c*x**S(2))**n)*log(d + e*x)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/(d + e*x)**S(2), x), x, n*sqrt(-S(4)*a*c + b**S(2))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/(a*e**S(2) - b*d*e + c*d**S(2)) + n*(-b*e/S(2) + c*d)*log(a + b*x + c*x**S(2))/(e*(a*e**S(2) - b*d*e + c*d**S(2))) + n*(b*e - S(2)*c*d)*log(d + e*x)/(e*(a*e**S(2) - b*d*e + c*d**S(2))) - log(d*(a + b*x + c*x**S(2))**n)/(e*(d + e*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/(d + e*x)**S(3), x), x, n*sqrt(-S(4)*a*c + b**S(2))*(-b*e/S(2) + c*d)*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/(a*e**S(2) - b*d*e + c*d**S(2))**S(2) + n*(b**S(2)*e**S(2)/S(4) + c**S(2)*d**S(2)/S(2) - c*e*(a*e + b*d)/S(2))*log(a + b*x + c*x**S(2))/(e*(a*e**S(2) - b*d*e + c*d**S(2))**S(2)) - n*(b**S(2)*e**S(2)/S(2) + c**S(2)*d**S(2) - c*e*(a*e + b*d))*log(d + e*x)/(e*(a*e**S(2) - b*d*e + c*d**S(2))**S(2)) + n*(-b*e/S(2) + c*d)/(e*(d + e*x)*(a*e**S(2) - b*d*e + c*d**S(2))) - log(d*(a + b*x + c*x**S(2))**n)/(S(2)*e*(d + e*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/(d + e*x)**S(4), x), x, n*sqrt(-S(4)*a*c + b**S(2))*(b**S(2)*e**S(2)/S(3) + c**S(2)*d**S(2) - c*e*(a*e + S(3)*b*d)/S(3))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/(a*e**S(2) - b*d*e + c*d**S(2))**S(3) - n*(-b*e/S(3) + S(2)*c*d/S(3))*(b**S(2)*e**S(2) + c**S(2)*d**S(2) - c*e*(S(3)*a*e + b*d))*log(d + e*x)/(e*(a*e**S(2) - b*d*e + c*d**S(2))**S(3)) + n*(-b*e/S(6) + c*d/S(3))*(b**S(2)*e**S(2) + c**S(2)*d**S(2) - c*e*(S(3)*a*e + b*d))*log(a + b*x + c*x**S(2))/(e*(a*e**S(2) - b*d*e + c*d**S(2))**S(3)) + n*(b**S(2)*e**S(2)/S(3) + S(2)*c**S(2)*d**S(2)/S(3) - S(2)*c*e*(a*e + b*d)/S(3))/(e*(d + e*x)*(a*e**S(2) - b*d*e + c*d**S(2))**S(2)) + n*(-b*e/S(6) + c*d/S(3))/(e*(d + e*x)**S(2)*(a*e**S(2) - b*d*e + c*d**S(2))) - log(d*(a + b*x + c*x**S(2))**n)/(S(3)*e*(d + e*x)**S(3)), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/(d + e*x)**S(5), x), x, n*sqrt(-S(4)*a*c + b**S(2))*(-b*e/S(4) + c*d/S(2))*(b**S(2)*e**S(2) + S(2)*c**S(2)*d**S(2) - S(2)*c*e*(a*e + b*d))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/(a*e**S(2) - b*d*e + c*d**S(2))**S(4) + n*(b**S(4)*e**S(4)/S(8) - b**S(2)*c*e**S(3)*(a*e + b*d)/S(2) + c**S(4)*d**S(4)/S(4) - c**S(3)*d**S(2)*e*(S(3)*a*e + b*d)/S(2) + c**S(2)*e**S(2)*(a**S(2)*e**S(2) + S(6)*a*b*d*e + S(3)*b**S(2)*d**S(2))/S(4))*log(a + b*x + c*x**S(2))/(e*(a*e**S(2) - b*d*e + c*d**S(2))**S(4)) - n*(b**S(4)*e**S(4)/S(4) - b**S(2)*c*e**S(3)*(a*e + b*d) + c**S(4)*d**S(4)/S(2) - c**S(3)*d**S(2)*e*(S(3)*a*e + b*d) + c**S(2)*e**S(2)*(a**S(2)*e**S(2) + S(6)*a*b*d*e + S(3)*b**S(2)*d**S(2))/S(2))*log(d + e*x)/(e*(a*e**S(2) - b*d*e + c*d**S(2))**S(4)) + n*(-b*e/S(4) + c*d/S(2))*(b**S(2)*e**S(2) + c**S(2)*d**S(2) - c*e*(S(3)*a*e + b*d))/(e*(d + e*x)*(a*e**S(2) - b*d*e + c*d**S(2))**S(3)) + n*(b**S(2)*e**S(2)/S(8) + c**S(2)*d**S(2)/S(4) - c*e*(a*e + b*d)/S(4))/(e*(d + e*x)**S(2)*(a*e**S(2) - b*d*e + c*d**S(2))**S(2)) + n*(-b*e/S(12) + c*d/S(6))/(e*(d + e*x)**S(3)*(a*e**S(2) - b*d*e + c*d**S(2))) - log(d*(a + b*x + c*x**S(2))**n)/(S(4)*e*(d + e*x)**S(4)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(d*(a + c*x**S(2))**n)/(a*e + c*e*x**S(2)), x), x, S(2)*n*log(S(2)*I*sqrt(a)/(I*sqrt(a) - sqrt(c)*x))*atan(sqrt(c)*x/sqrt(a))/(sqrt(a)*sqrt(c)*e) + I*n*atan(sqrt(c)*x/sqrt(a))**S(2)/(sqrt(a)*sqrt(c)*e) + I*n*polylog(S(2), (-sqrt(a) + I*sqrt(c)*x)/(sqrt(a) + I*sqrt(c)*x))/(sqrt(a)*sqrt(c)*e) + log(d*(a + c*x**S(2))**n)*atan(sqrt(c)*x/sqrt(a))/(sqrt(a)*sqrt(c)*e), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)/(a*e + b*e*x + c*e*x**S(2)), x), x, -S(4)*n*log(S(2)/(-b/sqrt(-S(4)*a*c + b**S(2)) - S(2)*c*x/sqrt(-S(4)*a*c + b**S(2)) + S(1)))*atanh(b/sqrt(-S(4)*a*c + b**S(2)) + S(2)*c*x/sqrt(-S(4)*a*c + b**S(2)))/(e*sqrt(-S(4)*a*c + b**S(2))) + S(2)*n*atanh(b/sqrt(-S(4)*a*c + b**S(2)) + S(2)*c*x/sqrt(-S(4)*a*c + b**S(2)))**S(2)/(e*sqrt(-S(4)*a*c + b**S(2))) - S(2)*n*polylog(S(2), (-b/sqrt(-S(4)*a*c + b**S(2)) - S(2)*c*x/sqrt(-S(4)*a*c + b**S(2)) + S(-1))/(-b/sqrt(-S(4)*a*c + b**S(2)) - S(2)*c*x/sqrt(-S(4)*a*c + b**S(2)) + S(1)))/(e*sqrt(-S(4)*a*c + b**S(2))) - S(2)*log(d*(a + b*x + c*x**S(2))**n)*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/(e*sqrt(-S(4)*a*c + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(g*(a + b*x + c*x**S(2))**n)/(d + e*x**S(2)), x), x, n*log(sqrt(e)*(-b - S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c*sqrt(-d) - sqrt(e)*(b - sqrt(-S(4)*a*c + b**S(2)))))*log(sqrt(e)*x + sqrt(-d))/(S(2)*sqrt(e)*sqrt(-d)) - n*log(sqrt(e)*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c*sqrt(-d) + sqrt(e)*(b - sqrt(-S(4)*a*c + b**S(2)))))*log(-sqrt(e)*x + sqrt(-d))/(S(2)*sqrt(e)*sqrt(-d)) + n*log(sqrt(e)*(-b - S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c*sqrt(-d) - sqrt(e)*(b + sqrt(-S(4)*a*c + b**S(2)))))*log(sqrt(e)*x + sqrt(-d))/(S(2)*sqrt(e)*sqrt(-d)) - n*log(sqrt(e)*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c*sqrt(-d) + sqrt(e)*(b + sqrt(-S(4)*a*c + b**S(2)))))*log(-sqrt(e)*x + sqrt(-d))/(S(2)*sqrt(e)*sqrt(-d)) + n*polylog(S(2), S(2)*c*(sqrt(e)*x + sqrt(-d))/(S(2)*c*sqrt(-d) - sqrt(e)*(b - sqrt(-S(4)*a*c + b**S(2)))))/(S(2)*sqrt(e)*sqrt(-d)) - n*polylog(S(2), S(2)*c*(-sqrt(e)*x + sqrt(-d))/(S(2)*c*sqrt(-d) + sqrt(e)*(b - sqrt(-S(4)*a*c + b**S(2)))))/(S(2)*sqrt(e)*sqrt(-d)) + n*polylog(S(2), S(2)*c*(sqrt(e)*x + sqrt(-d))/(S(2)*c*sqrt(-d) - sqrt(e)*(b + sqrt(-S(4)*a*c + b**S(2)))))/(S(2)*sqrt(e)*sqrt(-d)) - n*polylog(S(2), S(2)*c*(-sqrt(e)*x + sqrt(-d))/(S(2)*c*sqrt(-d) + sqrt(e)*(b + sqrt(-S(4)*a*c + b**S(2)))))/(S(2)*sqrt(e)*sqrt(-d)) + log(g*(a + b*x + c*x**S(2))**n)*log(-sqrt(e)*x + sqrt(-d))/(S(2)*sqrt(e)*sqrt(-d)) - log(g*(a + b*x + c*x**S(2))**n)*log(sqrt(e)*x + sqrt(-d))/(S(2)*sqrt(e)*sqrt(-d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(g*(a + b*x + c*x**S(2))**n)/(d + e*x + f*x**S(2)), x), x, -n*log(f*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(-c*(e - sqrt(-S(4)*d*f + e**S(2))) + f*(b + sqrt(-S(4)*a*c + b**S(2)))))*log(e + S(2)*f*x - sqrt(-S(4)*d*f + e**S(2)))/sqrt(-S(4)*d*f + e**S(2)) + n*log(f*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(-c*(e + sqrt(-S(4)*d*f + e**S(2))) + f*(b - sqrt(-S(4)*a*c + b**S(2)))))*log(e + S(2)*f*x + sqrt(-S(4)*d*f + e**S(2)))/sqrt(-S(4)*d*f + e**S(2)) + n*log(f*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(-c*(e + sqrt(-S(4)*d*f + e**S(2))) + f*(b + sqrt(-S(4)*a*c + b**S(2)))))*log(e + S(2)*f*x + sqrt(-S(4)*d*f + e**S(2)))/sqrt(-S(4)*d*f + e**S(2)) - n*log(-f*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(-b*f + c*e - c*sqrt(-S(4)*d*f + e**S(2)) + f*sqrt(-S(4)*a*c + b**S(2))))*log(e + S(2)*f*x - sqrt(-S(4)*d*f + e**S(2)))/sqrt(-S(4)*d*f + e**S(2)) - n*polylog(S(2), -c*(e + S(2)*f*x - sqrt(-S(4)*d*f + e**S(2)))/(-c*(e - sqrt(-S(4)*d*f + e**S(2))) + f*(b - sqrt(-S(4)*a*c + b**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) - n*polylog(S(2), -c*(e + S(2)*f*x - sqrt(-S(4)*d*f + e**S(2)))/(-c*(e - sqrt(-S(4)*d*f + e**S(2))) + f*(b + sqrt(-S(4)*a*c + b**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + n*polylog(S(2), -c*(e + S(2)*f*x + sqrt(-S(4)*d*f + e**S(2)))/(-c*(e + sqrt(-S(4)*d*f + e**S(2))) + f*(b - sqrt(-S(4)*a*c + b**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + n*polylog(S(2), -c*(e + S(2)*f*x + sqrt(-S(4)*d*f + e**S(2)))/(-c*(e + sqrt(-S(4)*d*f + e**S(2))) + f*(b + sqrt(-S(4)*a*c + b**S(2)))))/sqrt(-S(4)*d*f + e**S(2)) + log(g*(a + b*x + c*x**S(2))**n)*log(e + S(2)*f*x - sqrt(-S(4)*d*f + e**S(2)))/sqrt(-S(4)*d*f + e**S(2)) - log(g*(a + b*x + c*x**S(2))**n)*log(e + S(2)*f*x + sqrt(-S(4)*d*f + e**S(2)))/sqrt(-S(4)*d*f + e**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(b*x + c*x**S(2))**n)**S(2), x), x, -S(2)*b*n**S(2)*log(-c*x/b)*log(b + c*x)/c - b*n**S(2)*log(b + c*x)**S(2)/c - S(4)*b*n**S(2)*log(b + c*x)/c - S(2)*b*n**S(2)*polylog(S(2), (b + c*x)/b)/c + S(2)*b*n*log(d*(b*x + c*x**S(2))**n)*log(b + c*x)/c + S(8)*n**S(2)*x - S(4)*n*x*log(d*(b*x + c*x**S(2))**n) + x*log(d*(b*x + c*x**S(2))**n)**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x + c*x**S(2))**n)**S(2), x), x, -S(2)*b*n**S(2)*log(a + b*x + c*x**S(2))/c + S(8)*n**S(2)*x - S(4)*n*x*log(d*(a + b*x + c*x**S(2))**n) + x*log(d*(a + b*x + c*x**S(2))**n)**S(2) - n**S(2)*(b - sqrt(-S(4)*a*c + b**S(2)))*log((b/S(2) + c*x + sqrt(-S(4)*a*c + b**S(2))/S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/c - n**S(2)*(b - sqrt(-S(4)*a*c + b**S(2)))*log(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))**S(2)/(S(2)*c) - n**S(2)*(b - sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), (-b/S(2) - c*x + sqrt(-S(4)*a*c + b**S(2))/S(2))/sqrt(-S(4)*a*c + b**S(2)))/c - n**S(2)*(b + sqrt(-S(4)*a*c + b**S(2)))*log((-b/S(2) - c*x + sqrt(-S(4)*a*c + b**S(2))/S(2))/sqrt(-S(4)*a*c + b**S(2)))*log(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/c - n**S(2)*(b + sqrt(-S(4)*a*c + b**S(2)))*log(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))**S(2)/(S(2)*c) - n**S(2)*(b + sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), (b/S(2) + c*x + sqrt(-S(4)*a*c + b**S(2))/S(2))/sqrt(-S(4)*a*c + b**S(2)))/c - S(4)*n**S(2)*sqrt(-S(4)*a*c + b**S(2))*atanh((b + S(2)*c*x)/sqrt(-S(4)*a*c + b**S(2)))/c + n*(b - sqrt(-S(4)*a*c + b**S(2)))*log(d*(a + b*x + c*x**S(2))**n)*log(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/c + n*(b + sqrt(-S(4)*a*c + b**S(2)))*log(d*(a + b*x + c*x**S(2))**n)*log(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(x**S(2) + x + S(1))/(x**S(2) + S(3)*x + S(2)), x), x, x*log(x**S(2) + x + S(1)) - S(2)*x - log((-S(2)*x + S(-1) - sqrt(S(3))*I)/(S(1) - sqrt(S(3))*I))*log(S(2)*x + S(2)) - log((-S(2)*x + S(-1) + sqrt(S(3))*I)/(S(1) + sqrt(S(3))*I))*log(S(2)*x + S(2)) + S(4)*log((-S(2)*x + S(-1) - sqrt(S(3))*I)/(S(3) - sqrt(S(3))*I))*log(S(2)*x + S(4)) + S(4)*log((-S(2)*x + S(-1) + sqrt(S(3))*I)/(S(3) + sqrt(S(3))*I))*log(S(2)*x + S(4)) + log(S(2)*x + S(2))*log(x**S(2) + x + S(1)) - S(4)*log(S(2)*x + S(4))*log(x**S(2) + x + S(1)) + log(x**S(2) + x + S(1))/S(2) + sqrt(S(3))*atan(sqrt(S(3))*(S(2)*x + S(1))/S(3)) - polylog(S(2), (S(2)*x + S(2))/(S(1) - sqrt(S(3))*I)) - polylog(S(2), (S(2)*x + S(2))/(S(1) + sqrt(S(3))*I)) + S(4)*polylog(S(2), (S(2)*x + S(4))/(S(3) - sqrt(S(3))*I)) + S(4)*polylog(S(2), (S(2)*x + S(4))/(S(3) + sqrt(S(3))*I)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**S(2) + x + S(1))**S(2), x), x, x*log(x**S(2) + x + S(1))**S(2) - S(4)*x*log(x**S(2) + x + S(1)) + S(8)*x - (S(1) + sqrt(S(3))*I)*log(sqrt(S(3))*I*(S(2)*x + S(1) - sqrt(S(3))*I)/S(6))*log(S(2)*x + S(1) + sqrt(S(3))*I) - (S(1) - sqrt(S(3))*I)*log(-sqrt(S(3))*I*(S(2)*x + S(1) + sqrt(S(3))*I)/S(6))*log(S(2)*x + S(1) - sqrt(S(3))*I) - (S(1) - sqrt(S(3))*I)*log(S(2)*x + S(1) - sqrt(S(3))*I)**S(2)/S(2) + (S(1) - sqrt(S(3))*I)*log(S(2)*x + S(1) - sqrt(S(3))*I)*log(x**S(2) + x + S(1)) - (S(1) + sqrt(S(3))*I)*log(S(2)*x + S(1) + sqrt(S(3))*I)**S(2)/S(2) + (S(1) + sqrt(S(3))*I)*log(S(2)*x + S(1) + sqrt(S(3))*I)*log(x**S(2) + x + S(1)) - S(2)*log(x**S(2) + x + S(1)) - S(4)*sqrt(S(3))*atan(sqrt(S(3))*(S(2)*x + S(1))/S(3)) - (S(1) - sqrt(S(3))*I)*polylog(S(2), sqrt(S(3))*I*(S(2)*x + S(1) - sqrt(S(3))*I)/S(6)) - (S(1) + sqrt(S(3))*I)*polylog(S(2), -sqrt(S(3))*I*(S(2)*x + S(1) + sqrt(S(3))*I)/S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**S(2) + x + S(-1))**S(2)/x**S(3), x), x, S(3)*log(x)*log((S(2)*x + S(1) + sqrt(S(5)))/(S(1) + sqrt(S(5)))) - S(3)*log(x)*log(x**S(2) + x + S(-1)) + log(x) - (sqrt(S(5)) + S(3))*log(sqrt(S(5))*(x + S(1)/2 + sqrt(S(5))/S(2))/S(5))*log(S(2)*x - sqrt(S(5)) + S(1))/S(2) - (-sqrt(S(5)) + S(3))*log(S(2)*x + S(1) + sqrt(S(5)))**S(2)/S(4) + (-sqrt(S(5)) + S(3))*log(S(2)*x + S(1) + sqrt(S(5)))*log(x**S(2) + x + S(-1))/S(2) - (-sqrt(S(5)) + S(1))*log(S(2)*x + S(1) + sqrt(S(5)))/S(2) - (sqrt(S(5)) + S(3))*log(S(2)*x - sqrt(S(5)) + S(1))**S(2)/S(4) + (sqrt(S(5)) + S(3))*log(S(2)*x - sqrt(S(5)) + S(1))*log(x**S(2) + x + S(-1))/S(2) - (S(1) + sqrt(S(5)))*log(S(2)*x - sqrt(S(5)) + S(1))/S(2) + S(3)*log(S(-1)/2 + sqrt(S(5))/S(2))*log(S(2)*x - sqrt(S(5)) + S(1)) - (-sqrt(S(5)) + S(3))*log(S(2)*sqrt(S(5)))*log(S(2)*x - sqrt(S(5)) + S(1))/S(2) - (sqrt(S(5)) + S(3))*polylog(S(2), sqrt(S(5))*(-x + S(-1)/2 + sqrt(S(5))/S(2))/S(5))/S(2) + (-sqrt(S(5)) + S(3))*polylog(S(2), sqrt(S(5))*(-x + S(-1)/2 + sqrt(S(5))/S(2))/S(5))/S(2) + S(3)*polylog(S(2), -S(2)*x/(S(1) + sqrt(S(5)))) - S(3)*polylog(S(2), (S(2)*x - sqrt(S(5)) + S(1))/(-sqrt(S(5)) + S(1))) + log(x**S(2) + x + S(-1))/x - log(x**S(2) + x + S(-1))**S(2)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1)), x), x, x**S(4)*log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/S(4) - x**S(4)/S(32) + x**S(3)/S(192) - x**S(2)/S(1024) - x*(x**S(2) - x)**(S(3)/2)/S(32) + x/S(4096) + (-S(149)*x/S(1024) + S(149)/2048)*sqrt(x**S(2) - x) - (x**S(2) - x)**(S(3)/2)/S(12) - S(683)*sqrt(x**S(2) - x)/S(4096) - log(S(8)*x + S(1))/S(32768) - S(1537)*atanh(x/sqrt(x**S(2) - x))/S(16384) + atanh((-S(5)*x/S(3) + S(1)/6)/sqrt(x**S(2) - x))/S(32768), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1)), x), x, x**S(3)*log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/S(3) - x**S(3)/S(18) + x**S(2)/S(96) - x/S(384) + (-S(5)*x/S(32) + S(5)/64)*sqrt(x**S(2) - x) - (x**S(2) - x)**(S(3)/2)/S(18) - S(85)*sqrt(x**S(2) - x)/S(384) + log(S(8)*x + S(1))/S(3072) - S(223)*atanh(x/sqrt(x**S(2) - x))/S(1536) - atanh((-S(5)*x/S(3) + S(1)/6)/sqrt(x**S(2) - x))/S(3072), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1)), x), x, x**S(2)*log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/S(2) - x**S(2)/S(8) + x/S(32) + (-x/S(8) + S(1)/16)*sqrt(x**S(2) - x) - S(11)*sqrt(x**S(2) - x)/S(32) - log(S(8)*x + S(1))/S(256) - S(33)*atanh(x/sqrt(x**S(2) - x))/S(128) + atanh((-S(5)*x/S(3) + S(1)/6)/sqrt(x**S(2) - x))/S(256), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1)), x), x, x*log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1)) - x/S(2) - sqrt(x**S(2) - x)/S(2) + log(S(8)*x + S(1))/S(16) - S(7)*atanh(x/sqrt(x**S(2) - x))/S(8) - atanh((-S(5)*x/S(3) + S(1)/6)/sqrt(x**S(2) - x))/S(16), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1))/x, x), x, Integral(log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1))/x**S(2), x), x, S(4)*log(x) - S(4)*log(S(8)*x + S(1)) + S(4)*atanh((-S(5)*x/S(3) + S(1)/6)/sqrt(x**S(2) - x)) + S(4)*sqrt(x**S(2) - x)/x - log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1))/x**S(3), x), x, -S(16)*log(x) + S(16)*log(S(8)*x + S(1)) - S(16)*atanh((-S(5)*x/S(3) + S(1)/6)/sqrt(x**S(2) - x)) - S(10)*sqrt(x**S(2) - x)/x - S(2)/x - log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/(S(2)*x**S(2)) - S(2)*(x**S(2) - x)**(S(3)/2)/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(S(3)/2)*log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1)), x), x, S(2)*x**(S(5)/2)*log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/S(5) - S(2)*x**(S(5)/2)/S(25) + x**(S(3)/2)/S(60) - sqrt(x)/S(160) + sqrt(S(2))*atan(S(2)*sqrt(S(2))*sqrt(x))/S(640) - S(2)*(x**S(2) - x)**(S(3)/2)/(S(25)*sqrt(x)) - S(127)*sqrt(x**S(2) - x)/(S(480)*sqrt(x)) - sqrt(S(2))*sqrt(x**S(2) - x)*atan(S(2)*sqrt(S(2))*sqrt(x + S(-1))/S(3))/(S(640)*sqrt(x)*sqrt(x + S(-1))) - (-S(2)*x/S(15) + S(2)/15)*sqrt(x**S(2) - x)/(sqrt(x)*(sqrt(x) + S(1))) - (-S(2)*x/S(15) + S(2)/15)*sqrt(x**S(2) - x)/(sqrt(x)*(-sqrt(x) + S(1))) - S(71)*(x**S(2) - x)**(S(3)/2)/(S(300)*x**(S(3)/2)), expand=True, _diff=True, _numerical=True)

    # failing due to apart assert rubi_test(rubi_integrate(sqrt(x)*log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1)), x), x, S(2)*x**(S(3)/2)*log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/S(3) - S(2)*x**(S(3)/2)/S(9) + sqrt(x)/S(12) - sqrt(S(2))*atan(S(2)*sqrt(S(2))*sqrt(x))/S(48) - S(17)*sqrt(x**S(2) - x)/(S(36)*sqrt(x)) + sqrt(S(2))*sqrt(x**S(2) - x)*atan(S(2)*sqrt(S(2))*sqrt(x + S(-1))/S(3))/(S(48)*sqrt(x)*sqrt(x + S(-1))) - (-S(2)*x/S(9) + S(2)/9)*sqrt(x**S(2) - x)/(sqrt(x)*(sqrt(x) + S(1))) - (-S(2)*x/S(9) + S(2)/9)*sqrt(x**S(2) - x)/(sqrt(x)*(-sqrt(x) + S(1))) - S(2)*(x**S(2) - x)**(S(3)/2)/(S(9)*x**(S(3)/2)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1))/sqrt(x), x), x, S(2)*sqrt(x)*log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1)) - S(2)*sqrt(x) + sqrt(S(2))*atan(S(2)*sqrt(S(2))*sqrt(x))/S(2) - S(2)*sqrt(x**S(2) - x)/(S(3)*sqrt(x)) - sqrt(S(2))*sqrt(x**S(2) - x)*atan(S(2)*sqrt(S(2))*sqrt(x + S(-1))/S(3))/(S(2)*sqrt(x)*sqrt(x + S(-1))) - (-S(2)*x/S(3) + S(2)/3)*sqrt(x**S(2) - x)/(sqrt(x)*(sqrt(x) + S(1))) - (-S(2)*x/S(3) + S(2)/3)*sqrt(x**S(2) - x)/(sqrt(x)*(-sqrt(x) + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1))/x**(S(3)/2), x), x, S(4)*sqrt(S(2))*atan(S(2)*sqrt(S(2))*sqrt(x)) - S(8)*atan(sqrt(x)/sqrt(x**S(2) - x)) - S(4)*sqrt(x**S(2) - x)/(S(3)*sqrt(x)) - S(2)*log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/sqrt(x) - S(4)*sqrt(S(2))*sqrt(x**S(2) - x)*atan(S(2)*sqrt(S(2))*sqrt(x + S(-1))/S(3))/(sqrt(x)*sqrt(x + S(-1))) + (-S(2)*x/S(3) + S(2)/3)*sqrt(x**S(2) - x)/(sqrt(x)*(sqrt(x) + S(1))) + (-S(2)*x/S(3) + S(2)/3)*sqrt(x**S(2) - x)/(sqrt(x)*(-sqrt(x) + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(4)*x + S(4)*sqrt(x*(x + S(-1))) + S(-1))/x**(S(5)/2), x), x, -S(32)*sqrt(S(2))*atan(S(2)*sqrt(S(2))*sqrt(x))/S(3) + S(44)*atan(sqrt(x)/sqrt(x**S(2) - x))/S(3) - S(4)*sqrt(x**S(2) - x)/(S(9)*sqrt(x)) - S(16)/(S(3)*sqrt(x)) + S(32)*sqrt(S(2))*sqrt(x**S(2) - x)*atan(S(2)*sqrt(S(2))*sqrt(x + S(-1))/S(3))/(S(3)*sqrt(x)*sqrt(x + S(-1))) + (-S(2)*x/S(9) + S(2)/9)*sqrt(x**S(2) - x)/(sqrt(x)*(sqrt(x) + S(1))) + (-S(2)*x/S(9) + S(2)/9)*sqrt(x**S(2) - x)/(sqrt(x)*(-sqrt(x) + S(1))) + S(4)*sqrt(x**S(2) - x)/(S(3)*x**(S(3)/2)) - S(2)*log(S(4)*x + S(4)*sqrt(x**S(2) - x) + S(-1))/(S(3)*x**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((a + x)/x)/x, x), x, polylog(S(2), -a/x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((a + x**S(2))/x**S(2))/x, x), x, polylog(S(2), -a/x**S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**(-n)*(a + x**n))/x, x), x, polylog(S(2), -a*x**(-n))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((a + b*x)/x)/x, x), x, -log(-a/(b*x))*log(a/x + b) - polylog(S(2), (a/x + b)/b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((a + b*x**S(2))/x**S(2))/x, x), x, -log(-a/(b*x**S(2)))*log(a/x**S(2) + b)/S(2) - polylog(S(2), (a/x**S(2) + b)/b)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**(-n)*(a + b*x**n))/x, x), x, -log(-a*x**(-n)/b)*log(a*x**(-n) + b)/n - polylog(S(2), (a*x**(-n) + b)/b)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((a + b*x)/x)/(c + d*x), x), x, log((a + b*x)/x)*log(c + d*x)/d + log(-d*x/c)*log(c + d*x)/d - log(-d*(a + b*x)/(-a*d + b*c))*log(c + d*x)/d + polylog(S(2), (c + d*x)/c)/d - polylog(S(2), b*(c + d*x)/(-a*d + b*c))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((a + b*x**S(2))/x**S(2))/(c + d*x), x), x, S(2)*log(-d*x/c)*log(c + d*x)/d - log(-d*(sqrt(b)*x + sqrt(-a))/(sqrt(b)*c - d*sqrt(-a)))*log(c + d*x)/d - log(d*(-sqrt(b)*x + sqrt(-a))/(sqrt(b)*c + d*sqrt(-a)))*log(c + d*x)/d + log(c + d*x)*log(a/x**S(2) + b)/d + S(2)*polylog(S(2), (c + d*x)/c)/d - polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c - d*sqrt(-a)))/d - polylog(S(2), sqrt(b)*(c + d*x)/(sqrt(b)*c + d*sqrt(-a)))/d, expand=True, _diff=True, _numerical=True)

    # recursion sympy and mathematica assert rubi_test(rubi_integrate(log(x**(-n)*(a + b*x**n))/(c + d*x), x), x, a*n*Integral(log(c + d*x)/(x*(a + b*x**n)), x)/d + log(c + d*x)*log(a*x**(-n) + b)/d, expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(4), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)**S(4)/b + n**S(4)*(-S(24)*a*d + S(24)*b*c)*polylog(S(4), d*(a + b*x)/(b*(c + d*x)))/(b*d) - n**S(3)*(-S(24)*a*d + S(24)*b*c)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n**S(2)*(-S(12)*a*d + S(12)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n*(-S(4)*a*d + S(4)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(3)*log((-a*d + b*c)/(b*(c + d*x)))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(3), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)**S(3)/b - n**S(3)*(-S(6)*a*d + S(6)*b*c)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n**S(2)*(-S(6)*a*d + S(6)*b*c)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n*(-S(3)*a*d + S(3)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(2), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)**S(2)/b + n**S(2)*(-S(2)*a*d + S(2)*b*c)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n*(-S(2)*a*d + S(2)*b*c)*log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)/b - n*(-a*d + b*c)*log(c + d*x)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/log(e*((a + b*x)/(c + d*x))**n), x), x, Integral(S(1)/log(e*((a + b*x)/(c + d*x))**n), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**(S(-2)), x), x, Integral(log(e*((a + b*x)/(c + d*x))**n)**(S(-2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(3)/x, x), x, S(6)*n**S(3)*polylog(S(4), c*(a + b*x)/(a*(c + d*x))) - S(6)*n**S(3)*polylog(S(4), d*(a + b*x)/(b*(c + d*x))) - S(6)*n**S(2)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(3), c*(a + b*x)/(a*(c + d*x))) + S(6)*n**S(2)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(3), d*(a + b*x)/(b*(c + d*x))) + S(3)*n*log(e*((a + b*x)/(c + d*x))**n)**S(2)*polylog(S(2), c*(a + b*x)/(a*(c + d*x))) - S(3)*n*log(e*((a + b*x)/(c + d*x))**n)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x))) - log(e*((a + b*x)/(c + d*x))**n)**S(3)*log((-a*d + b*c)/(b*(c + d*x))) + log(e*((a + b*x)/(c + d*x))**n)**S(3)*log(x*(a*d - b*c)/(a*(c + d*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(2)/x, x), x, -S(2)*n**S(2)*polylog(S(3), c*(a + b*x)/(a*(c + d*x))) + S(2)*n**S(2)*polylog(S(3), d*(a + b*x)/(b*(c + d*x))) + S(2)*n*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), c*(a + b*x)/(a*(c + d*x))) - S(2)*n*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), d*(a + b*x)/(b*(c + d*x))) - log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((-a*d + b*c)/(b*(c + d*x))) + log(e*((a + b*x)/(c + d*x))**n)**S(2)*log(x*(a*d - b*c)/(a*(c + d*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/x, x), x, -n*log(x)*log((a + b*x)/a) + n*log(x)*log((c + d*x)/c) - n*polylog(S(2), -b*x/a) + n*polylog(S(2), -d*x/c) + log(x)*log(e*((a + b*x)/(c + d*x))**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(e*((a + b*x)/(c + d*x))**n)), x), x, Integral(S(1)/(x*log(e*((a + b*x)/(c + d*x))**n)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(e*((a + b*x)/(c + d*x))**n)**S(2)), x), x, Integral(S(1)/(x*log(e*((a + b*x)/(c + d*x))**n)**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*log(e*(a + b*x)/(c + d*x)), x), x, -x*(-a*d + b*c)**S(3)/(S(4)*d**S(3)) + (a + b*x)**S(4)*log(e*(a + b*x)/(c + d*x))/(S(4)*b) - (a + b*x)**S(3)*(-a*d/S(12) + b*c/S(12))/(b*d) + (a + b*x)**S(2)*(-a*d + b*c)**S(2)/(S(8)*b*d**S(2)) + (-a*d + b*c)**S(4)*log(c + d*x)/(S(4)*b*d**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x)), x), x, x*(-a*d + b*c)**S(2)/(S(3)*d**S(2)) + (a + b*x)**S(3)*log(e*(a + b*x)/(c + d*x))/(S(3)*b) - (a + b*x)**S(2)*(-a*d/S(6) + b*c/S(6))/(b*d) - (-a*d + b*c)**S(3)*log(c + d*x)/(S(3)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*log(e*(a + b*x)/(c + d*x)), x), x, x*(a*d/S(2) - b*c/S(2))/d + (a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))/(S(2)*b) + (-a*d + b*c)**S(2)*log(c + d*x)/(S(2)*b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x)), x), x, (a + b*x)*log(e*(a + b*x)/(c + d*x))/b - (-a*d + b*c)*log(c + d*x)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))/(a + b*x), x), x, -log((a*d - b*c)/(d*(a + b*x)))*log(e*(a + b*x)/(c + d*x))/b + polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))/(a + b*x)**S(2), x), x, -(c + d*x)*log(e*(a + b*x)/(c + d*x))/((a + b*x)*(-a*d + b*c)) - S(1)/(b*(a + b*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))/(a + b*x)**S(3), x), x, d**S(2)*log(a + b*x)/(S(2)*b*(-a*d + b*c)**S(2)) - d**S(2)*log(c + d*x)/(S(2)*b*(-a*d + b*c)**S(2)) + d/(S(2)*b*(a + b*x)*(-a*d + b*c)) - log(e*(a + b*x)/(c + d*x))/(S(2)*b*(a + b*x)**S(2)) - S(1)/(S(4)*b*(a + b*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*log(e*(a + b*x)/(c + d*x))**S(2), x), x, -S(5)*x*(-a*d + b*c)**S(3)/(S(12)*d**S(3)) + (a + b*x)**S(4)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(4)*b) - (a + b*x)**S(3)*(-a*d/S(6) + b*c/S(6))*log(e*(a + b*x)/(c + d*x))/(b*d) + (a + b*x)**S(2)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))/(S(4)*b*d**S(2)) + (a + b*x)**S(2)*(-a*d + b*c)**S(2)/(S(12)*b*d**S(2)) - (a + b*x)*(-a*d + b*c)**S(3)*log(e*(a + b*x)/(c + d*x))/(S(2)*b*d**S(3)) - (-a*d + b*c)**S(4)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))/(S(2)*b*d**S(4)) + S(11)*(-a*d + b*c)**S(4)*log(c + d*x)/(S(12)*b*d**S(4)) - (-a*d + b*c)**S(4)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(S(2)*b*d**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2), x), x, x*(-a*d + b*c)**S(2)/(S(3)*d**S(2)) + (a + b*x)**S(3)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(3)*b) - (a + b*x)**S(2)*(-a*d/S(3) + b*c/S(3))*log(e*(a + b*x)/(c + d*x))/(b*d) + S(2)*(a + b*x)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))/(S(3)*b*d**S(2)) + S(2)*(-a*d + b*c)**S(3)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))/(S(3)*b*d**S(3)) - (-a*d + b*c)**S(3)*log(c + d*x)/(b*d**S(3)) + S(2)*(-a*d + b*c)**S(3)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(S(3)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*log(e*(a + b*x)/(c + d*x))**S(2), x), x, (a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(2)*b) + (a + b*x)*(a*d - b*c)*log(e*(a + b*x)/(c + d*x))/(b*d) - (-a*d + b*c)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))/(b*d**S(2)) + (-a*d + b*c)**S(2)*log(c + d*x)/(b*d**S(2)) - (-a*d + b*c)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(2), x), x, (a + b*x)*log(e*(a + b*x)/(c + d*x))**S(2)/b + (-S(2)*a*d + S(2)*b*c)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))/(b*d) + (-S(2)*a*d + S(2)*b*c)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(2)/(a + b*x), x), x, -log((a*d - b*c)/(d*(a + b*x)))*log(e*(a + b*x)/(c + d*x))**S(2)/b + S(2)*log(e*(a + b*x)/(c + d*x))*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/b + S(2)*polylog(S(3), b*(c + d*x)/(d*(a + b*x)))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(2)/(a + b*x)**S(2), x), x, -(c + d*x)*log(e*(a + b*x)/(c + d*x))**S(2)/((a + b*x)*(-a*d + b*c)) - (S(2)*c + S(2)*d*x)*log(e*(a + b*x)/(c + d*x))/((a + b*x)*(-a*d + b*c)) - S(2)/(b*(a + b*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(2)/(a + b*x)**S(3), x), x, -b*(c + d*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(2)*(a + b*x)**S(2)*(-a*d + b*c)**S(2)) - b*(c + d*x)**S(2)*log(e*(a + b*x)/(c + d*x))/(S(2)*(a + b*x)**S(2)*(-a*d + b*c)**S(2)) - b*(c + d*x)**S(2)/(S(4)*(a + b*x)**S(2)*(-a*d + b*c)**S(2)) + d*(c + d*x)*log(e*(a + b*x)/(c + d*x))**S(2)/((a + b*x)*(-a*d + b*c)**S(2)) + S(2)*d*(c + d*x)*log(e*(a + b*x)/(c + d*x))/((a + b*x)*(-a*d + b*c)**S(2)) + S(2)*d/(b*(a + b*x)*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(3), x), x, (a + b*x)**S(3)*log(e*(a + b*x)/(c + d*x))**S(3)/(S(3)*b) - (a + b*x)**S(2)*(-a*d/S(2) + b*c/S(2))*log(e*(a + b*x)/(c + d*x))**S(2)/(b*d) + (a + b*x)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2)/(b*d**S(2)) + (a + b*x)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))/(b*d**S(2)) + (-a*d + b*c)**S(3)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))**S(2)/(b*d**S(3)) + S(3)*(-a*d + b*c)**S(3)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))/(b*d**S(3)) + S(2)*(-a*d + b*c)**S(3)*log(e*(a + b*x)/(c + d*x))*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d**S(3)) - (-a*d + b*c)**S(3)*log(c + d*x)/(b*d**S(3)) + S(3)*(-a*d + b*c)**S(3)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d**S(3)) - S(2)*(-a*d + b*c)**S(3)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*log(e*(a + b*x)/(c + d*x))**S(3), x), x, (a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(3)/(S(2)*b) - (a + b*x)*(-S(3)*a*d/S(2) + S(3)*b*c/S(2))*log(e*(a + b*x)/(c + d*x))**S(2)/(b*d) - S(3)*(-a*d + b*c)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))**S(2)/(S(2)*b*d**S(2)) - S(3)*(-a*d + b*c)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))/(b*d**S(2)) - S(3)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d**S(2)) - S(3)*(-a*d + b*c)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d**S(2)) + S(3)*(-a*d + b*c)**S(2)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(3), x), x, (a + b*x)*log(e*(a + b*x)/(c + d*x))**S(3)/b + (-S(6)*a*d + S(6)*b*c)*log(e*(a + b*x)/(c + d*x))*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) - (-S(6)*a*d + S(6)*b*c)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d) + (-S(3)*a*d + S(3)*b*c)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))**S(2)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(3)/(a + b*x), x), x, -log((a*d - b*c)/(d*(a + b*x)))*log(e*(a + b*x)/(c + d*x))**S(3)/b + S(3)*log(e*(a + b*x)/(c + d*x))**S(2)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/b + S(6)*log(e*(a + b*x)/(c + d*x))*polylog(S(3), b*(c + d*x)/(d*(a + b*x)))/b + S(6)*polylog(S(4), b*(c + d*x)/(d*(a + b*x)))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(3)/(a + b*x)**S(2), x), x, -(c + d*x)*log(e*(a + b*x)/(c + d*x))**S(3)/((a + b*x)*(-a*d + b*c)) - (S(3)*c + S(3)*d*x)*log(e*(a + b*x)/(c + d*x))**S(2)/((a + b*x)*(-a*d + b*c)) - (S(6)*c + S(6)*d*x)*log(e*(a + b*x)/(c + d*x))/((a + b*x)*(-a*d + b*c)) - S(6)/(b*(a + b*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(3)/(a + b*x)**S(3), x), x, -b*(c + d*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(3)/(S(2)*(a + b*x)**S(2)*(-a*d + b*c)**S(2)) - S(3)*b*(c + d*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(4)*(a + b*x)**S(2)*(-a*d + b*c)**S(2)) - S(3)*b*(c + d*x)**S(2)*log(e*(a + b*x)/(c + d*x))/(S(4)*(a + b*x)**S(2)*(-a*d + b*c)**S(2)) - S(3)*b*(c + d*x)**S(2)/(S(8)*(a + b*x)**S(2)*(-a*d + b*c)**S(2)) + d*(c + d*x)*log(e*(a + b*x)/(c + d*x))**S(3)/((a + b*x)*(-a*d + b*c)**S(2)) + S(3)*d*(c + d*x)*log(e*(a + b*x)/(c + d*x))**S(2)/((a + b*x)*(-a*d + b*c)**S(2)) + S(6)*d*(c + d*x)*log(e*(a + b*x)/(c + d*x))/((a + b*x)*(-a*d + b*c)**S(2)) + S(6)*d/(b*(a + b*x)*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)*log(e*((a + b*x)/(c + d*x))**n), x), x, (c + d*x)**S(4)*log(e*((a + b*x)/(c + d*x))**n)/(S(4)*d) - n*(c + d*x)**S(3)*(-a*d/S(12) + b*c/S(12))/(b*d) - n*(c + d*x)**S(2)*(-a*d + b*c)**S(2)/(S(8)*b**S(2)*d) - n*x*(-a*d + b*c)**S(3)/(S(4)*b**S(3)) - n*(-a*d + b*c)**S(4)*log(a + b*x)/(S(4)*b**S(4)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n), x), x, (c + d*x)**S(3)*log(e*((a + b*x)/(c + d*x))**n)/(S(3)*d) - n*(c + d*x)**S(2)*(-a*d/S(6) + b*c/S(6))/(b*d) - n*x*(-a*d + b*c)**S(2)/(S(3)*b**S(2)) - n*(-a*d + b*c)**S(3)*log(a + b*x)/(S(3)*b**S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)*log(e*((a + b*x)/(c + d*x))**n), x), x, (c + d*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n)/(S(2)*d) + n*x*(a*d/S(2) - b*c/S(2))/b - n*(-a*d + b*c)**S(2)*log(a + b*x)/(S(2)*b**S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)/b - n*(-a*d + b*c)*log(c + d*x)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(c + d*x), x), x, -n*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/d - log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(c + d*x)**S(2), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)/((c + d*x)*(-a*d + b*c)) + n/(d*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(c + d*x)**S(3), x), x, b**S(2)*n*log(a + b*x)/(S(2)*d*(-a*d + b*c)**S(2)) - b**S(2)*n*log(c + d*x)/(S(2)*d*(-a*d + b*c)**S(2)) + b*n/(S(2)*d*(c + d*x)*(-a*d + b*c)) + n/(S(4)*d*(c + d*x)**S(2)) - log(e*((a + b*x)/(c + d*x))**n)/(S(2)*d*(c + d*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(c + d*x)**S(4), x), x, b**S(3)*n*log(a + b*x)/(S(3)*d*(-a*d + b*c)**S(3)) - b**S(3)*n*log(c + d*x)/(S(3)*d*(-a*d + b*c)**S(3)) + b**S(2)*n/(S(3)*d*(c + d*x)*(-a*d + b*c)**S(2)) + b*n/(S(6)*d*(c + d*x)**S(2)*(-a*d + b*c)) + n/(S(9)*d*(c + d*x)**S(3)) - log(e*((a + b*x)/(c + d*x))**n)/(S(3)*d*(c + d*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(3)*log(e*(a + b*x)/(c + d*x))**S(2), x), x, (c + d*x)**S(4)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(4)*d) - (c + d*x)**S(3)*(-a*d/S(6) + b*c/S(6))*log(e*(a + b*x)/(c + d*x))/(b*d) - (c + d*x)**S(2)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))/(S(4)*b**S(2)*d) + (c + d*x)**S(2)*(-a*d + b*c)**S(2)/(S(12)*b**S(2)*d) + S(5)*x*(-a*d + b*c)**S(3)/(S(12)*b**S(3)) - (a + b*x)*(-a*d + b*c)**S(3)*log(e*(a + b*x)/(c + d*x))/(S(2)*b**S(4)) + (-a*d + b*c)**S(4)*log((a*d - b*c)/(d*(a + b*x)))*log(e*(a + b*x)/(c + d*x))/(S(2)*b**S(4)*d) + S(5)*(-a*d + b*c)**S(4)*log(a + b*x)/(S(12)*b**S(4)*d) + (-a*d + b*c)**S(4)*log(c + d*x)/(S(2)*b**S(4)*d) - (-a*d + b*c)**S(4)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(S(2)*b**S(4)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2), x), x, (c + d*x)**S(3)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(3)*d) - (c + d*x)**S(2)*(-a*d/S(3) + b*c/S(3))*log(e*(a + b*x)/(c + d*x))/(b*d) + x*(-a*d + b*c)**S(2)/(S(3)*b**S(2)) - S(2)*(a + b*x)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))/(S(3)*b**S(3)) + S(2)*(-a*d + b*c)**S(3)*log((a*d - b*c)/(d*(a + b*x)))*log(e*(a + b*x)/(c + d*x))/(S(3)*b**S(3)*d) + (-a*d + b*c)**S(3)*log(a + b*x)/(S(3)*b**S(3)*d) + S(2)*(-a*d + b*c)**S(3)*log(c + d*x)/(S(3)*b**S(3)*d) - S(2)*(-a*d + b*c)**S(3)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(S(3)*b**S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)*log(e*(a + b*x)/(c + d*x))**S(2), x), x, (c + d*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(2)*d) + (a + b*x)*(a*d - b*c)*log(e*(a + b*x)/(c + d*x))/b**S(2) + (-a*d + b*c)**S(2)*log((a*d - b*c)/(d*(a + b*x)))*log(e*(a + b*x)/(c + d*x))/(b**S(2)*d) + (-a*d + b*c)**S(2)*log(c + d*x)/(b**S(2)*d) - (-a*d + b*c)**S(2)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(b**S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(2), x), x, (a + b*x)*log(e*(a + b*x)/(c + d*x))**S(2)/b + (-S(2)*a*d + S(2)*b*c)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))/(b*d) + (-S(2)*a*d + S(2)*b*c)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(2)/(c + d*x), x), x, -log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))**S(2)/d - S(2)*log(e*(a + b*x)/(c + d*x))*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/d + S(2)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(2)/(c + d*x)**S(2), x), x, (a + b*x)*log(e*(a + b*x)/(c + d*x))**S(2)/((c + d*x)*(-a*d + b*c)) - (S(2)*a + S(2)*b*x)*log(e*(a + b*x)/(c + d*x))/((c + d*x)*(-a*d + b*c)) - S(2)/(d*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(2)/(c + d*x)**S(3), x), x, b*(a + b*x)*log(e*(a + b*x)/(c + d*x))**S(2)/((c + d*x)*(-a*d + b*c)**S(2)) - S(2)*b*(a + b*x)*log(e*(a + b*x)/(c + d*x))/((c + d*x)*(-a*d + b*c)**S(2)) - S(2)*b/(d*(c + d*x)*(-a*d + b*c)) - d*(a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(2)*(c + d*x)**S(2)*(-a*d + b*c)**S(2)) + d*(a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))/(S(2)*(c + d*x)**S(2)*(-a*d + b*c)**S(2)) - d*(a + b*x)**S(2)/(S(4)*(c + d*x)**S(2)*(-a*d + b*c)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(3), x), x, (c + d*x)**S(3)*log(e*(a + b*x)/(c + d*x))**S(3)/(S(3)*d) - (c + d*x)**S(2)*(-a*d/S(2) + b*c/S(2))*log(e*(a + b*x)/(c + d*x))**S(2)/(b*d) - (a + b*x)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2)/b**S(3) + (a + b*x)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))/b**S(3) - S(2)*(-a*d + b*c)**S(3)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))/(b**S(3)*d) + (-a*d + b*c)**S(3)*log((a*d - b*c)/(d*(a + b*x)))*log(e*(a + b*x)/(c + d*x))**S(2)/(b**S(3)*d) - (-a*d + b*c)**S(3)*log((a*d - b*c)/(d*(a + b*x)))*log(e*(a + b*x)/(c + d*x))/(b**S(3)*d) - S(2)*(-a*d + b*c)**S(3)*log(e*(a + b*x)/(c + d*x))*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(b**S(3)*d) - (-a*d + b*c)**S(3)*log(c + d*x)/(b**S(3)*d) - S(2)*(-a*d + b*c)**S(3)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b**S(3)*d) + (-a*d + b*c)**S(3)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(b**S(3)*d) - S(2)*(-a*d + b*c)**S(3)*polylog(S(3), b*(c + d*x)/(d*(a + b*x)))/(b**S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)*log(e*(a + b*x)/(c + d*x))**S(3), x), x, (c + d*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(3)/(S(2)*d) - (a + b*x)*(-S(3)*a*d/S(2) + S(3)*b*c/S(2))*log(e*(a + b*x)/(c + d*x))**S(2)/b**S(2) - S(3)*(-a*d + b*c)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))/(b**S(2)*d) + S(3)*(-a*d + b*c)**S(2)*log((a*d - b*c)/(d*(a + b*x)))*log(e*(a + b*x)/(c + d*x))**S(2)/(S(2)*b**S(2)*d) - S(3)*(-a*d + b*c)**S(2)*log(e*(a + b*x)/(c + d*x))*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(b**S(2)*d) - S(3)*(-a*d + b*c)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b**S(2)*d) - S(3)*(-a*d + b*c)**S(2)*polylog(S(3), b*(c + d*x)/(d*(a + b*x)))/(b**S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(3), x), x, (a + b*x)*log(e*(a + b*x)/(c + d*x))**S(3)/b + (-S(6)*a*d + S(6)*b*c)*log(e*(a + b*x)/(c + d*x))*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) - (-S(6)*a*d + S(6)*b*c)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d) + (-S(3)*a*d + S(3)*b*c)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))**S(2)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(3)/(c + d*x), x), x, -log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))**S(3)/d - S(3)*log(e*(a + b*x)/(c + d*x))**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/d + S(6)*log(e*(a + b*x)/(c + d*x))*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/d - S(6)*polylog(S(4), d*(a + b*x)/(b*(c + d*x)))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(3)/(c + d*x)**S(2), x), x, (a + b*x)*log(e*(a + b*x)/(c + d*x))**S(3)/((c + d*x)*(-a*d + b*c)) - (S(3)*a + S(3)*b*x)*log(e*(a + b*x)/(c + d*x))**S(2)/((c + d*x)*(-a*d + b*c)) + (S(6)*a + S(6)*b*x)*log(e*(a + b*x)/(c + d*x))/((c + d*x)*(-a*d + b*c)) + S(6)/(d*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(3)/(c + d*x)**S(3), x), x, b*(a + b*x)*log(e*(a + b*x)/(c + d*x))**S(3)/((c + d*x)*(-a*d + b*c)**S(2)) - S(3)*b*(a + b*x)*log(e*(a + b*x)/(c + d*x))**S(2)/((c + d*x)*(-a*d + b*c)**S(2)) + S(6)*b*(a + b*x)*log(e*(a + b*x)/(c + d*x))/((c + d*x)*(-a*d + b*c)**S(2)) + S(6)*b/(d*(c + d*x)*(-a*d + b*c)) - d*(a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(3)/(S(2)*(c + d*x)**S(2)*(-a*d + b*c)**S(2)) + S(3)*d*(a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))**S(2)/(S(4)*(c + d*x)**S(2)*(-a*d + b*c)**S(2)) - S(3)*d*(a + b*x)**S(2)*log(e*(a + b*x)/(c + d*x))/(S(4)*(c + d*x)**S(2)*(-a*d + b*c)**S(2)) + S(3)*d*(a + b*x)**S(2)/(S(8)*(c + d*x)**S(2)*(-a*d + b*c)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(4), x), x, (a + b*x)*log(e*(a + b*x)/(c + d*x))**S(4)/b - (-S(24)*a*d + S(24)*b*c)*log(e*(a + b*x)/(c + d*x))*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d) + (-S(24)*a*d + S(24)*b*c)*polylog(S(4), d*(a + b*x)/(b*(c + d*x)))/(b*d) + (-S(12)*a*d + S(12)*b*c)*log(e*(a + b*x)/(c + d*x))**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) + (-S(4)*a*d + S(4)*b*c)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))**S(3)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(a + b*x)/(c + d*x))**S(5), x), x, (a + b*x)*log(e*(a + b*x)/(c + d*x))**S(5)/b + (-S(120)*a*d + S(120)*b*c)*log(e*(a + b*x)/(c + d*x))*polylog(S(4), d*(a + b*x)/(b*(c + d*x)))/(b*d) - (-S(120)*a*d + S(120)*b*c)*polylog(S(5), d*(a + b*x)/(b*(c + d*x)))/(b*d) - (-S(60)*a*d + S(60)*b*c)*log(e*(a + b*x)/(c + d*x))**S(2)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d) + (-S(20)*a*d + S(20)*b*c)*log(e*(a + b*x)/(c + d*x))**S(3)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) + (-S(5)*a*d + S(5)*b*c)*log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))**S(4)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d*(a + b*x)/(b*(c + d*x)))/(c*f + d*f*x), x), x, polylog(S(2), (-a*d + b*c)/(b*(c + d*x)))/(d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(1) + S(1)/(a + b*x))/(a + b*x), x), x, polylog(S(2), -S(1)/(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(1) - S(1)/(a + b*x))/(a + b*x), x), x, polylog(S(2), S(1)/(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((f + g*x)**S(3)*log(e*((a + b*x)/(c + d*x))**n), x), x, (f + g*x)**S(4)*log(e*((a + b*x)/(c + d*x))**n)/(S(4)*g) + n*(-c*g + d*f)**S(4)*log(c + d*x)/(S(4)*d**S(4)*g) - g**S(3)*n*x**S(3)*(-a*d/S(12) + b*c/S(12))/(b*d) - g**S(2)*n*x**S(2)*(-a*d/S(8) + b*c/S(8))*(-a*d*g - b*c*g + S(4)*b*d*f)/(b**S(2)*d**S(2)) + g*n*x*(a*d/S(4) - b*c/S(4))*(a**S(2)*d**S(2)*g**S(2) - a*b*d*g*(-c*g + S(4)*d*f) + b**S(2)*(c**S(2)*g**S(2) - S(4)*c*d*f*g + S(6)*d**S(2)*f**S(2)))/(b**S(3)*d**S(3)) - n*(-a*g + b*f)**S(4)*log(a + b*x)/(S(4)*b**S(4)*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((f + g*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n), x), x, (f + g*x)**S(3)*log(e*((a + b*x)/(c + d*x))**n)/(S(3)*g) + n*(-c*g + d*f)**S(3)*log(c + d*x)/(S(3)*d**S(3)*g) - g**S(2)*n*x**S(2)*(-a*d/S(6) + b*c/S(6))/(b*d) + g*n*x*(a*d/S(3) - b*c/S(3))*(-a*d*g - b*c*g + S(3)*b*d*f)/(b**S(2)*d**S(2)) - n*(-a*g + b*f)**S(3)*log(a + b*x)/(S(3)*b**S(3)*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((f + g*x)*log(e*((a + b*x)/(c + d*x))**n), x), x, (f + g*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n)/(S(2)*g) + n*(-c*g + d*f)**S(2)*log(c + d*x)/(S(2)*d**S(2)*g) + g*n*x*(a*d/S(2) - b*c/S(2))/(b*d) - n*(-a*g + b*f)**S(2)*log(a + b*x)/(S(2)*b**S(2)*g), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)/b - n*(-a*d + b*c)*log(c + d*x)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(f + g*x), x), x, -n*log(-g*(a + b*x)/(-a*g + b*f))*log(f + g*x)/g + n*log(-g*(c + d*x)/(-c*g + d*f))*log(f + g*x)/g - n*polylog(S(2), b*(f + g*x)/(-a*g + b*f))/g + n*polylog(S(2), d*(f + g*x)/(-c*g + d*f))/g + log(e*((a + b*x)/(c + d*x))**n)*log(f + g*x)/g, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(f + g*x)**S(2), x), x, -n*(-a*d + b*c)*log(c + d*x)/((-a*g + b*f)*(-c*g + d*f)) + n*(-a*d + b*c)*log(f + g*x)/((-a*g + b*f)*(-c*g + d*f)) + (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)/((f + g*x)*(-a*g + b*f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(f + g*x)**S(3), x), x, b**S(2)*n*log(a + b*x)/(S(2)*g*(-a*g + b*f)**S(2)) - d**S(2)*n*log(c + d*x)/(S(2)*g*(-c*g + d*f)**S(2)) + n*(-a*d/S(2) + b*c/S(2))*(-a*d*g - b*c*g + S(2)*b*d*f)*log(f + g*x)/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + n*(a*d/S(2) - b*c/S(2))/((f + g*x)*(-a*g + b*f)*(-c*g + d*f)) - log(e*((a + b*x)/(c + d*x))**n)/(S(2)*g*(f + g*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(f + g*x)**S(4), x), x, b**S(3)*n*log(a + b*x)/(S(3)*g*(-a*g + b*f)**S(3)) - d**S(3)*n*log(c + d*x)/(S(3)*g*(-c*g + d*f)**S(3)) + n*(-a*d/S(3) + b*c/S(3))*(a**S(2)*d**S(2)*g**S(2) - a*b*d*g*(-c*g + S(3)*d*f) + b**S(2)*(c**S(2)*g**S(2) - S(3)*c*d*f*g + S(3)*d**S(2)*f**S(2)))*log(f + g*x)/((-a*g + b*f)**S(3)*(-c*g + d*f)**S(3)) - n*(-a*d/S(3) + b*c/S(3))*(-a*d*g - b*c*g + S(2)*b*d*f)/((f + g*x)*(-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + n*(a*d/S(6) - b*c/S(6))/((f + g*x)**S(2)*(-a*g + b*f)*(-c*g + d*f)) - log(e*((a + b*x)/(c + d*x))**n)/(S(3)*g*(f + g*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((f + g*x)**S(3)*log(e*((a + b*x)/(c + d*x))**n)**S(2), x), x, -a**S(3)*g**S(3)*n**S(2)*(-a*d + b*c)*log(a + b*x)/(S(6)*b**S(4)*d) + a**S(2)*g**S(2)*n**S(2)*(-a*d + b*c)*(-a*d*g - b*c*g + S(4)*b*d*f)*log(a + b*x)/(S(4)*b**S(4)*d**S(2)) + (f + g*x)**S(4)*log(e*((a + b*x)/(c + d*x))**n)**S(2)/(S(4)*g) - n**S(2)*(-c*g + d*f)**S(4)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(S(2)*d**S(4)*g) - n*(-c*g + d*f)**S(4)*log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/(S(2)*d**S(4)*g) + c**S(3)*g**S(3)*n**S(2)*(-a*d + b*c)*log(c + d*x)/(S(6)*b*d**S(4)) - g**S(3)*n*x**S(3)*(-a*d/S(6) + b*c/S(6))*log(e*((a + b*x)/(c + d*x))**n)/(b*d) - c**S(2)*g**S(2)*n**S(2)*(-a*d + b*c)*(-a*d*g - b*c*g + S(4)*b*d*f)*log(c + d*x)/(S(4)*b**S(2)*d**S(4)) + g**S(3)*n**S(2)*x**S(2)*(-a*d + b*c)**S(2)/(S(12)*b**S(2)*d**S(2)) - g**S(2)*n*x**S(2)*(-a*d/S(4) + b*c/S(4))*(-a*d*g - b*c*g + S(4)*b*d*f)*log(e*((a + b*x)/(c + d*x))**n)/(b**S(2)*d**S(2)) - g**S(3)*n**S(2)*x*(-a*d + b*c)**S(2)*(a*d + b*c)/(S(6)*b**S(3)*d**S(3)) + g**S(2)*n**S(2)*x*(-a*d + b*c)**S(2)*(-a*d*g - b*c*g + S(4)*b*d*f)/(S(4)*b**S(3)*d**S(3)) - n**S(2)*(-a*g + b*f)**S(4)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(S(2)*b**S(4)*g) + n*(-a*g + b*f)**S(4)*log(e*((a + b*x)/(c + d*x))**n)*log((a*d - b*c)/(d*(a + b*x)))/(S(2)*b**S(4)*g) - g*n*(a + b*x)*(-a*d/S(2) + b*c/S(2))*(a**S(2)*d**S(2)*g**S(2) - a*b*d*g*(-c*g + S(4)*d*f) + b**S(2)*(c**S(2)*g**S(2) - S(4)*c*d*f*g + S(6)*d**S(2)*f**S(2)))*log(e*((a + b*x)/(c + d*x))**n)/(b**S(4)*d**S(3)) + g*n**S(2)*(-a*d + b*c)**S(2)*(a**S(2)*d**S(2)*g**S(2) - a*b*d*g*(-c*g + S(4)*d*f) + b**S(2)*(c**S(2)*g**S(2) - S(4)*c*d*f*g + S(6)*d**S(2)*f**S(2)))*log(c + d*x)/(S(2)*b**S(4)*d**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((f + g*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n)**S(2), x), x, a**S(2)*g**S(2)*n**S(2)*(-a*d + b*c)*log(a + b*x)/(S(3)*b**S(3)*d) + (f + g*x)**S(3)*log(e*((a + b*x)/(c + d*x))**n)**S(2)/(S(3)*g) - S(2)*n**S(2)*(-c*g + d*f)**S(3)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(S(3)*d**S(3)*g) - S(2)*n*(-c*g + d*f)**S(3)*log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/(S(3)*d**S(3)*g) - c**S(2)*g**S(2)*n**S(2)*(-a*d + b*c)*log(c + d*x)/(S(3)*b*d**S(3)) - g**S(2)*n*x**S(2)*(-a*d/S(3) + b*c/S(3))*log(e*((a + b*x)/(c + d*x))**n)/(b*d) + g**S(2)*n**S(2)*x*(-a*d + b*c)**S(2)/(S(3)*b**S(2)*d**S(2)) - S(2)*n**S(2)*(-a*g + b*f)**S(3)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(S(3)*b**S(3)*g) + S(2)*n*(-a*g + b*f)**S(3)*log(e*((a + b*x)/(c + d*x))**n)*log((a*d - b*c)/(d*(a + b*x)))/(S(3)*b**S(3)*g) - g*n*(a + b*x)*(-S(2)*a*d/S(3) + S(2)*b*c/S(3))*(-a*d*g - b*c*g + S(3)*b*d*f)*log(e*((a + b*x)/(c + d*x))**n)/(b**S(3)*d**S(2)) + S(2)*g*n**S(2)*(-a*d + b*c)**S(2)*(-a*d*g - b*c*g + S(3)*b*d*f)*log(c + d*x)/(S(3)*b**S(3)*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((f + g*x)*log(e*((a + b*x)/(c + d*x))**n)**S(2), x), x, (f + g*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n)**S(2)/(S(2)*g) - n**S(2)*(-c*g + d*f)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(d**S(2)*g) - n*(-c*g + d*f)**S(2)*log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/(d**S(2)*g) - n**S(2)*(-a*g + b*f)**S(2)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(b**S(2)*g) + n*(-a*g + b*f)**S(2)*log(e*((a + b*x)/(c + d*x))**n)*log((a*d - b*c)/(d*(a + b*x)))/(b**S(2)*g) + g*n*(a + b*x)*(a*d - b*c)*log(e*((a + b*x)/(c + d*x))**n)/(b**S(2)*d) + g*n**S(2)*(-a*d + b*c)**S(2)*log(c + d*x)/(b**S(2)*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(2), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)**S(2)/b + n**S(2)*(-S(2)*a*d + S(2)*b*c)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n*(-S(2)*a*d + S(2)*b*c)*log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/(b*d), expand=True, _diff=True, _numerical=True)

    # taking long time in rubi_test assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(2)/(f + g*x), x), x, -S(2)*n**S(2)*polylog(S(3), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/g + S(2)*n**S(2)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/g + S(2)*n*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/g - S(2)*n*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/g - log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))/g + log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((f + g*x)*(-a*d + b*c)/((c + d*x)*(-a*g + b*f)))/g, expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(2)/(f + g*x)**S(2), x), x, n**S(2)*(-S(2)*a*d + S(2)*b*c)*polylog(S(2), (a + b*x)*(-c*g + d*f)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)*(-c*g + d*f)) + n*(-S(2)*a*d + S(2)*b*c)*log(e*((a + b*x)/(c + d*x))**n)*log((f + g*x)*(-a*d + b*c)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)*(-c*g + d*f)) + (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)**S(2)/((f + g*x)*(-a*g + b*f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(2)/(f + g*x)**S(3), x), x, b**S(2)*n**S(2)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(g*(-a*g + b*f)**S(2)) - b**S(2)*n*log(e*((a + b*x)/(c + d*x))**n)*log((a*d - b*c)/(d*(a + b*x)))/(g*(-a*g + b*f)**S(2)) + d**S(2)*n**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(g*(-c*g + d*f)**S(2)) + d**S(2)*n*log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/(g*(-c*g + d*f)**S(2)) - g*n**S(2)*(-a*d + b*c)**S(2)*log(c + d*x)/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + g*n**S(2)*(-a*d + b*c)**S(2)*log(f + g*x)/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + g*n*(a + b*x)*(-a*d + b*c)*log(e*((a + b*x)/(c + d*x))**n)/((f + g*x)*(-a*g + b*f)**S(2)*(-c*g + d*f)) - n**S(2)*(-a*d + b*c)*(-a*d*g - b*c*g + S(2)*b*d*f)*log(-g*(a + b*x)/(-a*g + b*f))*log(f + g*x)/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + n**S(2)*(-a*d + b*c)*(-a*d*g - b*c*g + S(2)*b*d*f)*log(-g*(c + d*x)/(-c*g + d*f))*log(f + g*x)/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) - n**S(2)*(-a*d + b*c)*(-a*d*g - b*c*g + S(2)*b*d*f)*polylog(S(2), b*(f + g*x)/(-a*g + b*f))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + n**S(2)*(-a*d + b*c)*(-a*d*g - b*c*g + S(2)*b*d*f)*polylog(S(2), d*(f + g*x)/(-c*g + d*f))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + n*(-a*d + b*c)*(-a*d*g - b*c*g + S(2)*b*d*f)*log(e*((a + b*x)/(c + d*x))**n)*log(f + g*x)/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) - log(e*((a + b*x)/(c + d*x))**n)**S(2)/(S(2)*g*(f + g*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((f + g*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n)**S(3), x), x, a**S(2)*g**S(2)*n**S(3)*(-a*d + b*c)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(b**S(3)*d) - a**S(2)*g**S(2)*n**S(2)*(-a*d + b*c)*log(e*((a + b*x)/(c + d*x))**n)*log((a*d - b*c)/(d*(a + b*x)))/(b**S(3)*d) + (f + g*x)**S(3)*log(e*((a + b*x)/(c + d*x))**n)**S(3)/(S(3)*g) + S(2)*n**S(3)*(-c*g + d*f)**S(3)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(d**S(3)*g) - S(2)*n**S(2)*(-c*g + d*f)**S(3)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(d**S(3)*g) - n*(-c*g + d*f)**S(3)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))/(d**S(3)*g) + c**S(2)*g**S(2)*n**S(3)*(-a*d + b*c)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d**S(3)) + c**S(2)*g**S(2)*n**S(2)*(-a*d + b*c)*log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/(b*d**S(3)) - g**S(2)*n*x**S(2)*(-a*d/S(2) + b*c/S(2))*log(e*((a + b*x)/(c + d*x))**n)**S(2)/(b*d) - S(2)*n**S(3)*(-a*g + b*f)**S(3)*polylog(S(3), b*(c + d*x)/(d*(a + b*x)))/(b**S(3)*g) - S(2)*n**S(2)*(-a*g + b*f)**S(3)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(b**S(3)*g) + n*(-a*g + b*f)**S(3)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((a*d - b*c)/(d*(a + b*x)))/(b**S(3)*g) + g**S(2)*n**S(2)*(a + b*x)*(-a*d + b*c)**S(2)*log(e*((a + b*x)/(c + d*x))**n)/(b**S(3)*d**S(2)) - g*n*(a + b*x)*(-a*d + b*c)*(-a*d*g - b*c*g + S(3)*b*d*f)*log(e*((a + b*x)/(c + d*x))**n)**S(2)/(b**S(3)*d**S(2)) - g**S(2)*n**S(3)*(-a*d + b*c)**S(3)*log(c + d*x)/(b**S(3)*d**S(3)) - S(2)*g*n**S(3)*(-a*d + b*c)**S(2)*(-a*d*g - b*c*g + S(3)*b*d*f)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b**S(3)*d**S(3)) - S(2)*g*n**S(2)*(-a*d + b*c)**S(2)*(-a*d*g - b*c*g + S(3)*b*d*f)*log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/(b**S(3)*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((f + g*x)*log(e*((a + b*x)/(c + d*x))**n)**S(3), x), x, (f + g*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n)**S(3)/(S(2)*g) + S(3)*n**S(3)*(-c*g + d*f)**S(2)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(d**S(2)*g) - S(3)*n**S(2)*(-c*g + d*f)**S(2)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(d**S(2)*g) - S(3)*n*(-c*g + d*f)**S(2)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))/(S(2)*d**S(2)*g) - S(3)*n**S(3)*(-a*g + b*f)**S(2)*polylog(S(3), b*(c + d*x)/(d*(a + b*x)))/(b**S(2)*g) - S(3)*n**S(2)*(-a*g + b*f)**S(2)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(b**S(2)*g) + S(3)*n*(-a*g + b*f)**S(2)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((a*d - b*c)/(d*(a + b*x)))/(S(2)*b**S(2)*g) + g*n*(a + b*x)*(S(3)*a*d/S(2) - S(3)*b*c/S(2))*log(e*((a + b*x)/(c + d*x))**n)**S(2)/(b**S(2)*d) - S(3)*g*n**S(3)*(-a*d + b*c)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b**S(2)*d**S(2)) - S(3)*g*n**S(2)*(-a*d + b*c)**S(2)*log(e*((a + b*x)/(c + d*x))**n)*log((-a*d + b*c)/(b*(c + d*x)))/(b**S(2)*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(3), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)**S(3)/b - n**S(3)*(-S(6)*a*d + S(6)*b*c)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n**S(2)*(-S(6)*a*d + S(6)*b*c)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n*(-S(3)*a*d + S(3)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))/(b*d), expand=True, _diff=True, _numerical=True)

    # takes long time in test assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(3)/(f + g*x), x), x, -S(6)*n**S(3)*polylog(S(4), d*(a + b*x)/(b*(c + d*x)))/g + S(6)*n**S(3)*polylog(S(4), (a + b*x)*(-c*g + d*f)/((c + d*x)*(-a*g + b*f)))/g - S(6)*n**S(2)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(3), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/g + S(6)*n**S(2)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/g + S(3)*n*log(e*((a + b*x)/(c + d*x))**n)**S(2)*polylog(S(2), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/g - S(3)*n*log(e*((a + b*x)/(c + d*x))**n)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/g - log(e*((a + b*x)/(c + d*x))**n)**S(3)*log((-a*d + b*c)/(b*(c + d*x)))/g + log(e*((a + b*x)/(c + d*x))**n)**S(3)*log((f + g*x)*(-a*d + b*c)/((c + d*x)*(-a*g + b*f)))/g, expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(3)/(f + g*x)**S(2), x), x, -n**S(3)*(-S(6)*a*d + S(6)*b*c)*polylog(S(3), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)*(-c*g + d*f)) + n**S(2)*(-S(6)*a*d + S(6)*b*c)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)*(-c*g + d*f)) + n*(-S(3)*a*d + S(3)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((f + g*x)*(-a*d + b*c)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)*(-c*g + d*f)) + (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)**S(3)/((f + g*x)*(-a*g + b*f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(3)/(f + g*x)**S(3), x), x, S(3)*b**S(2)*n**S(3)*polylog(S(3), b*(c + d*x)/(d*(a + b*x)))/(g*(-a*g + b*f)**S(2)) + S(3)*b**S(2)*n**S(2)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), b*(c + d*x)/(d*(a + b*x)))/(g*(-a*g + b*f)**S(2)) - S(3)*b**S(2)*n*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((a*d - b*c)/(d*(a + b*x)))/(S(2)*g*(-a*g + b*f)**S(2)) - S(3)*d**S(2)*n**S(3)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(g*(-c*g + d*f)**S(2)) + S(3)*d**S(2)*n**S(2)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(g*(-c*g + d*f)**S(2)) + S(3)*d**S(2)*n*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))/(S(2)*g*(-c*g + d*f)**S(2)) + S(3)*g*n**S(3)*(-a*d + b*c)**S(2)*polylog(S(2), (a + b*x)*(-c*g + d*f)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + S(3)*g*n**S(2)*(-a*d + b*c)**S(2)*log(e*((a + b*x)/(c + d*x))**n)*log((f + g*x)*(-a*d + b*c)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + g*n*(a + b*x)*(-S(3)*a*d/S(2) + S(3)*b*c/S(2))*log(e*((a + b*x)/(c + d*x))**n)**S(2)/((f + g*x)*(-a*g + b*f)**S(2)*(-c*g + d*f)) - n**S(3)*(-S(3)*a*d + S(3)*b*c)*(-a*d*g - b*c*g + S(2)*b*d*f)*polylog(S(3), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + n**S(3)*(-S(3)*a*d + S(3)*b*c)*(-a*d*g - b*c*g + S(2)*b*d*f)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + n**S(2)*(-S(3)*a*d + S(3)*b*c)*(-a*d*g - b*c*g + S(2)*b*d*f)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) - n**S(2)*(-S(3)*a*d + S(3)*b*c)*(-a*d*g - b*c*g + S(2)*b*d*f)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) - n*(-S(3)*a*d/S(2) + S(3)*b*c/S(2))*(-a*d*g - b*c*g + S(2)*b*d*f)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) + n*(-S(3)*a*d/S(2) + S(3)*b*c/S(2))*(-a*d*g - b*c*g + S(2)*b*d*f)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((f + g*x)*(-a*d + b*c)/((c + d*x)*(-a*g + b*f)))/((-a*g + b*f)**S(2)*(-c*g + d*f)**S(2)) - log(e*((a + b*x)/(c + d*x))**n)**S(3)/(S(2)*g*(f + g*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(4), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)**S(4)/b + n**S(4)*(-S(24)*a*d + S(24)*b*c)*polylog(S(4), d*(a + b*x)/(b*(c + d*x)))/(b*d) - n**S(3)*(-S(24)*a*d + S(24)*b*c)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n**S(2)*(-S(12)*a*d + S(12)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n*(-S(4)*a*d + S(4)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(3)*log((-a*d + b*c)/(b*(c + d*x)))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(5), x), x, (a + b*x)*log(e*((a + b*x)/(c + d*x))**n)**S(5)/b - n**S(5)*(-S(120)*a*d + S(120)*b*c)*polylog(S(5), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n**S(4)*(-S(120)*a*d + S(120)*b*c)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(4), d*(a + b*x)/(b*(c + d*x)))/(b*d) - n**S(3)*(-S(60)*a*d + S(60)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(2)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n**S(2)*(-S(20)*a*d + S(20)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(3)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(b*d) + n*(-S(5)*a*d + S(5)*b*c)*log(e*((a + b*x)/(c + d*x))**n)**S(4)*log((-a*d + b*c)/(b*(c + d*x)))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**m*(c + d*x)**(-m + S(-2))/log(e*((a + b*x)/(c + d*x))**n), x), x, (e*((a + b*x)/(c + d*x))**n)**(-(m + S(1))/n)*(a + b*x)**(m + S(1))*(c + d*x)**(-m + S(-1))*Ei((m + S(1))*log(e*((a + b*x)/(c + d*x))**n)/n)/(n*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)/((c + d*x)**S(5)*log(e*((a + b*x)/(c + d*x))**n)), x), x, (e*((a + b*x)/(c + d*x))**n)**(-S(4)/n)*(a + b*x)**S(4)*Ei(S(4)*log(e*((a + b*x)/(c + d*x))**n)/n)/(n*(c + d*x)**S(4)*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)/((c + d*x)**S(4)*log(e*((a + b*x)/(c + d*x))**n)), x), x, (e*((a + b*x)/(c + d*x))**n)**(-S(3)/n)*(a + b*x)**S(3)*Ei(S(3)*log(e*((a + b*x)/(c + d*x))**n)/n)/(n*(c + d*x)**S(3)*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)/((c + d*x)**S(3)*log(e*((a + b*x)/(c + d*x))**n)), x), x, (e*((a + b*x)/(c + d*x))**n)**(-S(2)/n)*(a + b*x)**S(2)*Ei(S(2)*log(e*((a + b*x)/(c + d*x))**n)/n)/(n*(c + d*x)**S(2)*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((c + d*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n)), x), x, (e*((a + b*x)/(c + d*x))**n)**(-S(1)/n)*(a + b*x)*Ei(log(e*((a + b*x)/(c + d*x))**n)/n)/(n*(c + d*x)*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*x)*(c + d*x)*log(e*((a + b*x)/(c + d*x))**n)), x), x, log(log(e*((a + b*x)/(c + d*x))**n))/(n*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*x)**S(2)*log(e*((a + b*x)/(c + d*x))**n)), x), x, (e*((a + b*x)/(c + d*x))**n)**(S(1)/n)*(c + d*x)*Ei(-log(e*((a + b*x)/(c + d*x))**n)/n)/(n*(a + b*x)*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)/((a + b*x)**S(3)*log(e*((a + b*x)/(c + d*x))**n)), x), x, (e*((a + b*x)/(c + d*x))**n)**(S(2)/n)*(c + d*x)**S(2)*Ei(-S(2)*log(e*((a + b*x)/(c + d*x))**n)/n)/(n*(a + b*x)**S(2)*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((c + d*x)**S(2)/((a + b*x)**S(4)*log(e*((a + b*x)/(c + d*x))**n)), x), x, (e*((a + b*x)/(c + d*x))**n)**(S(3)/n)*(c + d*x)**S(3)*Ei(-S(3)*log(e*((a + b*x)/(c + d*x))**n)/n)/(n*(a + b*x)**S(3)*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**p/((a + b*x)*(c + d*x)), x), x, log(e*((a + b*x)/(c + d*x))**n)**(p + S(1))/(n*(p + S(1))*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**p/(a*c + b*d*x**S(2) + x*(a*d + b*c)), x), x, log(e*((a + b*x)/(c + d*x))**n)**(p + S(1))/(n*(p + S(1))*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x/(a + b*x))/(a + b*x), x), x, -log(a/(a + b*x))*log(c*x/(a + b*x))/b - polylog(S(2), b*x/(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x/(a + b*x))**S(2)/(x*(a + b*x)), x), x, log(c*x/(a + b*x))**S(3)/(S(3)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a/(a + b*x))*log(c*x/(a + b*x))**S(2)/(x*(a + b*x)), x), x, -log(c*x/(a + b*x))**S(2)*polylog(S(2), b*x/(a + b*x))/a + S(2)*log(c*x/(a + b*x))*polylog(S(3), b*x/(a + b*x))/a - S(2)*polylog(S(4), b*x/(a + b*x))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/((c + d*x)*(f + g*x)), x), x, -n*polylog(S(2), (a + b*x)*(-c*g + d*f)/((c + d*x)*(-a*g + b*f)))/(-c*g + d*f) - log(e*((a + b*x)/(c + d*x))**n)*log((f + g*x)*(-a*d + b*c)/((c + d*x)*(-a*g + b*f)))/(-c*g + d*f), expand=True, _diff=True, _numerical=True)

    # long time in test assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(2)/((c + d*x)*(f + g*x)), x), x, S(2)*n**S(2)*polylog(S(3), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/(-c*g + d*f) - S(2)*n*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(2), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/(-c*g + d*f) - log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((f + g*x)*(-a*d + b*c)/((c + d*x)*(-a*g + b*f)))/(-c*g + d*f), expand=True, _diff=True, _numerical=True)
    # || assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(3)/((c + d*x)*(f + g*x)), x), x, -S(6)*n**S(3)*polylog(S(4), (a + b*x)*(-c*g + d*f)/((c + d*x)*(-a*g + b*f)))/(-c*g + d*f) + S(6)*n**S(2)*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(3), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/(-c*g + d*f) - S(3)*n*log(e*((a + b*x)/(c + d*x))**n)**S(2)*polylog(S(2), (a*(-c*g + d*f) - b*c*g*x + b*d*f*x)/((c + d*x)*(-a*g + b*f)))/(-c*g + d*f) - log(e*((a + b*x)/(c + d*x))**n)**S(3)*log((f + g*x)*(-a*d + b*c)/((c + d*x)*(-a*g + b*f)))/(-c*g + d*f), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log((-a*d + b*c)/(b*(c + d*x)))*log(e*(a + b*x)/(c + d*x))**S(2)/((c + d*x)*(a*g + b*g*x)), x), x, -log(e*(a + b*x)/(c + d*x))**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(g*(-a*d + b*c)) + S(2)*log(e*(a + b*x)/(c + d*x))*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(g*(-a*d + b*c)) - S(2)*polylog(S(4), d*(a + b*x)/(b*(c + d*x)))/(g*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)**S(2)*log((-a*d + b*c)/(b*(c + d*x)))/((c + d*x)*(a*g + b*g*x)), x), x, -S(2)*n**S(2)*polylog(S(4), d*(a + b*x)/(b*(c + d*x)))/(g*(-a*d + b*c)) + S(2)*n*log(e*((a + b*x)/(c + d*x))**n)*polylog(S(3), d*(a + b*x)/(b*(c + d*x)))/(g*(-a*d + b*c)) - log(e*((a + b*x)/(c + d*x))**n)**S(2)*polylog(S(2), d*(a + b*x)/(b*(c + d*x)))/(g*(-a*d + b*c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a*x + b)/x), x), x, b*log(x)/a + (a*x + b)*log(c*(a*x + b)/x)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a*x + b)/x)**S(2), x), x, -S(2)*b*log(-b/(a*x))*log(c*(a*x + b)/x)/a - S(2)*b*polylog(S(2), S(1) + b/(a*x))/a + (a*x + b)*log(c*(a*x + b)/x)**S(2)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a*x + b)/x)**S(3), x), x, -S(3)*b*log(-b/(a*x))*log(c*(a*x + b)/x)**S(2)/a - S(6)*b*log(c*(a*x + b)/x)*polylog(S(2), (a*x + b)/(a*x))/a + S(6)*b*polylog(S(3), (a*x + b)/(a*x))/a + (a*x + b)*log(c*(a*x + b)/x)**S(3)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a*x + b)**S(2)/x**S(2)), x), x, x*log(c*(a*x + b)**S(2)/x**S(2)) + S(2)*b*log(a*x + b)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a*x + b)**S(2)/x**S(2))**S(2), x), x, x*log(c*(a*x + b)**S(2)/x**S(2))**S(2) - S(4)*b*log(b/(a*x + b))*log(c*(a*x + b)**S(2)/x**S(2))/a + S(8)*b*polylog(S(2), a*x/(a*x + b))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a*x + b)**S(2)/x**S(2))**S(3), x), x, x*log(c*(a*x + b)**S(2)/x**S(2))**S(3) - S(6)*b*log(b/(a*x + b))*log(c*(a*x + b)**S(2)/x**S(2))**S(2)/a + S(24)*b*log(c*(a*x + b)**S(2)/x**S(2))*polylog(S(2), a*x/(a*x + b))/a + S(48)*b*polylog(S(3), a*x/(a*x + b))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**S(2)/(a*x + b)**S(2)), x), x, x*log(c*x**S(2)/(a*x + b)**S(2)) - S(2)*b*log(a*x + b)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**S(2)/(a*x + b)**S(2))**S(2), x), x, x*log(c*x**S(2)/(a*x + b)**S(2))**S(2) + S(4)*b*log(b/(a*x + b))*log(c*x**S(2)/(a*x + b)**S(2))/a + S(8)*b*polylog(S(2), a*x/(a*x + b))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**S(2)/(a*x + b)**S(2))**S(3), x), x, x*log(c*x**S(2)/(a*x + b)**S(2))**S(3) + S(6)*b*log(b/(a*x + b))*log(c*x**S(2)/(a*x + b)**S(2))**S(2)/a + S(24)*b*log(c*x**S(2)/(a*x + b)**S(2))*polylog(S(2), a*x/(a*x + b))/a - S(48)*b*polylog(S(3), a*x/(a*x + b))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b/x)/(d + e*x**S(2)), x), x, -I*log(sqrt(e)*(-a*x - b)/(I*a*sqrt(d) - b*sqrt(e)))*log(S(1) - I*sqrt(e)*x/sqrt(d))/(S(2)*sqrt(d)*sqrt(e)) + I*log(sqrt(e)*(a*x + b)/(I*a*sqrt(d) + b*sqrt(e)))*log(S(1) + I*sqrt(e)*x/sqrt(d))/(S(2)*sqrt(d)*sqrt(e)) + log(a + b/x)*atan(sqrt(e)*x/sqrt(d))/(sqrt(d)*sqrt(e)) - I*polylog(S(2), a*(sqrt(d) - I*sqrt(e)*x)/(a*sqrt(d) + I*b*sqrt(e)))/(S(2)*sqrt(d)*sqrt(e)) + I*polylog(S(2), a*(sqrt(d) + I*sqrt(e)*x)/(a*sqrt(d) - I*b*sqrt(e)))/(S(2)*sqrt(d)*sqrt(e)) + I*polylog(S(2), -I*sqrt(e)*x/sqrt(d))/(S(2)*sqrt(d)*sqrt(e)) - I*polylog(S(2), I*sqrt(e)*x/sqrt(d))/(S(2)*sqrt(d)*sqrt(e)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(f + g*x**S(2)), x), x, -I*n*log(sqrt(g)*(-a - b*x)/(-a*sqrt(g) + I*b*sqrt(f)))*log(S(1) - I*sqrt(g)*x/sqrt(f))/(S(2)*sqrt(f)*sqrt(g)) + I*n*log(sqrt(g)*(a + b*x)/(a*sqrt(g) + I*b*sqrt(f)))*log(S(1) + I*sqrt(g)*x/sqrt(f))/(S(2)*sqrt(f)*sqrt(g)) + I*n*log(sqrt(g)*(-c - d*x)/(-c*sqrt(g) + I*d*sqrt(f)))*log(S(1) - I*sqrt(g)*x/sqrt(f))/(S(2)*sqrt(f)*sqrt(g)) - I*n*log(sqrt(g)*(c + d*x)/(c*sqrt(g) + I*d*sqrt(f)))*log(S(1) + I*sqrt(g)*x/sqrt(f))/(S(2)*sqrt(f)*sqrt(g)) - I*n*polylog(S(2), b*(sqrt(f) - I*sqrt(g)*x)/(I*a*sqrt(g) + b*sqrt(f)))/(S(2)*sqrt(f)*sqrt(g)) + I*n*polylog(S(2), b*(sqrt(f) + I*sqrt(g)*x)/(-I*a*sqrt(g) + b*sqrt(f)))/(S(2)*sqrt(f)*sqrt(g)) + I*n*polylog(S(2), d*(sqrt(f) - I*sqrt(g)*x)/(I*c*sqrt(g) + d*sqrt(f)))/(S(2)*sqrt(f)*sqrt(g)) - I*n*polylog(S(2), d*(sqrt(f) + I*sqrt(g)*x)/(-I*c*sqrt(g) + d*sqrt(f)))/(S(2)*sqrt(f)*sqrt(g)) + log(e*((a + b*x)/(c + d*x))**n)*atan(sqrt(g)*x/sqrt(f))/(sqrt(f)*sqrt(g)), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate(log(e*((a + b*x)/(c + d*x))**n)/(f + g*x + h*x**S(2)), x), x, n*log((S(2)*a*h - b*g + b*(g + S(2)*h*x))/(S(2)*a*h - b*(g + sqrt(-S(4)*f*h + g**S(2)))))*log(g/sqrt(-S(4)*f*h + g**S(2)) + S(2)*h*x/sqrt(-S(4)*f*h + g**S(2)) + S(1))/sqrt(-S(4)*f*h + g**S(2)) - n*log((S(2)*c*h - d*g + d*(g + S(2)*h*x))/(S(2)*c*h - d*(g + sqrt(-S(4)*f*h + g**S(2)))))*log(g/sqrt(-S(4)*f*h + g**S(2)) + S(2)*h*x/sqrt(-S(4)*f*h + g**S(2)) + S(1))/sqrt(-S(4)*f*h + g**S(2)) - n*log((-S(2)*a*h + b*g - b*(g + S(2)*h*x))/(-S(2)*a*h + b*g - b*sqrt(-S(4)*f*h + g**S(2))))*log(-g/sqrt(-S(4)*f*h + g**S(2)) - S(2)*h*x/sqrt(-S(4)*f*h + g**S(2)) + S(1))/sqrt(-S(4)*f*h + g**S(2)) + n*log((-S(2)*c*h + d*g - d*(g + S(2)*h*x))/(-S(2)*c*h + d*g - d*sqrt(-S(4)*f*h + g**S(2))))*log(-g/sqrt(-S(4)*f*h + g**S(2)) - S(2)*h*x/sqrt(-S(4)*f*h + g**S(2)) + S(1))/sqrt(-S(4)*f*h + g**S(2)) - n*polylog(S(2), b*sqrt(-S(4)*f*h + g**S(2))*(-g/sqrt(-S(4)*f*h + g**S(2)) - S(2)*h*x/sqrt(-S(4)*f*h + g**S(2)) + S(1))/(S(2)*a*h - b*(g - sqrt(-S(4)*f*h + g**S(2)))))/sqrt(-S(4)*f*h + g**S(2)) + n*polylog(S(2), -b*sqrt(-S(4)*f*h + g**S(2))*(g/sqrt(-S(4)*f*h + g**S(2)) + S(2)*h*x/sqrt(-S(4)*f*h + g**S(2)) + S(1))/(S(2)*a*h - b*(g + sqrt(-S(4)*f*h + g**S(2)))))/sqrt(-S(4)*f*h + g**S(2)) + n*polylog(S(2), d*sqrt(-S(4)*f*h + g**S(2))*(-g/sqrt(-S(4)*f*h + g**S(2)) - S(2)*h*x/sqrt(-S(4)*f*h + g**S(2)) + S(1))/(S(2)*c*h - d*(g - sqrt(-S(4)*f*h + g**S(2)))))/sqrt(-S(4)*f*h + g**S(2)) - n*polylog(S(2), -d*sqrt(-S(4)*f*h + g**S(2))*(g/sqrt(-S(4)*f*h + g**S(2)) + S(2)*h*x/sqrt(-S(4)*f*h + g**S(2)) + S(1))/(S(2)*c*h - d*(g + sqrt(-S(4)*f*h + g**S(2)))))/sqrt(-S(4)*f*h + g**S(2)) - S(2)*log(e*((a + b*x)/(c + d*x))**n)*atanh((g + S(2)*h*x)/sqrt(-S(4)*f*h + g**S(2)))/sqrt(-S(4)*f*h + g**S(2)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**n/(-c**S(2)*x**S(2) + S(1)), x), x, -(a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**(n + S(1))/(b*c*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**S(3)/(-c**S(2)*x**S(2) + S(1)), x), x, -(a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**S(4)/(S(4)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**S(2)/(-c**S(2)*x**S(2) + S(1)), x), x, -(a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**S(3)/(S(3)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))/(-c**S(2)*x**S(2) + S(1)), x), x, -(a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**S(2)/(S(2)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))*(-c**S(2)*x**S(2) + S(1))), x), x, -log(a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))/(b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**S(2)*(-c**S(2)*x**S(2) + S(1))), x), x, S(1)/(b*c*(a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**S(3)*(-c**S(2)*x**S(2) + S(1))), x), x, S(1)/(S(2)*b*c*(a + b*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1))))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sqrt(-a*x + S(1))/sqrt(a*x + S(1)))/(-a**S(2)*x**S(2) + S(1)), x), x, -log(sqrt(-a*x + S(1))/sqrt(a*x + S(1)))**S(2)/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(a + b*exp(x)), x), x, -x**S(4)*log(S(1) + b*exp(x)/a)/S(4) + x**S(4)*log(a + b*exp(x))/S(4) - x**S(3)*polylog(S(2), -b*exp(x)/a) + S(3)*x**S(2)*polylog(S(3), -b*exp(x)/a) - S(6)*x*polylog(S(4), -b*exp(x)/a) + S(6)*polylog(S(5), -b*exp(x)/a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(a + b*exp(x)), x), x, -x**S(3)*log(S(1) + b*exp(x)/a)/S(3) + x**S(3)*log(a + b*exp(x))/S(3) - x**S(2)*polylog(S(2), -b*exp(x)/a) + S(2)*x*polylog(S(3), -b*exp(x)/a) - S(2)*polylog(S(4), -b*exp(x)/a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(a + b*exp(x)), x), x, -x**S(2)*log(S(1) + b*exp(x)/a)/S(2) + x**S(2)*log(a + b*exp(x))/S(2) - x*polylog(S(2), -b*exp(x)/a) + polylog(S(3), -b*exp(x)/a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*exp(x)), x), x, -x*log(S(1) + b*exp(x)/a) + x*log(a + b*exp(x)) - polylog(S(2), -b*exp(x)/a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*exp(x))/x, x), x, Integral(log(a + b*exp(x))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(e*(f**(c*(a + b*x)))**n + S(1)), x), x, -x**S(3)*polylog(S(2), -e*(f**(c*(a + b*x)))**n)/(b*c*n*log(f)) + S(3)*x**S(2)*polylog(S(3), -e*(f**(c*(a + b*x)))**n)/(b**S(2)*c**S(2)*n**S(2)*log(f)**S(2)) - S(6)*x*polylog(S(4), -e*(f**(c*(a + b*x)))**n)/(b**S(3)*c**S(3)*n**S(3)*log(f)**S(3)) + S(6)*polylog(S(5), -e*(f**(c*(a + b*x)))**n)/(b**S(4)*c**S(4)*n**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(e*(f**(c*(a + b*x)))**n + S(1)), x), x, -x**S(2)*polylog(S(2), -e*(f**(c*(a + b*x)))**n)/(b*c*n*log(f)) + S(2)*x*polylog(S(3), -e*(f**(c*(a + b*x)))**n)/(b**S(2)*c**S(2)*n**S(2)*log(f)**S(2)) - S(2)*polylog(S(4), -e*(f**(c*(a + b*x)))**n)/(b**S(3)*c**S(3)*n**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(e*(f**(c*(a + b*x)))**n + S(1)), x), x, -x*polylog(S(2), -e*(f**(c*(a + b*x)))**n)/(b*c*n*log(f)) + polylog(S(3), -e*(f**(c*(a + b*x)))**n)/(b**S(2)*c**S(2)*n**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(f**(c*(a + b*x)))**n + S(1)), x), x, -polylog(S(2), -e*(f**(c*(a + b*x)))**n)/(b*c*n*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(e*(f**(c*(a + b*x)))**n + S(1))/x, x), x, Integral(log(e*(f**(c*(a + b*x)))**n + S(1))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log(d + e*(f**(c*(a + b*x)))**n), x), x, -x**S(4)*log(S(1) + e*(f**(c*(a + b*x)))**n/d)/S(4) + x**S(4)*log(d + e*(f**(c*(a + b*x)))**n)/S(4) - x**S(3)*polylog(S(2), -e*(f**(c*(a + b*x)))**n/d)/(b*c*n*log(f)) + S(3)*x**S(2)*polylog(S(3), -e*(f**(c*(a + b*x)))**n/d)/(b**S(2)*c**S(2)*n**S(2)*log(f)**S(2)) - S(6)*x*polylog(S(4), -e*(f**(c*(a + b*x)))**n/d)/(b**S(3)*c**S(3)*n**S(3)*log(f)**S(3)) + S(6)*polylog(S(5), -e*(f**(c*(a + b*x)))**n/d)/(b**S(4)*c**S(4)*n**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*log(d + e*(f**(c*(a + b*x)))**n), x), x, -x**S(3)*log(S(1) + e*(f**(c*(a + b*x)))**n/d)/S(3) + x**S(3)*log(d + e*(f**(c*(a + b*x)))**n)/S(3) - x**S(2)*polylog(S(2), -e*(f**(c*(a + b*x)))**n/d)/(b*c*n*log(f)) + S(2)*x*polylog(S(3), -e*(f**(c*(a + b*x)))**n/d)/(b**S(2)*c**S(2)*n**S(2)*log(f)**S(2)) - S(2)*polylog(S(4), -e*(f**(c*(a + b*x)))**n/d)/(b**S(3)*c**S(3)*n**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(d + e*(f**(c*(a + b*x)))**n), x), x, -x**S(2)*log(S(1) + e*(f**(c*(a + b*x)))**n/d)/S(2) + x**S(2)*log(d + e*(f**(c*(a + b*x)))**n)/S(2) - x*polylog(S(2), -e*(f**(c*(a + b*x)))**n/d)/(b*c*n*log(f)) + polylog(S(3), -e*(f**(c*(a + b*x)))**n/d)/(b**S(2)*c**S(2)*n**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d + e*(f**(c*(a + b*x)))**n), x), x, -x*log(S(1) + e*(f**(c*(a + b*x)))**n/d) + x*log(d + e*(f**(c*(a + b*x)))**n) - polylog(S(2), -e*(f**(c*(a + b*x)))**n/d)/(b*c*n*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(d + e*(f**(c*(a + b*x)))**n)/x, x), x, Integral(log(d + e*(f**(c*(a + b*x)))**n)/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(b*(F**(e*(c + d*x)))**n + pi), x), x, x*log(pi) - polylog(S(2), -b*(F**(e*(c + d*x)))**n/pi)/(d*e*n*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*sin(a + b*x), x), x, -log(x)*cos(a + b*x)/b - sin(a)*Si(b*x)/b + cos(a)*Ci(b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*sin(a + b*x)**S(2), x), x, x*log(x)/S(2) - x/S(2) - log(x)*sin(a + b*x)*cos(a + b*x)/(S(2)*b) + sin(S(2)*a)*Ci(S(2)*b*x)/(S(4)*b) + cos(S(2)*a)*Si(S(2)*b*x)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*sin(a + b*x)**S(3), x), x, log(x)*cos(a + b*x)**S(3)/(S(3)*b) - log(x)*cos(a + b*x)/b - S(3)*sin(a)*Si(b*x)/(S(4)*b) + sin(S(3)*a)*Si(S(3)*b*x)/(S(12)*b) + S(3)*cos(a)*Ci(b*x)/(S(4)*b) - cos(S(3)*a)*Ci(S(3)*b*x)/(S(12)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*cos(a + b*x), x), x, log(x)*sin(a + b*x)/b - sin(a)*Ci(b*x)/b - cos(a)*Si(b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*cos(a + b*x)**S(2), x), x, x*log(x)/S(2) - x/S(2) + log(x)*sin(a + b*x)*cos(a + b*x)/(S(2)*b) - sin(S(2)*a)*Ci(S(2)*b*x)/(S(4)*b) - cos(S(2)*a)*Si(S(2)*b*x)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*cos(a + b*x)**S(3), x), x, -log(x)*sin(a + b*x)**S(3)/(S(3)*b) + log(x)*sin(a + b*x)/b - S(3)*sin(a)*Ci(b*x)/(S(4)*b) - sin(S(3)*a)*Ci(S(3)*b*x)/(S(12)*b) - S(3)*cos(a)*Si(b*x)/(S(4)*b) - cos(S(3)*a)*Si(S(3)*b*x)/(S(12)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*cos(x) + sin(x)/x, x), x, log(x)*sin(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sin(x)), x), x, I*x**S(2)/S(2) + x*log(a*sin(x)) - x*log(-exp(S(2)*I*x) + S(1)) + I*polylog(S(2), exp(S(2)*I*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sin(x)**S(2)), x), x, I*x**S(2) + x*log(a*sin(x)**S(2)) - S(2)*x*log(-exp(S(2)*I*x) + S(1)) + I*polylog(S(2), exp(S(2)*I*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sin(x)**n), x), x, I*n*x**S(2)/S(2) - n*x*log(-exp(S(2)*I*x) + S(1)) + I*n*polylog(S(2), exp(S(2)*I*x))/S(2) + x*log(a*sin(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*cos(x)), x), x, I*x**S(2)/S(2) + x*log(a*cos(x)) - x*log(exp(S(2)*I*x) + S(1)) + I*polylog(S(2), -exp(S(2)*I*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*cos(x)**S(2)), x), x, I*x**S(2) + x*log(a*cos(x)**S(2)) - S(2)*x*log(exp(S(2)*I*x) + S(1)) + I*polylog(S(2), -exp(S(2)*I*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*cos(x)**n), x), x, I*n*x**S(2)/S(2) - n*x*log(exp(S(2)*I*x) + S(1)) + I*n*polylog(S(2), -exp(S(2)*I*x))/S(2) + x*log(a*cos(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*tan(x)), x), x, x*log(a*tan(x)) + S(2)*x*atanh(exp(S(2)*I*x)) - I*polylog(S(2), -exp(S(2)*I*x))/S(2) + I*polylog(S(2), exp(S(2)*I*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*tan(x)**S(2)), x), x, x*log(a*tan(x)**S(2)) + S(4)*x*atanh(exp(S(2)*I*x)) - I*polylog(S(2), -exp(S(2)*I*x)) + I*polylog(S(2), exp(S(2)*I*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*tan(x)**n), x), x, S(2)*n*x*atanh(exp(S(2)*I*x)) - I*n*polylog(S(2), -exp(S(2)*I*x))/S(2) + I*n*polylog(S(2), exp(S(2)*I*x))/S(2) + x*log(a*tan(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*cot(x)), x), x, x*log(a*cot(x)) - S(2)*x*atanh(exp(S(2)*I*x)) + I*polylog(S(2), -exp(S(2)*I*x))/S(2) - I*polylog(S(2), exp(S(2)*I*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*cot(x)**S(2)), x), x, x*log(a*cot(x)**S(2)) - S(4)*x*atanh(exp(S(2)*I*x)) + I*polylog(S(2), -exp(S(2)*I*x)) - I*polylog(S(2), exp(S(2)*I*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*cot(x)**n), x), x, -S(2)*n*x*atanh(exp(S(2)*I*x)) + I*n*polylog(S(2), -exp(S(2)*I*x))/S(2) - I*n*polylog(S(2), exp(S(2)*I*x))/S(2) + x*log(a*cot(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sec(x)), x), x, -I*x**S(2)/S(2) + x*log(a*sec(x)) + x*log(exp(S(2)*I*x) + S(1)) - I*polylog(S(2), -exp(S(2)*I*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sec(x)**S(2)), x), x, -I*x**S(2) + x*log(a*sec(x)**S(2)) + S(2)*x*log(exp(S(2)*I*x) + S(1)) - I*polylog(S(2), -exp(S(2)*I*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sec(x)**n), x), x, -I*n*x**S(2)/S(2) + n*x*log(exp(S(2)*I*x) + S(1)) - I*n*polylog(S(2), -exp(S(2)*I*x))/S(2) + x*log(a*sec(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*csc(x)), x), x, -I*x**S(2)/S(2) + x*log(a*csc(x)) + x*log(-exp(S(2)*I*x) + S(1)) - I*polylog(S(2), exp(S(2)*I*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*csc(x)**S(2)), x), x, -I*x**S(2) + x*log(a*csc(x)**S(2)) + S(2)*x*log(-exp(S(2)*I*x) + S(1)) - I*polylog(S(2), exp(S(2)*I*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*csc(x)**n), x), x, -I*n*x**S(2)/S(2) + n*x*log(-exp(S(2)*I*x) + S(1)) - I*n*polylog(S(2), exp(S(2)*I*x))/S(2) + x*log(a*csc(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(-cos(S(2)*x)/S(2) + S(1)/2)*cos(x), x), x, log(-cos(S(2)*x)/S(2) + S(1)/2)*sin(x) - S(2)*sin(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(x)/log(E*sin(x)), x), x, log(log(E*sin(x))), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(cot(x)/log(E*sin(x)), x), x, log(log(sin(x)) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(x)/log(exp(sin(x))), x), x, log(log(exp(sin(x))))/(-log(exp(sin(x))) + sin(x)) - log(sin(x))/(-log(exp(sin(x))) + sin(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(cos(x))*sec(x)**S(2), x), x, -x + log(cos(x))*tan(x) + tan(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sin(x))*cot(x), x), x, log(sin(x))**S(2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sin(x))*sin(x)**S(2)*cos(x), x), x, log(sin(x))*sin(x)**S(3)/S(3) - sin(x)**S(3)/S(9), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sin(a/S(2) + b*x/S(2))*cos(a/S(2) + b*x/S(2)))*cos(a + b*x), x), x, log(sin(a/S(2) + b*x/S(2))*cos(a/S(2) + b*x/S(2)))*sin(a + b*x)/b - sin(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(x)/log(cos(x)), x), x, -log(log(cos(x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(cos(x))*tan(x), x), x, -log(cos(x))**S(2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(cos(x))*sin(x), x), x, -log(cos(x))*cos(x) + cos(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(cos(x))*cos(x), x), x, log(cos(x))*sin(x) - sin(x) + atanh(sin(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sin(x))*cos(x), x), x, log(sin(x))*sin(x) - sin(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sin(x))*sin(x)**S(2), x), x, I*x**S(2)/S(4) - x*log(-exp(S(2)*I*x) + S(1))/S(2) + x*log(sin(x))/S(2) + x/S(4) - log(sin(x))*sin(x)*cos(x)/S(2) + sin(x)*cos(x)/S(4) + I*polylog(S(2), exp(S(2)*I*x))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sin(x))*sin(x)**S(3), x), x, log(sin(x))*cos(x)**S(3)/S(3) - log(sin(x))*cos(x) - cos(x)**S(3)/S(9) + S(2)*cos(x)/S(3) - S(2)*atanh(cos(x))/S(3), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(sin(sqrt(x))), x), x, I*x**(S(3)/2)/S(3) + I*sqrt(x)*polylog(S(2), exp(S(2)*I*sqrt(x))) - x*log(-exp(S(2)*I*sqrt(x)) + S(1)) + x*log(sin(sqrt(x))) - polylog(S(3), exp(S(2)*I*sqrt(x)))/S(2), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(sin(x))*csc(x)**S(2), x), x, -x - log(sin(x))*cot(x) - cot(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*sinh(a + b*x), x), x, log(x)*cosh(a + b*x)/b - sinh(a)*Shi(b*x)/b - cosh(a)*Chi(b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*sinh(a + b*x)**S(2), x), x, -x*log(x)/S(2) + x/S(2) + log(x)*sinh(a + b*x)*cosh(a + b*x)/(S(2)*b) - sinh(S(2)*a)*Chi(S(2)*b*x)/(S(4)*b) - cosh(S(2)*a)*Shi(S(2)*b*x)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*sinh(a + b*x)**S(3), x), x, log(x)*cosh(a + b*x)**S(3)/(S(3)*b) - log(x)*cosh(a + b*x)/b + S(3)*sinh(a)*Shi(b*x)/(S(4)*b) - sinh(S(3)*a)*Shi(S(3)*b*x)/(S(12)*b) + S(3)*cosh(a)*Chi(b*x)/(S(4)*b) - cosh(S(3)*a)*Chi(S(3)*b*x)/(S(12)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*cosh(a + b*x), x), x, log(x)*sinh(a + b*x)/b - sinh(a)*Chi(b*x)/b - cosh(a)*Shi(b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*cosh(a + b*x)**S(2), x), x, x*log(x)/S(2) - x/S(2) + log(x)*sinh(a + b*x)*cosh(a + b*x)/(S(2)*b) - sinh(S(2)*a)*Chi(S(2)*b*x)/(S(4)*b) - cosh(S(2)*a)*Shi(S(2)*b*x)/(S(4)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*cosh(a + b*x)**S(3), x), x, log(x)*sinh(a + b*x)**S(3)/(S(3)*b) + log(x)*sinh(a + b*x)/b - S(3)*sinh(a)*Chi(b*x)/(S(4)*b) - sinh(S(3)*a)*Chi(S(3)*b*x)/(S(12)*b) - S(3)*cosh(a)*Shi(b*x)/(S(4)*b) - cosh(S(3)*a)*Shi(S(3)*b*x)/(S(12)*b), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(a*sinh(x)), x), x, x**S(2)/S(2) + x*log(a*sinh(x)) - x*log(-exp(S(2)*x) + S(1)) - polylog(S(2), exp(S(2)*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sinh(x)**S(2)), x), x, x**S(2) + x*log(a*sinh(x)**S(2)) - S(2)*x*log(-exp(S(2)*x) + S(1)) - polylog(S(2), exp(S(2)*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sinh(x)**n), x), x, n*x**S(2)/S(2) - n*x*log(-exp(S(2)*x) + S(1)) - n*polylog(S(2), exp(S(2)*x))/S(2) + x*log(a*sinh(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*cosh(x)), x), x, x**S(2)/S(2) + x*log(a*cosh(x)) - x*log(exp(S(2)*x) + S(1)) - polylog(S(2), -exp(S(2)*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*cosh(x)**S(2)), x), x, x**S(2) + x*log(a*cosh(x)**S(2)) - S(2)*x*log(exp(S(2)*x) + S(1)) - polylog(S(2), -exp(S(2)*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*cosh(x)**n), x), x, n*x**S(2)/S(2) - n*x*log(exp(S(2)*x) + S(1)) - n*polylog(S(2), -exp(S(2)*x))/S(2) + x*log(a*cosh(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(tanh(x)), x), x, x*log(tanh(x)) + S(2)*x*atanh(exp(S(2)*x)) + polylog(S(2), -exp(S(2)*x))/S(2) - polylog(S(2), exp(S(2)*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*tanh(x)), x), x, x*log(a*tanh(x)) + S(2)*x*atanh(exp(S(2)*x)) + polylog(S(2), -exp(S(2)*x))/S(2) - polylog(S(2), exp(S(2)*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*tanh(x)**S(2)), x), x, x*log(a*tanh(x)**S(2)) + S(4)*x*atanh(exp(S(2)*x)) + polylog(S(2), -exp(S(2)*x)) - polylog(S(2), exp(S(2)*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*tanh(x)**n), x), x, S(2)*n*x*atanh(exp(S(2)*x)) + n*polylog(S(2), -exp(S(2)*x))/S(2) - n*polylog(S(2), exp(S(2)*x))/S(2) + x*log(a*tanh(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(coth(x)), x), x, x*log(coth(x)) - S(2)*x*atanh(exp(S(2)*x)) - polylog(S(2), -exp(S(2)*x))/S(2) + polylog(S(2), exp(S(2)*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*coth(x)), x), x, x*log(a*coth(x)) - S(2)*x*atanh(exp(S(2)*x)) - polylog(S(2), -exp(S(2)*x))/S(2) + polylog(S(2), exp(S(2)*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*coth(x)**S(2)), x), x, x*log(a*coth(x)**S(2)) - S(4)*x*atanh(exp(S(2)*x)) - polylog(S(2), -exp(S(2)*x)) + polylog(S(2), exp(S(2)*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*coth(x)**n), x), x, -S(2)*n*x*atanh(exp(S(2)*x)) - n*polylog(S(2), -exp(S(2)*x))/S(2) + n*polylog(S(2), exp(S(2)*x))/S(2) + x*log(a*coth(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sech(x)), x), x, -x**S(2)/S(2) + x*log(a*sech(x)) + x*log(exp(S(2)*x) + S(1)) + polylog(S(2), -exp(S(2)*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sech(x)**S(2)), x), x, -x**S(2) + x*log(a*sech(x)**S(2)) + S(2)*x*log(exp(S(2)*x) + S(1)) + polylog(S(2), -exp(S(2)*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*sech(x)**n), x), x, -n*x**S(2)/S(2) + n*x*log(exp(S(2)*x) + S(1)) + n*polylog(S(2), -exp(S(2)*x))/S(2) + x*log(a*sech(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*csch(x)), x), x, -x**S(2)/S(2) + x*log(a*csch(x)) + x*log(-exp(S(2)*x) + S(1)) + polylog(S(2), exp(S(2)*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*csch(x)**S(2)), x), x, -x**S(2) + x*log(a*csch(x)**S(2)) + S(2)*x*log(-exp(S(2)*x) + S(1)) + polylog(S(2), exp(S(2)*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*csch(x)**n), x), x, -n*x**S(2)/S(2) + n*x*log(-exp(S(2)*x) + S(1)) + n*polylog(S(2), exp(S(2)*x))/S(2) + x*log(a*csch(x)**n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(cosh(x)**S(2))*sinh(x), x), x, log(cosh(x)**S(2))*cosh(x) - S(2)*cosh(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/sqrt(x), x), x, S(2)*sqrt(x)*log(x) - S(4)*sqrt(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(-S(3)*x**S(2) + S(2)), x), x, -x**S(2)/S(2) - (-x**S(2)/S(2) + S(1)/3)*log(-S(3)*x**S(2) + S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(-log(x)**S(2) + S(1))), x), x, asin(log(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(16)*x**S(3)*log(x)**S(2), x), x, S(4)*x**S(4)*log(x)**S(2) - S(2)*x**S(4)*log(x) + x**S(4)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sqrt(a + b*x)), x), x, -x/S(2) + (a + b*x)*log(sqrt(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(sqrt(x + S(2))), x), x, x**S(2)*log(sqrt(x + S(2)))/S(2) - x**S(2)/S(8) + x/S(2) - log(x + S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log((S(3)*x + S(1))**(S(1)/3)), x), x, x**S(2)*log((S(3)*x + S(1))**(S(1)/3))/S(2) - x**S(2)/S(12) + x/S(18) - log(S(3)*x + S(1))/S(54), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(x**S(3) + x), x), x, x**S(2)*log(x**S(3) + x)/S(2) - S(3)*x**S(2)/S(4) + log(x**S(2) + S(1))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x + sqrt(x**S(2) + S(1))), x), x, x*log(x + sqrt(x**S(2) + S(1))) - sqrt(x**S(2) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x + sqrt(x**S(2) + S(-1))), x), x, x*log(x + sqrt(x**S(2) + S(-1))) - sqrt(x**S(2) + S(-1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x - sqrt(x**S(2) + S(-1))), x), x, x*log(x - sqrt(x**S(2) + S(-1))) + sqrt(x**S(2) + S(-1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sqrt(x) + sqrt(x + S(1))), x), x, -sqrt(x)*sqrt(x + S(1))/S(2) + x*log(sqrt(x) + sqrt(x + S(1))) + asinh(sqrt(x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(S(1)/3)*log(x), x), x, S(3)*x**(S(4)/3)*log(x)/S(4) - S(9)*x**(S(4)/3)/S(16), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**log(x), x), x, x**(log(S(2)) + S(1))/(log(S(2)) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((-log(x) + S(1))/x**S(2), x), x, log(x)/x, expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((-log(x) + S(1))/x**S(2), x), x, (log(x) + S(-1))/x + S(1)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x + sqrt(x + S(1)) + S(1)), x), x, x*log(x + sqrt(x + S(1)) + S(1)) - x + sqrt(x + S(1)) + log(x + S(1))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**S(3) + x), x), x, x*log(x**S(3) + x) - S(3)*x + S(2)*atan(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**log(S(7)*x + S(-8)), x), x, (S(7)*x + S(-8))**(log(S(2)) + S(1))/(S(7)*(log(S(2)) + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((S(5)*x + S(-11))/(S(76)*x + S(5))), x), x, (x + S(-11)/5)*log((S(5)*x + S(-11))/(S(76)*x + S(5))) - S(861)*log(S(76)*x + S(5))/S(380), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((x + S(1))/(x + S(-1)))/x**S(2), x), x, S(2)*log(x) - S(2)*log(-x + S(1)) - (x + S(1))*log((-x + S(-1))/(-x + S(1)))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(1)/(x + S(13))), x), x, x + (x + S(13))*log(S(1)/(x + S(13))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log((x + S(1))/x**S(2)), x), x, x**S(2)*log((x + S(1))/x**S(2))/S(2) + x**S(2)/S(4) + x/S(2) - log(x + S(1))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*log((S(5)*x + S(7))/x**S(2)), x), x, x**S(4)*log((S(5)*x + S(7))/x**S(2))/S(4) + x**S(4)/S(16) + S(7)*x**S(3)/S(60) - S(49)*x**S(2)/S(200) + S(343)*x/S(500) - S(2401)*log(S(5)*x + S(7))/S(2500), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*log(a + b*x), x), x, -a*x/S(2) - b*x**S(2)/S(4) + (a + b*x)**S(2)*log(a + b*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*log(a + b*x), x), x, (a + b*x)**S(3)*log(a + b*x)/(S(3)*b) - (a + b*x)**S(3)/(S(9)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*x)/(a + b*x), x), x, log(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a + b*x)/(a + b*x)**S(2), x), x, -log(a + b*x)/(b*(a + b*x)) - S(1)/(b*(a + b*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**n*log(a + b*x), x), x, (a + b*x)**(n + S(1))*log(a + b*x)/(b*(n + S(1))) - (a + b*x)**(n + S(1))/(b*(n + S(1))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*log(b*x)**p), x), x, x*log(a*log(b*x)**p) - p*li(b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*log(b*x**n)**p), x), x, -p*x*(b*x**n)**(-S(1)/n)*Ei(log(b*x**n)/n) + x*log(a*log(b*x**n)**p), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*log(b*x)**p)/x, x), x, -(p - log(a*log(b*x)**p))*log(b*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(a*log(b*x**n)**p)/x, x), x, -(p - log(a*log(b*x**n)**p))*log(b*x**n)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(a*log(b*x)**p), x), x, -p*x**(m + S(1))*(b*x)**(-m + S(-1))*Ei((m + S(1))*log(b*x))/(m + S(1)) + x**(m + S(1))*log(a*log(b*x)**p)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*log(a*log(b*x**n)**p), x), x, -p*x**(m + S(1))*(b*x**n)**(-(m + S(1))/n)*Ei((m + S(1))*log(b*x**n)/n)/(m + S(1)) + x**(m + S(1))*log(a*log(b*x**n)**p)/(m + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/sqrt(a + b*log(x)), x), x, -sqrt(pi)*a*exp(-a/b)*erfi(sqrt(a + b*log(x))/sqrt(b))/b**(S(3)/2) + x*sqrt(a + b*log(x))/b - sqrt(pi)*exp(-a/b)*erfi(sqrt(a + b*log(x))/sqrt(b))/(S(2)*sqrt(b)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/sqrt(a - b*log(x)), x), x, -sqrt(pi)*a*exp(a/b)*erf(sqrt(a - b*log(x))/sqrt(b))/b**(S(3)/2) - x*sqrt(a - b*log(x))/b + sqrt(pi)*exp(a/b)*erf(sqrt(a - b*log(x))/sqrt(b))/(S(2)*sqrt(b)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((A + B*log(x))/sqrt(a + b*log(x)), x), x, B*x*sqrt(a + b*log(x))/b - sqrt(pi)*B*exp(-a/b)*erfi(sqrt(a + b*log(x))/sqrt(b))/(S(2)*sqrt(b)) + sqrt(pi)*(A*b - B*a)*exp(-a/b)*erfi(sqrt(a + b*log(x))/sqrt(b))/b**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((A + B*log(x))/sqrt(a - b*log(x)), x), x, -B*x*sqrt(a - b*log(x))/b + sqrt(pi)*B*exp(a/b)*erf(sqrt(a - b*log(x))/sqrt(b))/(S(2)*sqrt(b)) + sqrt(pi)*(-A*b - B*a)*exp(a/b)*erf(sqrt(a - b*log(x))/sqrt(b))/b**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(x)/sqrt(x**S(2) + S(-1)), x), x, sqrt(x**S(2) + S(-1))*log(x) - sqrt(x**S(2) + S(-1)) + atan(sqrt(x**S(2) + S(-1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*sqrt(x**S(2) + S(4))*log(x), x), x, (x**S(2) + S(4))**(S(3)/2)*log(x)/S(3) - (x**S(2) + S(4))**(S(3)/2)/S(9) - S(4)*sqrt(x**S(2) + S(4))/S(3) + S(8)*atanh(sqrt(x**S(2) + S(4))/S(2))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a*x + b*x*log(c*x**n)), x), x, log(a + b*log(c*x**n))/(b*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a*x + b*x*log(c*x**n)**S(2)), x), x, atan(sqrt(b)*log(c*x**n)/sqrt(a))/(sqrt(a)*sqrt(b)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a*x + b*x*log(c*x**n)**S(3)), x), x, log(a**(S(1)/3) + b**(S(1)/3)*log(c*x**n))/(S(3)*a**(S(2)/3)*b**(S(1)/3)*n) - log(a**(S(2)/3) - a**(S(1)/3)*b**(S(1)/3)*log(c*x**n) + b**(S(2)/3)*log(c*x**n)**S(2))/(S(6)*a**(S(2)/3)*b**(S(1)/3)*n) - sqrt(S(3))*atan(sqrt(S(3))*(a**(S(1)/3) - S(2)*b**(S(1)/3)*log(c*x**n))/(S(3)*a**(S(1)/3)))/(S(3)*a**(S(2)/3)*b**(S(1)/3)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a*x + b*x*log(c*x**n)**S(4)), x), x, -sqrt(S(2))*log(-sqrt(S(2))*a**(S(1)/4)*b**(S(1)/4)*log(c*x**n) + sqrt(a) + sqrt(b)*log(c*x**n)**S(2))/(S(8)*a**(S(3)/4)*b**(S(1)/4)*n) + sqrt(S(2))*log(sqrt(S(2))*a**(S(1)/4)*b**(S(1)/4)*log(c*x**n) + sqrt(a) + sqrt(b)*log(c*x**n)**S(2))/(S(8)*a**(S(3)/4)*b**(S(1)/4)*n) - sqrt(S(2))*atan(S(1) - sqrt(S(2))*b**(S(1)/4)*log(c*x**n)/a**(S(1)/4))/(S(4)*a**(S(3)/4)*b**(S(1)/4)*n) + sqrt(S(2))*atan(S(1) + sqrt(S(2))*b**(S(1)/4)*log(c*x**n)/a**(S(1)/4))/(S(4)*a**(S(3)/4)*b**(S(1)/4)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a*x + b*x/log(c*x**n)), x), x, log(x)/a - b*log(a*log(c*x**n) + b)/(a**S(2)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a*x + b*x/log(c*x**n)**S(2)), x), x, log(x)/a - sqrt(b)*atan(sqrt(a)*log(c*x**n)/sqrt(b))/(a**(S(3)/2)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a*x + b*x/log(c*x**n)**S(3)), x), x, log(x)/a - b**(S(1)/3)*log(a**(S(1)/3)*log(c*x**n) + b**(S(1)/3))/(S(3)*a**(S(4)/3)*n) + b**(S(1)/3)*log(a**(S(2)/3)*log(c*x**n)**S(2) - a**(S(1)/3)*b**(S(1)/3)*log(c*x**n) + b**(S(2)/3))/(S(6)*a**(S(4)/3)*n) + sqrt(S(3))*b**(S(1)/3)*atan(sqrt(S(3))*(-S(2)*a**(S(1)/3)*log(c*x**n) + b**(S(1)/3))/(S(3)*b**(S(1)/3)))/(S(3)*a**(S(4)/3)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a*x + b*x/log(c*x**n)**S(4)), x), x, log(x)/a + sqrt(S(2))*b**(S(1)/4)*log(-sqrt(S(2))*a**(S(1)/4)*b**(S(1)/4)*log(c*x**n) + sqrt(a)*log(c*x**n)**S(2) + sqrt(b))/(S(8)*a**(S(5)/4)*n) - sqrt(S(2))*b**(S(1)/4)*log(sqrt(S(2))*a**(S(1)/4)*b**(S(1)/4)*log(c*x**n) + sqrt(a)*log(c*x**n)**S(2) + sqrt(b))/(S(8)*a**(S(5)/4)*n) - sqrt(S(2))*b**(S(1)/4)*atan(sqrt(S(2))*a**(S(1)/4)*log(c*x**n)/b**(S(1)/4) + S(-1))/(S(4)*a**(S(5)/4)*n) - sqrt(S(2))*b**(S(1)/4)*atan(sqrt(S(2))*a**(S(1)/4)*log(c*x**n)/b**(S(1)/4) + S(1))/(S(4)*a**(S(5)/4)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(S(4)*x*log(x)**S(2) + x), x), x, log(S(4)*log(x)**S(2) + S(1))/S(8), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(S(7)*x)**S(2) + x*log(S(7)*x) + x), x), x, S(2)*sqrt(S(3))*atan(sqrt(S(3))*(S(2)*log(S(7)*x) + S(1))/S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((log(S(3)*x) + S(-1))/(x*(log(S(3)*x)**S(2) - log(S(3)*x) + S(1))), x), x, log(log(S(3)*x)**S(2) - log(S(3)*x) + S(1))/S(2) + sqrt(S(3))*atan(sqrt(S(3))*(-S(2)*log(S(3)*x) + S(1))/S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((log(S(3)*x)**S(2) + S(-1))/(x*log(S(3)*x)**S(3) + x), x), x, log(log(S(3)*x)**S(2) - log(S(3)*x) + S(1))/S(2) + sqrt(S(3))*atan(sqrt(S(3))*(-S(2)*log(S(3)*x) + S(1))/S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((log(S(3)*x)**S(2) + S(-1))/(x*log(S(3)*x)**S(2) + x*log(S(3)*x) + x), x), x, log(x) - log(log(S(3)*x)**S(2) + log(S(3)*x) + S(1))/S(2) - sqrt(S(3))*atan(sqrt(S(3))*(S(2)*log(S(3)*x) + S(1))/S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(log(x) + S(3))), x), x, log(log(x) + S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(log(x) + S(1))/x, x), x, S(2)*(log(x) + S(1))**(S(3)/2)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((log(x) + S(1))**S(5)/x, x), x, (log(x) + S(1))**S(6)/S(6), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(log(x))), x), x, S(2)*sqrt(log(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(log(x)**S(2) + S(1))), x), x, atan(log(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(log(x)**S(2) + S(-3))), x), x, atanh(log(x)/sqrt(log(x)**S(2) + S(-3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(-S(9)*log(x)**S(2) + S(4))), x), x, asin(S(3)*log(x)/S(2))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*sqrt(log(x)**S(2) + S(4))), x), x, asinh(log(x)/S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(S(3)*log(S(6)*x)**S(3) + S(2))), x), x, S(2)**(S(1)/3)*S(3)**(S(2)/3)*log(S(3)**(S(1)/3)*log(S(6)*x) + S(2)**(S(1)/3))/S(18) - S(2)**(S(1)/3)*S(3)**(S(2)/3)*log(S(3)**(S(2)/3)*log(S(6)*x)**S(2) - S(6)**(S(1)/3)*log(S(6)*x) + S(2)**(S(2)/3))/S(36) - S(2)**(S(1)/3)*S(3)**(S(1)/6)*atan(sqrt(S(3))*(-S(2)**(S(2)/3)*S(3)**(S(1)/3)*log(S(6)*x) + S(1))/S(3))/S(6), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(log(S(6)*x))/(x*log(S(6)*x)), x), x, log(log(S(6)*x))**S(2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**log(x)/x, x), x, S(2)**log(x)/log(S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(log(x))**S(2)/x, x), x, log(x)/S(2) - sin(log(x))*cos(log(x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((-log(x) + S(7))/(x*(log(x) + S(3))), x), x, -log(x) + S(10)*log(log(x) + S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((-log(x) + S(2))*(log(x) + S(3))**S(2)/x, x), x, -log(x)**S(4)/S(4) - S(4)*log(x)**S(3)/S(3) + S(3)*log(x)**S(2)/S(2) + S(18)*log(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(log(x)**S(2) + S(1))*log(x)**S(2)/x, x), x, sqrt(log(x)**S(2) + S(1))*log(x)**S(3)/S(4) + sqrt(log(x)**S(2) + S(1))*log(x)/S(8) - asinh(log(x))/S(8), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((log(x) + S(1))/(x*(S(2)*log(x) + S(3))**S(2)), x), x, log(S(2)*log(x) + S(3))/S(4) + S(1)/(S(4)*(S(2)*log(x) + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(x*sqrt(log(x) + S(1))), x), x, S(2)*(log(x) + S(1))**(S(3)/2)/S(3) - S(2)*sqrt(log(x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(x*sqrt(S(4)*log(x) + S(-1))), x), x, (S(4)*log(x) + S(-1))**(S(3)/2)/S(24) + sqrt(S(4)*log(x) + S(-1))/S(8), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(log(x) + S(1))/(x*log(x)), x), x, S(2)*sqrt(log(x) + S(1)) - S(2)*atanh(sqrt(log(x) + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((log(x)**S(2) - S(4)*log(x) + S(1))/(x*(log(x) + S(-1))**S(4)), x), x, (log(x) + S(-1))**(S(-2)) + S(1)/(-log(x) + S(1)) - S(2)/(S(3)*(-log(x) + S(1))**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(S(1)/x)**S(2)/x**S(5), x), x, -log(S(1)/x)**S(2)/(S(4)*x**S(4)) + log(S(1)/x)/(S(8)*x**S(4)) - S(1)/(S(32)*x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((log(a*x**n)**S(2))**p/x, x), x, (log(a*x**n)**S(2))**p*log(a*x**n)/(n*(S(2)*p + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((log(a*x**n)**m)**p/x, x), x, (log(a*x**n)**m)**p*log(a*x**n)/(n*(m*p + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(log(a*x**n)**S(2))/x, x), x, sqrt(log(a*x**n)**S(2))*log(a*x**n)/(S(2)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*log(a*x**n)**m)**p/x, x), x, (b*log(a*x**n)**m)**p*log(a*x**n)/(n*(m*p + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(-log(a*x**S(2))), x), x, -sqrt(S(2))*sqrt(pi)*x*erf(sqrt(S(2))*sqrt(-log(a*x**S(2)))/S(2))/(S(2)*sqrt(a*x**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(-log(a/x**S(2))), x), x, sqrt(S(2))*sqrt(pi)*x*sqrt(a/x**S(2))*erfi(sqrt(S(2))*sqrt(-log(a/x**S(2)))/S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(-log(a*x**n)), x), x, -sqrt(pi)*x*(a*x**n)**(-S(1)/n)*erf(sqrt(-log(a*x**n))/sqrt(n))/sqrt(n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sqrt(x) - x + S(1))/x, x), x, -S(2)*log(sqrt(x))*log((-S(2)*sqrt(x) - sqrt(S(5)) + S(1))/(-sqrt(S(5)) + S(1))) + S(2)*log(sqrt(x))*log(sqrt(x) - x + S(1)) - S(2)*log(S(1)/2 + sqrt(S(5))/S(2))*log(-S(2)*sqrt(x) + S(1) + sqrt(S(5))) - S(2)*polylog(S(2), S(2)*sqrt(x)/(-sqrt(S(5)) + S(1))) + S(2)*polylog(S(2), (-S(2)*sqrt(x) + S(1) + sqrt(S(5)))/(S(1) + sqrt(S(5)))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(c + d*x)/(a + b*x), x), x, -a*log(-d*(a + b*x)/(-a*d + b*c))*log(c + d*x)/b**S(2) - a*polylog(S(2), b*(c + d*x)/(-a*d + b*c))/b**S(2) - x/b + (c + d*x)*log(c + d*x)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(x + S(-1)), x), x, -polylog(S(2), -x + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*log(-a - b*x + S(1))/(a + b*x), x), x, a*polylog(S(2), a + b*x)/b**S(2) - x/b - (-a - b*x + S(1))*log(-a - b*x + S(1))/b**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*log(x)/(x*(b + c*x)), x), x, log(x)**S(2)/S(2) + log(x)*log((b + c*x)/b) + polylog(S(2), -c*x/b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)*sin(x*log(x)) + sin(x*log(x)), x), x, -cos(x*log(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((-(x + S(-1))**S(2) + S(1))/((x + S(-1))**S(2) + S(1)))/x**S(2), x), x, log(x)/S(2) + log(-x + S(2))/S(2) - log(x**S(2) - S(2)*x + S(2))/S(2) - atan(x + S(-1)) - log((-(-x + S(1))**S(2) + S(1))/((x + S(-1))**S(2) + S(1)))/x - S(1)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(sqrt(x) + x), x), x, sqrt(x) + x*log(sqrt(x) + x) - x - log(sqrt(x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(-x/(x + S(1))), x), x, x*log(-x/(x + S(1))) - log(x + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((x + S(-1))/(x + S(1))), x), x, (x + S(-1))*log((x + S(-1))/(x + S(1))) - S(2)*log(x + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log((-x**S(2) + S(1))/(x**S(2) + S(1)))/(x + S(1))**S(2), x), x, log(-x**S(2) + S(1))/S(2) - log(x**S(2) + S(1))/S(2) - atan(x) - log((-x**S(2) + S(1))/(x**S(2) + S(1)))/(x + S(1)) - S(1)/(x + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(-x**S(2) + S(1)), x), x, log(x)*atanh(x) + polylog(S(2), -x)/S(2) - polylog(S(2), x)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x)/(x**S(2) + S(1)), x), x, log(x)*atan(x) - I*polylog(S(2), -I*x)/S(2) + I*polylog(S(2), I*x)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(x**S(2) + S(1))**n)/(x**S(2) + S(1)), x), x, S(2)*n*log(S(2)*I/(-x + I))*atan(x) + I*n*atan(x)**S(2) + I*n*polylog(S(2), (-x - I)/(-x + I)) + log(c*(x**S(2) + S(1))**n)*atan(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(x**S(2)/(x**S(2) + S(1)))/(x**S(2) + S(1)), x), x, -S(2)*log(S(2)*x/(x + I))*atan(x) + log(x**S(2)/(x**S(2) + S(1)))*atan(x) + I*atan(x)**S(2) + I*polylog(S(2), (-x + I)/(x + I)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**n)/(a + b*x**S(2)), x), x, -I*n*polylog(S(2), -I*sqrt(b)*x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)) + I*n*polylog(S(2), I*sqrt(b)*x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)) + log(c*x**n)*atan(sqrt(b)*x/sqrt(a))/(sqrt(a)*sqrt(b)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*(a + b*x**S(2))**n)/(a + b*x**S(2)), x), x, S(2)*n*log(S(2)*I*sqrt(a)/(I*sqrt(a) - sqrt(b)*x))*atan(sqrt(b)*x/sqrt(a))/(sqrt(a)*sqrt(b)) + I*n*atan(sqrt(b)*x/sqrt(a))**S(2)/(sqrt(a)*sqrt(b)) + I*n*polylog(S(2), (-sqrt(a) + I*sqrt(b)*x)/(sqrt(a) + I*sqrt(b)*x))/(sqrt(a)*sqrt(b)) + log(c*(a + b*x**S(2))**n)*atan(sqrt(b)*x/sqrt(a))/(sqrt(a)*sqrt(b)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(c*x**S(2)/(a + b*x**S(2)))/(a + b*x**S(2)), x), x, -S(2)*log(S(2)*sqrt(b)*x/(I*sqrt(a) + sqrt(b)*x))*atan(sqrt(b)*x/sqrt(a))/(sqrt(a)*sqrt(b)) + log(c*x**S(2)/(a + b*x**S(2)))*atan(sqrt(b)*x/sqrt(a))/(sqrt(a)*sqrt(b)) + I*atan(sqrt(b)*x/sqrt(a))**S(2)/(sqrt(a)*sqrt(b)) + I*polylog(S(2), (sqrt(a) + I*sqrt(b)*x)/(sqrt(a) - I*sqrt(b)*x))/(sqrt(a)*sqrt(b)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(I*sqrt(-a*x + S(1))/sqrt(a*x + S(1)) + S(1))/(-a**S(2)*x**S(2) + S(1)), x), x, polylog(S(2), -I*sqrt(-a*x + S(1))/sqrt(a*x + S(1)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(-I*sqrt(-a*x + S(1))/sqrt(a*x + S(1)) + S(1))/(-a**S(2)*x**S(2) + S(1)), x), x, polylog(S(2), I*sqrt(-a*x + S(1))/sqrt(a*x + S(1)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(exp(a + b*x)), x), x, log(exp(a + b*x))**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(log(exp(a + b*x**n)), x), x, -b*n*x**(n + S(1))/(n + S(1)) + x*log(exp(a + b*x**n)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)*log(a + b*exp(x)), x), x, -exp(x) + (a + b*exp(x))*log(a + b*exp(x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*log(exp(x))), x), x, -log(x)/(x - log(exp(x))) + log(log(exp(x)))/(x - log(exp(x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a + b*x)*log(x), x), x, -exp(a)*Ei(b*x)/b + exp(a + b*x)*log(x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(x + log(x)), x), x, Integral(x**S(2)/(x + log(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(x + log(x)), x), x, Integral(x/(x + log(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x + log(x)), x), x, Integral(S(1)/(x + log(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(x + log(x))), x), x, Integral(S(1)/(x*(x + log(x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x**S(2)*(x + log(x))), x), x, Integral(S(1)/(x**S(2)*(x + log(x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((-log(x) + S(1))/(x*(x + log(x))), x), x, log(S(1) + log(x)/x), expand=True, _diff=True, _numerical=True)

    ''' apart
    # apart assert rubi_test(rubi_integrate((x + S(1))/((x + log(x))*log(x)), x), x, -log(x + log(x)) + log(log(x)) + li(x), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((x + S(1))/((x + log(x))*log(x)), x), x, -log(x + log(x)) + log(log(x)) + Ei(log(x)), expand=True, _diff=True, _numerical=True)

    # assert rubi_test(rubi_integrate(log(sqrt((x + S(1))/x) + S(2)), x), x, x*log(sqrt((x + S(1))/x) + S(2)) - log(-sqrt((x + S(1))/x) + S(1))/S(6) + log(sqrt((x + S(1))/x) + S(1))/S(2) - log(sqrt((x + S(1))/x) + S(2))/S(3), expand=True, _diff=True, _numerical=True)
    # assert rubi_test(rubi_integrate(log(sqrt((x + S(1))/x) + S(1)), x), x, x*log(sqrt((x + S(1))/x) + S(1)) + atanh(sqrt((x + S(1))/x))/S(2) - S(1)/(S(2)*(sqrt((x + S(1))/x) + S(1))), expand=True, _diff=True, _numerical=True)
    # assert rubi_test(rubi_integrate(log(sqrt((x + S(1))/x)), x), x, (x + S(1))*log(sqrt((x + S(1))/x)) + log(x)/S(2), expand=True, _diff=True, _numerical=True)
    # assert rubi_test(rubi_integrate(log(sqrt((x + S(1))/x) + S(-1)), x), x, x*log(sqrt((x + S(1))/x) + S(-1)) - atanh(sqrt(S(1) + S(1)/x))/S(2) - S(1)/(S(2)*(-sqrt(S(1) + S(1)/x) + S(1))), expand=True, _diff=True, _numerical=True)
    # assert rubi_test(rubi_integrate(log(sqrt((x + S(1))/x) + S(-2)), x), x, x*log(sqrt((x + S(1))/x) + S(-2)) + log(-sqrt(S(1) + S(1)/x) + S(1))/S(2) - log(-sqrt(S(1) + S(1)/x) + S(2))/S(3) - log(sqrt(S(1) + S(1)/x) + S(1))/S(6), expand=True, _diff=True, _numerical=True)
    '''
    assert rubi_test(rubi_integrate(x**(a*x)*log(x) + x**(a*x), x), x, x**(a*x)/a, expand=True, _diff=True, _numerical=True)

    # fails in mathematica too assert rubi_test(rubi_integrate((log(x)**m)**p, x), x, (-log(x))**(-m*p)*(log(x)**m)**p*Gamma(m*p + S(1), -log(x)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(log(a + b*x + c*sqrt(d + e*x))/(f + g*x**S(2)), x), x, -log((a*e - b*d + b*(d + e*x) + c*e*sqrt(d + e*x))/e)*log(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) + log((a*e - b*d + b*(d + e*x) + c*e*sqrt(d + e*x))/e)*log(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) - log((a*e - b*d + b*(d + e*x) + c*e*sqrt(d + e*x))/e)*log(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) + log((a*e - b*d + b*(d + e*x) + c*e*sqrt(d + e*x))/e)*log(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) + log(-g**(S(1)/4)*(S(2)*b*sqrt(d + e*x) + c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))/(S(2)*b*sqrt(d*sqrt(g) - e*sqrt(-f)) - g**(S(1)/4)*(c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))*log(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) + log(g**(S(1)/4)*(S(2)*b*sqrt(d + e*x) + c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))/(S(2)*b*sqrt(d*sqrt(g) - e*sqrt(-f)) + g**(S(1)/4)*(c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))*log(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) + log(-g**(S(1)/4)*(S(2)*b*sqrt(d + e*x) + c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))/(S(2)*b*sqrt(d*sqrt(g) - e*sqrt(-f)) - g**(S(1)/4)*(c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))*log(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) + log(g**(S(1)/4)*(S(2)*b*sqrt(d + e*x) + c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))/(S(2)*b*sqrt(d*sqrt(g) - e*sqrt(-f)) + g**(S(1)/4)*(c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))*log(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) - log(-g**(S(1)/4)*(S(2)*b*sqrt(d + e*x) + c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))/(S(2)*b*sqrt(d*sqrt(g) + e*sqrt(-f)) - g**(S(1)/4)*(c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))*log(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) - log(g**(S(1)/4)*(S(2)*b*sqrt(d + e*x) + c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))/(S(2)*b*sqrt(d*sqrt(g) + e*sqrt(-f)) + g**(S(1)/4)*(c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))*log(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) - log(-g**(S(1)/4)*(S(2)*b*sqrt(d + e*x) + c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))/(S(2)*b*sqrt(d*sqrt(g) + e*sqrt(-f)) - g**(S(1)/4)*(c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))*log(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) - log(g**(S(1)/4)*(S(2)*b*sqrt(d + e*x) + c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))/(S(2)*b*sqrt(d*sqrt(g) + e*sqrt(-f)) + g**(S(1)/4)*(c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))*log(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*sqrt(g)*sqrt(-f)) + polylog(S(2), S(2)*b*(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*b*sqrt(d*sqrt(g) - e*sqrt(-f)) - g**(S(1)/4)*(c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))/(S(2)*sqrt(g)*sqrt(-f)) + polylog(S(2), S(2)*b*(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*b*sqrt(d*sqrt(g) - e*sqrt(-f)) + g**(S(1)/4)*(c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))/(S(2)*sqrt(g)*sqrt(-f)) + polylog(S(2), S(2)*b*(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*b*sqrt(d*sqrt(g) - e*sqrt(-f)) - g**(S(1)/4)*(c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))/(S(2)*sqrt(g)*sqrt(-f)) + polylog(S(2), S(2)*b*(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) - e*sqrt(-f)))/(S(2)*b*sqrt(d*sqrt(g) - e*sqrt(-f)) + g**(S(1)/4)*(c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))/(S(2)*sqrt(g)*sqrt(-f)) - polylog(S(2), S(2)*b*(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*b*sqrt(d*sqrt(g) + e*sqrt(-f)) - g**(S(1)/4)*(c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))/(S(2)*sqrt(g)*sqrt(-f)) - polylog(S(2), S(2)*b*(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*b*sqrt(d*sqrt(g) + e*sqrt(-f)) + g**(S(1)/4)*(c*e - sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))/(S(2)*sqrt(g)*sqrt(-f)) - polylog(S(2), S(2)*b*(g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*b*sqrt(d*sqrt(g) + e*sqrt(-f)) - g**(S(1)/4)*(c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))/(S(2)*sqrt(g)*sqrt(-f)) - polylog(S(2), S(2)*b*(-g**(S(1)/4)*sqrt(d + e*x) + sqrt(d*sqrt(g) + e*sqrt(-f)))/(S(2)*b*sqrt(d*sqrt(g) + e*sqrt(-f)) + g**(S(1)/4)*(c*e + sqrt(-S(4)*a*b*e + S(4)*b**S(2)*d + c**S(2)*e**S(2)))))/(S(2)*sqrt(g)*sqrt(-f)), expand=True, _diff=True, _numerical=True)
