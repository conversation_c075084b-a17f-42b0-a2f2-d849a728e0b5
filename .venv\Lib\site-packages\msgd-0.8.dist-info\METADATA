Metadata-Version: 2.4
Name: MSGD
Version: 0.8
Summary: MSG-based Multiscale Structural Design Tool
Author: <PERSON> <PERSON>
Author-email: <PERSON> <PERSON><PERSON> <<EMAIL>>
Requires-Python: >=3.11
Description-Content-Type: text/markdown
Requires-Dist: numpy>=1.26.0
Requires-Dist: PyYAML>=6.0.1
Requires-Dist: scipy>=1.11.0
Requires-Dist: sympy==1.11.1
Requires-Dist: tqdm==4.65.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: sgio
Dynamic: author

# MSG Design

A mechanics of structure genome (MSG) based framework for multiscale consitutitve modeling and structural analysis and design of composite structures.

This framework is developed to
- simplify the multiscale constitutive modeling and analysis across two or more length scales
- integrate homogenized constitutive models to global structural analysis
- carry out design optimization with respect to certain parameterizations
of advanced materials and structures such as composite laminates, sandwich structures, metamaterials, and so on.

This framework allows users to
- use the best separation of scales
- create arbitrary SG structures using built-in or external tool

This framework integrates the following components:
- SG builder
- iterative analysis package (Dakota)
- interface to external structural modeling and analysis tools

Two variants of this tool are iVABS and DATC.


## Installation

- Add `bin` to the system environment variable `PATH`.
- Add `scripts` to the system environment variable `PYTHONPATH`.


## Python configuration

Required Python packages are listed in the file `requirements.txt`.

It is recommended to create a Python virtual environment and install the required packages by

    pip install -r requirements.txt



## Run

    datc <input>.yml


Run `datc -h` to get help on the command line.


