import sys
from sympy.external import import_module
matchpy = import_module("matchpy")

if not matchpy:
    #bin/test will not execute any tests now
    disabled = True

if sys.version_info[:2] < (3, 6):
    disabled = True

from sympy.integrals.rubi.utility_function import (
        sympy_op_factory, Int, Sum, Set, With, Module, Scan, MapAnd, FalseQ,
        ZeroQ, NegativeQ, NonzeroQ, FreeQ, NFreeQ, List, Log, PositiveQ,
        PositiveIntegerQ, NegativeIntegerQ, IntegerQ, IntegersQ,
        ComplexNumberQ, PureComplexNumberQ, RealNumericQ, PositiveOrZeroQ,
        NegativeOrZeroQ, FractionOrNegativeQ, NegQ, Equal, Unequal, IntPart,
        FracPart, RationalQ, ProductQ, SumQ, NonsumQ, Subst, First, Rest,
        SqrtNumberQ, SqrtNumberSumQ, LinearQ, Sqrt, ArcCosh, Coefficient,
        Denominator, Hypergeometric2F1, Not, Simplify, FractionalPart,
        IntegerPart, AppellF1, EllipticPi, EllipticE, EllipticF, ArcTan,
        ArcCot, ArcCoth, ArcTanh, ArcSin, ArcSinh, ArcCos, ArcCsc, ArcSec,
        ArcCsch, ArcSech, Sinh, Tanh, Cosh, Sech, Csch, Coth, LessEqual, Less,
        Greater, GreaterEqual, FractionQ, IntLinearcQ, Expand, IndependentQ,
        PowerQ, IntegerPowerQ, PositiveIntegerPowerQ, FractionalPowerQ, AtomQ,
        ExpQ, LogQ, Head, MemberQ, TrigQ, SinQ, CosQ, TanQ, CotQ, SecQ, CscQ,
        Sin, Cos, Tan, Cot, Sec, Csc, HyperbolicQ, SinhQ, CoshQ, TanhQ, CothQ,
        SechQ, CschQ, InverseTrigQ, SinCosQ, SinhCoshQ, LeafCount, Numerator,
        NumberQ, NumericQ, Length, ListQ, Im, Re, InverseHyperbolicQ,
        InverseFunctionQ, TrigHyperbolicFreeQ, InverseFunctionFreeQ, RealQ,
        EqQ, FractionalPowerFreeQ, ComplexFreeQ, PolynomialQ, FactorSquareFree,
        PowerOfLinearQ, Exponent, QuadraticQ, LinearPairQ, BinomialParts,
        TrinomialParts, PolyQ, EvenQ, OddQ, PerfectSquareQ, NiceSqrtAuxQ,
        NiceSqrtQ, Together, PosAux, PosQ, CoefficientList, ReplaceAll,
        ExpandLinearProduct, GCD, ContentFactor, NumericFactor,
        NonnumericFactors, MakeAssocList, GensymSubst, KernelSubst,
        ExpandExpression, Apart, SmartApart, MatchQ,
        PolynomialQuotientRemainder, FreeFactors, NonfreeFactors,
        RemoveContentAux, RemoveContent, FreeTerms, NonfreeTerms,
        ExpandAlgebraicFunction, CollectReciprocals, ExpandCleanup,
        AlgebraicFunctionQ, Coeff, LeadTerm, RemainingTerms, LeadFactor,
        RemainingFactors, LeadBase, LeadDegree, Numer, Denom, hypergeom, Expon,
        MergeMonomials, PolynomialDivide, BinomialQ, TrinomialQ,
        GeneralizedBinomialQ, GeneralizedTrinomialQ, FactorSquareFreeList,
        PerfectPowerTest, SquareFreeFactorTest, RationalFunctionQ,
        RationalFunctionFactors, NonrationalFunctionFactors, Reverse,
        RationalFunctionExponents, RationalFunctionExpand, ExpandIntegrand,
        SimplerQ, SimplerSqrtQ, SumSimplerQ, BinomialDegree, TrinomialDegree,
        CancelCommonFactors, SimplerIntegrandQ, GeneralizedBinomialDegree,
        GeneralizedBinomialParts, GeneralizedTrinomialDegree,
        GeneralizedTrinomialParts, MonomialQ, MonomialSumQ,
        MinimumMonomialExponent, MonomialExponent, LinearMatchQ,
        PowerOfLinearMatchQ, QuadraticMatchQ, CubicMatchQ, BinomialMatchQ,
        TrinomialMatchQ, GeneralizedBinomialMatchQ, GeneralizedTrinomialMatchQ,
        QuotientOfLinearsMatchQ, PolynomialTermQ, PolynomialTerms,
        NonpolynomialTerms, PseudoBinomialParts, NormalizePseudoBinomial,
        PseudoBinomialPairQ, PseudoBinomialQ, PolynomialGCD, PolyGCD,
        AlgebraicFunctionFactors, NonalgebraicFunctionFactors,
        QuotientOfLinearsP, QuotientOfLinearsParts, QuotientOfLinearsQ,
        Flatten, Sort, AbsurdNumberQ, AbsurdNumberFactors,
        NonabsurdNumberFactors, SumSimplerAuxQ, Prepend, Drop,
        CombineExponents, FactorInteger, FactorAbsurdNumber,
        SubstForInverseFunction, SubstForFractionalPower,
        SubstForFractionalPowerOfQuotientOfLinears,
        FractionalPowerOfQuotientOfLinears, SubstForFractionalPowerQ,
        SubstForFractionalPowerAuxQ, FractionalPowerOfSquareQ,
        FractionalPowerSubexpressionQ, Apply, FactorNumericGcd,
        MergeableFactorQ, MergeFactor, MergeFactors, TrigSimplifyQ,
        TrigSimplify, TrigSimplifyRecur, Order, FactorOrder, Smallest,
        OrderedQ, MinimumDegree, PositiveFactors, Sign, NonpositiveFactors,
        PolynomialInAuxQ, PolynomialInQ, ExponentInAux, ExponentIn,
        PolynomialInSubstAux, PolynomialInSubst, Distrib, DistributeDegree,
        FunctionOfPower, DivideDegreesOfFactors, MonomialFactor, FullSimplify,
        FunctionOfLinearSubst, FunctionOfLinear, NormalizeIntegrand,
        NormalizeIntegrandAux, NormalizeIntegrandFactor,
        NormalizeIntegrandFactorBase, NormalizeTogether,
        NormalizeLeadTermSigns, AbsorbMinusSign, NormalizeSumFactors,
        SignOfFactor, NormalizePowerOfLinear, SimplifyIntegrand, SimplifyTerm,
        TogetherSimplify, SmartSimplify, SubstForExpn, ExpandToSum, UnifySum,
        UnifyTerms, UnifyTerm, CalculusQ, FunctionOfInverseLinear,
        PureFunctionOfSinhQ, PureFunctionOfTanhQ, PureFunctionOfCoshQ,
        IntegerQuotientQ, OddQuotientQ, EvenQuotientQ, FindTrigFactor,
        FunctionOfSinhQ, FunctionOfCoshQ, OddHyperbolicPowerQ, FunctionOfTanhQ,
        FunctionOfTanhWeight, FunctionOfHyperbolicQ, SmartNumerator,
        SmartDenominator, SubstForAux, ActivateTrig, ExpandTrig, TrigExpand,
        SubstForTrig, SubstForHyperbolic, InertTrigFreeQ, LCM,
        SubstForFractionalPowerOfLinear, FractionalPowerOfLinear,
        InverseFunctionOfLinear, InertTrigQ, InertReciprocalQ, DeactivateTrig,
        FixInertTrigFunction, DeactivateTrigAux, PowerOfInertTrigSumQ,
        PiecewiseLinearQ, KnownTrigIntegrandQ, KnownSineIntegrandQ,
        KnownTangentIntegrandQ, KnownCotangentIntegrandQ,
        KnownSecantIntegrandQ, TryPureTanSubst, TryTanhSubst, TryPureTanhSubst,
        AbsurdNumberGCD, AbsurdNumberGCDList, ExpandTrigExpand,
        ExpandTrigReduce, ExpandTrigReduceAux, NormalizeTrig, TrigToExp,
        ExpandTrigToExp, TrigReduce, FunctionOfTrig, AlgebraicTrigFunctionQ,
        FunctionOfHyperbolic, FunctionOfQ, FunctionOfExpnQ, PureFunctionOfSinQ,
        PureFunctionOfCosQ, PureFunctionOfTanQ, PureFunctionOfCotQ,
        FunctionOfCosQ, FunctionOfSinQ, OddTrigPowerQ, FunctionOfTanQ,
        FunctionOfTanWeight, FunctionOfTrigQ, FunctionOfDensePolynomialsQ,
        FunctionOfLog, PowerVariableExpn, PowerVariableDegree,
        PowerVariableSubst, EulerIntegrandQ, FunctionOfSquareRootOfQuadratic,
        SquareRootOfQuadraticSubst, Divides, EasyDQ, ProductOfLinearPowersQ,
        Rt, NthRoot, AtomBaseQ, SumBaseQ, NegSumBaseQ, AllNegTermQ,
        SomeNegTermQ, TrigSquareQ, RtAux, TrigSquare, IntSum, IntTerm, Map2,
        ConstantFactor, SameQ, ReplacePart, CommonFactors,
        MostMainFactorPosition, FunctionOfExponentialQ, FunctionOfExponential,
        FunctionOfExponentialFunction, FunctionOfExponentialFunctionAux,
        FunctionOfExponentialTest, FunctionOfExponentialTestAux, stdev,
        rubi_test, If, IntQuadraticQ, IntBinomialQ, RectifyTangent,
        RectifyCotangent, Inequality, Condition, Simp, SimpHelp, SplitProduct,
        SplitSum, SubstFor, SubstForAux, FresnelS, FresnelC, Erfc, Erfi, Gamma,
        FunctionOfTrigOfLinearQ, ElementaryFunctionQ, Complex, UnsameQ,
        _SimpFixFactor, SimpFixFactor, _FixSimplify, FixSimplify,
        _SimplifyAntiderivativeSum, SimplifyAntiderivativeSum,
        _SimplifyAntiderivative, SimplifyAntiderivative, _TrigSimplifyAux,
        TrigSimplifyAux, Cancel, Part, PolyLog, D, Dist, Sum_doit, PolynomialQuotient, Floor,
        PolynomialRemainder, Factor, PolyLog, CosIntegral, SinIntegral, LogIntegral, SinhIntegral,
        CoshIntegral, Rule, Erf, PolyGamma, ExpIntegralEi, ExpIntegralE, LogGamma , UtilityOperator, Factorial,
        Zeta, ProductLog, DerivativeDivides, HypergeometricPFQ, IntHide, OneQ
    )
from sympy.core.add import Add
from sympy.core.mod import Mod
from sympy.core.mul import Mul
from sympy.core.numbers import (Float, I, Integer)
from sympy.core.power import Pow
from sympy.core.singleton import S
from sympy.functions.elementary.complexes import Abs
from sympy.functions.elementary.miscellaneous import sqrt
from sympy.integrals.integrals import Integral
from sympy.logic.boolalg import (And, Or)
from sympy.simplify.simplify import simplify
from sympy.integrals.rubi.symbol import WC
from sympy.core.symbol import symbols, Symbol
from sympy.functions import (sin, cos, tan, cot, csc, sec, sqrt, erf, exp, log)
from sympy.functions.elementary.hyperbolic import (acosh, asinh, atanh, acoth, acsch, asech, cosh, sinh, tanh, coth, sech, csch)
from sympy.functions.elementary.trigonometric import (atan, acsc, asin, acot, acos, asec)
from sympy.integrals.rubi.rubimain import rubi_integrate
from sympy.core.numbers import pi as Pi
a, b, c, d, e, f, m, n, x, u , k, p, r, s, t, i, j= symbols('a b c d e f m n x u k p r s t i j')
A, B, C, D, a, b, c, d, e, f, g, h, y, z, m, n, p, q, u, v, w, F = symbols('A B C D a b c d e f g h y z m n p q u v w F', )


def test_1():

    assert rubi_test(rubi_integrate(tan(c + d*x), x), x, -log(cos(c + d*x))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(c + d*x)**S(2), x), x, -x + tan(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(c + d*x)**S(3), x), x, log(cos(c + d*x))/d + tan(c + d*x)**S(2)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(c + d*x)**S(4), x), x, x + tan(c + d*x)**S(3)/(S(3)*d) - tan(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(c + d*x)**S(5), x), x, -log(cos(c + d*x))/d + tan(c + d*x)**S(4)/(S(4)*d) - tan(c + d*x)**S(2)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(c + d*x)**S(6), x), x, -x + tan(c + d*x)**S(5)/(S(5)*d) - tan(c + d*x)**S(3)/(S(3)*d) + tan(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(c + d*x)**S(7), x), x, log(cos(c + d*x))/d + tan(c + d*x)**S(6)/(S(6)*d) - tan(c + d*x)**S(4)/(S(4)*d) + tan(c + d*x)**S(2)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(c + d*x)**S(8), x), x, x + tan(c + d*x)**S(7)/(S(7)*d) - tan(c + d*x)**S(5)/(S(5)*d) + tan(c + d*x)**S(3)/(S(3)*d) - tan(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(7)/2), x), x, -sqrt(S(2))*b**(S(7)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*d) + sqrt(S(2))*b**(S(7)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*d) - sqrt(S(2))*b**(S(7)/2)*log(sqrt(b)*tan(c + d*x) + sqrt(b) - sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*d) + sqrt(S(2))*b**(S(7)/2)*log(sqrt(b)*tan(c + d*x) + sqrt(b) + sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*d) - S(2)*b**S(3)*sqrt(b*tan(c + d*x))/d + S(2)*b*(b*tan(c + d*x))**(S(5)/2)/(S(5)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(5)/2), x), x, sqrt(S(2))*b**(S(5)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*d) - sqrt(S(2))*b**(S(5)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*d) - sqrt(S(2))*b**(S(5)/2)*log(sqrt(b)*tan(c + d*x) + sqrt(b) - sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*d) + sqrt(S(2))*b**(S(5)/2)*log(sqrt(b)*tan(c + d*x) + sqrt(b) + sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*d) + S(2)*b*(b*tan(c + d*x))**(S(3)/2)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(3)/2), x), x, sqrt(S(2))*b**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*d) - sqrt(S(2))*b**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*d) + sqrt(S(2))*b**(S(3)/2)*log(sqrt(b)*tan(c + d*x) + sqrt(b) - sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*d) - sqrt(S(2))*b**(S(3)/2)*log(sqrt(b)*tan(c + d*x) + sqrt(b) + sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*d) + S(2)*b*sqrt(b*tan(c + d*x))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(c + d*x)), x), x, -sqrt(S(2))*sqrt(b)*ArcTan(S(1) - sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*d) + sqrt(S(2))*sqrt(b)*ArcTan(S(1) + sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*d) + sqrt(S(2))*sqrt(b)*log(sqrt(b)*tan(c + d*x) + sqrt(b) - sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*d) - sqrt(S(2))*sqrt(b)*log(sqrt(b)*tan(c + d*x) + sqrt(b) + sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(b*tan(c + d*x)), x), x, -sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*sqrt(b)*d) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*sqrt(b)*d) - sqrt(S(2))*log(sqrt(b)*tan(c + d*x) + sqrt(b) - sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*sqrt(b)*d) + sqrt(S(2))*log(sqrt(b)*tan(c + d*x) + sqrt(b) + sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*sqrt(b)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(-3)/2), x), x, -S(2)/(b*d*sqrt(b*tan(c + d*x))) + sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*b**(S(3)/2)*d) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*b**(S(3)/2)*d) - sqrt(S(2))*log(sqrt(b)*tan(c + d*x) + sqrt(b) - sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*b**(S(3)/2)*d) + sqrt(S(2))*log(sqrt(b)*tan(c + d*x) + sqrt(b) + sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*b**(S(3)/2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(-5)/2), x), x, -S(2)/(S(3)*b*d*(b*tan(c + d*x))**(S(3)/2)) + sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*b**(S(5)/2)*d) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*b**(S(5)/2)*d) + sqrt(S(2))*log(sqrt(b)*tan(c + d*x) + sqrt(b) - sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*b**(S(5)/2)*d) - sqrt(S(2))*log(sqrt(b)*tan(c + d*x) + sqrt(b) + sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*b**(S(5)/2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(-7)/2), x), x, -S(2)/(S(5)*b*d*(b*tan(c + d*x))**(S(5)/2)) + S(2)/(b**S(3)*d*sqrt(b*tan(c + d*x))) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*b**(S(7)/2)*d) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(b*tan(c + d*x))/sqrt(b))/(S(2)*b**(S(7)/2)*d) + sqrt(S(2))*log(sqrt(b)*tan(c + d*x) + sqrt(b) - sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*b**(S(7)/2)*d) - sqrt(S(2))*log(sqrt(b)*tan(c + d*x) + sqrt(b) + sqrt(S(2))*sqrt(b*tan(c + d*x)))/(S(4)*b**(S(7)/2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(4)/3), x), x, -b**(S(4)/3)*ArcTan((b*tan(c + d*x))**(S(1)/3)/b**(S(1)/3))/d + b**(S(4)/3)*ArcTan((sqrt(S(3))*b**(S(1)/3) - S(2)*(b*tan(c + d*x))**(S(1)/3))/b**(S(1)/3))/(S(2)*d) - b**(S(4)/3)*ArcTan((sqrt(S(3))*b**(S(1)/3) + S(2)*(b*tan(c + d*x))**(S(1)/3))/b**(S(1)/3))/(S(2)*d) + sqrt(S(3))*b**(S(4)/3)*log(b**(S(2)/3) - sqrt(S(3))*b**(S(1)/3)*(b*tan(c + d*x))**(S(1)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(4)*d) - sqrt(S(3))*b**(S(4)/3)*log(b**(S(2)/3) + sqrt(S(3))*b**(S(1)/3)*(b*tan(c + d*x))**(S(1)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(4)*d) + S(3)*b*(b*tan(c + d*x))**(S(1)/3)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(2)/3), x), x, b**(S(2)/3)*ArcTan((b*tan(c + d*x))**(S(1)/3)/b**(S(1)/3))/d - b**(S(2)/3)*ArcTan((sqrt(S(3))*b**(S(1)/3) - S(2)*(b*tan(c + d*x))**(S(1)/3))/b**(S(1)/3))/(S(2)*d) + b**(S(2)/3)*ArcTan((sqrt(S(3))*b**(S(1)/3) + S(2)*(b*tan(c + d*x))**(S(1)/3))/b**(S(1)/3))/(S(2)*d) + sqrt(S(3))*b**(S(2)/3)*log(b**(S(2)/3) - sqrt(S(3))*b**(S(1)/3)*(b*tan(c + d*x))**(S(1)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(4)*d) - sqrt(S(3))*b**(S(2)/3)*log(b**(S(2)/3) + sqrt(S(3))*b**(S(1)/3)*(b*tan(c + d*x))**(S(1)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(4)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(1)/3), x), x, -sqrt(S(3))*b**(S(1)/3)*ArcTan(sqrt(S(3))*(b**(S(2)/3) - S(2)*(b*tan(c + d*x))**(S(2)/3))/(S(3)*b**(S(2)/3)))/(S(2)*d) - b**(S(1)/3)*log(b**(S(2)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(2)*d) + b**(S(1)/3)*log(b**(S(4)/3) - b**(S(2)/3)*(b*tan(c + d*x))**(S(2)/3) + (b*tan(c + d*x))**(S(4)/3))/(S(4)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(-1)/3), x), x, -sqrt(S(3))*ArcTan(sqrt(S(3))*(b**(S(2)/3) - S(2)*(b*tan(c + d*x))**(S(2)/3))/(S(3)*b**(S(2)/3)))/(S(2)*b**(S(1)/3)*d) + log(b**(S(2)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(2)*b**(S(1)/3)*d) - log(b**(S(4)/3) - b**(S(2)/3)*(b*tan(c + d*x))**(S(2)/3) + (b*tan(c + d*x))**(S(4)/3))/(S(4)*b**(S(1)/3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(-2)/3), x), x, ArcTan((b*tan(c + d*x))**(S(1)/3)/b**(S(1)/3))/(b**(S(2)/3)*d) - ArcTan((sqrt(S(3))*b**(S(1)/3) - S(2)*(b*tan(c + d*x))**(S(1)/3))/b**(S(1)/3))/(S(2)*b**(S(2)/3)*d) + ArcTan((sqrt(S(3))*b**(S(1)/3) + S(2)*(b*tan(c + d*x))**(S(1)/3))/b**(S(1)/3))/(S(2)*b**(S(2)/3)*d) - sqrt(S(3))*log(b**(S(2)/3) - sqrt(S(3))*b**(S(1)/3)*(b*tan(c + d*x))**(S(1)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(4)*b**(S(2)/3)*d) + sqrt(S(3))*log(b**(S(2)/3) + sqrt(S(3))*b**(S(1)/3)*(b*tan(c + d*x))**(S(1)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(4)*b**(S(2)/3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**(S(-4)/3), x), x, -S(3)/(b*d*(b*tan(c + d*x))**(S(1)/3)) - ArcTan((b*tan(c + d*x))**(S(1)/3)/b**(S(1)/3))/(b**(S(4)/3)*d) + ArcTan((sqrt(S(3))*b**(S(1)/3) - S(2)*(b*tan(c + d*x))**(S(1)/3))/b**(S(1)/3))/(S(2)*b**(S(4)/3)*d) - ArcTan((sqrt(S(3))*b**(S(1)/3) + S(2)*(b*tan(c + d*x))**(S(1)/3))/b**(S(1)/3))/(S(2)*b**(S(4)/3)*d) - sqrt(S(3))*log(b**(S(2)/3) - sqrt(S(3))*b**(S(1)/3)*(b*tan(c + d*x))**(S(1)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(4)*b**(S(4)/3)*d) + sqrt(S(3))*log(b**(S(2)/3) + sqrt(S(3))*b**(S(1)/3)*(b*tan(c + d*x))**(S(1)/3) + (b*tan(c + d*x))**(S(2)/3))/(S(4)*b**(S(4)/3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x))**n, x), x, (b*tan(c + d*x))**(n + S(1))*Hypergeometric2F1(S(1), n/S(2) + S(1)/2, n/S(2) + S(3)/2, -tan(c + d*x)**S(2))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(2))**(S(5)/2), x), x, -b**S(2)*sqrt(b*tan(c + d*x)**S(2))*log(cos(c + d*x))*cot(c + d*x)/d + b**S(2)*sqrt(b*tan(c + d*x)**S(2))*tan(c + d*x)**S(3)/(S(4)*d) - b**S(2)*sqrt(b*tan(c + d*x)**S(2))*tan(c + d*x)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(2))**(S(3)/2), x), x, b*sqrt(b*tan(c + d*x)**S(2))*log(cos(c + d*x))*cot(c + d*x)/d + b*sqrt(b*tan(c + d*x)**S(2))*tan(c + d*x)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(c + d*x)**S(2)), x), x, -sqrt(b*tan(c + d*x)**S(2))*log(cos(c + d*x))*cot(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(b*tan(c + d*x)**S(2)), x), x, log(sin(c + d*x))*tan(c + d*x)/(d*sqrt(b*tan(c + d*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(2))**(S(-3)/2), x), x, -log(sin(c + d*x))*tan(c + d*x)/(b*d*sqrt(b*tan(c + d*x)**S(2))) - cot(c + d*x)/(S(2)*b*d*sqrt(b*tan(c + d*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(2))**(S(-5)/2), x), x, log(sin(c + d*x))*tan(c + d*x)/(b**S(2)*d*sqrt(b*tan(c + d*x)**S(2))) - cot(c + d*x)**S(3)/(S(4)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(2))) + cot(c + d*x)/(S(2)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(3))**(S(5)/2), x), x, -sqrt(S(2))*b**S(2)*sqrt(b*tan(c + d*x)**S(3))*ArcTan(-sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))/(S(2)*d*tan(c + d*x)**(S(3)/2)) + sqrt(S(2))*b**S(2)*sqrt(b*tan(c + d*x)**S(3))*ArcTan(sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))/(S(2)*d*tan(c + d*x)**(S(3)/2)) - sqrt(S(2))*b**S(2)*sqrt(b*tan(c + d*x)**S(3))*log(-sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))/(S(4)*d*tan(c + d*x)**(S(3)/2)) + sqrt(S(2))*b**S(2)*sqrt(b*tan(c + d*x)**S(3))*log(sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))/(S(4)*d*tan(c + d*x)**(S(3)/2)) + S(2)*b**S(2)*sqrt(b*tan(c + d*x)**S(3))*tan(c + d*x)**S(5)/(S(13)*d) - S(2)*b**S(2)*sqrt(b*tan(c + d*x)**S(3))*tan(c + d*x)**S(3)/(S(9)*d) + S(2)*b**S(2)*sqrt(b*tan(c + d*x)**S(3))*tan(c + d*x)/(S(5)*d) - S(2)*b**S(2)*sqrt(b*tan(c + d*x)**S(3))*cot(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(3))**(S(3)/2), x), x, -sqrt(S(2))*b*sqrt(b*tan(c + d*x)**S(3))*ArcTan(-sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))/(S(2)*d*tan(c + d*x)**(S(3)/2)) + sqrt(S(2))*b*sqrt(b*tan(c + d*x)**S(3))*ArcTan(sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))/(S(2)*d*tan(c + d*x)**(S(3)/2)) + sqrt(S(2))*b*sqrt(b*tan(c + d*x)**S(3))*log(-sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))/(S(4)*d*tan(c + d*x)**(S(3)/2)) - sqrt(S(2))*b*sqrt(b*tan(c + d*x)**S(3))*log(sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))/(S(4)*d*tan(c + d*x)**(S(3)/2)) + S(2)*b*sqrt(b*tan(c + d*x)**S(3))*tan(c + d*x)**S(2)/(S(7)*d) - S(2)*b*sqrt(b*tan(c + d*x)**S(3))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(c + d*x)**S(3)), x), x, sqrt(S(2))*sqrt(b*tan(c + d*x)**S(3))*ArcTan(-sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))/(S(2)*d*tan(c + d*x)**(S(3)/2)) - sqrt(S(2))*sqrt(b*tan(c + d*x)**S(3))*ArcTan(sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))/(S(2)*d*tan(c + d*x)**(S(3)/2)) + sqrt(S(2))*sqrt(b*tan(c + d*x)**S(3))*log(-sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))/(S(4)*d*tan(c + d*x)**(S(3)/2)) - sqrt(S(2))*sqrt(b*tan(c + d*x)**S(3))*log(sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))/(S(4)*d*tan(c + d*x)**(S(3)/2)) + S(2)*sqrt(b*tan(c + d*x)**S(3))*cot(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(b*tan(c + d*x)**S(3)), x), x, sqrt(S(2))*ArcTan(-sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))*tan(c + d*x)**(S(3)/2)/(S(2)*d*sqrt(b*tan(c + d*x)**S(3))) - sqrt(S(2))*ArcTan(sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))*tan(c + d*x)**(S(3)/2)/(S(2)*d*sqrt(b*tan(c + d*x)**S(3))) - sqrt(S(2))*log(-sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))*tan(c + d*x)**(S(3)/2)/(S(4)*d*sqrt(b*tan(c + d*x)**S(3))) + sqrt(S(2))*log(sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))*tan(c + d*x)**(S(3)/2)/(S(4)*d*sqrt(b*tan(c + d*x)**S(3))) - S(2)*tan(c + d*x)/(d*sqrt(b*tan(c + d*x)**S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(3))**(S(-3)/2), x), x, -sqrt(S(2))*ArcTan(-sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))*tan(c + d*x)**(S(3)/2)/(S(2)*b*d*sqrt(b*tan(c + d*x)**S(3))) + sqrt(S(2))*ArcTan(sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))*tan(c + d*x)**(S(3)/2)/(S(2)*b*d*sqrt(b*tan(c + d*x)**S(3))) - sqrt(S(2))*log(-sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))*tan(c + d*x)**(S(3)/2)/(S(4)*b*d*sqrt(b*tan(c + d*x)**S(3))) + sqrt(S(2))*log(sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))*tan(c + d*x)**(S(3)/2)/(S(4)*b*d*sqrt(b*tan(c + d*x)**S(3))) - S(2)*cot(c + d*x)**S(2)/(S(7)*b*d*sqrt(b*tan(c + d*x)**S(3))) + S(2)/(S(3)*b*d*sqrt(b*tan(c + d*x)**S(3))), expand=True, _diff=True, _numerical=True)

    # taking a long time assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(3))**(S(-5)/2), x), x, -sqrt(S(2))*ArcTan(-sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))*tan(c + d*x)**(S(3)/2)/(S(2)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(3))) + sqrt(S(2))*ArcTan(sqrt(S(2))*sqrt(tan(c + d*x)) + S(1))*tan(c + d*x)**(S(3)/2)/(S(2)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(3))) + sqrt(S(2))*log(-sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))*tan(c + d*x)**(S(3)/2)/(S(4)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(3))) - sqrt(S(2))*log(sqrt(S(2))*sqrt(tan(c + d*x)) + tan(c + d*x) + S(1))*tan(c + d*x)**(S(3)/2)/(S(4)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(3))) + S(2)*tan(c + d*x)/(b**S(2)*d*sqrt(b*tan(c + d*x)**S(3))) - S(2)*cot(c + d*x)**S(5)/(S(13)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(3))) + S(2)*cot(c + d*x)**S(3)/(S(9)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(3))) - S(2)*cot(c + d*x)/(S(5)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(3))), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(4))**(S(5)/2), x), x, -b**S(2)*x*sqrt(b*tan(c + d*x)**S(4))*cot(c + d*x)**S(2) + b**S(2)*sqrt(b*tan(c + d*x)**S(4))*tan(c + d*x)**S(7)/(S(9)*d) - b**S(2)*sqrt(b*tan(c + d*x)**S(4))*tan(c + d*x)**S(5)/(S(7)*d) + b**S(2)*sqrt(b*tan(c + d*x)**S(4))*tan(c + d*x)**S(3)/(S(5)*d) - b**S(2)*sqrt(b*tan(c + d*x)**S(4))*tan(c + d*x)/(S(3)*d) + b**S(2)*sqrt(b*tan(c + d*x)**S(4))*cot(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(4))**(S(3)/2), x), x, -b*x*sqrt(b*tan(c + d*x)**S(4))*cot(c + d*x)**S(2) + b*sqrt(b*tan(c + d*x)**S(4))*tan(c + d*x)**S(3)/(S(5)*d) - b*sqrt(b*tan(c + d*x)**S(4))*tan(c + d*x)/(S(3)*d) + b*sqrt(b*tan(c + d*x)**S(4))*cot(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(c + d*x)**S(4)), x), x, -x*sqrt(b*tan(c + d*x)**S(4))*cot(c + d*x)**S(2) + sqrt(b*tan(c + d*x)**S(4))*cot(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(b*tan(c + d*x)**S(4)), x), x, -x*tan(c + d*x)**S(2)/sqrt(b*tan(c + d*x)**S(4)) - tan(c + d*x)/(d*sqrt(b*tan(c + d*x)**S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(4))**(S(-3)/2), x), x, -x*tan(c + d*x)**S(2)/(b*sqrt(b*tan(c + d*x)**S(4))) - tan(c + d*x)/(b*d*sqrt(b*tan(c + d*x)**S(4))) - cot(c + d*x)**S(3)/(S(5)*b*d*sqrt(b*tan(c + d*x)**S(4))) + cot(c + d*x)/(S(3)*b*d*sqrt(b*tan(c + d*x)**S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(4))**(S(-5)/2), x), x, -x*tan(c + d*x)**S(2)/(b**S(2)*sqrt(b*tan(c + d*x)**S(4))) - tan(c + d*x)/(b**S(2)*d*sqrt(b*tan(c + d*x)**S(4))) - cot(c + d*x)**S(7)/(S(9)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(4))) + cot(c + d*x)**S(5)/(S(7)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(4))) - cot(c + d*x)**S(3)/(S(5)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(4))) + cot(c + d*x)/(S(3)*b**S(2)*d*sqrt(b*tan(c + d*x)**S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**p)**n, x), x, (b*tan(c + d*x)**p)**n*Hypergeometric2F1(S(1), n*p/S(2) + S(1)/2, n*p/S(2) + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)/(d*(n*p + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(2))**n, x), x, (b*tan(c + d*x)**S(2))**n*Hypergeometric2F1(S(1), n + S(1)/2, n + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)/(d*(S(2)*n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(3))**n, x), x, (b*tan(c + d*x)**S(3))**n*Hypergeometric2F1(S(1), S(3)*n/S(2) + S(1)/2, S(3)*n/S(2) + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)/(d*(S(3)*n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**S(4))**n, x), x, (b*tan(c + d*x)**S(4))**n*Hypergeometric2F1(S(1), S(2)*n + S(1)/2, S(2)*n + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)/(d*(S(4)*n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**p)**(S(5)/2), x), x, S(2)*b**S(2)*sqrt(b*tan(c + d*x)**p)*Hypergeometric2F1(S(1), S(5)*p/S(4) + S(1)/2, S(5)*p/S(4) + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)**(S(2)*p + S(1))/(d*(S(5)*p + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**p)**(S(3)/2), x), x, S(2)*b*sqrt(b*tan(c + d*x)**p)*Hypergeometric2F1(S(1), S(3)*p/S(4) + S(1)/2, S(3)*p/S(4) + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)**(p + S(1))/(d*(S(3)*p + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(c + d*x)**p), x), x, S(2)*sqrt(b*tan(c + d*x)**p)*Hypergeometric2F1(S(1), p/S(4) + S(1)/2, p/S(4) + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)/(d*(p + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(b*tan(c + d*x)**p), x), x, S(2)*Hypergeometric2F1(S(1), -p/S(4) + S(1)/2, -p/S(4) + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)/(d*sqrt(b*tan(c + d*x)**p)*(-p + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**p)**(S(-3)/2), x), x, S(2)*Hypergeometric2F1(S(1), -S(3)*p/S(4) + S(1)/2, -S(3)*p/S(4) + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)**(-p + S(1))/(b*d*sqrt(b*tan(c + d*x)**p)*(-S(3)*p + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**p)**(S(-5)/2), x), x, S(2)*Hypergeometric2F1(S(1), -S(5)*p/S(4) + S(1)/2, -S(5)*p/S(4) + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)**(-S(2)*p + S(1))/(b**S(2)*d*sqrt(b*tan(c + d*x)**p)*(-S(5)*p + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(c + d*x)**p)**(S(1)/p), x), x, -(b*tan(c + d*x)**p)**(S(1)/p)*log(cos(c + d*x))*cot(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*(b*tan(c + d*x))**p)**n, x), x, (a*(b*tan(c + d*x))**p)**n*Hypergeometric2F1(S(1), n*p/S(2) + S(1)/2, n*p/S(2) + S(3)/2, -tan(c + d*x)**S(2))*tan(c + d*x)/(d*(n*p + S(1))), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*sin(a + b*x)**S(4), x), x, -S(21)*sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b) + S(21)*sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b) + S(21)*sqrt(S(2))*sqrt(d)*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b) - S(21)*sqrt(S(2))*sqrt(d)*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b) - S(7)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(2)/(S(16)*b*d) - (d*tan(a + b*x))**(S(7)/2)*cos(a + b*x)**S(4)/(S(4)*b*d**S(3)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*sin(a + b*x)**S(2), x), x, -S(3)*sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b) + S(3)*sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b) + S(3)*sqrt(S(2))*sqrt(d)*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b) - S(3)*sqrt(S(2))*sqrt(d)*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b) - (d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(2)/(S(2)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*csc(a + b*x)**S(2), x), x, -S(2)*d/(b*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*csc(a + b*x)**S(4), x), x, -S(2)*d**S(3)/(S(5)*b*(d*tan(a + b*x))**(S(5)/2)) - S(2)*d/(b*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*csc(a + b*x)**S(6), x), x, -S(2)*d**S(5)/(S(9)*b*(d*tan(a + b*x))**(S(9)/2)) - S(4)*d**S(3)/(S(5)*b*(d*tan(a + b*x))**(S(5)/2)) - S(2)*d/(b*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*sin(a + b*x)**S(3), x), x, S(5)*d*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(12)*b*sqrt(d*tan(a + b*x))) - S(5)*sqrt(d*tan(a + b*x))*cos(a + b*x)/(S(6)*b) - (d*tan(a + b*x))**(S(5)/2)*cos(a + b*x)**S(3)/(S(3)*b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*sin(a + b*x), x), x, d*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(2)*b*sqrt(d*tan(a + b*x))) - sqrt(d*tan(a + b*x))*cos(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*csc(a + b*x), x), x, d*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(b*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*csc(a + b*x)**S(3), x), x, -S(2)*d**S(2)*sec(a + b*x)/(S(3)*b*(d*tan(a + b*x))**(S(3)/2)) + S(2)*d*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(3)*b*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(a + b*x))*csc(a + b*x)**S(5), x), x, -S(2)*d**S(4)*sec(a + b*x)**S(3)/(S(7)*b*(d*tan(a + b*x))**(S(7)/2)) - S(4)*d**S(2)*sec(a + b*x)/(S(7)*b*(d*tan(a + b*x))**(S(3)/2)) + S(4)*d*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(7)*b*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sin(a + b*x)**S(4), x), x, S(45)*sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b) - S(45)*sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b) + S(45)*sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b) - S(45)*sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b) + S(45)*d*sqrt(d*tan(a + b*x))/(S(16)*b) - S(9)*(d*tan(a + b*x))**(S(5)/2)*cos(a + b*x)**S(2)/(S(16)*b*d) - (d*tan(a + b*x))**(S(9)/2)*cos(a + b*x)**S(4)/(S(4)*b*d**S(3)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sin(a + b*x)**S(2), x), x, S(5)*sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b) - S(5)*sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b) + S(5)*sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b) - S(5)*sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b) + S(5)*d*sqrt(d*tan(a + b*x))/(S(2)*b) - (d*tan(a + b*x))**(S(5)/2)*cos(a + b*x)**S(2)/(S(2)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*csc(a + b*x)**S(2), x), x, S(2)*d*sqrt(d*tan(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*csc(a + b*x)**S(4), x), x, -S(2)*d**S(3)/(S(3)*b*(d*tan(a + b*x))**(S(3)/2)) + S(2)*d*sqrt(d*tan(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*csc(a + b*x)**S(6), x), x, -S(2)*d**S(5)/(S(7)*b*(d*tan(a + b*x))**(S(7)/2)) - S(4)*d**S(3)/(S(3)*b*(d*tan(a + b*x))**(S(3)/2)) + S(2)*d*sqrt(d*tan(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sin(a + b*x)**S(3), x), x, -S(7)*d*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(2)*b*sqrt(sin(S(2)*a + S(2)*b*x))) + S(7)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)/(S(3)*b) - (d*tan(a + b*x))**(S(7)/2)*cos(a + b*x)**S(3)/(S(3)*b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sin(a + b*x), x), x, -S(3)*d*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(b*sqrt(sin(S(2)*a + S(2)*b*x))) + S(2)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*csc(a + b*x), x), x, -S(2)*d*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(b*sqrt(sin(S(2)*a + S(2)*b*x))) + S(2)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*csc(a + b*x)**S(3), x), x, -S(2)*d**S(2)*sec(a + b*x)/(b*sqrt(d*tan(a + b*x))) - S(4)*d*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(b*sqrt(sin(S(2)*a + S(2)*b*x))) + S(4)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)/b, expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*sin(a + b*x)**S(4), x), x, S(77)*sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b) - S(77)*sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b) - S(77)*sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b) + S(77)*sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b) + S(77)*d*(d*tan(a + b*x))**(S(3)/2)/(S(48)*b) - S(11)*(d*tan(a + b*x))**(S(7)/2)*cos(a + b*x)**S(2)/(S(16)*b*d) - (d*tan(a + b*x))**(S(11)/2)*cos(a + b*x)**S(4)/(S(4)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    # long time assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*sin(a + b*x)**S(2), x), x, S(7)*sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b) - S(7)*sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b) - S(7)*sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b) + S(7)*sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b) + S(7)*d*(d*tan(a + b*x))**(S(3)/2)/(S(6)*b) - (d*tan(a + b*x))**(S(7)/2)*cos(a + b*x)**S(2)/(S(2)*b*d), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*csc(a + b*x)**S(2), x), x, S(2)*d*(d*tan(a + b*x))**(S(3)/2)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*csc(a + b*x)**S(4), x), x, -S(2)*d**S(3)/(b*sqrt(d*tan(a + b*x))) + S(2)*d*(d*tan(a + b*x))**(S(3)/2)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*csc(a + b*x)**S(6), x), x, -S(2)*d**S(5)/(S(5)*b*(d*tan(a + b*x))**(S(5)/2)) - S(4)*d**S(3)/(b*sqrt(d*tan(a + b*x))) + S(2)*d*(d*tan(a + b*x))**(S(3)/2)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*sin(a + b*x)**S(3), x), x, -S(5)*d**S(3)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(4)*b*sqrt(d*tan(a + b*x))) + S(5)*d**S(2)*sqrt(d*tan(a + b*x))*cos(a + b*x)/(S(2)*b) + (d*tan(a + b*x))**(S(5)/2)*cos(a + b*x)/b - (d*tan(a + b*x))**(S(9)/2)*cos(a + b*x)**S(3)/(S(3)*b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*sin(a + b*x), x), x, -S(5)*d**S(3)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(6)*b*sqrt(d*tan(a + b*x))) + S(5)*d**S(2)*sqrt(d*tan(a + b*x))*cos(a + b*x)/(S(3)*b) + S(2)*(d*tan(a + b*x))**(S(5)/2)*cos(a + b*x)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*csc(a + b*x), x), x, -d**S(3)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(3)*b*sqrt(d*tan(a + b*x))) + S(2)*d**S(2)*sqrt(d*tan(a + b*x))*sec(a + b*x)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*csc(a + b*x)**S(3), x), x, S(2)*d**S(3)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(3)*b*sqrt(d*tan(a + b*x))) + S(2)*d**S(2)*sqrt(d*tan(a + b*x))*sec(a + b*x)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*csc(a + b*x)**S(5), x), x, -S(2)*d**S(4)*sec(a + b*x)**S(3)/(S(3)*b*(d*tan(a + b*x))**(S(3)/2)) + S(4)*d**S(3)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(3)*b*sqrt(d*tan(a + b*x))) + S(4)*d**S(2)*sqrt(d*tan(a + b*x))*sec(a + b*x)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(5)/2)*csc(a + b*x)**S(7), x), x, -S(2)*d**S(6)*sec(a + b*x)**S(5)/(S(7)*b*(d*tan(a + b*x))**(S(7)/2)) - S(20)*d**S(4)*sec(a + b*x)**S(3)/(S(21)*b*(d*tan(a + b*x))**(S(3)/2)) + S(40)*d**S(3)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(21)*b*sqrt(d*tan(a + b*x))) + S(40)*d**S(2)*sqrt(d*tan(a + b*x))*sec(a + b*x)/(S(21)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)/sqrt(d*tan(a + b*x)), x), x, -S(5)*sqrt(d*tan(a + b*x))*cos(a + b*x)**S(2)/(S(16)*b*d) - (d*tan(a + b*x))**(S(5)/2)*cos(a + b*x)**S(4)/(S(4)*b*d**S(3)) - S(5)*sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b*sqrt(d)) + S(5)*sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b*sqrt(d)) - S(5)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b*sqrt(d)) + S(5)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b*sqrt(d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)/sqrt(d*tan(a + b*x)), x), x, -sqrt(d*tan(a + b*x))*cos(a + b*x)**S(2)/(S(2)*b*d) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b*sqrt(d)) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b*sqrt(d)) - sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b*sqrt(d)) + sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b*sqrt(d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(2)/sqrt(d*tan(a + b*x)), x), x, -S(2)*d/(S(3)*b*(d*tan(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(4)/sqrt(d*tan(a + b*x)), x), x, -S(2)*d**S(3)/(S(7)*b*(d*tan(a + b*x))**(S(7)/2)) - S(2)*d/(S(3)*b*(d*tan(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(6)/sqrt(d*tan(a + b*x)), x), x, -S(2)*d**S(5)/(S(11)*b*(d*tan(a + b*x))**(S(11)/2)) - S(4)*d**S(3)/(S(7)*b*(d*tan(a + b*x))**(S(7)/2)) - S(2)*d/(S(3)*b*(d*tan(a + b*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)/sqrt(d*tan(a + b*x)), x), x, sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(2)*b*d*sqrt(sin(S(2)*a + S(2)*b*x))) - (d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(3)/(S(3)*b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)/sqrt(d*tan(a + b*x)), x), x, sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(b*d*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)/sqrt(d*tan(a + b*x)), x), x, -S(2)*cos(a + b*x)/(b*sqrt(d*tan(a + b*x))) - S(2)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(b*d*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(3)/sqrt(d*tan(a + b*x)), x), x, -S(2)*d**S(2)*sec(a + b*x)/(S(5)*b*(d*tan(a + b*x))**(S(5)/2)) - S(4)*cos(a + b*x)/(S(5)*b*sqrt(d*tan(a + b*x))) - S(4)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(5)*b*d*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)/(d*tan(a + b*x))**(S(3)/2), x), x, -(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(4)/(S(4)*b*d**S(3)) + S(3)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(2)/(S(16)*b*d**S(3)) - S(3)*sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b*d**(S(3)/2)) + S(3)*sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b*d**(S(3)/2)) + S(3)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b*d**(S(3)/2)) - S(3)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b*d**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)/(d*tan(a + b*x))**(S(3)/2), x), x, (d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(2)/(S(2)*b*d**S(3)) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b*d**(S(3)/2)) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b*d**(S(3)/2)) + sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b*d**(S(3)/2)) - sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b*d**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(2)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*d/(S(5)*b*(d*tan(a + b*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(4)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*d**S(3)/(S(9)*b*(d*tan(a + b*x))**(S(9)/2)) - S(2)*d/(S(5)*b*(d*tan(a + b*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(6)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*d**S(5)/(S(13)*b*(d*tan(a + b*x))**(S(13)/2)) - S(4)*d**S(3)/(S(9)*b*(d*tan(a + b*x))**(S(9)/2)) - S(2)*d/(S(5)*b*(d*tan(a + b*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)/(d*tan(a + b*x))**(S(3)/2), x), x, EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(12)*b*d*sqrt(d*tan(a + b*x))) - sqrt(d*tan(a + b*x))*cos(a + b*x)**S(3)/(S(3)*b*d**S(2)) + sqrt(d*tan(a + b*x))*cos(a + b*x)/(S(6)*b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)/(d*tan(a + b*x))**(S(3)/2), x), x, EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(2)*b*d*sqrt(d*tan(a + b*x))) + sqrt(d*tan(a + b*x))*cos(a + b*x)/(b*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*sec(a + b*x)/(S(3)*b*(d*tan(a + b*x))**(S(3)/2)) - EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(3)*b*d*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(3)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*d**S(2)*sec(a + b*x)/(S(7)*b*(d*tan(a + b*x))**(S(7)/2)) - S(4)*sec(a + b*x)/(S(21)*b*(d*tan(a + b*x))**(S(3)/2)) - S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(21)*b*d*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(4)/(d*tan(a + b*x))**(S(5)/2), x), x, -sqrt(d*tan(a + b*x))*cos(a + b*x)**S(4)/(S(4)*b*d**S(3)) + sqrt(d*tan(a + b*x))*cos(a + b*x)**S(2)/(S(16)*b*d**S(3)) - S(3)*sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b*d**(S(5)/2)) + S(3)*sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(64)*b*d**(S(5)/2)) - S(3)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b*d**(S(5)/2)) + S(3)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(128)*b*d**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(2)/(d*tan(a + b*x))**(S(5)/2), x), x, sqrt(d*tan(a + b*x))*cos(a + b*x)**S(2)/(S(2)*b*d**S(3)) - S(3)*sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b*d**(S(5)/2)) + S(3)*sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b*d**(S(5)/2)) - S(3)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b*d**(S(5)/2)) + S(3)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b*d**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(2)/(d*tan(a + b*x))**(S(5)/2), x), x, -S(2)*d/(S(7)*b*(d*tan(a + b*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(4)/(d*tan(a + b*x))**(S(5)/2), x), x, -S(2)*d**S(3)/(S(11)*b*(d*tan(a + b*x))**(S(11)/2)) - S(2)*d/(S(7)*b*(d*tan(a + b*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(6)/(d*tan(a + b*x))**(S(5)/2), x), x, -S(2)*d**S(5)/(S(15)*b*(d*tan(a + b*x))**(S(15)/2)) - S(4)*d**S(3)/(S(11)*b*(d*tan(a + b*x))**(S(11)/2)) - S(2)*d/(S(7)*b*(d*tan(a + b*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(5)/(d*tan(a + b*x))**(S(5)/2), x), x, S(3)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(20)*b*d**S(3)*sqrt(sin(S(2)*a + S(2)*b*x))) - (d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(5)/(S(5)*b*d**S(4)) + (d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(3)/(S(10)*b*d**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)**S(3)/(d*tan(a + b*x))**(S(5)/2), x), x, sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(2)*b*d**S(3)*sqrt(sin(S(2)*a + S(2)*b*x))) + (d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(3)/(S(3)*b*d**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(a + b*x)/(d*tan(a + b*x))**(S(5)/2), x), x, -S(2)*cos(a + b*x)/(b*d**S(2)*sqrt(d*tan(a + b*x))) - S(3)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(b*d**S(3)*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)/(d*tan(a + b*x))**(S(5)/2), x), x, -S(2)*sec(a + b*x)/(S(5)*b*(d*tan(a + b*x))**(S(5)/2)) + S(6)*cos(a + b*x)/(S(5)*b*d**S(2)*sqrt(d*tan(a + b*x))) + S(6)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(5)*b*d**S(3)*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(csc(a + b*x)**S(3)/(d*tan(a + b*x))**(S(5)/2), x), x, -S(2)*d**S(2)*sec(a + b*x)/(S(9)*b*(d*tan(a + b*x))**(S(9)/2)) - S(4)*sec(a + b*x)/(S(45)*b*(d*tan(a + b*x))**(S(5)/2)) + S(4)*cos(a + b*x)/(S(15)*b*d**S(2)*sqrt(d*tan(a + b*x))) + S(4)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(15)*b*d**S(3)*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(3)/2), x), x, S(8)*b**S(2)*d*sqrt(d*tan(e + f*x))/(S(3)*f*sqrt(b*sin(e + f*x))) - S(2)*d*(b*sin(e + f*x))**(S(3)/2)*sqrt(d*tan(e + f*x))/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sin(x)**(S(5)/2)/tan(x)**(S(3)/2), x), x, -S(2)*sin(x)**(S(5)/2)/(S(5)*tan(x)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(4)/3)*sqrt(d*tan(e + f*x)), x), x, S(6)*(b*sin(e + f*x))**(S(4)/3)*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(3)/4, S(17)/12, S(29)/12, sin(e + f*x)**S(2))/(S(17)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(1)/3)*sqrt(d*tan(e + f*x)), x), x, S(6)*(b*sin(e + f*x))**(S(1)/3)*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(3)/4, S(11)/12, S(23)/12, sin(e + f*x)**S(2))/(S(11)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))/(b*sin(e + f*x))**(S(1)/3), x), x, S(6)*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(7)/12, S(3)/4, S(19)/12, sin(e + f*x)**S(2))/(S(7)*d*f*(b*sin(e + f*x))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))/(b*sin(e + f*x))**(S(4)/3), x), x, S(6)*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(1)/12, S(3)/4, S(13)/12, sin(e + f*x)**S(2))/(d*f*(b*sin(e + f*x))**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(4)/3)*(d*tan(e + f*x))**(S(3)/2), x), x, S(6)*d*(b*sin(e + f*x))**(S(10)/3)*sqrt(d*tan(e + f*x))*(cos(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(5)/4, S(23)/12, S(35)/12, sin(e + f*x)**S(2))/(S(23)*b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(1)/3)*(d*tan(e + f*x))**(S(3)/2), x), x, S(6)*d*(b*sin(e + f*x))**(S(7)/3)*sqrt(d*tan(e + f*x))*(cos(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(5)/4, S(17)/12, S(29)/12, sin(e + f*x)**S(2))/(S(17)*b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(3)/2)/(b*sin(e + f*x))**(S(1)/3), x), x, S(6)*d*(b*sin(e + f*x))**(S(5)/3)*sqrt(d*tan(e + f*x))*(cos(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(13)/12, S(5)/4, S(25)/12, sin(e + f*x)**S(2))/(S(13)*b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(3)/2)/(b*sin(e + f*x))**(S(4)/3), x), x, S(6)*d*(b*sin(e + f*x))**(S(2)/3)*sqrt(d*tan(e + f*x))*(cos(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(7)/12, S(5)/4, S(19)/12, sin(e + f*x)**S(2))/(S(7)*b**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sin(e + f*x))*(d*tan(e + f*x))**(S(4)/3), x), x, S(6)*d*(b*sin(e + f*x))**(S(5)/2)*(d*tan(e + f*x))**(S(1)/3)*(cos(e + f*x)**S(2))**(S(1)/6)*Hypergeometric2F1(S(7)/6, S(17)/12, S(29)/12, sin(e + f*x)**S(2))/(S(17)*b**S(2)*f), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(sqrt(b*sin(e + f*x))*(d*tan(e + f*x))**(S(4)/3), x), x, -S(3)*d*sqrt(b*sin(e + f*x))*(d*tan(e + f*x))**(S(1)/3)*(sec(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(5)/12, S(5)/4, S(17)/12, -tan(e + f*x)**S(2))/f + S(3)*d*sqrt(b*sin(e + f*x))*(d*tan(e + f*x))**(S(1)/3)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sin(e + f*x))*(d*tan(e + f*x))**(S(1)/3), x), x, S(6)*sqrt(b*sin(e + f*x))*(d*tan(e + f*x))**(S(4)/3)*(cos(e + f*x)**S(2))**(S(2)/3)*Hypergeometric2F1(S(2)/3, S(11)/12, S(23)/12, sin(e + f*x)**S(2))/(S(11)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sin(e + f*x))/(d*tan(e + f*x))**(S(1)/3), x), x, S(6)*sqrt(b*sin(e + f*x))*(d*tan(e + f*x))**(S(2)/3)*(cos(e + f*x)**S(2))**(S(1)/3)*Hypergeometric2F1(S(1)/3, S(7)/12, S(19)/12, sin(e + f*x)**S(2))/(S(7)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sin(e + f*x))/(d*tan(e + f*x))**(S(4)/3), x), x, S(6)*sqrt(b*sin(e + f*x))*Hypergeometric2F1(S(-1)/6, S(1)/12, S(13)/12, sin(e + f*x)**S(2))/(d*f*(d*tan(e + f*x))**(S(1)/3)*(cos(e + f*x)**S(2))**(S(1)/6)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(sqrt(b*sin(e + f*x))/(d*tan(e + f*x))**(S(4)/3), x), x, S(4)*sqrt(b*sin(e + f*x))*(sec(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(1)/12, S(1)/4, S(13)/12, -tan(e + f*x)**S(2))/(d*f*(d*tan(e + f*x))**(S(1)/3)) + S(2)*sqrt(b*sin(e + f*x))/(d*f*(d*tan(e + f*x))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(4)/3), x), x, S(6)*d*(b*sin(e + f*x))**(S(7)/2)*(d*tan(e + f*x))**(S(1)/3)*(cos(e + f*x)**S(2))**(S(1)/6)*Hypergeometric2F1(S(7)/6, S(23)/12, S(35)/12, sin(e + f*x)**S(2))/(S(23)*b**S(2)*f), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((b*sin(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(4)/3), x), x, -S(3)*d*(b*sin(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(1)/3)*(sec(e + f*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(11)/12, S(7)/4, S(23)/12, -tan(e + f*x)**S(2))/f + S(3)*d*(b*sin(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(1)/3)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(1)/3), x), x, S(6)*(b*sin(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(4)/3)*(cos(e + f*x)**S(2))**(S(2)/3)*Hypergeometric2F1(S(2)/3, S(17)/12, S(29)/12, sin(e + f*x)**S(2))/(S(17)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(3)/2)/(d*tan(e + f*x))**(S(1)/3), x), x, S(6)*(b*sin(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(2)/3)*(cos(e + f*x)**S(2))**(S(1)/3)*Hypergeometric2F1(S(1)/3, S(13)/12, S(25)/12, sin(e + f*x)**S(2))/(S(13)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**(S(3)/2)/(d*tan(e + f*x))**(S(4)/3), x), x, S(6)*(b*sin(e + f*x))**(S(3)/2)*Hypergeometric2F1(S(-1)/6, S(7)/12, S(19)/12, sin(e + f*x)**S(2))/(S(7)*d*f*(d*tan(e + f*x))**(S(1)/3)*(cos(e + f*x)**S(2))**(S(1)/6)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((b*sin(e + f*x))**(S(3)/2)/(d*tan(e + f*x))**(S(4)/3), x), x, S(4)*(b*sin(e + f*x))**(S(3)/2)*(sec(e + f*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(7)/12, S(3)/4, S(19)/12, -tan(e + f*x)**S(2))/(S(21)*d*f*(d*tan(e + f*x))**(S(1)/3)) + S(2)*(b*sin(e + f*x))**(S(3)/2)/(S(3)*d*f*(d*tan(e + f*x))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*tan(e + f*x)**S(3), x), x, (b*sin(e + f*x))**(m + S(4))*Hypergeometric2F1(S(2), m/S(2) + S(2), m/S(2) + S(3), sin(e + f*x)**S(2))/(b**S(4)*f*(m + S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*tan(e + f*x), x), x, (b*sin(e + f*x))**(m + S(2))*Hypergeometric2F1(S(1), m/S(2) + S(1), m/S(2) + S(2), sin(e + f*x)**S(2))/(b**S(2)*f*(m + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*cot(e + f*x), x), x, (b*sin(e + f*x))**m/(f*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*cot(e + f*x)**S(3), x), x, -b**S(2)*(b*sin(e + f*x))**(m + S(-2))/(f*(-m + S(2))) - (b*sin(e + f*x))**m/(f*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*cot(e + f*x)**S(5), x), x, -b**S(4)*(b*sin(e + f*x))**(m + S(-4))/(f*(-m + S(4))) + S(2)*b**S(2)*(b*sin(e + f*x))**(m + S(-2))/(f*(-m + S(2))) + (b*sin(e + f*x))**m/(f*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*tan(e + f*x)**S(4), x), x, (b*sin(e + f*x))**(m + S(5))*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(5)/2, m/S(2) + S(5)/2, m/S(2) + S(7)/2, sin(e + f*x)**S(2))*sec(e + f*x)/(b**S(5)*f*(m + S(5))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*tan(e + f*x)**S(2), x), x, (b*sin(e + f*x))**(m + S(3))*sqrt(cos(e + f*x)**S(2))*Hypergeometric2F1(S(3)/2, m/S(2) + S(3)/2, m/S(2) + S(5)/2, sin(e + f*x)**S(2))*sec(e + f*x)/(b**S(3)*f*(m + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*cot(e + f*x)**S(2), x), x, -b*(b*sin(e + f*x))**(m + S(-1))*Hypergeometric2F1(S(-1)/2, m/S(2) + S(-1)/2, m/S(2) + S(1)/2, sin(e + f*x)**S(2))*cos(e + f*x)/(f*(-m + S(1))*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*cot(e + f*x)**S(4), x), x, -b**S(3)*(b*sin(e + f*x))**(m + S(-3))*Hypergeometric2F1(S(-3)/2, m/S(2) + S(-3)/2, m/S(2) + S(-1)/2, sin(e + f*x)**S(2))*cos(e + f*x)/(f*(-m + S(3))*sqrt(cos(e + f*x)**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*(d*tan(e + f*x))**(S(3)/2), x), x, S(2)*d*(b*sin(e + f*x))**(m + S(2))*sqrt(d*tan(e + f*x))*(cos(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(5)/4, m/S(2) + S(5)/4, m/S(2) + S(9)/4, sin(e + f*x)**S(2))/(b**S(2)*f*(S(2)*m + S(5))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m*sqrt(d*tan(e + f*x)), x), x, S(2)*(b*sin(e + f*x))**m*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(3)/4, m/S(2) + S(3)/4, m/S(2) + S(7)/4, sin(e + f*x)**S(2))/(d*f*(S(2)*m + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m/sqrt(d*tan(e + f*x)), x), x, S(2)*(b*sin(e + f*x))**m*sqrt(d*tan(e + f*x))*(cos(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(1)/4, m/S(2) + S(1)/4, m/S(2) + S(5)/4, sin(e + f*x)**S(2))/(d*f*(S(2)*m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sin(e + f*x))**m/(d*tan(e + f*x))**(S(3)/2), x), x, -S(2)*(b*sin(e + f*x))**m*Hypergeometric2F1(S(-1)/4, m/S(2) + S(-1)/4, m/S(2) + S(3)/4, sin(e + f*x)**S(2))/(d*f*sqrt(d*tan(e + f*x))*(-S(2)*m + S(1))*(cos(e + f*x)**S(2))**(S(1)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sin(e + f*x))**m*(b*tan(e + f*x))**n, x), x, (a*sin(e + f*x))**m*(b*tan(e + f*x))**(n + S(1))*(cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(n/S(2) + S(1)/2, m/S(2) + n/S(2) + S(1)/2, m/S(2) + n/S(2) + S(3)/2, sin(e + f*x)**S(2))/(b*f*(m + n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*sin(e + f*x)**S(4), x), x, (b*tan(e + f*x))**(n + S(5))*Hypergeometric2F1(S(3), n/S(2) + S(5)/2, n/S(2) + S(7)/2, -tan(e + f*x)**S(2))/(b**S(5)*f*(n + S(5))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*sin(e + f*x)**S(2), x), x, (b*tan(e + f*x))**(n + S(3))*Hypergeometric2F1(S(2), n/S(2) + S(3)/2, n/S(2) + S(5)/2, -tan(e + f*x)**S(2))/(b**S(3)*f*(n + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*csc(e + f*x)**S(2), x), x, -b*(b*tan(e + f*x))**(n + S(-1))/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*csc(e + f*x)**S(4), x), x, -b**S(3)*(b*tan(e + f*x))**(n + S(-3))/(f*(-n + S(3))) - b*(b*tan(e + f*x))**(n + S(-1))/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*csc(e + f*x)**S(6), x), x, -b**S(5)*(b*tan(e + f*x))**(n + S(-5))/(f*(-n + S(5))) - S(2)*b**S(3)*(b*tan(e + f*x))**(n + S(-3))/(f*(-n + S(3))) - b*(b*tan(e + f*x))**(n + S(-1))/(f*(-n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*sin(e + f*x)**S(3), x), x, (b*tan(e + f*x))**(n + S(4))*(cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(n/S(2) + S(1)/2, n/S(2) + S(2), n/S(2) + S(3), sin(e + f*x)**S(2))*cos(e + f*x)**S(3)/(b**S(4)*f*(n + S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*sin(e + f*x), x), x, (b*tan(e + f*x))**(n + S(2))*(cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(n/S(2) + S(1)/2, n/S(2) + S(1), n/S(2) + S(2), sin(e + f*x)**S(2))*cos(e + f*x)/(b**S(2)*f*(n + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*csc(e + f*x), x), x, (b*tan(e + f*x))**n*(cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(n/S(2), n/S(2) + S(1)/2, n/S(2) + S(1), sin(e + f*x)**S(2))*sec(e + f*x)/(f*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*csc(e + f*x)**S(3), x), x, -b**S(2)*(b*tan(e + f*x))**(n + S(-2))*(cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(n/S(2) + S(-1), n/S(2) + S(1)/2, n/S(2), sin(e + f*x)**S(2))*sec(e + f*x)**S(3)/(f*(-n + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*cos(e + f*x))**m*(b*tan(e + f*x))**n, x), x, (a*cos(e + f*x))**m*(b*tan(e + f*x))**(n + S(1))*(cos(e + f*x)**S(2))**(-m/S(2) + n/S(2) + S(1)/2)*Hypergeometric2F1(n/S(2) + S(1)/2, -m/S(2) + n/S(2) + S(1)/2, n/S(2) + S(3)/2, sin(e + f*x)**S(2))/(b*f*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*tan(e + f*x))**m*(b*tan(e + f*x))**n, x), x, (a*tan(e + f*x))**(m + S(1))*(b*tan(e + f*x))**n*Hypergeometric2F1(S(1), m/S(2) + n/S(2) + S(1)/2, m/S(2) + n/S(2) + S(3)/2, -tan(e + f*x)**S(2))/(a*f*(m + n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cot(e + f*x))*tan(e + f*x)**S(4), x), x, sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + S(2)*d**S(3)/(S(5)*f*(d*cot(e + f*x))**(S(5)/2)) - S(2)*d/(f*sqrt(d*cot(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cot(e + f*x))*tan(e + f*x)**S(3), x), x, -sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + S(2)*d**S(2)/(S(3)*f*(d*cot(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cot(e + f*x))*tan(e + f*x)**S(2), x), x, -sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + S(2)*d/(f*sqrt(d*cot(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cot(e + f*x))*tan(e + f*x), x), x, sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cot(e + f*x)), x), x, sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cot(e + f*x))*cot(e + f*x), x), x, -sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - S(2)*sqrt(d*cot(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cot(e + f*x))*cot(e + f*x)**S(2), x), x, -sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - S(2)*(d*cot(e + f*x))**(S(3)/2)/(S(3)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*cot(e + f*x))*cot(e + f*x)**S(3), x), x, sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + S(2)*sqrt(d*cot(e + f*x))/f - S(2)*(d*cot(e + f*x))**(S(5)/2)/(S(5)*d**S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cot(e + f*x))**(S(3)/2)*tan(e + f*x)**S(5), x), x, sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + S(2)*d**S(4)/(S(5)*f*(d*cot(e + f*x))**(S(5)/2)) - S(2)*d**S(2)/(f*sqrt(d*cot(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cot(e + f*x))**(S(3)/2)*tan(e + f*x)**S(4), x), x, -sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + S(2)*d**S(3)/(S(3)*f*(d*cot(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cot(e + f*x))**(S(3)/2)*tan(e + f*x)**S(3), x), x, -sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + S(2)*d**S(2)/(f*sqrt(d*cot(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cot(e + f*x))**(S(3)/2)*tan(e + f*x)**S(2), x), x, sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cot(e + f*x))**(S(3)/2)*tan(e + f*x), x), x, sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cot(e + f*x))**(S(3)/2), x), x, -sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - S(2)*d*sqrt(d*cot(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cot(e + f*x))**(S(3)/2)*cot(e + f*x), x), x, -sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - S(2)*(d*cot(e + f*x))**(S(3)/2)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cot(e + f*x))**(S(3)/2)*cot(e + f*x)**S(2), x), x, sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*f) + S(2)*d*sqrt(d*cot(e + f*x))/f - S(2)*(d*cot(e + f*x))**(S(5)/2)/(S(5)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(3)/sqrt(d*cot(e + f*x)), x), x, S(2)*d**S(2)/(S(5)*f*(d*cot(e + f*x))**(S(5)/2)) - S(2)/(f*sqrt(d*cot(e + f*x))) + sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(2)/sqrt(d*cot(e + f*x)), x), x, S(2)*d/(S(3)*f*(d*cot(e + f*x))**(S(3)/2)) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)/sqrt(d*cot(e + f*x)), x), x, S(2)/(f*sqrt(d*cot(e + f*x))) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(d*cot(e + f*x)), x), x, sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(e + f*x)/sqrt(d*cot(e + f*x)), x), x, sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(e + f*x)**S(2)/sqrt(d*cot(e + f*x)), x), x, -S(2)*sqrt(d*cot(e + f*x))/(d*f) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(e + f*x)**S(3)/sqrt(d*cot(e + f*x)), x), x, -S(2)*(d*cot(e + f*x))**(S(3)/2)/(S(3)*d**S(2)*f) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*sqrt(d)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*sqrt(d)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(2)/(d*cot(e + f*x))**(S(3)/2), x), x, S(2)*d/(S(5)*f*(d*cot(e + f*x))**(S(5)/2)) - S(2)/(d*f*sqrt(d*cot(e + f*x))) + sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)/(d*cot(e + f*x))**(S(3)/2), x), x, S(2)/(S(3)*f*(d*cot(e + f*x))**(S(3)/2)) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*cot(e + f*x))**(S(-3)/2), x), x, S(2)/(d*f*sqrt(d*cot(e + f*x))) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(e + f*x)/(d*cot(e + f*x))**(S(3)/2), x), x, sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(e + f*x)**S(2)/(d*cot(e + f*x))**(S(3)/2), x), x, sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(e + f*x)**S(3)/(d*cot(e + f*x))**(S(3)/2), x), x, -S(2)*sqrt(d*cot(e + f*x))/(d**S(2)*f) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(e + f*x)**S(4)/(d*cot(e + f*x))**(S(3)/2), x), x, -S(2)*(d*cot(e + f*x))**(S(3)/2)/(S(3)*d**S(3)*f) - sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) + sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cot(e + f*x)**S(5)/(d*cot(e + f*x))**(S(3)/2), x), x, S(2)*sqrt(d*cot(e + f*x))/(d**S(2)*f) - S(2)*(d*cot(e + f*x))**(S(5)/2)/(S(5)*d**S(4)*f) + sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*cot(e + f*x))/sqrt(d))/(S(2)*d**(S(3)/2)*f) + sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f) - sqrt(S(2))*log(sqrt(d)*cot(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*cot(e + f*x)))/(S(4)*d**(S(3)/2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**n*cot(e + f*x)**m, x), x, Hypergeometric2F1(S(1), -m/S(2) + n/S(2) + S(1)/2, -m/S(2) + n/S(2) + S(3)/2, -tan(e + f*x)**S(2))*tan(e + f*x)**(n + S(1))*cot(e + f*x)**m/(f*(-m + n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**n*cot(e + f*x)**m, x), x, (b*tan(e + f*x))**(n + S(1))*Hypergeometric2F1(S(1), -m/S(2) + n/S(2) + S(1)/2, -m/S(2) + n/S(2) + S(3)/2, -tan(e + f*x)**S(2))*cot(e + f*x)**m/(b*f*(-m + n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*cot(e + f*x))**m*tan(e + f*x)**n, x), x, (a*cot(e + f*x))**m*Hypergeometric2F1(S(1), -m/S(2) + n/S(2) + S(1)/2, -m/S(2) + n/S(2) + S(3)/2, -tan(e + f*x)**S(2))*tan(e + f*x)**(n + S(1))/(f*(-m + n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*cot(e + f*x))**m*(b*tan(e + f*x))**n, x), x, (a*cot(e + f*x))**m*(b*tan(e + f*x))**(n + S(1))*Hypergeometric2F1(S(1), -m/S(2) + n/S(2) + S(1)/2, -m/S(2) + n/S(2) + S(3)/2, -tan(e + f*x)**S(2))/(b*f*(-m + n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))*sec(e + f*x)**S(6), x), x, S(2)*(d*tan(e + f*x))**(S(3)/2)/(S(3)*d*f) + S(4)*(d*tan(e + f*x))**(S(7)/2)/(S(7)*d**S(3)*f) + S(2)*(d*tan(e + f*x))**(S(11)/2)/(S(11)*d**S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))*sec(e + f*x)**S(4), x), x, S(2)*(d*tan(e + f*x))**(S(3)/2)/(S(3)*d*f) + S(2)*(d*tan(e + f*x))**(S(7)/2)/(S(7)*d**S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))*sec(e + f*x)**S(2), x), x, S(2)*(d*tan(e + f*x))**(S(3)/2)/(S(3)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x)), x), x, -sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(2)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*tan(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(4)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*tan(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(4)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))*cos(e + f*x)**S(2), x), x, -sqrt(S(2))*sqrt(d)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(8)*f) + sqrt(S(2))*sqrt(d)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(8)*f) + sqrt(S(2))*sqrt(d)*log(sqrt(d)*tan(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(16)*f) - sqrt(S(2))*sqrt(d)*log(sqrt(d)*tan(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(16)*f) + (d*tan(e + f*x))**(S(3)/2)*cos(e + f*x)**S(2)/(S(2)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))*sec(e + f*x)**S(3), x), x, -S(4)*sqrt(d*tan(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*cos(e + f*x)/(S(5)*f*sqrt(sin(S(2)*e + S(2)*f*x))) + S(4)*(d*tan(e + f*x))**(S(3)/2)*cos(e + f*x)/(S(5)*d*f) + S(2)*(d*tan(e + f*x))**(S(3)/2)*sec(e + f*x)/(S(5)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))*sec(e + f*x), x), x, -S(2)*sqrt(d*tan(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*cos(e + f*x)/(f*sqrt(sin(S(2)*e + S(2)*f*x))) + S(2)*(d*tan(e + f*x))**(S(3)/2)*cos(e + f*x)/(d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))*cos(e + f*x), x), x, sqrt(d*tan(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*cos(e + f*x)/(f*sqrt(sin(S(2)*e + S(2)*f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))*cos(e + f*x)**S(3), x), x, sqrt(d*tan(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*cos(e + f*x)/(S(2)*f*sqrt(sin(S(2)*e + S(2)*f*x))) + (d*tan(e + f*x))**(S(3)/2)*cos(e + f*x)**S(3)/(S(3)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))*cos(e + f*x)**S(5), x), x, S(7)*sqrt(d*tan(e + f*x))*EllipticE(-Pi/S(4) + e + f*x, S(2))*cos(e + f*x)/(S(20)*f*sqrt(sin(S(2)*e + S(2)*f*x))) + (d*tan(e + f*x))**(S(3)/2)*cos(e + f*x)**S(5)/(S(5)*d*f) + S(7)*(d*tan(e + f*x))**(S(3)/2)*cos(e + f*x)**S(3)/(S(30)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sec(a + b*x)**S(6), x), x, S(2)*(d*tan(a + b*x))**(S(5)/2)/(S(5)*b*d) + S(4)*(d*tan(a + b*x))**(S(9)/2)/(S(9)*b*d**S(3)) + S(2)*(d*tan(a + b*x))**(S(13)/2)/(S(13)*b*d**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sec(a + b*x)**S(4), x), x, S(2)*(d*tan(a + b*x))**(S(5)/2)/(S(5)*b*d) + S(2)*(d*tan(a + b*x))**(S(9)/2)/(S(9)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sec(a + b*x)**S(2), x), x, S(2)*(d*tan(a + b*x))**(S(5)/2)/(S(5)*b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2), x), x, sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(2)*b) - sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(2)*b) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(4)*b) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(4)*b) + S(2)*d*sqrt(d*tan(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(2), x), x, -sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b) + sqrt(S(2))*d**(S(3)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b) - sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b) + sqrt(S(2))*d**(S(3)/2)*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b) - d*sqrt(d*tan(a + b*x))*cos(a + b*x)**S(2)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sec(a + b*x)**S(5), x), x, -S(4)*d**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(77)*b*sqrt(d*tan(a + b*x))) + S(2)*d*sqrt(d*tan(a + b*x))*sec(a + b*x)**S(5)/(S(11)*b) - S(2)*d*sqrt(d*tan(a + b*x))*sec(a + b*x)**S(3)/(S(77)*b) - S(4)*d*sqrt(d*tan(a + b*x))*sec(a + b*x)/(S(77)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sec(a + b*x)**S(3), x), x, -S(2)*d**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(21)*b*sqrt(d*tan(a + b*x))) + S(2)*d*sqrt(d*tan(a + b*x))*sec(a + b*x)**S(3)/(S(7)*b) - S(2)*d*sqrt(d*tan(a + b*x))*sec(a + b*x)/(S(21)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*sec(a + b*x), x), x, -d**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(3)*b*sqrt(d*tan(a + b*x))) + S(2)*d*sqrt(d*tan(a + b*x))*sec(a + b*x)/(S(3)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*cos(a + b*x), x), x, d**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(2)*b*sqrt(d*tan(a + b*x))) - d*sqrt(d*tan(a + b*x))*cos(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(3), x), x, d**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(12)*b*sqrt(d*tan(a + b*x))) - d*sqrt(d*tan(a + b*x))*cos(a + b*x)**S(3)/(S(3)*b) + d*sqrt(d*tan(a + b*x))*cos(a + b*x)/(S(6)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(5), x), x, d**S(2)*EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(24)*b*sqrt(d*tan(a + b*x))) - d*sqrt(d*tan(a + b*x))*cos(a + b*x)**S(5)/(S(5)*b) + d*sqrt(d*tan(a + b*x))*cos(a + b*x)**S(3)/(S(30)*b) + d*sqrt(d*tan(a + b*x))*cos(a + b*x)/(S(12)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(5)/2)*sec(e + f*x)**S(6), x), x, S(2)*(d*tan(e + f*x))**(S(7)/2)/(S(7)*d*f) + S(4)*(d*tan(e + f*x))**(S(11)/2)/(S(11)*d**S(3)*f) + S(2)*(d*tan(e + f*x))**(S(15)/2)/(S(15)*d**S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(5)/2)*sec(e + f*x)**S(4), x), x, S(2)*(d*tan(e + f*x))**(S(7)/2)/(S(7)*d*f) + S(2)*(d*tan(e + f*x))**(S(11)/2)/(S(11)*d**S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(5)/2)*sec(e + f*x)**S(2), x), x, S(2)*(d*tan(e + f*x))**(S(7)/2)/(S(7)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(5)/2), x), x, sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(2)*f) - sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(4)*f) + sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(4)*f) + S(2)*d*(d*tan(e + f*x))**(S(3)/2)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(5)/2)*cos(e + f*x)**S(2), x), x, -S(3)*sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(8)*f) + S(3)*sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(8)*f) + S(3)*sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(16)*f) - S(3)*sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(16)*f) - d*(d*tan(e + f*x))**(S(3)/2)*cos(e + f*x)**S(2)/(S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(5)/2)*cos(e + f*x)**S(4), x), x, -S(3)*sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(64)*f) + S(3)*sqrt(S(2))*d**(S(5)/2)*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(e + f*x))/sqrt(d))/(S(64)*f) + S(3)*sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(e + f*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(128)*f) - S(3)*sqrt(S(2))*d**(S(5)/2)*log(sqrt(d)*tan(e + f*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(e + f*x)))/(S(128)*f) - d*(d*tan(e + f*x))**(S(3)/2)*cos(e + f*x)**S(4)/(S(4)*f) + S(3)*d*(d*tan(e + f*x))**(S(3)/2)*cos(e + f*x)**S(2)/(S(16)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(e + f*x)**S(5)/sqrt(d*tan(e + f*x)), x), x, S(4)*EllipticF(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(S(2)*e + S(2)*f*x))*sec(e + f*x)/(S(7)*f*sqrt(d*tan(e + f*x))) + S(2)*sqrt(d*tan(e + f*x))*sec(e + f*x)**S(3)/(S(7)*d*f) + S(4)*sqrt(d*tan(e + f*x))*sec(e + f*x)/(S(7)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(e + f*x)**S(3)/sqrt(d*tan(e + f*x)), x), x, S(2)*EllipticF(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(S(2)*e + S(2)*f*x))*sec(e + f*x)/(S(3)*f*sqrt(d*tan(e + f*x))) + S(2)*sqrt(d*tan(e + f*x))*sec(e + f*x)/(S(3)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(e + f*x)/sqrt(d*tan(e + f*x)), x), x, EllipticF(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(S(2)*e + S(2)*f*x))*sec(e + f*x)/(f*sqrt(d*tan(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(e + f*x)/sqrt(d*tan(e + f*x)), x), x, EllipticF(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(S(2)*e + S(2)*f*x))*sec(e + f*x)/(S(2)*f*sqrt(d*tan(e + f*x))) + sqrt(d*tan(e + f*x))*cos(e + f*x)/(d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(e + f*x)**S(3)/sqrt(d*tan(e + f*x)), x), x, S(5)*EllipticF(-Pi/S(4) + e + f*x, S(2))*sqrt(sin(S(2)*e + S(2)*f*x))*sec(e + f*x)/(S(12)*f*sqrt(d*tan(e + f*x))) + sqrt(d*tan(e + f*x))*cos(e + f*x)**S(3)/(S(3)*d*f) + S(5)*sqrt(d*tan(e + f*x))*cos(e + f*x)/(S(6)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(6)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)/(b*d*sqrt(d*tan(a + b*x))) + S(4)*(d*tan(a + b*x))**(S(3)/2)/(S(3)*b*d**S(3)) + S(2)*(d*tan(a + b*x))**(S(7)/2)/(S(7)*b*d**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(4)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)/(b*d*sqrt(d*tan(a + b*x))) + S(2)*(d*tan(a + b*x))**(S(3)/2)/(S(3)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(2)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)/(b*d*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**(S(-3)/2), x), x, -S(2)/(b*d*sqrt(d*tan(a + b*x))) + sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(2)*b*d**(S(3)/2)) - sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(2)*b*d**(S(3)/2)) - sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(4)*b*d**(S(3)/2)) + sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(4)*b*d**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(2)/(d*tan(a + b*x))**(S(3)/2), x), x, cos(a + b*x)**S(2)/(S(2)*b*d*sqrt(d*tan(a + b*x))) - S(5)/(S(2)*b*d*sqrt(d*tan(a + b*x))) + S(5)*sqrt(S(2))*ArcTan(S(1) - sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b*d**(S(3)/2)) - S(5)*sqrt(S(2))*ArcTan(S(1) + sqrt(S(2))*sqrt(d*tan(a + b*x))/sqrt(d))/(S(8)*b*d**(S(3)/2)) - S(5)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) - sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b*d**(S(3)/2)) + S(5)*sqrt(S(2))*log(sqrt(d)*tan(a + b*x) + sqrt(d) + sqrt(S(2))*sqrt(d*tan(a + b*x)))/(S(16)*b*d**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(5)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*sec(a + b*x)**S(3)/(b*d*sqrt(d*tan(a + b*x))) - S(24)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(5)*b*d**S(2)*sqrt(sin(S(2)*a + S(2)*b*x))) + S(24)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)/(S(5)*b*d**S(3)) + S(12)*(d*tan(a + b*x))**(S(3)/2)*sec(a + b*x)/(S(5)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(3)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*sec(a + b*x)/(b*d*sqrt(d*tan(a + b*x))) - S(4)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(b*d**S(2)*sqrt(sin(S(2)*a + S(2)*b*x))) + S(4)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)/(b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*cos(a + b*x)/(b*d*sqrt(d*tan(a + b*x))) - S(2)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(b*d**S(2)*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*cos(a + b*x)/(b*d*sqrt(d*tan(a + b*x))) - S(3)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(b*d**S(2)*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(3)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*cos(a + b*x)**S(3)/(b*d*sqrt(d*tan(a + b*x))) - S(7)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(2)*b*d**S(2)*sqrt(sin(S(2)*a + S(2)*b*x))) - S(7)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(3)/(S(3)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(cos(a + b*x)**S(5)/(d*tan(a + b*x))**(S(3)/2), x), x, -S(2)*cos(a + b*x)**S(5)/(b*d*sqrt(d*tan(a + b*x))) - S(77)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(20)*b*d**S(2)*sqrt(sin(S(2)*a + S(2)*b*x))) - S(11)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(5)/(S(5)*b*d**S(3)) - S(77)*(d*tan(a + b*x))**(S(3)/2)*cos(a + b*x)**S(3)/(S(30)*b*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)/(d*tan(a + b*x))**(S(5)/2), x), x, -S(2)*sec(a + b*x)/(S(3)*b*d*(d*tan(a + b*x))**(S(3)/2)) - EllipticF(-Pi/S(4) + a + b*x, S(2))*sqrt(sin(S(2)*a + S(2)*b*x))*sec(a + b*x)/(S(3)*b*d**S(2)*sqrt(d*tan(a + b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sec(a + b*x)**S(3)/(d*tan(a + b*x))**(S(7)/2), x), x, -S(2)*sec(a + b*x)/(S(5)*b*d*(d*tan(a + b*x))**(S(5)/2)) - S(4)*cos(a + b*x)/(S(5)*b*d**S(3)*sqrt(d*tan(a + b*x))) - S(4)*sqrt(d*tan(a + b*x))*EllipticE(-Pi/S(4) + a + b*x, S(2))*cos(a + b*x)/(S(5)*b*d**S(4)*sqrt(sin(S(2)*a + S(2)*b*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(2)*sec(e + f*x)**(S(4)/3), x), x, (cos(e + f*x)**S(2))**(S(1)/6)*Hypergeometric2F1(S(3)/2, S(13)/6, S(5)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(3)*sec(e + f*x)**(S(1)/3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(2)*sec(e + f*x)**(S(2)/3), x), x, (cos(e + f*x)**S(2))**(S(5)/6)*Hypergeometric2F1(S(3)/2, S(11)/6, S(5)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(3)*sec(e + f*x)**(S(5)/3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(2)*sec(e + f*x)**(S(1)/3), x), x, (cos(e + f*x)**S(2))**(S(2)/3)*Hypergeometric2F1(S(3)/2, S(5)/3, S(5)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(3)*sec(e + f*x)**(S(4)/3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(2)/sec(e + f*x)**(S(1)/3), x), x, (cos(e + f*x)**S(2))**(S(1)/3)*Hypergeometric2F1(S(4)/3, S(3)/2, S(5)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(3)*sec(e + f*x)**(S(2)/3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(2)/sec(e + f*x)**(S(2)/3), x), x, (cos(e + f*x)**S(2))**(S(1)/6)*Hypergeometric2F1(S(7)/6, S(3)/2, S(5)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(3)*sec(e + f*x)**(S(1)/3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(4)*sec(e + f*x)**(S(4)/3), x), x, (cos(e + f*x)**S(2))**(S(1)/6)*Hypergeometric2F1(S(5)/2, S(19)/6, S(7)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(5)*sec(e + f*x)**(S(1)/3)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(4)*sec(e + f*x)**(S(2)/3), x), x, (cos(e + f*x)**S(2))**(S(5)/6)*Hypergeometric2F1(S(5)/2, S(17)/6, S(7)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(5)*sec(e + f*x)**(S(5)/3)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(4)*sec(e + f*x)**(S(1)/3), x), x, (cos(e + f*x)**S(2))**(S(2)/3)*Hypergeometric2F1(S(5)/2, S(8)/3, S(7)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(5)*sec(e + f*x)**(S(4)/3)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(4)/sec(e + f*x)**(S(1)/3), x), x, (cos(e + f*x)**S(2))**(S(1)/3)*Hypergeometric2F1(S(7)/3, S(5)/2, S(7)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(5)*sec(e + f*x)**(S(2)/3)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(4)/sec(e + f*x)**(S(2)/3), x), x, (cos(e + f*x)**S(2))**(S(1)/6)*Hypergeometric2F1(S(13)/6, S(5)/2, S(7)/2, sin(e + f*x)**S(2))*sin(e + f*x)**S(5)*sec(e + f*x)**(S(1)/3)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(4)/3)*tan(e + f*x)**S(2), x), x, (d*sec(e + f*x))**(S(4)/3)*(cos(e + f*x)**S(2))**(S(13)/6)*Hypergeometric2F1(S(3)/2, S(13)/6, S(5)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(2)/3)*tan(e + f*x)**S(2), x), x, (d*sec(e + f*x))**(S(2)/3)*(cos(e + f*x)**S(2))**(S(11)/6)*Hypergeometric2F1(S(3)/2, S(11)/6, S(5)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(1)/3)*tan(e + f*x)**S(2), x), x, (d*sec(e + f*x))**(S(1)/3)*(cos(e + f*x)**S(2))**(S(5)/3)*Hypergeometric2F1(S(3)/2, S(5)/3, S(5)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(2)/(d*sec(e + f*x))**(S(1)/3), x), x, (cos(e + f*x)**S(2))**(S(4)/3)*Hypergeometric2F1(S(4)/3, S(3)/2, S(5)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(3)/(S(3)*f*(d*sec(e + f*x))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(2)/(d*sec(e + f*x))**(S(2)/3), x), x, (cos(e + f*x)**S(2))**(S(7)/6)*Hypergeometric2F1(S(7)/6, S(3)/2, S(5)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(3)/(S(3)*f*(d*sec(e + f*x))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(4)/3)*tan(e + f*x)**S(4), x), x, (d*sec(e + f*x))**(S(4)/3)*(cos(e + f*x)**S(2))**(S(19)/6)*Hypergeometric2F1(S(5)/2, S(19)/6, S(7)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(5)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(2)/3)*tan(e + f*x)**S(4), x), x, (d*sec(e + f*x))**(S(2)/3)*(cos(e + f*x)**S(2))**(S(17)/6)*Hypergeometric2F1(S(5)/2, S(17)/6, S(7)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(5)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(1)/3)*tan(e + f*x)**S(4), x), x, (d*sec(e + f*x))**(S(1)/3)*(cos(e + f*x)**S(2))**(S(8)/3)*Hypergeometric2F1(S(5)/2, S(8)/3, S(7)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(5)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(4)/(d*sec(e + f*x))**(S(1)/3), x), x, (cos(e + f*x)**S(2))**(S(7)/3)*Hypergeometric2F1(S(7)/3, S(5)/2, S(7)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(5)/(S(5)*f*(d*sec(e + f*x))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(tan(e + f*x)**S(4)/(d*sec(e + f*x))**(S(2)/3), x), x, (cos(e + f*x)**S(2))**(S(13)/6)*Hypergeometric2F1(S(13)/6, S(5)/2, S(7)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(5)/(S(5)*f*(d*sec(e + f*x))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(5)/2), x), x, -sqrt(b)*d**S(3)*sqrt(b*tan(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(S(4)*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) + sqrt(b)*d**S(3)*sqrt(b*tan(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(S(4)*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) + d**S(2)*(b*tan(e + f*x))**(S(3)/2)*sqrt(d*sec(e + f*x))/(S(2)*b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(3)/2), x), x, -d**S(2)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))) + d**S(2)*(b*tan(e + f*x))**(S(3)/2)/(b*f*sqrt(d*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(e + f*x))*sqrt(d*sec(e + f*x)), x), x, -sqrt(b)*d*sqrt(b*tan(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) + sqrt(b)*d*sqrt(b*tan(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(e + f*x))/sqrt(d*sec(e + f*x)), x), x, S(2)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(e + f*x))/(d*sec(e + f*x))**(S(3)/2), x), x, S(2)*(b*tan(e + f*x))**(S(3)/2)/(S(3)*b*f*(d*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(e + f*x))/(d*sec(e + f*x))**(S(5)/2), x), x, S(4)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*d**S(2)*f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))) + S(2)*(b*tan(e + f*x))**(S(3)/2)/(S(5)*b*f*(d*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(e + f*x))/(d*sec(e + f*x))**(S(7)/2), x), x, S(2)*(b*tan(e + f*x))**(S(3)/2)/(S(7)*b*f*(d*sec(e + f*x))**(S(7)/2)) + S(8)*(b*tan(e + f*x))**(S(3)/2)/(S(21)*b*d**S(2)*f*(d*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*tan(e + f*x))/(d*sec(e + f*x))**(S(9)/2), x), x, S(8)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(15)*d**S(4)*f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))) + S(2)*(b*tan(e + f*x))**(S(3)/2)/(S(9)*b*f*(d*sec(e + f*x))**(S(9)/2)) + S(4)*(b*tan(e + f*x))**(S(3)/2)/(S(15)*b*d**S(2)*f*(d*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(5)/2), x), x, -b**S(2)*d**S(2)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(6)*f*sqrt(b*tan(e + f*x))) - b*d**S(2)*sqrt(b*tan(e + f*x))*sqrt(d*sec(e + f*x))/(S(6)*f) + b*sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(5)/2)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(3)/2), x), x, -b**(S(3)/2)*d*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(S(4)*f*sqrt(b*tan(e + f*x))) - b**(S(3)/2)*d*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(S(4)*f*sqrt(b*tan(e + f*x))) + b*sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(3)/2)/(S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(3)/2)*sqrt(d*sec(e + f*x)), x), x, -b**S(2)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(f*sqrt(b*tan(e + f*x))) + b*sqrt(b*tan(e + f*x))*sqrt(d*sec(e + f*x))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(3)/2)/sqrt(d*sec(e + f*x)), x), x, b**(S(3)/2)*d*(b*tan(e + f*x))**(S(3)/2)*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(f*(b*sin(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(3)/2)) + b**(S(3)/2)*d*(b*tan(e + f*x))**(S(3)/2)*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(f*(b*sin(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(3)/2)) - S(2)*d*(b*tan(e + f*x))**(S(3)/2)*csc(e + f*x)/(f*(d*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(3)/2)/(d*sec(e + f*x))**(S(3)/2), x), x, S(2)*b**S(2)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*d**S(2)*f*sqrt(b*tan(e + f*x))) - S(2)*b*sqrt(b*tan(e + f*x))/(S(3)*f*(d*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(3)/2)/(d*sec(e + f*x))**(S(5)/2), x), x, S(2)*(b*tan(e + f*x))**(S(5)/2)/(S(5)*b*f*(d*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(3)/2)/(d*sec(e + f*x))**(S(7)/2), x), x, S(4)*b**S(2)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(21)*d**S(4)*f*sqrt(b*tan(e + f*x))) - S(2)*b*sqrt(b*tan(e + f*x))/(S(7)*f*(d*sec(e + f*x))**(S(7)/2)) + S(2)*b*sqrt(b*tan(e + f*x))/(S(21)*d**S(2)*f*(d*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(3)/2)/(d*sec(e + f*x))**(S(9)/2), x), x, -S(2)*b*sqrt(b*tan(e + f*x))/(S(9)*f*(d*sec(e + f*x))**(S(9)/2)) + S(2)*b*sqrt(b*tan(e + f*x))/(S(45)*d**S(2)*f*(d*sec(e + f*x))**(S(5)/2)) + S(8)*b*sqrt(b*tan(e + f*x))/(S(45)*d**S(4)*f*sqrt(d*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(5)/2)*(d*sec(e + f*x))**(S(5)/2), x), x, S(3)*b**(S(5)/2)*d**S(3)*sqrt(b*tan(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(S(32)*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) - S(3)*b**(S(5)/2)*d**S(3)*sqrt(b*tan(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(S(32)*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) - S(3)*b*d**S(2)*(b*tan(e + f*x))**(S(3)/2)*sqrt(d*sec(e + f*x))/(S(16)*f) + b*(b*tan(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(5)/2)/(S(4)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(5)/2)*(d*sec(e + f*x))**(S(3)/2), x), x, b**S(2)*d**S(2)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(2)*f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))) - b*d**S(2)*(b*tan(e + f*x))**(S(3)/2)/(S(2)*f*sqrt(d*sec(e + f*x))) + b*(b*tan(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(3)/2)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(5)/2)*sqrt(d*sec(e + f*x)), x), x, S(3)*b**(S(5)/2)*d*sqrt(b*tan(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(S(4)*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) - S(3)*b**(S(5)/2)*d*sqrt(b*tan(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(S(4)*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) + b*(b*tan(e + f*x))**(S(3)/2)*sqrt(d*sec(e + f*x))/(S(2)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(5)/2)/sqrt(d*sec(e + f*x)), x), x, -S(3)*b**S(2)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))) + b*(b*tan(e + f*x))**(S(3)/2)/(f*sqrt(d*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(5)/2)/(d*sec(e + f*x))**(S(3)/2), x), x, -b**(S(5)/2)*sqrt(b*tan(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(d*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) + b**(S(5)/2)*sqrt(b*tan(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(d*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) - S(2)*b*(b*tan(e + f*x))**(S(3)/2)/(S(3)*f*(d*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(5)/2)/(d*sec(e + f*x))**(S(5)/2), x), x, S(6)*b**S(2)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*d**S(2)*f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))) - S(2)*b*(b*tan(e + f*x))**(S(3)/2)/(S(5)*f*(d*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(5)/2)/(d*sec(e + f*x))**(S(7)/2), x), x, S(2)*(b*tan(e + f*x))**(S(7)/2)/(S(7)*b*f*(d*sec(e + f*x))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*tan(e + f*x))**(S(5)/2)/(d*sec(e + f*x))**(S(9)/2), x), x, S(4)*b**S(2)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(15)*d**S(4)*f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))) - S(2)*b*(b*tan(e + f*x))**(S(3)/2)/(S(9)*f*(d*sec(e + f*x))**(S(9)/2)) + S(2)*b*(b*tan(e + f*x))**(S(3)/2)/(S(15)*d**S(2)*f*(d*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(7)/2)/sqrt(b*tan(e + f*x)), x), x, d**S(2)*sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(3)/2)/(S(2)*b*f) + S(3)*d**S(3)*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(S(4)*sqrt(b)*f*sqrt(b*tan(e + f*x))) + S(3)*d**S(3)*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(S(4)*sqrt(b)*f*sqrt(b*tan(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(5)/2)/sqrt(b*tan(e + f*x)), x), x, d**S(2)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(f*sqrt(b*tan(e + f*x))) + d**S(2)*sqrt(b*tan(e + f*x))*sqrt(d*sec(e + f*x))/(b*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(3)/2)/sqrt(b*tan(e + f*x)), x), x, d*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(sqrt(b)*f*sqrt(b*tan(e + f*x))) + d*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(sqrt(b)*f*sqrt(b*tan(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*sec(e + f*x))/sqrt(b*tan(e + f*x)), x), x, S(2)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(f*sqrt(b*tan(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*tan(e + f*x))*sqrt(d*sec(e + f*x))), x), x, S(2)*sqrt(b*tan(e + f*x))/(b*f*sqrt(d*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(3)/2)), x), x, S(4)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*d**S(2)*f*sqrt(b*tan(e + f*x))) + S(2)*sqrt(b*tan(e + f*x))/(S(3)*b*f*(d*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(5)/2)), x), x, S(2)*sqrt(b*tan(e + f*x))/(S(5)*b*f*(d*sec(e + f*x))**(S(5)/2)) + S(8)*sqrt(b*tan(e + f*x))/(S(5)*b*d**S(2)*f*sqrt(d*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(5)/2)/(b*tan(e + f*x))**(S(3)/2), x), x, -S(2)*d**S(2)*sqrt(d*sec(e + f*x))/(b*f*sqrt(b*tan(e + f*x))) - d**S(3)*sqrt(b*tan(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(b**(S(3)/2)*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))) + d**S(3)*sqrt(b*tan(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(b**(S(3)/2)*f*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(3)/2)/(b*tan(e + f*x))**(S(3)/2), x), x, -S(2)*d**S(2)/(b*f*sqrt(b*tan(e + f*x))*sqrt(d*sec(e + f*x))) - S(2)*d**S(2)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(b**S(2)*f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*sec(e + f*x))/(b*tan(e + f*x))**(S(3)/2), x), x, -S(2)*sqrt(d*sec(e + f*x))/(b*f*sqrt(b*tan(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((b*tan(e + f*x))**(S(3)/2)*sqrt(d*sec(e + f*x))), x), x, -S(2)/(b*f*sqrt(b*tan(e + f*x))*sqrt(d*sec(e + f*x))) - S(4)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(b**S(2)*f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((b*tan(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(3)/2)), x), x, S(2)/(S(3)*b*f*sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(3)/2)) - S(8)*sqrt(d*sec(e + f*x))/(S(3)*b*d**S(2)*f*sqrt(b*tan(e + f*x))), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(S(1)/((b*tan(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(3)/2)), x), x, -S(2)/(b*f*sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(3)/2)) - S(8)*(b*tan(e + f*x))**(S(3)/2)/(S(3)*b**S(3)*f*(d*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((b*tan(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(5)/2)), x), x, -S(2)/(b*f*sqrt(b*tan(e + f*x))*(d*sec(e + f*x))**(S(5)/2)) - S(24)*sqrt(b*tan(e + f*x))*EllipticE(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))/(S(5)*b**S(2)*d**S(2)*f*sqrt(d*sec(e + f*x))*sqrt(sin(e + f*x))) - S(12)*(b*tan(e + f*x))**(S(3)/2)/(S(5)*b**S(3)*f*(d*sec(e + f*x))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(7)/2)/(b*tan(e + f*x))**(S(5)/2), x), x, -S(2)*d**S(2)*(d*sec(e + f*x))**(S(3)/2)/(S(3)*b*f*(b*tan(e + f*x))**(S(3)/2)) + d**S(3)*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))*ArcTan(sqrt(b*sin(e + f*x))/sqrt(b))/(b**(S(5)/2)*f*sqrt(b*tan(e + f*x))) + d**S(3)*sqrt(b*sin(e + f*x))*sqrt(d*sec(e + f*x))*atanh(sqrt(b*sin(e + f*x))/sqrt(b))/(b**(S(5)/2)*f*sqrt(b*tan(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(5)/2)/(b*tan(e + f*x))**(S(5)/2), x), x, -S(2)*d**S(2)*sqrt(d*sec(e + f*x))/(S(3)*b*f*(b*tan(e + f*x))**(S(3)/2)) + S(2)*d**S(2)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*b**S(2)*f*sqrt(b*tan(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*sec(e + f*x))**(S(3)/2)/(b*tan(e + f*x))**(S(5)/2), x), x, -S(2)*(d*sec(e + f*x))**(S(3)/2)/(S(3)*b*f*(b*tan(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*sec(e + f*x))/(b*tan(e + f*x))**(S(5)/2), x), x, -S(2)*sqrt(d*sec(e + f*x))/(S(3)*b*f*(b*tan(e + f*x))**(S(3)/2)) - S(4)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*b**S(2)*f*sqrt(b*tan(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((b*tan(e + f*x))**(S(5)/2)*sqrt(d*sec(e + f*x))), x), x, -S(2)/(S(3)*b*f*(b*tan(e + f*x))**(S(3)/2)*sqrt(d*sec(e + f*x))) - S(8)*sqrt(b*tan(e + f*x))/(S(3)*b**S(3)*f*sqrt(d*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((b*tan(e + f*x))**(S(5)/2)*(d*sec(e + f*x))**(S(3)/2)), x), x, -S(2)/(S(3)*b*f*(b*tan(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(3)/2)) - S(8)*sqrt(d*sec(e + f*x))*EllipticF(-Pi/S(4) + e/S(2) + f*x/S(2), S(2))*sqrt(sin(e + f*x))/(S(3)*b**S(2)*d**S(2)*f*sqrt(b*tan(e + f*x))) - S(4)*sqrt(b*tan(e + f*x))/(S(3)*b**S(3)*f*(d*sec(e + f*x))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((b*tan(e + f*x))**(S(5)/2)*(d*sec(e + f*x))**(S(5)/2)), x), x, -S(2)/(S(3)*b*f*(b*tan(e + f*x))**(S(3)/2)*(d*sec(e + f*x))**(S(5)/2)) - S(16)*sqrt(b*tan(e + f*x))/(S(15)*b**S(3)*f*(d*sec(e + f*x))**(S(5)/2)) - S(64)*sqrt(b*tan(e + f*x))/(S(15)*b**S(3)*d**S(2)*f*sqrt(d*sec(e + f*x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(4)/3)*sqrt(d*tan(e + f*x)), x), x, S(2)*(b*sec(e + f*x))**(S(4)/3)*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(17)/12)*Hypergeometric2F1(S(3)/4, S(17)/12, S(7)/4, sin(e + f*x)**S(2))/(S(3)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(1)/3)*sqrt(d*tan(e + f*x)), x), x, S(2)*(b*sec(e + f*x))**(S(1)/3)*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(11)/12)*Hypergeometric2F1(S(3)/4, S(11)/12, S(7)/4, sin(e + f*x)**S(2))/(S(3)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))/(b*sec(e + f*x))**(S(1)/3), x), x, S(2)*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(7)/12)*Hypergeometric2F1(S(7)/12, S(3)/4, S(7)/4, sin(e + f*x)**S(2))/(S(3)*d*f*(b*sec(e + f*x))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(d*tan(e + f*x))/(b*sec(e + f*x))**(S(4)/3), x), x, S(2)*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(1)/12)*Hypergeometric2F1(S(1)/12, S(3)/4, S(7)/4, sin(e + f*x)**S(2))/(S(3)*d*f*(b*sec(e + f*x))**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(4)/3)*(d*tan(e + f*x))**(S(3)/2), x), x, S(2)*(b*sec(e + f*x))**(S(4)/3)*(d*tan(e + f*x))**(S(5)/2)*(cos(e + f*x)**S(2))**(S(23)/12)*Hypergeometric2F1(S(5)/4, S(23)/12, S(9)/4, sin(e + f*x)**S(2))/(S(5)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(1)/3)*(d*tan(e + f*x))**(S(3)/2), x), x, S(2)*(b*sec(e + f*x))**(S(1)/3)*(d*tan(e + f*x))**(S(5)/2)*(cos(e + f*x)**S(2))**(S(17)/12)*Hypergeometric2F1(S(5)/4, S(17)/12, S(9)/4, sin(e + f*x)**S(2))/(S(5)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(3)/2)/(b*sec(e + f*x))**(S(1)/3), x), x, S(2)*(d*tan(e + f*x))**(S(5)/2)*(cos(e + f*x)**S(2))**(S(13)/12)*Hypergeometric2F1(S(13)/12, S(5)/4, S(9)/4, sin(e + f*x)**S(2))/(S(5)*d*f*(b*sec(e + f*x))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(e + f*x))**(S(3)/2)/(b*sec(e + f*x))**(S(4)/3), x), x, S(2)*(d*tan(e + f*x))**(S(5)/2)*(cos(e + f*x)**S(2))**(S(7)/12)*Hypergeometric2F1(S(7)/12, S(5)/4, S(9)/4, sin(e + f*x)**S(2))/(S(5)*d*f*(b*sec(e + f*x))**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*(d*tan(e + f*x))**(S(4)/3), x), x, S(3)*sqrt(b*sec(e + f*x))*(d*tan(e + f*x))**(S(7)/3)*(cos(e + f*x)**S(2))**(S(17)/12)*Hypergeometric2F1(S(7)/6, S(17)/12, S(13)/6, sin(e + f*x)**S(2))/(S(7)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))*(d*tan(e + f*x))**(S(1)/3), x), x, S(3)*sqrt(b*sec(e + f*x))*(d*tan(e + f*x))**(S(4)/3)*(cos(e + f*x)**S(2))**(S(11)/12)*Hypergeometric2F1(S(2)/3, S(11)/12, S(5)/3, sin(e + f*x)**S(2))/(S(4)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))/(d*tan(e + f*x))**(S(1)/3), x), x, S(3)*sqrt(b*sec(e + f*x))*(d*tan(e + f*x))**(S(2)/3)*(cos(e + f*x)**S(2))**(S(7)/12)*Hypergeometric2F1(S(1)/3, S(7)/12, S(4)/3, sin(e + f*x)**S(2))/(S(2)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(b*sec(e + f*x))/(d*tan(e + f*x))**(S(4)/3), x), x, -S(3)*sqrt(b*sec(e + f*x))*(cos(e + f*x)**S(2))**(S(1)/12)*Hypergeometric2F1(S(-1)/6, S(1)/12, S(5)/6, sin(e + f*x)**S(2))/(d*f*(d*tan(e + f*x))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(4)/3), x), x, S(3)*(b*sec(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(7)/3)*(cos(e + f*x)**S(2))**(S(23)/12)*Hypergeometric2F1(S(7)/6, S(23)/12, S(13)/6, sin(e + f*x)**S(2))/(S(7)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(1)/3), x), x, S(3)*(b*sec(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(4)/3)*(cos(e + f*x)**S(2))**(S(17)/12)*Hypergeometric2F1(S(2)/3, S(17)/12, S(5)/3, sin(e + f*x)**S(2))/(S(4)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)/(d*tan(e + f*x))**(S(1)/3), x), x, S(3)*(b*sec(e + f*x))**(S(3)/2)*(d*tan(e + f*x))**(S(2)/3)*(cos(e + f*x)**S(2))**(S(13)/12)*Hypergeometric2F1(S(1)/3, S(13)/12, S(4)/3, sin(e + f*x)**S(2))/(S(2)*d*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**(S(3)/2)/(d*tan(e + f*x))**(S(4)/3), x), x, -S(3)*(b*sec(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(7)/12)*Hypergeometric2F1(S(-1)/6, S(7)/12, S(5)/6, sin(e + f*x)**S(2))/(d*f*(d*tan(e + f*x))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*tan(e + f*x)**S(5), x), x, (b*sec(e + f*x))**m/(f*m) - S(2)*(b*sec(e + f*x))**(m + S(2))/(b**S(2)*f*(m + S(2))) + (b*sec(e + f*x))**(m + S(4))/(b**S(4)*f*(m + S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*tan(e + f*x)**S(3), x), x, -(b*sec(e + f*x))**m/(f*m) + (b*sec(e + f*x))**(m + S(2))/(b**S(2)*f*(m + S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*tan(e + f*x), x), x, (b*sec(e + f*x))**m/(f*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*cot(e + f*x), x), x, -(b*sec(e + f*x))**m*Hypergeometric2F1(S(1), m/S(2), m/S(2) + S(1), sec(e + f*x)**S(2))/(f*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*cot(e + f*x)**S(3), x), x, (b*sec(e + f*x))**m*Hypergeometric2F1(S(2), m/S(2), m/S(2) + S(1), sec(e + f*x)**S(2))/(f*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*cot(e + f*x)**S(5), x), x, -(b*sec(e + f*x))**m*Hypergeometric2F1(S(3), m/S(2), m/S(2) + S(1), sec(e + f*x)**S(2))/(f*m), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*tan(e + f*x)**S(4), x), x, (b*sec(e + f*x))**m*(cos(e + f*x)**S(2))**(m/S(2) + S(5)/2)*Hypergeometric2F1(S(5)/2, m/S(2) + S(5)/2, S(7)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(5)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*tan(e + f*x)**S(2), x), x, (b*sec(e + f*x))**m*(cos(e + f*x)**S(2))**(m/S(2) + S(3)/2)*Hypergeometric2F1(S(3)/2, m/S(2) + S(3)/2, S(5)/2, sin(e + f*x)**S(2))*tan(e + f*x)**S(3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*cot(e + f*x)**S(2), x), x, -(b*sec(e + f*x))**m*(cos(e + f*x)**S(2))**(m/S(2) + S(-1)/2)*Hypergeometric2F1(S(-1)/2, m/S(2) + S(-1)/2, S(1)/2, sin(e + f*x)**S(2))*cot(e + f*x)/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*cot(e + f*x)**S(4), x), x, -(b*sec(e + f*x))**m*(cos(e + f*x)**S(2))**(m/S(2) + S(-3)/2)*Hypergeometric2F1(S(-3)/2, m/S(2) + S(-3)/2, S(-1)/2, sin(e + f*x)**S(2))*cot(e + f*x)**S(3)/(S(3)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*sec(e + f*x))**m*cot(e + f*x)**S(6), x), x, -(b*sec(e + f*x))**m*(cos(e + f*x)**S(2))**(m/S(2) + S(-5)/2)*Hypergeometric2F1(S(-5)/2, m/S(2) + S(-5)/2, S(-3)/2, sin(e + f*x)**S(2))*cot(e + f*x)**S(5)/(S(5)*f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*sec(e + f*x))**m*(b*tan(e + f*x))**n, x), x, (a*sec(e + f*x))**m*(b*tan(e + f*x))**(n + S(1))*(cos(e + f*x)**S(2))**(m/S(2) + n/S(2) + S(1)/2)*Hypergeometric2F1(n/S(2) + S(1)/2, m/S(2) + n/S(2) + S(1)/2, n/S(2) + S(3)/2, sin(e + f*x)**S(2))/(b*f*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*sec(a + b*x)**S(6), x), x, (d*tan(a + b*x))**(n + S(1))/(b*d*(n + S(1))) + S(2)*(d*tan(a + b*x))**(n + S(3))/(b*d**S(3)*(n + S(3))) + (d*tan(a + b*x))**(n + S(5))/(b*d**S(5)*(n + S(5))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*sec(a + b*x)**S(4), x), x, (d*tan(a + b*x))**(n + S(1))/(b*d*(n + S(1))) + (d*tan(a + b*x))**(n + S(3))/(b*d**S(3)*(n + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*sec(a + b*x)**S(2), x), x, (d*tan(a + b*x))**(n + S(1))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n, x), x, (d*tan(a + b*x))**(n + S(1))*Hypergeometric2F1(S(1), n/S(2) + S(1)/2, n/S(2) + S(3)/2, -tan(a + b*x)**S(2))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*cos(a + b*x)**S(2), x), x, (d*tan(a + b*x))**(n + S(1))*Hypergeometric2F1(S(2), n/S(2) + S(1)/2, n/S(2) + S(3)/2, -tan(a + b*x)**S(2))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*cos(a + b*x)**S(4), x), x, (d*tan(a + b*x))**(n + S(1))*Hypergeometric2F1(S(3), n/S(2) + S(1)/2, n/S(2) + S(3)/2, -tan(a + b*x)**S(2))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*sec(a + b*x)**S(5), x), x, (d*tan(a + b*x))**(n + S(1))*(cos(a + b*x)**S(2))**(n/S(2) + S(3))*Hypergeometric2F1(n/S(2) + S(1)/2, n/S(2) + S(3), n/S(2) + S(3)/2, sin(a + b*x)**S(2))*sec(a + b*x)**S(5)/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*sec(a + b*x)**S(3), x), x, (d*tan(a + b*x))**(n + S(1))*(cos(a + b*x)**S(2))**(n/S(2) + S(2))*Hypergeometric2F1(n/S(2) + S(1)/2, n/S(2) + S(2), n/S(2) + S(3)/2, sin(a + b*x)**S(2))*sec(a + b*x)**S(3)/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*sec(a + b*x), x), x, (d*tan(a + b*x))**(n + S(1))*(cos(a + b*x)**S(2))**(n/S(2) + S(1))*Hypergeometric2F1(n/S(2) + S(1)/2, n/S(2) + S(1), n/S(2) + S(3)/2, sin(a + b*x)**S(2))*sec(a + b*x)/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*cos(a + b*x), x), x, (d*tan(a + b*x))**(n + S(1))*(cos(a + b*x)**S(2))**(n/S(2))*Hypergeometric2F1(n/S(2), n/S(2) + S(1)/2, n/S(2) + S(3)/2, sin(a + b*x)**S(2))*cos(a + b*x)/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d*tan(a + b*x))**n*cos(a + b*x)**S(3), x), x, (d*tan(a + b*x))**(n + S(1))*(cos(a + b*x)**S(2))**(n/S(2) + S(-1))*Hypergeometric2F1(n/S(2) + S(-1), n/S(2) + S(1)/2, n/S(2) + S(3)/2, sin(a + b*x)**S(2))*cos(a + b*x)**S(3)/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate((b*csc(e + f*x))**m*tan(e + f*x)**S(3), x), x, -(b*csc(e + f*x))**m*Hypergeometric2F1(S(2), m/S(2), m/S(2) + S(1), csc(e + f*x)**S(2))/(f*m), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((b*csc(e + f*x))**m*(d*tan(e + f*x))**(S(3)/2), x), x, (b*csc(e + f*x))**m*(d*tan(e + f*x))**(S(5)/2)*(cos(e + f*x)**S(2))**(S(5)/4)*Hypergeometric2F1(S(5)/4, -m/S(2) + S(5)/4, -m/S(2) + S(9)/4, sin(e + f*x)**S(2))/(d*f*(-m + S(5)/2)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((b*csc(e + f*x))**m*(d*tan(e + f*x))**(S(3)/2), x), x, S(2)*d*(b*csc(e + f*x))**m*sqrt(d*tan(e + f*x))*(cos(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(5)/4, -m/S(2) + S(5)/4, -m/S(2) + S(9)/4, sin(e + f*x)**S(2))*sin(e + f*x)**S(2)/(f*(-S(2)*m + S(5))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*csc(e + f*x))**m*sqrt(d*tan(e + f*x)), x), x, S(2)*(b*csc(e + f*x))**m*(d*tan(e + f*x))**(S(3)/2)*(cos(e + f*x)**S(2))**(S(3)/4)*Hypergeometric2F1(S(3)/4, -m/S(2) + S(3)/4, -m/S(2) + S(7)/4, sin(e + f*x)**S(2))/(d*f*(-S(2)*m + S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*csc(e + f*x))**m/sqrt(d*tan(e + f*x)), x), x, S(2)*(b*csc(e + f*x))**m*sqrt(d*tan(e + f*x))*(cos(e + f*x)**S(2))**(S(1)/4)*Hypergeometric2F1(S(1)/4, -m/S(2) + S(1)/4, -m/S(2) + S(5)/4, sin(e + f*x)**S(2))/(d*f*(-S(2)*m + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b*csc(e + f*x))**m/(d*tan(e + f*x))**(S(3)/2), x), x, -S(2)*(b*csc(e + f*x))**m*Hypergeometric2F1(S(-1)/4, -m/S(2) + S(-1)/4, -m/S(2) + S(3)/4, sin(e + f*x)**S(2))/(d*f*sqrt(d*tan(e + f*x))*(S(2)*m + S(1))*(cos(e + f*x)**S(2))**(S(1)/4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*csc(e + f*x))**m*(b*tan(e + f*x))**n, x), x, (a*csc(e + f*x))**m*(b*tan(e + f*x))**(n + S(1))*(cos(e + f*x)**S(2))**(n/S(2) + S(1)/2)*Hypergeometric2F1(n/S(2) + S(1)/2, -m/S(2) + n/S(2) + S(1)/2, -m/S(2) + n/S(2) + S(3)/2, sin(e + f*x)**S(2))/(b*f*(-m + n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((-tan(e + f*x)**S(2))**n*sin(e + f*x), x), x, -Hypergeometric2F1(S(-1)/2, -n, S(1)/2, sec(e + f*x)**S(2))*cos(e + f*x)/f, expand=True, _diff=True, _numerical=True)
