import sys
from sympy.external import import_module
matchpy = import_module("matchpy")

if not matchpy:
    #bin/test will not execute any tests now
    disabled = True

if sys.version_info[:2] < (3, 6):
    disabled = True

from sympy.integrals.rubi.rubimain import rubi_integrate
from sympy.functions import log, sqrt, exp, cos, sin, tan, sec, csc, cot, sinh, sech, atan, asin, acos, atanh, asinh, acosh
from sympy.functions.elementary.hyperbolic import acsch as arccsch
from sympy.functions.elementary.trigonometric import acsc as arccsc

from sympy.integrals.rubi.utility_function import (EllipticE, EllipticF, Int, ArcCsch, ArcCsc, Gamma,
    hypergeom, rubi_test, AppellF1, EllipticPi, Log, Sqrt, ArcTan, ArcTanh, ArcSin, ArcSinh, ArcCosh, ArcTanh, ArcCos, Hypergeometric2F1)
from sympy.core.numbers import (I, pi)
from sympy.core.singleton import S
from sympy.core.symbol import symbols
from sympy.functions.elementary.exponential import (exp, exp_polar)
from sympy.functions.special.error_functions import (Ei, erf, erfi)
from sympy.functions.special.gamma_functions import (gamma, uppergamma)
from sympy.functions.special.hyper import hyper
from sympy.functions.special.zeta_functions import polylog
from sympy.integrals.integrals import Integral
from sympy.simplify.simplify import simplify
from sympy.testing.pytest import SKIP

a, b, c, d, e, f, m, n, x, u , k, p, r, s, t= symbols('a b c d e f m n x u k p r s t')
A, B, C, D, a, b, c, d, e, f, g, h, i, y, z, m, n, p, q, u, v, w, E, F, G, H = symbols('A B C D a b c d e f g h i y z m n p q u v w E F G H')


def test_1():
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**m, x), x, F**(c*(a - b*d/e))*(-b*c*(d + e*x)*log(F)/e)**(-m)*(d + e*x)**m*Gamma(m + S(1), -b*c*(d + e*x)*log(F)/e)/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**S(4), x), x, F**(c*(a + b*x))*(d + e*x)**S(4)/(b*c*log(F)) - S(4)*F**(c*(a + b*x))*e*(d + e*x)**S(3)/(b**S(2)*c**S(2)*log(F)**S(2)) + S(12)*F**(c*(a + b*x))*e**S(2)*(d + e*x)**S(2)/(b**S(3)*c**S(3)*log(F)**S(3)) - S(24)*F**(c*(a + b*x))*e**S(3)*(d + e*x)/(b**S(4)*c**S(4)*log(F)**S(4)) + S(24)*F**(c*(a + b*x))*e**S(4)/(b**S(5)*c**S(5)*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**S(3), x), x, F**(c*(a + b*x))*(d + e*x)**S(3)/(b*c*log(F)) - S(3)*F**(c*(a + b*x))*e*(d + e*x)**S(2)/(b**S(2)*c**S(2)*log(F)**S(2)) + S(6)*F**(c*(a + b*x))*e**S(2)*(d + e*x)/(b**S(3)*c**S(3)*log(F)**S(3)) - S(6)*F**(c*(a + b*x))*e**S(3)/(b**S(4)*c**S(4)*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**S(2), x), x, F**(c*(a + b*x))*(d + e*x)**S(2)/(b*c*log(F)) - S(2)*F**(c*(a + b*x))*e*(d + e*x)/(b**S(2)*c**S(2)*log(F)**S(2)) + S(2)*F**(c*(a + b*x))*e**S(2)/(b**S(3)*c**S(3)*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x), x), x, F**(c*(a + b*x))*(d + e*x)/(b*c*log(F)) - F**(c*(a + b*x))*e/(b**S(2)*c**S(2)*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x)), x), x, F**(c*(a + b*x))/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d + e*x), x), x, F**(c*(a - b*d/e))*Ei(b*c*(d + e*x)*log(F)/e)/e, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d + e*x)**S(2), x), x, -F**(c*(a + b*x))/(e*(d + e*x)) + F**(c*(a - b*d/e))*b*c*log(F)*Ei(b*c*(d + e*x)*log(F)/e)/e**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d + e*x)**S(3), x), x, -F**(c*(a + b*x))*b*c*log(F)/(S(2)*e**S(2)*(d + e*x)) - F**(c*(a + b*x))/(S(2)*e*(d + e*x)**S(2)) + F**(c*(a - b*d/e))*b**S(2)*c**S(2)*log(F)**S(2)*Ei(b*c*(d + e*x)*log(F)/e)/(S(2)*e**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d + e*x)**S(4), x), x, -F**(c*(a + b*x))*b**S(2)*c**S(2)*log(F)**S(2)/(S(6)*e**S(3)*(d + e*x)) - F**(c*(a + b*x))*b*c*log(F)/(S(6)*e**S(2)*(d + e*x)**S(2)) - F**(c*(a + b*x))/(S(3)*e*(d + e*x)**S(3)) + F**(c*(a - b*d/e))*b**S(3)*c**S(3)*log(F)**S(3)*Ei(b*c*(d + e*x)*log(F)/e)/(S(6)*e**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d + e*x)**S(5), x), x, -F**(c*(a + b*x))*b**S(3)*c**S(3)*log(F)**S(3)/(S(24)*e**S(4)*(d + e*x)) - F**(c*(a + b*x))*b**S(2)*c**S(2)*log(F)**S(2)/(S(24)*e**S(3)*(d + e*x)**S(2)) - F**(c*(a + b*x))*b*c*log(F)/(S(12)*e**S(2)*(d + e*x)**S(3)) - F**(c*(a + b*x))/(S(4)*e*(d + e*x)**S(4)) + F**(c*(a - b*d/e))*b**S(4)*c**S(4)*log(F)**S(4)*Ei(b*c*(d + e*x)*log(F)/e)/(S(24)*e**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d**S(4) + S(4)*d**S(3)*e*x + S(6)*d**S(2)*e**S(2)*x**S(2) + S(4)*d*e**S(3)*x**S(3) + e**S(4)*x**S(4)), x), x, F**(c*(a + b*x))*(d + e*x)**S(4)/(b*c*log(F)) - S(4)*F**(c*(a + b*x))*e*(d + e*x)**S(3)/(b**S(2)*c**S(2)*log(F)**S(2)) + S(12)*F**(c*(a + b*x))*e**S(2)*(d + e*x)**S(2)/(b**S(3)*c**S(3)*log(F)**S(3)) - S(24)*F**(c*(a + b*x))*e**S(3)*(d + e*x)/(b**S(4)*c**S(4)*log(F)**S(4)) + S(24)*F**(c*(a + b*x))*e**S(4)/(b**S(5)*c**S(5)*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d**S(3) + S(3)*d**S(2)*e*x + S(3)*d*e**S(2)*x**S(2) + e**S(3)*x**S(3)), x), x, F**(c*(a + b*x))*(d + e*x)**S(3)/(b*c*log(F)) - S(3)*F**(c*(a + b*x))*e*(d + e*x)**S(2)/(b**S(2)*c**S(2)*log(F)**S(2)) + S(6)*F**(c*(a + b*x))*e**S(2)*(d + e*x)/(b**S(3)*c**S(3)*log(F)**S(3)) - S(6)*F**(c*(a + b*x))*e**S(3)/(b**S(4)*c**S(4)*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d**S(2) + S(2)*d*e*x + e**S(2)*x**S(2)), x), x, F**(c*(a + b*x))*(d + e*x)**S(2)/(b*c*log(F)) - S(2)*F**(c*(a + b*x))*e*(d + e*x)/(b**S(2)*c**S(2)*log(F)**S(2)) + S(2)*F**(c*(a + b*x))*e**S(2)/(b**S(3)*c**S(3)*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d**S(2) + S(2)*d*e*x + e**S(2)*x**S(2)), x), x, -F**(c*(a + b*x))/(e*(d + e*x)) + F**(c*(a - b*d/e))*b*c*log(F)*Ei(b*c*(d + e*x)*log(F)/e)/e**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d**S(3) + S(3)*d**S(2)*e*x + S(3)*d*e**S(2)*x**S(2) + e**S(3)*x**S(3)), x), x, -F**(c*(a + b*x))*b*c*log(F)/(S(2)*e**S(2)*(d + e*x)) - F**(c*(a + b*x))/(S(2)*e*(d + e*x)**S(2)) + F**(c*(a - b*d/e))*b**S(2)*c**S(2)*log(F)**S(2)*Ei(b*c*(d + e*x)*log(F)/e)/(S(2)*e**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d**S(4) + S(4)*d**S(3)*e*x + S(6)*d**S(2)*e**S(2)*x**S(2) + S(4)*d*e**S(3)*x**S(3) + e**S(4)*x**S(4)), x), x, -F**(c*(a + b*x))*b**S(2)*c**S(2)*log(F)**S(2)/(S(6)*e**S(3)*(d + e*x)) - F**(c*(a + b*x))*b*c*log(F)/(S(6)*e**S(2)*(d + e*x)**S(2)) - F**(c*(a + b*x))/(S(3)*e*(d + e*x)**S(3)) + F**(c*(a - b*d/e))*b**S(3)*c**S(3)*log(F)**S(3)*Ei(b*c*(d + e*x)*log(F)/e)/(S(6)*e**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d**S(5) + S(5)*d**S(4)*e*x + S(10)*d**S(3)*e**S(2)*x**S(2) + S(10)*d**S(2)*e**S(3)*x**S(3) + S(5)*d*e**S(4)*x**S(4) + e**S(5)*x**S(5)), x), x, -F**(c*(a + b*x))*b**S(3)*c**S(3)*log(F)**S(3)/(S(24)*e**S(4)*(d + e*x)) - F**(c*(a + b*x))*b**S(2)*c**S(2)*log(F)**S(2)/(S(24)*e**S(3)*(d + e*x)**S(2)) - F**(c*(a + b*x))*b*c*log(F)/(S(12)*e**S(2)*(d + e*x)**S(3)) - F**(c*(a + b*x))/(S(4)*e*(d + e*x)**S(4)) + F**(c*(a - b*d/e))*b**S(4)*c**S(4)*log(F)**S(4)*Ei(b*c*(d + e*x)*log(F)/e)/(S(24)*e**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*((d + e*x)**n)**m, x), x, F**(c*(a - b*d/e))*(-b*c*(d + e*x)*log(F)/e)**(-m*n)*((d + e*x)**n)**m*Gamma(m*n + S(1), -b*c*(d + e*x)*log(F)/e)/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d**S(4) + S(4)*d**S(3)*e*x + S(6)*d**S(2)*e**S(2)*x**S(2) + S(4)*d*e**S(3)*x**S(3) + e**S(4)*x**S(4))**m, x), x, F**(c*(a - b*d/e))*(-b*c*(d + e*x)*log(F)/e)**(-S(4)*m)*((d + e*x)**S(4))**m*Gamma(S(4)*m + S(1), -b*c*(d + e*x)*log(F)/e)/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d**S(3) + S(3)*d**S(2)*e*x + S(3)*d*e**S(2)*x**S(2) + e**S(3)*x**S(3))**m, x), x, F**(c*(a - b*d/e))*(-b*c*(d + e*x)*log(F)/e)**(-S(3)*m)*((d + e*x)**S(3))**m*Gamma(S(3)*m + S(1), -b*c*(d + e*x)*log(F)/e)/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d**S(2) + S(2)*d*e*x + e**S(2)*x**S(2))**m, x), x, F**(c*(a - b*d/e))*(-b*c*(d + e*x)*log(F)/e)**(-S(2)*m)*((d + e*x)**S(2))**m*Gamma(S(2)*m + S(1), -b*c*(d + e*x)*log(F)/e)/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**m, x), x, F**(c*(a - b*d/e))*(-b*c*(d + e*x)*log(F)/e)**(-m)*(d + e*x)**m*Gamma(m + S(1), -b*c*(d + e*x)*log(F)/e)/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**(-m), x), x, F**(c*(a - b*d/e))*(-b*c*(d + e*x)*log(F)/e)**m*(d + e*x)**(-m)*Gamma(-m + S(1), -b*c*(d + e*x)*log(F)/e)/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d**S(2) + S(2)*d*e*x + e**S(2)*x**S(2))**(-m), x), x, F**(c*(a - b*d/e))*(-b*c*(d + e*x)*log(F)/e)**(S(2)*m)*((d + e*x)**S(2))**(-m)*Gamma(-S(2)*m + S(1), -b*c*(d + e*x)*log(F)/e)/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d**S(3) + S(3)*d**S(2)*e*x + S(3)*d*e**S(2)*x**S(2) + e**S(3)*x**S(3))**(-m), x), x, F**(c*(a - b*d/e))*(-b*c*(d + e*x)*log(F)/e)**(S(3)*m)*((d + e*x)**S(3))**(-m)*Gamma(-S(3)*m + S(1), -b*c*(d + e*x)*log(F)/e)/(b*c*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(S(5)*x + S(2)), x), x, F**(S(5)*x + S(2))/(S(5)*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x), x), x, F**(a + b*x)/(b*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(10)**(S(5)*x + S(2)), x), x, S(2)**(S(5)*x + S(2))*S(5)**(S(5)*x + S(1))/log(S(10)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x)*x**(S(7)/2), x), x, S(105)*sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(x)*sqrt(log(F)))/(S(16)*b**(S(9)/2)*log(F)**(S(9)/2)) + F**(a + b*x)*x**(S(7)/2)/(b*log(F)) - S(7)*F**(a + b*x)*x**(S(5)/2)/(S(2)*b**S(2)*log(F)**S(2)) + S(35)*F**(a + b*x)*x**(S(3)/2)/(S(4)*b**S(3)*log(F)**S(3)) - S(105)*F**(a + b*x)*sqrt(x)/(S(8)*b**S(4)*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x)*x**(S(5)/2), x), x, -S(15)*sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(x)*sqrt(log(F)))/(S(8)*b**(S(7)/2)*log(F)**(S(7)/2)) + F**(a + b*x)*x**(S(5)/2)/(b*log(F)) - S(5)*F**(a + b*x)*x**(S(3)/2)/(S(2)*b**S(2)*log(F)**S(2)) + S(15)*F**(a + b*x)*sqrt(x)/(S(4)*b**S(3)*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x)*x**(S(3)/2), x), x, S(3)*sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(x)*sqrt(log(F)))/(S(4)*b**(S(5)/2)*log(F)**(S(5)/2)) + F**(a + b*x)*x**(S(3)/2)/(b*log(F)) - S(3)*F**(a + b*x)*sqrt(x)/(S(2)*b**S(2)*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x)*sqrt(x), x), x, -sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(x)*sqrt(log(F)))/(S(2)*b**(S(3)/2)*log(F)**(S(3)/2)) + F**(a + b*x)*sqrt(x)/(b*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x)/sqrt(x), x), x, sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(x)*sqrt(log(F)))/(sqrt(b)*sqrt(log(F))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x)/x**(S(3)/2), x), x, S(2)*sqrt(pi)*F**a*sqrt(b)*sqrt(log(F))*erfi(sqrt(b)*sqrt(x)*sqrt(log(F))) - S(2)*F**(a + b*x)/sqrt(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x)/x**(S(5)/2), x), x, S(4)*sqrt(pi)*F**a*b**(S(3)/2)*log(F)**(S(3)/2)*erfi(sqrt(b)*sqrt(x)*sqrt(log(F)))/S(3) - S(4)*F**(a + b*x)*b*log(F)/(S(3)*sqrt(x)) - S(2)*F**(a + b*x)/(S(3)*x**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x)/x**(S(7)/2), x), x, S(8)*sqrt(pi)*F**a*b**(S(5)/2)*log(F)**(S(5)/2)*erfi(sqrt(b)*sqrt(x)*sqrt(log(F)))/S(15) - S(8)*F**(a + b*x)*b**S(2)*log(F)**S(2)/(S(15)*sqrt(x)) - S(4)*F**(a + b*x)*b*log(F)/(S(15)*x**(S(3)/2)) - S(2)*F**(a + b*x)/(S(5)*x**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x)/x**(S(9)/2), x), x, S(16)*sqrt(pi)*F**a*b**(S(7)/2)*log(F)**(S(7)/2)*erfi(sqrt(b)*sqrt(x)*sqrt(log(F)))/S(105) - S(16)*F**(a + b*x)*b**S(3)*log(F)**S(3)/(S(105)*sqrt(x)) - S(8)*F**(a + b*x)*b**S(2)*log(F)**S(2)/(S(105)*x**(S(3)/2)) - S(4)*F**(a + b*x)*b*log(F)/(S(35)*x**(S(5)/2)) - S(2)*F**(a + b*x)/(S(7)*x**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**(S(7)/2), x), x, F**(c*(a + b*x))*(d + e*x)**(S(7)/2)/(b*c*log(F)) - S(7)*F**(c*(a + b*x))*e*(d + e*x)**(S(5)/2)/(S(2)*b**S(2)*c**S(2)*log(F)**S(2)) + S(35)*F**(c*(a + b*x))*e**S(2)*(d + e*x)**(S(3)/2)/(S(4)*b**S(3)*c**S(3)*log(F)**S(3)) - S(105)*F**(c*(a + b*x))*e**S(3)*sqrt(d + e*x)/(S(8)*b**S(4)*c**S(4)*log(F)**S(4)) + S(105)*sqrt(pi)*F**(c*(a - b*d/e))*e**(S(7)/2)*erfi(sqrt(b)*sqrt(c)*sqrt(d + e*x)*sqrt(log(F))/sqrt(e))/(S(16)*b**(S(9)/2)*c**(S(9)/2)*log(F)**(S(9)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**(S(5)/2), x), x, F**(c*(a + b*x))*(d + e*x)**(S(5)/2)/(b*c*log(F)) - S(5)*F**(c*(a + b*x))*e*(d + e*x)**(S(3)/2)/(S(2)*b**S(2)*c**S(2)*log(F)**S(2)) + S(15)*F**(c*(a + b*x))*e**S(2)*sqrt(d + e*x)/(S(4)*b**S(3)*c**S(3)*log(F)**S(3)) - S(15)*sqrt(pi)*F**(c*(a - b*d/e))*e**(S(5)/2)*erfi(sqrt(b)*sqrt(c)*sqrt(d + e*x)*sqrt(log(F))/sqrt(e))/(S(8)*b**(S(7)/2)*c**(S(7)/2)*log(F)**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**(S(3)/2), x), x, F**(c*(a + b*x))*(d + e*x)**(S(3)/2)/(b*c*log(F)) - S(3)*F**(c*(a + b*x))*e*sqrt(d + e*x)/(S(2)*b**S(2)*c**S(2)*log(F)**S(2)) + S(3)*sqrt(pi)*F**(c*(a - b*d/e))*e**(S(3)/2)*erfi(sqrt(b)*sqrt(c)*sqrt(d + e*x)*sqrt(log(F))/sqrt(e))/(S(4)*b**(S(5)/2)*c**(S(5)/2)*log(F)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*sqrt(d + e*x), x), x, F**(c*(a + b*x))*sqrt(d + e*x)/(b*c*log(F)) - sqrt(pi)*F**(c*(a - b*d/e))*sqrt(e)*erfi(sqrt(b)*sqrt(c)*sqrt(d + e*x)*sqrt(log(F))/sqrt(e))/(S(2)*b**(S(3)/2)*c**(S(3)/2)*log(F)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/sqrt(d + e*x), x), x, sqrt(pi)*F**(c*(a - b*d/e))*erfi(sqrt(b)*sqrt(c)*sqrt(d + e*x)*sqrt(log(F))/sqrt(e))/(sqrt(b)*sqrt(c)*sqrt(e)*sqrt(log(F))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d + e*x)**(S(3)/2), x), x, -S(2)*F**(c*(a + b*x))/(e*sqrt(d + e*x)) + S(2)*sqrt(pi)*F**(c*(a - b*d/e))*sqrt(b)*sqrt(c)*sqrt(log(F))*erfi(sqrt(b)*sqrt(c)*sqrt(d + e*x)*sqrt(log(F))/sqrt(e))/e**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d + e*x)**(S(5)/2), x), x, -S(4)*F**(c*(a + b*x))*b*c*log(F)/(S(3)*e**S(2)*sqrt(d + e*x)) - S(2)*F**(c*(a + b*x))/(S(3)*e*(d + e*x)**(S(3)/2)) + S(4)*sqrt(pi)*F**(c*(a - b*d/e))*b**(S(3)/2)*c**(S(3)/2)*log(F)**(S(3)/2)*erfi(sqrt(b)*sqrt(c)*sqrt(d + e*x)*sqrt(log(F))/sqrt(e))/(S(3)*e**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d + e*x)**(S(7)/2), x), x, -S(8)*F**(c*(a + b*x))*b**S(2)*c**S(2)*log(F)**S(2)/(S(15)*e**S(3)*sqrt(d + e*x)) - S(4)*F**(c*(a + b*x))*b*c*log(F)/(S(15)*e**S(2)*(d + e*x)**(S(3)/2)) - S(2)*F**(c*(a + b*x))/(S(5)*e*(d + e*x)**(S(5)/2)) + S(8)*sqrt(pi)*F**(c*(a - b*d/e))*b**(S(5)/2)*c**(S(5)/2)*log(F)**(S(5)/2)*erfi(sqrt(b)*sqrt(c)*sqrt(d + e*x)*sqrt(log(F))/sqrt(e))/(S(15)*e**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))/(d + e*x)**(S(9)/2), x), x, -S(16)*F**(c*(a + b*x))*b**S(3)*c**S(3)*log(F)**S(3)/(S(105)*e**S(4)*sqrt(d + e*x)) - S(8)*F**(c*(a + b*x))*b**S(2)*c**S(2)*log(F)**S(2)/(S(105)*e**S(3)*(d + e*x)**(S(3)/2)) - S(4)*F**(c*(a + b*x))*b*c*log(F)/(S(35)*e**S(2)*(d + e*x)**(S(5)/2)) - S(2)*F**(c*(a + b*x))/(S(7)*e*(d + e*x)**(S(7)/2)) + S(16)*sqrt(pi)*F**(c*(a - b*d/e))*b**(S(7)/2)*c**(S(7)/2)*log(F)**(S(7)/2)*erfi(sqrt(b)*sqrt(c)*sqrt(d + e*x)*sqrt(log(F))/sqrt(e))/(S(105)*e**(S(9)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(S(13)/2)*exp(-b*x), x), x, -x**(S(13)/2)*exp(-b*x)/b - S(13)*x**(S(11)/2)*exp(-b*x)/(S(2)*b**S(2)) - S(143)*x**(S(9)/2)*exp(-b*x)/(S(4)*b**S(3)) - S(1287)*x**(S(7)/2)*exp(-b*x)/(S(8)*b**S(4)) - S(9009)*x**(S(5)/2)*exp(-b*x)/(S(16)*b**S(5)) - S(45045)*x**(S(3)/2)*exp(-b*x)/(S(32)*b**S(6)) - S(135135)*sqrt(x)*exp(-b*x)/(S(64)*b**S(7)) + S(135135)*sqrt(pi)*erf(sqrt(b)*sqrt(x))/(S(128)*b**(S(15)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x)**(S(4)/3), x), x, -F**(c*(a - b*d/e))*e*(d + e*x)**(S(1)/3)*Gamma(S(7)/3, -b*c*(d + e*x)*log(F)/e)/(b**S(2)*c**S(2)*(-b*c*(d + e*x)*log(F)/e)**(S(1)/3)*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*x)**(S(4)/3)*(F**(c*(a + b*x)))**n, x), x, -F**(-c*n*(a + b*x) + c*n*(a - b*d/e))*e*(d + e*x)**(S(1)/3)*(F**(c*(a + b*x)))**n*Gamma(S(7)/3, -b*c*n*(d + e*x)*log(F)/e)/(b**S(2)*c**S(2)*n**S(2)*(-b*c*n*(d + e*x)*log(F)/e)**(S(1)/3)*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x), x), x, F**(c*(a + b*x))*(d + e*x)/(b*c*log(F)) - F**(c*(a + b*x))*e/(b**S(2)*c**S(2)*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x + f*x**S(2)), x), x, F**(c*(a + b*x))*d/(b*c*log(F)) + F**(c*(a + b*x))*e*x/(b*c*log(F)) + F**(c*(a + b*x))*f*x**S(2)/(b*c*log(F)) - F**(c*(a + b*x))*e/(b**S(2)*c**S(2)*log(F)**S(2)) - S(2)*F**(c*(a + b*x))*f*x/(b**S(2)*c**S(2)*log(F)**S(2)) + S(2)*F**(c*(a + b*x))*f/(b**S(3)*c**S(3)*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x + f*x**S(2) + g*x**S(3)), x), x, F**(c*(a + b*x))*d/(b*c*log(F)) + F**(c*(a + b*x))*e*x/(b*c*log(F)) + F**(c*(a + b*x))*f*x**S(2)/(b*c*log(F)) + F**(c*(a + b*x))*g*x**S(3)/(b*c*log(F)) - F**(c*(a + b*x))*e/(b**S(2)*c**S(2)*log(F)**S(2)) - S(2)*F**(c*(a + b*x))*f*x/(b**S(2)*c**S(2)*log(F)**S(2)) - S(3)*F**(c*(a + b*x))*g*x**S(2)/(b**S(2)*c**S(2)*log(F)**S(2)) + S(2)*F**(c*(a + b*x))*f/(b**S(3)*c**S(3)*log(F)**S(3)) + S(6)*F**(c*(a + b*x))*g*x/(b**S(3)*c**S(3)*log(F)**S(3)) - S(6)*F**(c*(a + b*x))*g/(b**S(4)*c**S(4)*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(d + e*x + f*x**S(2) + g*x**S(3) + h*x**S(4)), x), x, F**(c*(a + b*x))*d/(b*c*log(F)) + F**(c*(a + b*x))*e*x/(b*c*log(F)) + F**(c*(a + b*x))*f*x**S(2)/(b*c*log(F)) + F**(c*(a + b*x))*g*x**S(3)/(b*c*log(F)) + F**(c*(a + b*x))*h*x**S(4)/(b*c*log(F)) - F**(c*(a + b*x))*e/(b**S(2)*c**S(2)*log(F)**S(2)) - S(2)*F**(c*(a + b*x))*f*x/(b**S(2)*c**S(2)*log(F)**S(2)) - S(3)*F**(c*(a + b*x))*g*x**S(2)/(b**S(2)*c**S(2)*log(F)**S(2)) - S(4)*F**(c*(a + b*x))*h*x**S(3)/(b**S(2)*c**S(2)*log(F)**S(2)) + S(2)*F**(c*(a + b*x))*f/(b**S(3)*c**S(3)*log(F)**S(3)) + S(6)*F**(c*(a + b*x))*g*x/(b**S(3)*c**S(3)*log(F)**S(3)) + S(12)*F**(c*(a + b*x))*h*x**S(2)/(b**S(3)*c**S(3)*log(F)**S(3)) - S(6)*F**(c*(a + b*x))*g/(b**S(4)*c**S(4)*log(F)**S(4)) - S(24)*F**(c*(a + b*x))*h*x/(b**S(4)*c**S(4)*log(F)**S(4)) + S(24)*F**(c*(a + b*x))*h/(b**S(5)*c**S(5)*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**m*(a + b*x)**S(3)*exp(-a - b*x), x), x, -a**S(3)*x**m*(b*x)**(-m)*Gamma(m + S(1), b*x)*exp(-a)/b - S(3)*a**S(2)*x**m*(b*x)**(-m)*Gamma(m + S(2), b*x)*exp(-a)/b - S(3)*a*x**m*(b*x)**(-m)*Gamma(m + S(3), b*x)*exp(-a)/b - x**m*(b*x)**(-m)*Gamma(m + S(4), b*x)*exp(-a)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*(a + b*x)**S(3)*exp(-a - b*x), x), x, -a**S(3)*x**S(3)*exp(-a - b*x)/b - S(3)*a**S(3)*x**S(2)*exp(-a - b*x)/b**S(2) - S(6)*a**S(3)*x*exp(-a - b*x)/b**S(3) - S(6)*a**S(3)*exp(-a - b*x)/b**S(4) - S(3)*a**S(2)*x**S(4)*exp(-a - b*x) - S(12)*a**S(2)*x**S(3)*exp(-a - b*x)/b - S(36)*a**S(2)*x**S(2)*exp(-a - b*x)/b**S(2) - S(72)*a**S(2)*x*exp(-a - b*x)/b**S(3) - S(72)*a**S(2)*exp(-a - b*x)/b**S(4) - S(3)*a*b*x**S(5)*exp(-a - b*x) - S(15)*a*x**S(4)*exp(-a - b*x) - S(60)*a*x**S(3)*exp(-a - b*x)/b - S(180)*a*x**S(2)*exp(-a - b*x)/b**S(2) - S(360)*a*x*exp(-a - b*x)/b**S(3) - S(360)*a*exp(-a - b*x)/b**S(4) - b**S(2)*x**S(6)*exp(-a - b*x) - S(6)*b*x**S(5)*exp(-a - b*x) - S(30)*x**S(4)*exp(-a - b*x) - S(120)*x**S(3)*exp(-a - b*x)/b - S(360)*x**S(2)*exp(-a - b*x)/b**S(2) - S(720)*x*exp(-a - b*x)/b**S(3) - S(720)*exp(-a - b*x)/b**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(a + b*x)**S(3)*exp(-a - b*x), x), x, -a**S(3)*x**S(2)*exp(-a - b*x)/b - S(2)*a**S(3)*x*exp(-a - b*x)/b**S(2) - S(2)*a**S(3)*exp(-a - b*x)/b**S(3) - S(3)*a**S(2)*x**S(3)*exp(-a - b*x) - S(9)*a**S(2)*x**S(2)*exp(-a - b*x)/b - S(18)*a**S(2)*x*exp(-a - b*x)/b**S(2) - S(18)*a**S(2)*exp(-a - b*x)/b**S(3) - S(3)*a*b*x**S(4)*exp(-a - b*x) - S(12)*a*x**S(3)*exp(-a - b*x) - S(36)*a*x**S(2)*exp(-a - b*x)/b - S(72)*a*x*exp(-a - b*x)/b**S(2) - S(72)*a*exp(-a - b*x)/b**S(3) - b**S(2)*x**S(5)*exp(-a - b*x) - S(5)*b*x**S(4)*exp(-a - b*x) - S(20)*x**S(3)*exp(-a - b*x) - S(60)*x**S(2)*exp(-a - b*x)/b - S(120)*x*exp(-a - b*x)/b**S(2) - S(120)*exp(-a - b*x)/b**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(a + b*x)**S(3)*exp(-a - b*x), x), x, a*(a + b*x)**S(3)*exp(-a - b*x)/b**S(2) + S(3)*a*(a + b*x)**S(2)*exp(-a - b*x)/b**S(2) + S(6)*a*(a + b*x)*exp(-a - b*x)/b**S(2) + S(6)*a*exp(-a - b*x)/b**S(2) - (a + b*x)**S(4)*exp(-a - b*x)/b**S(2) - S(4)*(a + b*x)**S(3)*exp(-a - b*x)/b**S(2) - S(12)*(a + b*x)**S(2)*exp(-a - b*x)/b**S(2) - S(24)*(a + b*x)*exp(-a - b*x)/b**S(2) - S(24)*exp(-a - b*x)/b**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*exp(-a - b*x), x), x, -(a + b*x)**S(3)*exp(-a - b*x)/b - S(3)*(a + b*x)**S(2)*exp(-a - b*x)/b - S(6)*(a + b*x)*exp(-a - b*x)/b - S(6)*exp(-a - b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*exp(-a - b*x)/x, x), x, a**S(3)*exp(-a)*Ei(-b*x) - S(3)*a**S(2)*exp(-a - b*x) - S(3)*a*b*x*exp(-a - b*x) - S(3)*a*exp(-a - b*x) - b**S(2)*x**S(2)*exp(-a - b*x) - S(2)*b*x*exp(-a - b*x) - S(2)*exp(-a - b*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*exp(-a - b*x)/x**S(2), x), x, -a**S(3)*b*exp(-a)*Ei(-b*x) - a**S(3)*exp(-a - b*x)/x + S(3)*a**S(2)*b*exp(-a)*Ei(-b*x) - S(3)*a*b*exp(-a - b*x) - b**S(2)*x*exp(-a - b*x) - b*exp(-a - b*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*exp(-a - b*x)/x**S(3), x), x, a**S(3)*b**S(2)*exp(-a)*Ei(-b*x)/S(2) + a**S(3)*b*exp(-a - b*x)/(S(2)*x) - a**S(3)*exp(-a - b*x)/(S(2)*x**S(2)) - S(3)*a**S(2)*b**S(2)*exp(-a)*Ei(-b*x) - S(3)*a**S(2)*b*exp(-a - b*x)/x + S(3)*a*b**S(2)*exp(-a)*Ei(-b*x) - b**S(2)*exp(-a - b*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*exp(-a - b*x)/x**S(4), x), x, -a**S(3)*b**S(3)*exp(-a)*Ei(-b*x)/S(6) - a**S(3)*b**S(2)*exp(-a - b*x)/(S(6)*x) + a**S(3)*b*exp(-a - b*x)/(S(6)*x**S(2)) - a**S(3)*exp(-a - b*x)/(S(3)*x**S(3)) + S(3)*a**S(2)*b**S(3)*exp(-a)*Ei(-b*x)/S(2) + S(3)*a**S(2)*b**S(2)*exp(-a - b*x)/(S(2)*x) - S(3)*a**S(2)*b*exp(-a - b*x)/(S(2)*x**S(2)) - S(3)*a*b**S(3)*exp(-a)*Ei(-b*x) - S(3)*a*b**S(2)*exp(-a - b*x)/x + b**S(3)*exp(-a)*Ei(-b*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*x**m*(e + f*x)**S(2), x), x, F**(a + b*c)*e**S(2)*x**m*(-b*d*x*log(F))**(-m)*Gamma(m + S(1), -b*d*x*log(F))/(b*d*log(F)) - S(2)*F**(a + b*c)*e*f*x**m*(-b*d*x*log(F))**(-m)*Gamma(m + S(2), -b*d*x*log(F))/(b**S(2)*d**S(2)*log(F)**S(2)) + F**(a + b*c)*f**S(2)*x**m*(-b*d*x*log(F))**(-m)*Gamma(m + S(3), -b*d*x*log(F))/(b**S(3)*d**S(3)*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*x**S(3)*(e + f*x)**S(2), x), x, F**(a + b*c + b*d*x)*e**S(2)*x**S(3)/(b*d*log(F)) + S(2)*F**(a + b*c + b*d*x)*e*f*x**S(4)/(b*d*log(F)) + F**(a + b*c + b*d*x)*f**S(2)*x**S(5)/(b*d*log(F)) - S(3)*F**(a + b*c + b*d*x)*e**S(2)*x**S(2)/(b**S(2)*d**S(2)*log(F)**S(2)) - S(8)*F**(a + b*c + b*d*x)*e*f*x**S(3)/(b**S(2)*d**S(2)*log(F)**S(2)) - S(5)*F**(a + b*c + b*d*x)*f**S(2)*x**S(4)/(b**S(2)*d**S(2)*log(F)**S(2)) + S(6)*F**(a + b*c + b*d*x)*e**S(2)*x/(b**S(3)*d**S(3)*log(F)**S(3)) + S(24)*F**(a + b*c + b*d*x)*e*f*x**S(2)/(b**S(3)*d**S(3)*log(F)**S(3)) + S(20)*F**(a + b*c + b*d*x)*f**S(2)*x**S(3)/(b**S(3)*d**S(3)*log(F)**S(3)) - S(6)*F**(a + b*c + b*d*x)*e**S(2)/(b**S(4)*d**S(4)*log(F)**S(4)) - S(48)*F**(a + b*c + b*d*x)*e*f*x/(b**S(4)*d**S(4)*log(F)**S(4)) - S(60)*F**(a + b*c + b*d*x)*f**S(2)*x**S(2)/(b**S(4)*d**S(4)*log(F)**S(4)) + S(48)*F**(a + b*c + b*d*x)*e*f/(b**S(5)*d**S(5)*log(F)**S(5)) + S(120)*F**(a + b*c + b*d*x)*f**S(2)*x/(b**S(5)*d**S(5)*log(F)**S(5)) - S(120)*F**(a + b*c + b*d*x)*f**S(2)/(b**S(6)*d**S(6)*log(F)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*x**S(2)*(e + f*x)**S(2), x), x, F**(a + b*c + b*d*x)*e**S(2)*x**S(2)/(b*d*log(F)) + S(2)*F**(a + b*c + b*d*x)*e*f*x**S(3)/(b*d*log(F)) + F**(a + b*c + b*d*x)*f**S(2)*x**S(4)/(b*d*log(F)) - S(2)*F**(a + b*c + b*d*x)*e**S(2)*x/(b**S(2)*d**S(2)*log(F)**S(2)) - S(6)*F**(a + b*c + b*d*x)*e*f*x**S(2)/(b**S(2)*d**S(2)*log(F)**S(2)) - S(4)*F**(a + b*c + b*d*x)*f**S(2)*x**S(3)/(b**S(2)*d**S(2)*log(F)**S(2)) + S(2)*F**(a + b*c + b*d*x)*e**S(2)/(b**S(3)*d**S(3)*log(F)**S(3)) + S(12)*F**(a + b*c + b*d*x)*e*f*x/(b**S(3)*d**S(3)*log(F)**S(3)) + S(12)*F**(a + b*c + b*d*x)*f**S(2)*x**S(2)/(b**S(3)*d**S(3)*log(F)**S(3)) - S(12)*F**(a + b*c + b*d*x)*e*f/(b**S(4)*d**S(4)*log(F)**S(4)) - S(24)*F**(a + b*c + b*d*x)*f**S(2)*x/(b**S(4)*d**S(4)*log(F)**S(4)) + S(24)*F**(a + b*c + b*d*x)*f**S(2)/(b**S(5)*d**S(5)*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*x*(e + f*x)**S(2), x), x, F**(a + b*c + b*d*x)*e**S(2)*x/(b*d*log(F)) + S(2)*F**(a + b*c + b*d*x)*e*f*x**S(2)/(b*d*log(F)) + F**(a + b*c + b*d*x)*f**S(2)*x**S(3)/(b*d*log(F)) - F**(a + b*c + b*d*x)*e**S(2)/(b**S(2)*d**S(2)*log(F)**S(2)) - S(4)*F**(a + b*c + b*d*x)*e*f*x/(b**S(2)*d**S(2)*log(F)**S(2)) - S(3)*F**(a + b*c + b*d*x)*f**S(2)*x**S(2)/(b**S(2)*d**S(2)*log(F)**S(2)) + S(4)*F**(a + b*c + b*d*x)*e*f/(b**S(3)*d**S(3)*log(F)**S(3)) + S(6)*F**(a + b*c + b*d*x)*f**S(2)*x/(b**S(3)*d**S(3)*log(F)**S(3)) - S(6)*F**(a + b*c + b*d*x)*f**S(2)/(b**S(4)*d**S(4)*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*(e + f*x)**S(2), x), x, F**(a + b*c + b*d*x)*(e + f*x)**S(2)/(b*d*log(F)) - S(2)*F**(a + b*c + b*d*x)*f*(e + f*x)/(b**S(2)*d**S(2)*log(F)**S(2)) + S(2)*F**(a + b*c + b*d*x)*f**S(2)/(b**S(3)*d**S(3)*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*(e + f*x)**S(2)/x, x), x, F**(a + b*c)*e**S(2)*Ei(b*d*x*log(F)) + S(2)*F**(a + b*c + b*d*x)*e*f/(b*d*log(F)) + F**(a + b*c + b*d*x)*f**S(2)*x/(b*d*log(F)) - F**(a + b*c + b*d*x)*f**S(2)/(b**S(2)*d**S(2)*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*(e + f*x)**S(2)/x**S(2), x), x, F**(a + b*c)*b*d*e**S(2)*log(F)*Ei(b*d*x*log(F)) + S(2)*F**(a + b*c)*e*f*Ei(b*d*x*log(F)) - F**(a + b*c + b*d*x)*e**S(2)/x + F**(a + b*c + b*d*x)*f**S(2)/(b*d*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*(e + f*x)**S(2)/x**S(3), x), x, F**(a + b*c)*b**S(2)*d**S(2)*e**S(2)*log(F)**S(2)*Ei(b*d*x*log(F))/S(2) + S(2)*F**(a + b*c)*b*d*e*f*log(F)*Ei(b*d*x*log(F)) + F**(a + b*c)*f**S(2)*Ei(b*d*x*log(F)) - F**(a + b*c + b*d*x)*b*d*e**S(2)*log(F)/(S(2)*x) - F**(a + b*c + b*d*x)*e**S(2)/(S(2)*x**S(2)) - S(2)*F**(a + b*c + b*d*x)*e*f/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*(e + f*x)**S(2)/x**S(4), x), x, F**(a + b*c)*b**S(3)*d**S(3)*e**S(2)*log(F)**S(3)*Ei(b*d*x*log(F))/S(6) + F**(a + b*c)*b**S(2)*d**S(2)*e*f*log(F)**S(2)*Ei(b*d*x*log(F)) + F**(a + b*c)*b*d*f**S(2)*log(F)*Ei(b*d*x*log(F)) - F**(a + b*c + b*d*x)*b**S(2)*d**S(2)*e**S(2)*log(F)**S(2)/(S(6)*x) - F**(a + b*c + b*d*x)*b*d*e**S(2)*log(F)/(S(6)*x**S(2)) - F**(a + b*c + b*d*x)*b*d*e*f*log(F)/x - F**(a + b*c + b*d*x)*e**S(2)/(S(3)*x**S(3)) - F**(a + b*c + b*d*x)*e*f/x**S(2) - F**(a + b*c + b*d*x)*f**S(2)/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x))*(e + f*x)**S(2)/x**S(5), x), x, F**(a + b*c)*b**S(4)*d**S(4)*e**S(2)*log(F)**S(4)*Ei(b*d*x*log(F))/S(24) + F**(a + b*c)*b**S(3)*d**S(3)*e*f*log(F)**S(3)*Ei(b*d*x*log(F))/S(3) + F**(a + b*c)*b**S(2)*d**S(2)*f**S(2)*log(F)**S(2)*Ei(b*d*x*log(F))/S(2) - F**(a + b*c + b*d*x)*b**S(3)*d**S(3)*e**S(2)*log(F)**S(3)/(S(24)*x) - F**(a + b*c + b*d*x)*b**S(2)*d**S(2)*e**S(2)*log(F)**S(2)/(S(24)*x**S(2)) - F**(a + b*c + b*d*x)*b**S(2)*d**S(2)*e*f*log(F)**S(2)/(S(3)*x) - F**(a + b*c + b*d*x)*b*d*e**S(2)*log(F)/(S(12)*x**S(3)) - F**(a + b*c + b*d*x)*b*d*e*f*log(F)/(S(3)*x**S(2)) - F**(a + b*c + b*d*x)*b*d*f**S(2)*log(F)/(S(2)*x) - F**(a + b*c + b*d*x)*e**S(2)/(S(4)*x**S(4)) - S(2)*F**(a + b*c + b*d*x)*e*f/(S(3)*x**S(3)) - F**(a + b*c + b*d*x)*f**S(2)/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*(c + d*x)**S(3)*exp(-a - b*x), x), x, -d**S(3)*(a + b*x)**S(7)*exp(-a - b*x)/b**S(4) - S(7)*d**S(3)*(a + b*x)**S(6)*exp(-a - b*x)/b**S(4) - S(42)*d**S(3)*(a + b*x)**S(5)*exp(-a - b*x)/b**S(4) - S(210)*d**S(3)*(a + b*x)**S(4)*exp(-a - b*x)/b**S(4) - S(840)*d**S(3)*(a + b*x)**S(3)*exp(-a - b*x)/b**S(4) - S(2520)*d**S(3)*(a + b*x)**S(2)*exp(-a - b*x)/b**S(4) - S(5040)*d**S(3)*(a + b*x)*exp(-a - b*x)/b**S(4) - S(5040)*d**S(3)*exp(-a - b*x)/b**S(4) - S(3)*d**S(2)*(a + b*x)**S(6)*(-a*d + b*c)*exp(-a - b*x)/b**S(4) - S(18)*d**S(2)*(a + b*x)**S(5)*(-a*d + b*c)*exp(-a - b*x)/b**S(4) - S(90)*d**S(2)*(a + b*x)**S(4)*(-a*d + b*c)*exp(-a - b*x)/b**S(4) - S(360)*d**S(2)*(a + b*x)**S(3)*(-a*d + b*c)*exp(-a - b*x)/b**S(4) - S(1080)*d**S(2)*(a + b*x)**S(2)*(-a*d + b*c)*exp(-a - b*x)/b**S(4) - S(2160)*d**S(2)*(a + b*x)*(-a*d + b*c)*exp(-a - b*x)/b**S(4) - S(2160)*d**S(2)*(-a*d + b*c)*exp(-a - b*x)/b**S(4) - S(3)*d*(a + b*x)**S(5)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(4) - S(15)*d*(a + b*x)**S(4)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(4) - S(60)*d*(a + b*x)**S(3)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(4) - S(180)*d*(a + b*x)**S(2)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(4) - S(360)*d*(a + b*x)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(4) - S(360)*d*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(4) - (a + b*x)**S(4)*(-a*d + b*c)**S(3)*exp(-a - b*x)/b**S(4) - S(4)*(a + b*x)**S(3)*(-a*d + b*c)**S(3)*exp(-a - b*x)/b**S(4) - S(12)*(a + b*x)**S(2)*(-a*d + b*c)**S(3)*exp(-a - b*x)/b**S(4) - S(24)*(a + b*x)*(-a*d + b*c)**S(3)*exp(-a - b*x)/b**S(4) - S(24)*(-a*d + b*c)**S(3)*exp(-a - b*x)/b**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*(c + d*x)**S(2)*exp(-a - b*x), x), x, -d**S(2)*(a + b*x)**S(6)*exp(-a - b*x)/b**S(3) - S(6)*d**S(2)*(a + b*x)**S(5)*exp(-a - b*x)/b**S(3) - S(30)*d**S(2)*(a + b*x)**S(4)*exp(-a - b*x)/b**S(3) - S(120)*d**S(2)*(a + b*x)**S(3)*exp(-a - b*x)/b**S(3) - S(360)*d**S(2)*(a + b*x)**S(2)*exp(-a - b*x)/b**S(3) - S(720)*d**S(2)*(a + b*x)*exp(-a - b*x)/b**S(3) - S(720)*d**S(2)*exp(-a - b*x)/b**S(3) - S(2)*d*(a + b*x)**S(5)*(-a*d + b*c)*exp(-a - b*x)/b**S(3) - S(10)*d*(a + b*x)**S(4)*(-a*d + b*c)*exp(-a - b*x)/b**S(3) - S(40)*d*(a + b*x)**S(3)*(-a*d + b*c)*exp(-a - b*x)/b**S(3) - S(120)*d*(a + b*x)**S(2)*(-a*d + b*c)*exp(-a - b*x)/b**S(3) - S(240)*d*(a + b*x)*(-a*d + b*c)*exp(-a - b*x)/b**S(3) - S(240)*d*(-a*d + b*c)*exp(-a - b*x)/b**S(3) - (a + b*x)**S(4)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(3) - S(4)*(a + b*x)**S(3)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(3) - S(12)*(a + b*x)**S(2)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(3) - S(24)*(a + b*x)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(3) - S(24)*(-a*d + b*c)**S(2)*exp(-a - b*x)/b**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*(c + d*x)*exp(-a - b*x), x), x, -d*(a + b*x)**S(5)*exp(-a - b*x)/b**S(2) - S(5)*d*(a + b*x)**S(4)*exp(-a - b*x)/b**S(2) - S(20)*d*(a + b*x)**S(3)*exp(-a - b*x)/b**S(2) - S(60)*d*(a + b*x)**S(2)*exp(-a - b*x)/b**S(2) - S(120)*d*(a + b*x)*exp(-a - b*x)/b**S(2) - S(120)*d*exp(-a - b*x)/b**S(2) - (a + b*x)**S(4)*(-a*d + b*c)*exp(-a - b*x)/b**S(2) - (a + b*x)**S(3)*(-S(4)*a*d + S(4)*b*c)*exp(-a - b*x)/b**S(2) - (a + b*x)**S(2)*(-S(12)*a*d + S(12)*b*c)*exp(-a - b*x)/b**S(2) - (a + b*x)*(-S(24)*a*d + S(24)*b*c)*exp(-a - b*x)/b**S(2) - (-S(24)*a*d + S(24)*b*c)*exp(-a - b*x)/b**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*exp(-a - b*x), x), x, -(a + b*x)**S(4)*exp(-a - b*x)/b - S(4)*(a + b*x)**S(3)*exp(-a - b*x)/b - S(12)*(a + b*x)**S(2)*exp(-a - b*x)/b - S(24)*(a + b*x)*exp(-a - b*x)/b - S(24)*exp(-a - b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*exp(-a - b*x)/(c + d*x), x), x, -(a + b*x)**S(3)*exp(-a - b*x)/d - S(3)*(a + b*x)**S(2)*exp(-a - b*x)/d - S(6)*(a + b*x)*exp(-a - b*x)/d - S(6)*exp(-a - b*x)/d + (a + b*x)**S(2)*(-a*d + b*c)*exp(-a - b*x)/d**S(2) + (a + b*x)*(-S(2)*a*d + S(2)*b*c)*exp(-a - b*x)/d**S(2) + (-S(2)*a*d + S(2)*b*c)*exp(-a - b*x)/d**S(2) - (a + b*x)*(-a*d + b*c)**S(2)*exp(-a - b*x)/d**S(3) - (-a*d + b*c)**S(2)*exp(-a - b*x)/d**S(3) + (-a*d + b*c)**S(3)*exp(-a - b*x)/d**S(4) + (-a*d + b*c)**S(4)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*exp(-a - b*x)/(c + d*x)**S(2), x), x, -b**S(3)*(c + d*x)**S(2)*exp(-a - b*x)/d**S(4) - S(2)*b**S(2)*(c + d*x)*exp(-a - b*x)/d**S(3) + S(4)*b**S(2)*(c + d*x)*(-a*d + b*c)*exp(-a - b*x)/d**S(4) - S(2)*b*exp(-a - b*x)/d**S(2) + S(4)*b*(-a*d + b*c)*exp(-a - b*x)/d**S(3) - S(6)*b*(-a*d + b*c)**S(2)*exp(-a - b*x)/d**S(4) - S(4)*b*(-a*d + b*c)**S(3)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(5) - b*(-a*d + b*c)**S(4)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(6) - (-a*d + b*c)**S(4)*exp(-a - b*x)/(d**S(5)*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*exp(-a - b*x)/(c + d*x)**S(3), x), x, -b**S(3)*x*exp(-a - b*x)/d**S(3) - b**S(2)*exp(-a - b*x)/d**S(3) + b**S(2)*(-S(4)*a*d + S(3)*b*c)*exp(-a - b*x)/d**S(4) + S(6)*b**S(2)*(-a*d + b*c)**S(2)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(5) + S(4)*b**S(2)*(-a*d + b*c)**S(3)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(6) + b**S(2)*(-a*d + b*c)**S(4)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/(S(2)*d**S(7)) + S(4)*b*(-a*d + b*c)**S(3)*exp(-a - b*x)/(d**S(5)*(c + d*x)) + b*(-a*d + b*c)**S(4)*exp(-a - b*x)/(S(2)*d**S(6)*(c + d*x)) - (-a*d + b*c)**S(4)*exp(-a - b*x)/(S(2)*d**S(5)*(c + d*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*exp(-a - b*x)/(c + d*x)**S(4), x), x, -b**S(3)*exp(-a - b*x)/d**S(4) - S(4)*b**S(3)*(-a*d + b*c)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(5) - S(6)*b**S(3)*(-a*d + b*c)**S(2)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(6) - S(2)*b**S(3)*(-a*d + b*c)**S(3)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(7) - b**S(3)*(-a*d + b*c)**S(4)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/(S(6)*d**S(8)) - S(6)*b**S(2)*(-a*d + b*c)**S(2)*exp(-a - b*x)/(d**S(5)*(c + d*x)) - S(2)*b**S(2)*(-a*d + b*c)**S(3)*exp(-a - b*x)/(d**S(6)*(c + d*x)) - b**S(2)*(-a*d + b*c)**S(4)*exp(-a - b*x)/(S(6)*d**S(7)*(c + d*x)) + S(2)*b*(-a*d + b*c)**S(3)*exp(-a - b*x)/(d**S(5)*(c + d*x)**S(2)) + b*(-a*d + b*c)**S(4)*exp(-a - b*x)/(S(6)*d**S(6)*(c + d*x)**S(2)) - (-a*d + b*c)**S(4)*exp(-a - b*x)/(S(3)*d**S(5)*(c + d*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*exp(-a - b*x)/(c + d*x)**S(5), x), x, b**S(4)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(5) + S(4)*b**S(4)*(-a*d + b*c)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(6) + S(3)*b**S(4)*(-a*d + b*c)**S(2)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/d**S(7) + S(2)*b**S(4)*(-a*d + b*c)**S(3)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/(S(3)*d**S(8)) + b**S(4)*(-a*d + b*c)**S(4)*exp(-a + b*c/d)*Ei(-b*(c + d*x)/d)/(S(24)*d**S(9)) + S(4)*b**S(3)*(-a*d + b*c)*exp(-a - b*x)/(d**S(5)*(c + d*x)) + S(3)*b**S(3)*(-a*d + b*c)**S(2)*exp(-a - b*x)/(d**S(6)*(c + d*x)) + S(2)*b**S(3)*(-a*d + b*c)**S(3)*exp(-a - b*x)/(S(3)*d**S(7)*(c + d*x)) + b**S(3)*(-a*d + b*c)**S(4)*exp(-a - b*x)/(S(24)*d**S(8)*(c + d*x)) - S(3)*b**S(2)*(-a*d + b*c)**S(2)*exp(-a - b*x)/(d**S(5)*(c + d*x)**S(2)) - S(2)*b**S(2)*(-a*d + b*c)**S(3)*exp(-a - b*x)/(S(3)*d**S(6)*(c + d*x)**S(2)) - b**S(2)*(-a*d + b*c)**S(4)*exp(-a - b*x)/(S(24)*d**S(7)*(c + d*x)**S(2)) + S(4)*b*(-a*d + b*c)**S(3)*exp(-a - b*x)/(S(3)*d**S(5)*(c + d*x)**S(3)) + b*(-a*d + b*c)**S(4)*exp(-a - b*x)/(S(12)*d**S(6)*(c + d*x)**S(3)) - (-a*d + b*c)**S(4)*exp(-a - b*x)/(S(4)*d**S(5)*(c + d*x)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*x**m*(e*n + e*(b*c*x*log(F) + m + S(1))*log(d*x) + e)*log(d*x)**n, x), x, F**(c*(a + b*x))*e*x**(m + S(1))*log(d*x)**(n + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*x**S(2)*(e*n + e*(b*c*x*log(F) + S(3))*log(d*x) + e)*log(d*x)**n, x), x, F**(c*(a + b*x))*e*x**S(3)*log(d*x)**(n + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*x*(e*n + e*(b*c*x*log(F) + S(2))*log(d*x) + e)*log(d*x)**n, x), x, F**(c*(a + b*x))*e*x**S(2)*log(d*x)**(n + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(e*n + e*(b*c*x*log(F) + S(1))*log(d*x) + e)*log(d*x)**n, x), x, F**(c*(a + b*x))*e*x*log(d*x)**(n + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(b*c*e*x*log(F)*log(d*x) + e*n + e)*log(d*x)**n/x, x), x, F**(c*(a + b*x))*e*log(d*x)**(n + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(e*n + e*(b*c*x*log(F) + S(-1))*log(d*x) + e)*log(d*x)**n/x**S(2), x), x, F**(c*(a + b*x))*e*log(d*x)**(n + S(1))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x))*(e*n + e*(b*c*x*log(F) + S(-2))*log(d*x) + e)*log(d*x)**n/x**S(3), x), x, F**(c*(a + b*x))*e*log(d*x)**(n + S(1))/x**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*sqrt(exp(a + b*x)), x), x, S(2)*x**S(4)*sqrt(exp(a + b*x))/b - S(16)*x**S(3)*sqrt(exp(a + b*x))/b**S(2) + S(96)*x**S(2)*sqrt(exp(a + b*x))/b**S(3) - S(384)*x*sqrt(exp(a + b*x))/b**S(4) + S(768)*sqrt(exp(a + b*x))/b**S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*sqrt(exp(a + b*x)), x), x, S(2)*x**S(3)*sqrt(exp(a + b*x))/b - S(12)*x**S(2)*sqrt(exp(a + b*x))/b**S(2) + S(48)*x*sqrt(exp(a + b*x))/b**S(3) - S(96)*sqrt(exp(a + b*x))/b**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*sqrt(exp(a + b*x)), x), x, S(2)*x**S(2)*sqrt(exp(a + b*x))/b - S(8)*x*sqrt(exp(a + b*x))/b**S(2) + S(16)*sqrt(exp(a + b*x))/b**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*sqrt(exp(a + b*x)), x), x, S(2)*x*sqrt(exp(a + b*x))/b - S(4)*sqrt(exp(a + b*x))/b**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(exp(a + b*x)), x), x, S(2)*sqrt(exp(a + b*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(exp(a + b*x))/x, x), x, exp(-b*x/S(2))*sqrt(exp(a + b*x))*Ei(b*x/S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(exp(a + b*x))/x**S(2), x), x, b*exp(-b*x/S(2))*sqrt(exp(a + b*x))*Ei(b*x/S(2))/S(2) - sqrt(exp(a + b*x))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(exp(a + b*x))/x**S(3), x), x, b**S(2)*exp(-b*x/S(2))*sqrt(exp(a + b*x))*Ei(b*x/S(2))/S(8) - b*sqrt(exp(a + b*x))/(S(4)*x) - sqrt(exp(a + b*x))/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(exp(a + b*x))/x**S(4), x), x, b**S(3)*exp(-b*x/S(2))*sqrt(exp(a + b*x))*Ei(b*x/S(2))/S(48) - b**S(2)*sqrt(exp(a + b*x))/(S(24)*x) - b*sqrt(exp(a + b*x))/(S(12)*x**S(2)) - sqrt(exp(a + b*x))/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)


def test_2():
    assert rubi_test(rubi_integrate(f**(c + d*x)*x**S(3)/(a + b*f**(c + d*x)), x), x, x**S(3)*log(S(1) + b*f**(c + d*x)/a)/(b*d*log(f)) + S(3)*x**S(2)*polylog(S(2), -b*f**(c + d*x)/a)/(b*d**S(2)*log(f)**S(2)) - S(6)*x*polylog(S(3), -b*f**(c + d*x)/a)/(b*d**S(3)*log(f)**S(3)) + S(6)*polylog(S(4), -b*f**(c + d*x)/a)/(b*d**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)*x**S(2)/(a + b*f**(c + d*x)), x), x, x**S(2)*log(S(1) + b*f**(c + d*x)/a)/(b*d*log(f)) + S(2)*x*polylog(S(2), -b*f**(c + d*x)/a)/(b*d**S(2)*log(f)**S(2)) - S(2)*polylog(S(3), -b*f**(c + d*x)/a)/(b*d**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)*x/(a + b*f**(c + d*x)), x), x, x*log(S(1) + b*f**(c + d*x)/a)/(b*d*log(f)) + polylog(S(2), -b*f**(c + d*x)/a)/(b*d**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)/(a + b*f**(c + d*x)), x), x, log(a + b*f**(c + d*x))/(b*d*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)/(x*(a + b*f**(c + d*x))), x), x, Integral(f**(c + d*x)/(x*(a + b*f**(c + d*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)/(x**S(2)*(a + b*f**(c + d*x))), x), x, Integral(f**(c + d*x)/(x**S(2)*(a + b*f**(c + d*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)*x**S(3)/(a + b*f**(c + d*x))**S(2), x), x, -x**S(3)/(b*d*(a + b*f**(c + d*x))*log(f)) - S(3)*x**S(2)*log(a*f**(-c - d*x)/b + S(1))/(a*b*d**S(2)*log(f)**S(2)) + S(6)*x*polylog(S(2), -a*f**(-c - d*x)/b)/(a*b*d**S(3)*log(f)**S(3)) + S(6)*polylog(S(3), -a*f**(-c - d*x)/b)/(a*b*d**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)*x**S(2)/(a + b*f**(c + d*x))**S(2), x), x, -x**S(2)/(b*d*(a + b*f**(c + d*x))*log(f)) - S(2)*x*log(a*f**(-c - d*x)/b + S(1))/(a*b*d**S(2)*log(f)**S(2)) + S(2)*polylog(S(2), -a*f**(-c - d*x)/b)/(a*b*d**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)*x/(a + b*f**(c + d*x))**S(2), x), x, -x/(b*d*(a + b*f**(c + d*x))*log(f)) + x/(a*b*d*log(f)) - log(a + b*f**(c + d*x))/(a*b*d**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)/(a + b*f**(c + d*x))**S(2), x), x, -S(1)/(b*d*(a + b*f**(c + d*x))*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)/(x*(a + b*f**(c + d*x))**S(2)), x), x, -Integral(S(1)/(x**S(2)*(a + b*f**(c + d*x))), x)/(b*d*log(f)) - S(1)/(b*d*x*(a + b*f**(c + d*x))*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)/(x**S(2)*(a + b*f**(c + d*x))**S(2)), x), x, -S(2)*Integral(S(1)/(x**S(3)*(a + b*f**(c + d*x))), x)/(b*d*log(f)) - S(1)/(b*d*x**S(2)*(a + b*f**(c + d*x))*log(f)), expand=True, _diff=True, _numerical=True)
    # recursion assert rubi_test(rubi_integrate(f**(c + d*x)*x**S(3)/(a + b*f**(c + d*x))**S(3), x), x, -x**S(3)/(S(2)*b*d*(a + b*f**(c + d*x))**S(2)*log(f)) + S(3)*x**S(2)/(S(2)*a*b*d**S(2)*(a + b*f**(c + d*x))*log(f)**S(2)) + x**S(3)/(S(2)*a**S(2)*b*d*log(f)) - S(3)*x**S(2)*log(S(1) + b*f**(c + d*x)/a)/(S(2)*a**S(2)*b*d**S(2)*log(f)**S(2)) + S(3)*x*log(a*f**(-c - d*x)/b + S(1))/(a**S(2)*b*d**S(3)*log(f)**S(3)) - S(3)*x*polylog(S(2), -b*f**(c + d*x)/a)/(a**S(2)*b*d**S(3)*log(f)**S(3)) - S(3)*polylog(S(2), -a*f**(-c - d*x)/b)/(a**S(2)*b*d**S(4)*log(f)**S(4)) + S(3)*polylog(S(3), -b*f**(c + d*x)/a)/(a**S(2)*b*d**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)*x**S(2)/(a + b*f**(c + d*x))**S(3), x), x, -x**S(2)/(S(2)*b*d*(a + b*f**(c + d*x))**S(2)*log(f)) + x/(a*b*d**S(2)*(a + b*f**(c + d*x))*log(f)**S(2)) + x**S(2)/(S(2)*a**S(2)*b*d*log(f)) - x*log(S(1) + b*f**(c + d*x)/a)/(a**S(2)*b*d**S(2)*log(f)**S(2)) - x/(a**S(2)*b*d**S(2)*log(f)**S(2)) + log(a + b*f**(c + d*x))/(a**S(2)*b*d**S(3)*log(f)**S(3)) - polylog(S(2), -b*f**(c + d*x)/a)/(a**S(2)*b*d**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)*x/(a + b*f**(c + d*x))**S(3), x), x, -x/(S(2)*b*d*(a + b*f**(c + d*x))**S(2)*log(f)) + S(1)/(S(2)*a*b*d**S(2)*(a + b*f**(c + d*x))*log(f)**S(2)) + x/(S(2)*a**S(2)*b*d*log(f)) - log(a + b*f**(c + d*x))/(S(2)*a**S(2)*b*d**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)/(a + b*f**(c + d*x))**S(3), x), x, -S(1)/(S(2)*b*d*(a + b*f**(c + d*x))**S(2)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)/(x*(a + b*f**(c + d*x))**S(3)), x), x, -Integral(S(1)/(x**S(2)*(a + b*f**(c + d*x))**S(2)), x)/(S(2)*b*d*log(f)) - S(1)/(S(2)*b*d*x*(a + b*f**(c + d*x))**S(2)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c + d*x)/(x**S(2)*(a + b*f**(c + d*x))**S(3)), x), x, -Integral(S(1)/(x**S(3)*(a + b*f**(c + d*x))**S(2)), x)/(b*d*log(f)) - S(1)/(S(2)*b*d*x**S(2)*(a + b*f**(c + d*x))**S(2)*log(f)), expand=True, _diff=True, _numerical=True)


def test_3():
    assert rubi_test(rubi_integrate(exp(x)/(S(6)*exp(x) + S(4)), x), x, log(S(3)*exp(x) + S(2))/S(6), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(a + b*exp(x)), x), x, log(a + b*exp(x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(d*x)/(a + b*exp(c + d*x)), x), x, exp(-c)*log(a + b*exp(c + d*x))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(c + d*x)/(a + b*exp(c + d*x)), x), x, log(a + b*exp(c + d*x))/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(x))**n*exp(x), x), x, (a + b*exp(x))**(n + S(1))/(b*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(c + d*x))**n*exp(d*x), x), x, (a + b*exp(c + d*x))**(n + S(1))*exp(-c)/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(c + d*x))**n*exp(c + d*x), x), x, (a + b*exp(c + d*x))**(n + S(1))/(b*d*(n + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**x/(F**x*b + a), x), x, log(F**x*b + a)/(b*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(d*x)/(F**(c + d*x)*b + a), x), x, F**(-c)*log(F**(c + d*x)*b + a)/(b*d*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c + d*x)/(F**(c + d*x)*b + a), x), x, log(F**(c + d*x)*b + a)/(b*d*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**x*(F**x*b + a)**n, x), x, (F**x*b + a)**(n + S(1))/(b*(n + S(1))*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(d*x)*(F**(c + d*x)*b + a)**n, x), x, F**(-c)*(F**(c + d*x)*b + a)**(n + S(1))/(b*d*(n + S(1))*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c + d*x)*(F**(c + d*x)*b + a)**n, x), x, (F**(c + d*x)*b + a)**(n + S(1))/(b*d*(n + S(1))*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**m, x), x, -f**a*x**(m + S(1))*(-b*x**S(2)*log(f))**(-m/S(2) + S(-1)/2)*Gamma(m/S(2) + S(1)/2, -b*x**S(2)*log(f))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(11), x), x, -f**a*Gamma(S(6), -b*x**S(2)*log(f))/(S(2)*b**S(6)*log(f)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(9), x), x, f**a*Gamma(S(5), -b*x**S(2)*log(f))/(S(2)*b**S(5)*log(f)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(7), x), x, f**(a + b*x**S(2))*x**S(6)/(S(2)*b*log(f)) - S(3)*f**(a + b*x**S(2))*x**S(4)/(S(2)*b**S(2)*log(f)**S(2)) + S(3)*f**(a + b*x**S(2))*x**S(2)/(b**S(3)*log(f)**S(3)) - S(3)*f**(a + b*x**S(2))/(b**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(5), x), x, f**(a + b*x**S(2))*x**S(4)/(S(2)*b*log(f)) - f**(a + b*x**S(2))*x**S(2)/(b**S(2)*log(f)**S(2)) + f**(a + b*x**S(2))/(b**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(3), x), x, f**(a + b*x**S(2))*x**S(2)/(S(2)*b*log(f)) - f**(a + b*x**S(2))/(S(2)*b**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x, x), x, f**(a + b*x**S(2))/(S(2)*b*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x, x), x, f**a*Ei(b*x**S(2)*log(f))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(3), x), x, b*f**a*log(f)*Ei(b*x**S(2)*log(f))/S(2) - f**(a + b*x**S(2))/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(5), x), x, b**S(2)*f**a*log(f)**S(2)*Ei(b*x**S(2)*log(f))/S(4) - b*f**(a + b*x**S(2))*log(f)/(S(4)*x**S(2)) - f**(a + b*x**S(2))/(S(4)*x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(7), x), x, b**S(3)*f**a*log(f)**S(3)*Ei(b*x**S(2)*log(f))/S(12) - b**S(2)*f**(a + b*x**S(2))*log(f)**S(2)/(S(12)*x**S(2)) - b*f**(a + b*x**S(2))*log(f)/(S(12)*x**S(4)) - f**(a + b*x**S(2))/(S(6)*x**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(9), x), x, -b**S(4)*f**a*Gamma(S(-4), -b*x**S(2)*log(f))*log(f)**S(4)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(11), x), x, b**S(5)*f**a*Gamma(S(-5), -b*x**S(2)*log(f))*log(f)**S(5)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(12), x), x, -f**a*x**S(13)*Gamma(S(13)/2, -b*x**S(2)*log(f))/(S(2)*(-b*x**S(2)*log(f))**(S(13)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(10), x), x, -f**a*x**S(11)*Gamma(S(11)/2, -b*x**S(2)*log(f))/(S(2)*(-b*x**S(2)*log(f))**(S(11)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(8), x), x, f**(a + b*x**S(2))*x**S(7)/(S(2)*b*log(f)) - S(7)*f**(a + b*x**S(2))*x**S(5)/(S(4)*b**S(2)*log(f)**S(2)) + S(35)*f**(a + b*x**S(2))*x**S(3)/(S(8)*b**S(3)*log(f)**S(3)) - S(105)*f**(a + b*x**S(2))*x/(S(16)*b**S(4)*log(f)**S(4)) + S(105)*sqrt(pi)*f**a*erfi(sqrt(b)*x*sqrt(log(f)))/(S(32)*b**(S(9)/2)*log(f)**(S(9)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(6), x), x, f**(a + b*x**S(2))*x**S(5)/(S(2)*b*log(f)) - S(5)*f**(a + b*x**S(2))*x**S(3)/(S(4)*b**S(2)*log(f)**S(2)) + S(15)*f**(a + b*x**S(2))*x/(S(8)*b**S(3)*log(f)**S(3)) - S(15)*sqrt(pi)*f**a*erfi(sqrt(b)*x*sqrt(log(f)))/(S(16)*b**(S(7)/2)*log(f)**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(4), x), x, f**(a + b*x**S(2))*x**S(3)/(S(2)*b*log(f)) - S(3)*f**(a + b*x**S(2))*x/(S(4)*b**S(2)*log(f)**S(2)) + S(3)*sqrt(pi)*f**a*erfi(sqrt(b)*x*sqrt(log(f)))/(S(8)*b**(S(5)/2)*log(f)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))*x**S(2), x), x, f**(a + b*x**S(2))*x/(S(2)*b*log(f)) - sqrt(pi)*f**a*erfi(sqrt(b)*x*sqrt(log(f)))/(S(4)*b**(S(3)/2)*log(f)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2)), x), x, sqrt(pi)*f**a*erfi(sqrt(b)*x*sqrt(log(f)))/(S(2)*sqrt(b)*sqrt(log(f))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(2), x), x, sqrt(pi)*sqrt(b)*f**a*sqrt(log(f))*erfi(sqrt(b)*x*sqrt(log(f))) - f**(a + b*x**S(2))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(4), x), x, S(2)*sqrt(pi)*b**(S(3)/2)*f**a*log(f)**(S(3)/2)*erfi(sqrt(b)*x*sqrt(log(f)))/S(3) - S(2)*b*f**(a + b*x**S(2))*log(f)/(S(3)*x) - f**(a + b*x**S(2))/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(6), x), x, S(4)*sqrt(pi)*b**(S(5)/2)*f**a*log(f)**(S(5)/2)*erfi(sqrt(b)*x*sqrt(log(f)))/S(15) - S(4)*b**S(2)*f**(a + b*x**S(2))*log(f)**S(2)/(S(15)*x) - S(2)*b*f**(a + b*x**S(2))*log(f)/(S(15)*x**S(3)) - f**(a + b*x**S(2))/(S(5)*x**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(8), x), x, S(8)*sqrt(pi)*b**(S(7)/2)*f**a*log(f)**(S(7)/2)*erfi(sqrt(b)*x*sqrt(log(f)))/S(105) - S(8)*b**S(3)*f**(a + b*x**S(2))*log(f)**S(3)/(S(105)*x) - S(4)*b**S(2)*f**(a + b*x**S(2))*log(f)**S(2)/(S(105)*x**S(3)) - S(2)*b*f**(a + b*x**S(2))*log(f)/(S(35)*x**S(5)) - f**(a + b*x**S(2))/(S(7)*x**S(7)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(10), x), x, -f**a*(-b*x**S(2)*log(f))**(S(9)/2)*Gamma(S(-9)/2, -b*x**S(2)*log(f))/(S(2)*x**S(9)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(2))/x**S(12), x), x, -f**a*(-b*x**S(2)*log(f))**(S(11)/2)*Gamma(S(-11)/2, -b*x**S(2)*log(f))/(S(2)*x**S(11)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x**m, x), x, -f**a*x**(m + S(1))*(-b*x**S(3)*log(f))**(-m/S(3) + S(-1)/3)*Gamma(m/S(3) + S(1)/3, -b*x**S(3)*log(f))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x**S(17), x), x, -f**a*Gamma(S(6), -b*x**S(3)*log(f))/(S(3)*b**S(6)*log(f)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x**S(14), x), x, f**a*Gamma(S(5), -b*x**S(3)*log(f))/(S(3)*b**S(5)*log(f)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x**S(11), x), x, f**(a + b*x**S(3))*x**S(9)/(S(3)*b*log(f)) - f**(a + b*x**S(3))*x**S(6)/(b**S(2)*log(f)**S(2)) + S(2)*f**(a + b*x**S(3))*x**S(3)/(b**S(3)*log(f)**S(3)) - S(2)*f**(a + b*x**S(3))/(b**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x**S(8), x), x, f**(a + b*x**S(3))*x**S(6)/(S(3)*b*log(f)) - S(2)*f**(a + b*x**S(3))*x**S(3)/(S(3)*b**S(2)*log(f)**S(2)) + S(2)*f**(a + b*x**S(3))/(S(3)*b**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x**S(5), x), x, f**(a + b*x**S(3))*x**S(3)/(S(3)*b*log(f)) - f**(a + b*x**S(3))/(S(3)*b**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x**S(2), x), x, f**(a + b*x**S(3))/(S(3)*b*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))/x, x), x, f**a*Ei(b*x**S(3)*log(f))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))/x**S(4), x), x, b*f**a*log(f)*Ei(b*x**S(3)*log(f))/S(3) - f**(a + b*x**S(3))/(S(3)*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))/x**S(7), x), x, b**S(2)*f**a*log(f)**S(2)*Ei(b*x**S(3)*log(f))/S(6) - b*f**(a + b*x**S(3))*log(f)/(S(6)*x**S(3)) - f**(a + b*x**S(3))/(S(6)*x**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))/x**S(10), x), x, b**S(3)*f**a*log(f)**S(3)*Ei(b*x**S(3)*log(f))/S(18) - b**S(2)*f**(a + b*x**S(3))*log(f)**S(2)/(S(18)*x**S(3)) - b*f**(a + b*x**S(3))*log(f)/(S(18)*x**S(6)) - f**(a + b*x**S(3))/(S(9)*x**S(9)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))/x**S(13), x), x, -b**S(4)*f**a*Gamma(S(-4), -b*x**S(3)*log(f))*log(f)**S(4)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))/x**S(16), x), x, b**S(5)*f**a*Gamma(S(-5), -b*x**S(3)*log(f))*log(f)**S(5)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x**S(4), x), x, -f**a*x**S(5)*Gamma(S(5)/3, -b*x**S(3)*log(f))/(S(3)*(-b*x**S(3)*log(f))**(S(5)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x**S(3), x), x, -f**a*x**S(4)*Gamma(S(4)/3, -b*x**S(3)*log(f))/(S(3)*(-b*x**S(3)*log(f))**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))*x, x), x, -f**a*x**S(2)*Gamma(S(2)/3, -b*x**S(3)*log(f))/(S(3)*(-b*x**S(3)*log(f))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3)), x), x, -f**a*x*Gamma(S(1)/3, -b*x**S(3)*log(f))/(S(3)*(-b*x**S(3)*log(f))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))/x**S(2), x), x, -f**a*(-b*x**S(3)*log(f))**(S(1)/3)*Gamma(S(-1)/3, -b*x**S(3)*log(f))/(S(3)*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**S(3))/x**S(3), x), x, -f**a*(-b*x**S(3)*log(f))**(S(2)/3)*Gamma(S(-2)/3, -b*x**S(3)*log(f))/(S(3)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(S(4)*x**S(3)), x), x, exp(S(4)*x**S(3))/S(12), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)*x**m, x), x, f**a*x**(m + S(1))*(-b*log(f)/x)**(m + S(1))*Gamma(-m + S(-1), -b*log(f)/x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)*x**S(4), x), x, -b**S(5)*f**a*Gamma(S(-5), -b*log(f)/x)*log(f)**S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)*x**S(3), x), x, b**S(4)*f**a*Gamma(S(-4), -b*log(f)/x)*log(f)**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)*x**S(2), x), x, -b**S(3)*f**a*log(f)**S(3)*Ei(b*log(f)/x)/S(6) + b**S(2)*f**(a + b/x)*x*log(f)**S(2)/S(6) + b*f**(a + b/x)*x**S(2)*log(f)/S(6) + f**(a + b/x)*x**S(3)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)*x, x), x, -b**S(2)*f**a*log(f)**S(2)*Ei(b*log(f)/x)/S(2) + b*f**(a + b/x)*x*log(f)/S(2) + f**(a + b/x)*x**S(2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x), x), x, -b*f**a*log(f)*Ei(b*log(f)/x) + f**(a + b/x)*x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)/x, x), x, -f**a*Ei(b*log(f)/x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)/x**S(2), x), x, -f**(a + b/x)/(b*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)/x**S(3), x), x, -f**(a + b/x)/(b*x*log(f)) + f**(a + b/x)/(b**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)/x**S(4), x), x, -f**(a + b/x)/(b*x**S(2)*log(f)) + S(2)*f**(a + b/x)/(b**S(2)*x*log(f)**S(2)) - S(2)*f**(a + b/x)/(b**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)/x**S(5), x), x, -f**(a + b/x)/(b*x**S(3)*log(f)) + S(3)*f**(a + b/x)/(b**S(2)*x**S(2)*log(f)**S(2)) - S(6)*f**(a + b/x)/(b**S(3)*x*log(f)**S(3)) + S(6)*f**(a + b/x)/(b**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)/x**S(6), x), x, -f**a*Gamma(S(5), -b*log(f)/x)/(b**S(5)*log(f)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x)/x**S(7), x), x, f**a*Gamma(S(6), -b*log(f)/x)/(b**S(6)*log(f)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**m, x), x, f**a*x**(m + S(1))*(-b*log(f)/x**S(2))**(m/S(2) + S(1)/2)*Gamma(-m/S(2) + S(-1)/2, -b*log(f)/x**S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**S(9), x), x, -b**S(5)*f**a*Gamma(S(-5), -b*log(f)/x**S(2))*log(f)**S(5)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**S(7), x), x, b**S(4)*f**a*Gamma(S(-4), -b*log(f)/x**S(2))*log(f)**S(4)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**S(5), x), x, -b**S(3)*f**a*log(f)**S(3)*Ei(b*log(f)/x**S(2))/S(12) + b**S(2)*f**(a + b/x**S(2))*x**S(2)*log(f)**S(2)/S(12) + b*f**(a + b/x**S(2))*x**S(4)*log(f)/S(12) + f**(a + b/x**S(2))*x**S(6)/S(6), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**S(3), x), x, -b**S(2)*f**a*log(f)**S(2)*Ei(b*log(f)/x**S(2))/S(4) + b*f**(a + b/x**S(2))*x**S(2)*log(f)/S(4) + f**(a + b/x**S(2))*x**S(4)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x, x), x, -b*f**a*log(f)*Ei(b*log(f)/x**S(2))/S(2) + f**(a + b/x**S(2))*x**S(2)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x, x), x, -f**a*Ei(b*log(f)/x**S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(3), x), x, -f**(a + b/x**S(2))/(S(2)*b*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(5), x), x, -f**(a + b/x**S(2))/(S(2)*b*x**S(2)*log(f)) + f**(a + b/x**S(2))/(S(2)*b**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(7), x), x, -f**(a + b/x**S(2))/(S(2)*b*x**S(4)*log(f)) + f**(a + b/x**S(2))/(b**S(2)*x**S(2)*log(f)**S(2)) - f**(a + b/x**S(2))/(b**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(9), x), x, -f**(a + b/x**S(2))/(S(2)*b*x**S(6)*log(f)) + S(3)*f**(a + b/x**S(2))/(S(2)*b**S(2)*x**S(4)*log(f)**S(2)) - S(3)*f**(a + b/x**S(2))/(b**S(3)*x**S(2)*log(f)**S(3)) + S(3)*f**(a + b/x**S(2))/(b**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(11), x), x, -f**a*Gamma(S(5), -b*log(f)/x**S(2))/(S(2)*b**S(5)*log(f)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(13), x), x, f**a*Gamma(S(6), -b*log(f)/x**S(2))/(S(2)*b**S(6)*log(f)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**S(10), x), x, f**a*x**S(11)*(-b*log(f)/x**S(2))**(S(11)/2)*Gamma(S(-11)/2, -b*log(f)/x**S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**S(8), x), x, f**a*x**S(9)*(-b*log(f)/x**S(2))**(S(9)/2)*Gamma(S(-9)/2, -b*log(f)/x**S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**S(6), x), x, -S(8)*sqrt(pi)*b**(S(7)/2)*f**a*log(f)**(S(7)/2)*erfi(sqrt(b)*sqrt(log(f))/x)/S(105) + S(8)*b**S(3)*f**(a + b/x**S(2))*x*log(f)**S(3)/S(105) + S(4)*b**S(2)*f**(a + b/x**S(2))*x**S(3)*log(f)**S(2)/S(105) + S(2)*b*f**(a + b/x**S(2))*x**S(5)*log(f)/S(35) + f**(a + b/x**S(2))*x**S(7)/S(7), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**S(4), x), x, -S(4)*sqrt(pi)*b**(S(5)/2)*f**a*log(f)**(S(5)/2)*erfi(sqrt(b)*sqrt(log(f))/x)/S(15) + S(4)*b**S(2)*f**(a + b/x**S(2))*x*log(f)**S(2)/S(15) + S(2)*b*f**(a + b/x**S(2))*x**S(3)*log(f)/S(15) + f**(a + b/x**S(2))*x**S(5)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))*x**S(2), x), x, -S(2)*sqrt(pi)*b**(S(3)/2)*f**a*log(f)**(S(3)/2)*erfi(sqrt(b)*sqrt(log(f))/x)/S(3) + S(2)*b*f**(a + b/x**S(2))*x*log(f)/S(3) + f**(a + b/x**S(2))*x**S(3)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2)), x), x, -sqrt(pi)*sqrt(b)*f**a*sqrt(log(f))*erfi(sqrt(b)*sqrt(log(f))/x) + f**(a + b/x**S(2))*x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(2), x), x, -sqrt(pi)*f**a*erfi(sqrt(b)*sqrt(log(f))/x)/(S(2)*sqrt(b)*sqrt(log(f))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(4), x), x, -f**(a + b/x**S(2))/(S(2)*b*x*log(f)) + sqrt(pi)*f**a*erfi(sqrt(b)*sqrt(log(f))/x)/(S(4)*b**(S(3)/2)*log(f)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(6), x), x, -f**(a + b/x**S(2))/(S(2)*b*x**S(3)*log(f)) + S(3)*f**(a + b/x**S(2))/(S(4)*b**S(2)*x*log(f)**S(2)) - S(3)*sqrt(pi)*f**a*erfi(sqrt(b)*sqrt(log(f))/x)/(S(8)*b**(S(5)/2)*log(f)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(8), x), x, -f**(a + b/x**S(2))/(S(2)*b*x**S(5)*log(f)) + S(5)*f**(a + b/x**S(2))/(S(4)*b**S(2)*x**S(3)*log(f)**S(2)) - S(15)*f**(a + b/x**S(2))/(S(8)*b**S(3)*x*log(f)**S(3)) + S(15)*sqrt(pi)*f**a*erfi(sqrt(b)*sqrt(log(f))/x)/(S(16)*b**(S(7)/2)*log(f)**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(10), x), x, -f**(a + b/x**S(2))/(S(2)*b*x**S(7)*log(f)) + S(7)*f**(a + b/x**S(2))/(S(4)*b**S(2)*x**S(5)*log(f)**S(2)) - S(35)*f**(a + b/x**S(2))/(S(8)*b**S(3)*x**S(3)*log(f)**S(3)) + S(105)*f**(a + b/x**S(2))/(S(16)*b**S(4)*x*log(f)**S(4)) - S(105)*sqrt(pi)*f**a*erfi(sqrt(b)*sqrt(log(f))/x)/(S(32)*b**(S(9)/2)*log(f)**(S(9)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(12), x), x, f**a*Gamma(S(11)/2, -b*log(f)/x**S(2))/(S(2)*x**S(11)*(-b*log(f)/x**S(2))**(S(11)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(2))/x**S(14), x), x, f**a*Gamma(S(13)/2, -b*log(f)/x**S(2))/(S(2)*x**S(13)*(-b*log(f)/x**S(2))**(S(13)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))*x**m, x), x, f**a*x**(m + S(1))*(-b*log(f)/x**S(3))**(m/S(3) + S(1)/3)*Gamma(-m/S(3) + S(-1)/3, -b*log(f)/x**S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))*x**S(14), x), x, -b**S(5)*f**a*Gamma(S(-5), -b*log(f)/x**S(3))*log(f)**S(5)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))*x**S(11), x), x, b**S(4)*f**a*Gamma(S(-4), -b*log(f)/x**S(3))*log(f)**S(4)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))*x**S(8), x), x, -b**S(3)*f**a*log(f)**S(3)*Ei(b*log(f)/x**S(3))/S(18) + b**S(2)*f**(a + b/x**S(3))*x**S(3)*log(f)**S(2)/S(18) + b*f**(a + b/x**S(3))*x**S(6)*log(f)/S(18) + f**(a + b/x**S(3))*x**S(9)/S(9), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))*x**S(5), x), x, -b**S(2)*f**a*log(f)**S(2)*Ei(b*log(f)/x**S(3))/S(6) + b*f**(a + b/x**S(3))*x**S(3)*log(f)/S(6) + f**(a + b/x**S(3))*x**S(6)/S(6), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))*x**S(2), x), x, -b*f**a*log(f)*Ei(b*log(f)/x**S(3))/S(3) + f**(a + b/x**S(3))*x**S(3)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x, x), x, -f**a*Ei(b*log(f)/x**S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x**S(4), x), x, -f**(a + b/x**S(3))/(S(3)*b*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x**S(7), x), x, -f**(a + b/x**S(3))/(S(3)*b*x**S(3)*log(f)) + f**(a + b/x**S(3))/(S(3)*b**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x**S(10), x), x, -f**(a + b/x**S(3))/(S(3)*b*x**S(6)*log(f)) + S(2)*f**(a + b/x**S(3))/(S(3)*b**S(2)*x**S(3)*log(f)**S(2)) - S(2)*f**(a + b/x**S(3))/(S(3)*b**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x**S(13), x), x, -f**(a + b/x**S(3))/(S(3)*b*x**S(9)*log(f)) + f**(a + b/x**S(3))/(b**S(2)*x**S(6)*log(f)**S(2)) - S(2)*f**(a + b/x**S(3))/(b**S(3)*x**S(3)*log(f)**S(3)) + S(2)*f**(a + b/x**S(3))/(b**S(4)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x**S(16), x), x, -f**a*Gamma(S(5), -b*log(f)/x**S(3))/(S(3)*b**S(5)*log(f)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x**S(19), x), x, f**a*Gamma(S(6), -b*log(f)/x**S(3))/(S(3)*b**S(6)*log(f)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))*x**S(4), x), x, f**a*x**S(5)*(-b*log(f)/x**S(3))**(S(5)/3)*Gamma(S(-5)/3, -b*log(f)/x**S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))*x**S(3), x), x, f**a*x**S(4)*(-b*log(f)/x**S(3))**(S(4)/3)*Gamma(S(-4)/3, -b*log(f)/x**S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))*x, x), x, f**a*x**S(2)*(-b*log(f)/x**S(3))**(S(2)/3)*Gamma(S(-2)/3, -b*log(f)/x**S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3)), x), x, f**a*x*(-b*log(f)/x**S(3))**(S(1)/3)*Gamma(S(-1)/3, -b*log(f)/x**S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x**S(2), x), x, f**a*Gamma(S(1)/3, -b*log(f)/x**S(3))/(S(3)*x*(-b*log(f)/x**S(3))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x**S(3), x), x, f**a*Gamma(S(2)/3, -b*log(f)/x**S(3))/(S(3)*x**S(2)*(-b*log(f)/x**S(3))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b/x**S(3))/x**S(5), x), x, f**a*Gamma(S(4)/3, -b*log(f)/x**S(3))/(S(3)*x**S(4)*(-b*log(f)/x**S(3))**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**m, x), x, -f**a*x**(m + S(1))*(-b*x**n*log(f))**(-(m + S(1))/n)*Gamma((m + S(1))/n, -b*x**n*log(f))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**S(3), x), x, -f**a*x**S(4)*(-b*x**n*log(f))**(-S(4)/n)*Gamma(S(4)/n, -b*x**n*log(f))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**S(2), x), x, -f**a*x**S(3)*(-b*x**n*log(f))**(-S(3)/n)*Gamma(S(3)/n, -b*x**n*log(f))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x, x), x, -f**a*x**S(2)*(-b*x**n*log(f))**(-S(2)/n)*Gamma(S(2)/n, -b*x**n*log(f))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n), x), x, -f**a*x*(-b*x**n*log(f))**(-S(1)/n)*Gamma(S(1)/n, -b*x**n*log(f))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)/x, x), x, f**a*Ei(b*x**n*log(f))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)/x**S(2), x), x, -f**a*(-b*x**n*log(f))**(S(1)/n)*Gamma(-S(1)/n, -b*x**n*log(f))/(n*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)/x**S(3), x), x, -f**a*(-b*x**n*log(f))**(S(2)/n)*Gamma(-S(2)/n, -b*x**n*log(f))/(n*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)/x**S(4), x), x, -f**a*(-b*x**n*log(f))**(S(3)/n)*Gamma(-S(3)/n, -b*x**n*log(f))/(n*x**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(S(3)*n + S(-1)), x), x, f**(a + b*x**n)*x**(S(2)*n)/(b*n*log(f)) - S(2)*f**(a + b*x**n)*x**n/(b**S(2)*n*log(f)**S(2)) + S(2)*f**(a + b*x**n)/(b**S(3)*n*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(S(2)*n + S(-1)), x), x, f**(a + b*x**n)*x**n/(b*n*log(f)) - f**(a + b*x**n)/(b**S(2)*n*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(n + S(-1)), x), x, f**(a + b*x**n)/(b*n*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)/x, x), x, f**a*Ei(b*x**n*log(f))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(-n + S(-1)), x), x, b*f**a*log(f)*Ei(b*x**n*log(f))/n - f**(a + b*x**n)*x**(-n)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(-S(2)*n + S(-1)), x), x, b**S(2)*f**a*log(f)**S(2)*Ei(b*x**n*log(f))/(S(2)*n) - b*f**(a + b*x**n)*x**(-n)*log(f)/(S(2)*n) - f**(a + b*x**n)*x**(-S(2)*n)/(S(2)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(S(5)*n/S(2) + S(-1)), x), x, f**(a + b*x**n)*x**(S(3)*n/S(2))/(b*n*log(f)) - S(3)*f**(a + b*x**n)*x**(n/S(2))/(S(2)*b**S(2)*n*log(f)**S(2)) + S(3)*sqrt(pi)*f**a*erfi(sqrt(b)*x**(n/S(2))*sqrt(log(f)))/(S(4)*b**(S(5)/2)*n*log(f)**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(S(3)*n/S(2) + S(-1)), x), x, f**(a + b*x**n)*x**(n/S(2))/(b*n*log(f)) - sqrt(pi)*f**a*erfi(sqrt(b)*x**(n/S(2))*sqrt(log(f)))/(S(2)*b**(S(3)/2)*n*log(f)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(n/S(2) + S(-1)), x), x, sqrt(pi)*f**a*erfi(sqrt(b)*x**(n/S(2))*sqrt(log(f)))/(sqrt(b)*n*sqrt(log(f))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(-n/S(2) + S(-1)), x), x, S(2)*sqrt(pi)*sqrt(b)*f**a*sqrt(log(f))*erfi(sqrt(b)*x**(n/S(2))*sqrt(log(f)))/n - S(2)*f**(a + b*x**n)*x**(-n/S(2))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x**n)*x**(-S(3)*n/S(2) + S(-1)), x), x, S(4)*sqrt(pi)*b**(S(3)/2)*f**a*log(f)**(S(3)/2)*erfi(sqrt(b)*x**(n/S(2))*sqrt(log(f)))/(S(3)*n) - S(4)*b*f**(a + b*x**n)*x**(-n/S(2))*log(f)/(S(3)*n) - S(2)*f**(a + b*x**n)*x**(-S(3)*n/S(2))/(S(3)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(-0.1*x), x), x, -10.0*x*exp(-0.1*x) - 100.0*exp(-0.1*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(2))*x**m, x), x, Integral(f**(a**S(2)*c + S(2)*a*b*c*x + b**S(2)*c*x**S(2))*x**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(2))*x**S(3), x), x, -sqrt(pi)*a**S(3)*erfi(sqrt(c)*(a + b*x)*sqrt(log(f)))/(S(2)*b**S(4)*sqrt(c)*sqrt(log(f))) + S(3)*a**S(2)*f**(c*(a + b*x)**S(2))/(S(2)*b**S(4)*c*log(f)) - S(3)*a*f**(c*(a + b*x)**S(2))*(a + b*x)/(S(2)*b**S(4)*c*log(f)) + S(3)*sqrt(pi)*a*erfi(sqrt(c)*(a + b*x)*sqrt(log(f)))/(S(4)*b**S(4)*c**(S(3)/2)*log(f)**(S(3)/2)) + f**(c*(a + b*x)**S(2))*(a + b*x)**S(2)/(S(2)*b**S(4)*c*log(f)) - f**(c*(a + b*x)**S(2))/(S(2)*b**S(4)*c**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(2))*x**S(2), x), x, sqrt(pi)*a**S(2)*erfi(sqrt(c)*(a + b*x)*sqrt(log(f)))/(S(2)*b**S(3)*sqrt(c)*sqrt(log(f))) - a*f**(c*(a + b*x)**S(2))/(b**S(3)*c*log(f)) + f**(c*(a + b*x)**S(2))*(a + b*x)/(S(2)*b**S(3)*c*log(f)) - sqrt(pi)*erfi(sqrt(c)*(a + b*x)*sqrt(log(f)))/(S(4)*b**S(3)*c**(S(3)/2)*log(f)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(2))*x, x), x, -sqrt(pi)*a*erfi(sqrt(c)*(a + b*x)*sqrt(log(f)))/(S(2)*b**S(2)*sqrt(c)*sqrt(log(f))) + f**(c*(a + b*x)**S(2))/(S(2)*b**S(2)*c*log(f)), expand=True, _diff=True, _numerical=True)

    # long time in rubi_test(1940 is matched before 1909) assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(2)), x), x, sqrt(pi)*erfi(sqrt(c)*(a + b*x)*sqrt(log(f)))/(S(2)*b*sqrt(c)*sqrt(log(f))), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(2))/x, x), x, Integral(f**(c*(a + b*x)**S(2))/x, x), expand=True, _diff=True, _numerical=True)

    # long time in rubi_test(1940 is matched before 1909) assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(2))/x**S(2), x), x, S(2)*a*b*c*log(f)*Integral(f**(c*(a + b*x)**S(2))/x, x) + sqrt(pi)*b*sqrt(c)*sqrt(log(f))*erfi(sqrt(c)*(a + b*x)*sqrt(log(f))) - f**(c*(a + b*x)**S(2))/x, expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(2))/x**S(3), x), x, S(2)*a**S(2)*b**S(2)*c**S(2)*log(f)**S(2)*Integral(f**(c*(a + b*x)**S(2))/x, x) + sqrt(pi)*a*b**S(2)*c**(S(3)/2)*log(f)**(S(3)/2)*erfi(sqrt(c)*(a + b*x)*sqrt(log(f))) - a*b*c*f**(c*(a + b*x)**S(2))*log(f)/x + b**S(2)*c*log(f)*Integral(f**(c*(a + b*x)**S(2))/x, x) - f**(c*(a + b*x)**S(2))/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(3))*x**m, x), x, Integral(f**(c*(a + b*x)**S(3))*x**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(3))*x**S(2), x), x, -a**S(2)*(a + b*x)*Gamma(S(1)/3, -c*(a + b*x)**S(3)*log(f))/(S(3)*b**S(3)*(-c*(a + b*x)**S(3)*log(f))**(S(1)/3)) + S(2)*a*(a + b*x)**S(2)*Gamma(S(2)/3, -c*(a + b*x)**S(3)*log(f))/(S(3)*b**S(3)*(-c*(a + b*x)**S(3)*log(f))**(S(2)/3)) + f**(c*(a + b*x)**S(3))/(S(3)*b**S(3)*c*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(3))*x, x), x, a*(a + b*x)*Gamma(S(1)/3, -c*(a + b*x)**S(3)*log(f))/(S(3)*b**S(2)*(-c*(a + b*x)**S(3)*log(f))**(S(1)/3)) - (a + b*x)**S(2)*Gamma(S(2)/3, -c*(a + b*x)**S(3)*log(f))/(S(3)*b**S(2)*(-c*(a + b*x)**S(3)*log(f))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(3)), x), x, (-a/S(3) - b*x/S(3))*Gamma(S(1)/3, -c*(a + b*x)**S(3)*log(f))/(b*(-c*(a + b*x)**S(3)*log(f))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(3))/x, x), x, Integral(f**(c*(a + b*x)**S(3))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(3))/x**S(2), x), x, S(3)*a**S(2)*b*c*log(f)*Integral(f**(c*(a + b*x)**S(3))/x, x) - a*b*c*(a + b*x)*Gamma(S(1)/3, -c*(a + b*x)**S(3)*log(f))*log(f)/(-c*(a + b*x)**S(3)*log(f))**(S(1)/3) - b*c*(a + b*x)**S(2)*Gamma(S(2)/3, -c*(a + b*x)**S(3)*log(f))*log(f)/(-c*(a + b*x)**S(3)*log(f))**(S(2)/3) - f**(c*(a + b*x)**S(3))/x, expand=True, _diff=True, _numerical=True)

    # difference in simplify of sympy and mathematica assert rubi_test(rubi_integrate(f**(c*(a + b*x)**S(3))/x**S(3), x), x, S(9)*a**S(4)*b**S(2)*c**S(2)*log(f)**S(2)*Integral(f**(c*(a + b*x)**S(3))/x, x)/S(2) - S(3)*a**S(3)*b**S(2)*c**S(2)*(a + b*x)*Gamma(S(1)/3, -c*(a + b*x)**S(3)*log(f))*log(f)**S(2)/(S(2)*(-c*(a + b*x)**S(3)*log(f))**(S(1)/3)) - S(3)*a**S(2)*b**S(2)*c**S(2)*(a + b*x)**S(2)*Gamma(S(2)/3, -c*(a + b*x)**S(3)*log(f))*log(f)**S(2)/(S(2)*(-c*(a + b*x)**S(3)*log(f))**(S(2)/3)) - S(3)*a**S(2)*b*c*f**(c*(a + b*x)**S(3))*log(f)/(S(2)*x) + S(3)*a*b**S(2)*c*log(f)*Integral(f**(c*(a + b*x)**S(3))/x, x) - b**S(2)*c*(a + b*x)*Gamma(S(1)/3, -c*(a + b*x)**S(3)*log(f))*log(f)/(S(2)*(-c*(a + b*x)**S(3)*log(f))**(S(1)/3)) - f**(c*(a + b*x)**S(3))/(S(2)*x**S(2)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(x**m*exp(a**S(3) + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*x**S(2) + b**S(3)*x**S(3)), x), x, Integral(x**m*exp(a**S(3) + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*x**S(2) + b**S(3)*x**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(4)*exp(a**S(3) + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*x**S(2) + b**S(3)*x**S(3)), x), x, -a**S(4)*(a + b*x)*Gamma(S(1)/3, -(a + b*x)**S(3))/(S(3)*b**S(5)*(-(a + b*x)**S(3))**(S(1)/3)) + S(4)*a**S(3)*(a + b*x)**S(2)*Gamma(S(2)/3, -(a + b*x)**S(3))/(S(3)*b**S(5)*(-(a + b*x)**S(3))**(S(2)/3)) + S(2)*a**S(2)*exp((a + b*x)**S(3))/b**S(5) + S(4)*a*(a + b*x)**S(4)*Gamma(S(4)/3, -(a + b*x)**S(3))/(S(3)*b**S(5)*(-(a + b*x)**S(3))**(S(4)/3)) - (a + b*x)**S(5)*Gamma(S(5)/3, -(a + b*x)**S(3))/(S(3)*b**S(5)*(-(a + b*x)**S(3))**(S(5)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*exp(a**S(3) + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*x**S(2) + b**S(3)*x**S(3)), x), x, a**S(3)*(a + b*x)*Gamma(S(1)/3, -(a + b*x)**S(3))/(S(3)*b**S(4)*(-(a + b*x)**S(3))**(S(1)/3)) - a**S(2)*(a + b*x)**S(2)*Gamma(S(2)/3, -(a + b*x)**S(3))/(b**S(4)*(-(a + b*x)**S(3))**(S(2)/3)) - a*exp((a + b*x)**S(3))/b**S(4) - (a + b*x)**S(4)*Gamma(S(4)/3, -(a + b*x)**S(3))/(S(3)*b**S(4)*(-(a + b*x)**S(3))**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(a**S(3) + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*x**S(2) + b**S(3)*x**S(3)), x), x, -a**S(2)*(a + b*x)*Gamma(S(1)/3, -(a + b*x)**S(3))/(S(3)*b**S(3)*(-(a + b*x)**S(3))**(S(1)/3)) + S(2)*a*(a + b*x)**S(2)*Gamma(S(2)/3, -(a + b*x)**S(3))/(S(3)*b**S(3)*(-(a + b*x)**S(3))**(S(2)/3)) + exp((a + b*x)**S(3))/(S(3)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(a**S(3) + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*x**S(2) + b**S(3)*x**S(3)), x), x, a*(a + b*x)*Gamma(S(1)/3, -(a + b*x)**S(3))/(S(3)*b**S(2)*(-(a + b*x)**S(3))**(S(1)/3)) - (a + b*x)**S(2)*Gamma(S(2)/3, -(a + b*x)**S(3))/(S(3)*b**S(2)*(-(a + b*x)**S(3))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a**S(3) + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*x**S(2) + b**S(3)*x**S(3)), x), x, (-a/S(3) - b*x/S(3))*Gamma(S(1)/3, -(a + b*x)**S(3))/(b*(-(a + b*x)**S(3))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a**S(3) + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*x**S(2) + b**S(3)*x**S(3))/x, x), x, Integral(exp(a**S(3) + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*x**S(2) + b**S(3)*x**S(3))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(sqrt(S(3)*x + S(5))), x), x, S(2)*sqrt(S(3)*x + S(5))*exp(sqrt(S(3)*x + S(5)))/S(3) - S(2)*exp(sqrt(S(3)*x + S(5)))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x))*x**m, x), x, Integral(f**(c/(a + b*x))*x**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x))*x**S(4), x), x, -a**S(4)*c*log(f)*Ei(c*log(f)/(a + b*x))/b**S(5) + a**S(4)*f**(c/(a + b*x))*(a + b*x)/b**S(5) + S(2)*a**S(3)*c**S(2)*log(f)**S(2)*Ei(c*log(f)/(a + b*x))/b**S(5) - S(2)*a**S(3)*c*f**(c/(a + b*x))*(a + b*x)*log(f)/b**S(5) - S(2)*a**S(3)*f**(c/(a + b*x))*(a + b*x)**S(2)/b**S(5) - a**S(2)*c**S(3)*log(f)**S(3)*Ei(c*log(f)/(a + b*x))/b**S(5) + a**S(2)*c**S(2)*f**(c/(a + b*x))*(a + b*x)*log(f)**S(2)/b**S(5) + a**S(2)*c*f**(c/(a + b*x))*(a + b*x)**S(2)*log(f)/b**S(5) + S(2)*a**S(2)*f**(c/(a + b*x))*(a + b*x)**S(3)/b**S(5) - S(4)*a*c**S(4)*Gamma(S(-4), -c*log(f)/(a + b*x))*log(f)**S(4)/b**S(5) - c**S(5)*Gamma(S(-5), -c*log(f)/(a + b*x))*log(f)**S(5)/b**S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x))*x**S(3), x), x, a**S(3)*c*log(f)*Ei(c*log(f)/(a + b*x))/b**S(4) - a**S(3)*f**(c/(a + b*x))*(a + b*x)/b**S(4) - S(3)*a**S(2)*c**S(2)*log(f)**S(2)*Ei(c*log(f)/(a + b*x))/(S(2)*b**S(4)) + S(3)*a**S(2)*c*f**(c/(a + b*x))*(a + b*x)*log(f)/(S(2)*b**S(4)) + S(3)*a**S(2)*f**(c/(a + b*x))*(a + b*x)**S(2)/(S(2)*b**S(4)) + a*c**S(3)*log(f)**S(3)*Ei(c*log(f)/(a + b*x))/(S(2)*b**S(4)) - a*c**S(2)*f**(c/(a + b*x))*(a + b*x)*log(f)**S(2)/(S(2)*b**S(4)) - a*c*f**(c/(a + b*x))*(a + b*x)**S(2)*log(f)/(S(2)*b**S(4)) - a*f**(c/(a + b*x))*(a + b*x)**S(3)/b**S(4) + c**S(4)*Gamma(S(-4), -c*log(f)/(a + b*x))*log(f)**S(4)/b**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x))*x**S(2), x), x, -a**S(2)*c*log(f)*Ei(c*log(f)/(a + b*x))/b**S(3) + a**S(2)*f**(c/(a + b*x))*(a + b*x)/b**S(3) + a*c**S(2)*log(f)**S(2)*Ei(c*log(f)/(a + b*x))/b**S(3) - a*c*f**(c/(a + b*x))*(a + b*x)*log(f)/b**S(3) - a*f**(c/(a + b*x))*(a + b*x)**S(2)/b**S(3) - c**S(3)*log(f)**S(3)*Ei(c*log(f)/(a + b*x))/(S(6)*b**S(3)) + c**S(2)*f**(c/(a + b*x))*(a + b*x)*log(f)**S(2)/(S(6)*b**S(3)) + c*f**(c/(a + b*x))*(a + b*x)**S(2)*log(f)/(S(6)*b**S(3)) + f**(c/(a + b*x))*(a + b*x)**S(3)/(S(3)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x))*x, x), x, a*c*log(f)*Ei(c*log(f)/(a + b*x))/b**S(2) - a*f**(c/(a + b*x))*(a + b*x)/b**S(2) - c**S(2)*log(f)**S(2)*Ei(c*log(f)/(a + b*x))/(S(2)*b**S(2)) + c*f**(c/(a + b*x))*(a + b*x)*log(f)/(S(2)*b**S(2)) + f**(c/(a + b*x))*(a + b*x)**S(2)/(S(2)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)), x), x, -c*log(f)*Ei(c*log(f)/(a + b*x))/b + f**(c/(a + b*x))*(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x))/x, x), x, f**(c/a)*Ei(-b*c*x*log(f)/(a*(a + b*x))) - Ei(c*log(f)/(a + b*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x))/x**S(2), x), x, -f**(c/(a + b*x))/x - b*f**(c/(a + b*x))/a - b*c*f**(c/a)*log(f)*Ei(-b*c*x*log(f)/(a*(a + b*x)))/a**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x))/x**S(3), x), x, -f**(c/(a + b*x))/(S(2)*x**S(2)) + b**S(2)*f**(c/(a + b*x))/(S(2)*a**S(2)) + b*c*f**(c/(a + b*x))*log(f)/(S(2)*a**S(2)*x) + b**S(2)*c*f**(c/a)*log(f)*Ei(-b*c*x*log(f)/(a*(a + b*x)))/a**S(3) + b**S(2)*c*f**(c/(a + b*x))*log(f)/(S(2)*a**S(3)) + b**S(2)*c**S(2)*f**(c/a)*log(f)**S(2)*Ei(-b*c*x*log(f)/(a*(a + b*x)))/(S(2)*a**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(2))*x**m, x), x, Integral(f**(c/(a + b*x)**S(2))*x**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(2))*x**S(4), x), x, -sqrt(pi)*a**S(4)*sqrt(c)*sqrt(log(f))*erfi(sqrt(c)*sqrt(log(f))/(a + b*x))/b**S(5) + a**S(4)*f**(c/(a + b*x)**S(2))*(a + b*x)/b**S(5) + S(2)*a**S(3)*c*log(f)*Ei(c*log(f)/(a + b*x)**S(2))/b**S(5) - S(2)*a**S(3)*f**(c/(a + b*x)**S(2))*(a + b*x)**S(2)/b**S(5) - S(4)*sqrt(pi)*a**S(2)*c**(S(3)/2)*log(f)**(S(3)/2)*erfi(sqrt(c)*sqrt(log(f))/(a + b*x))/b**S(5) + S(4)*a**S(2)*c*f**(c/(a + b*x)**S(2))*(a + b*x)*log(f)/b**S(5) + S(2)*a**S(2)*f**(c/(a + b*x)**S(2))*(a + b*x)**S(3)/b**S(5) + a*c**S(2)*log(f)**S(2)*Ei(c*log(f)/(a + b*x)**S(2))/b**S(5) - a*c*f**(c/(a + b*x)**S(2))*(a + b*x)**S(2)*log(f)/b**S(5) - a*f**(c/(a + b*x)**S(2))*(a + b*x)**S(4)/b**S(5) - S(4)*sqrt(pi)*c**(S(5)/2)*log(f)**(S(5)/2)*erfi(sqrt(c)*sqrt(log(f))/(a + b*x))/(S(15)*b**S(5)) + S(4)*c**S(2)*f**(c/(a + b*x)**S(2))*(a + b*x)*log(f)**S(2)/(S(15)*b**S(5)) + S(2)*c*f**(c/(a + b*x)**S(2))*(a + b*x)**S(3)*log(f)/(S(15)*b**S(5)) + f**(c/(a + b*x)**S(2))*(a + b*x)**S(5)/(S(5)*b**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(2))*x**S(3), x), x, sqrt(pi)*a**S(3)*sqrt(c)*sqrt(log(f))*erfi(sqrt(c)*sqrt(log(f))/(a + b*x))/b**S(4) - a**S(3)*f**(c/(a + b*x)**S(2))*(a + b*x)/b**S(4) - S(3)*a**S(2)*c*log(f)*Ei(c*log(f)/(a + b*x)**S(2))/(S(2)*b**S(4)) + S(3)*a**S(2)*f**(c/(a + b*x)**S(2))*(a + b*x)**S(2)/(S(2)*b**S(4)) + S(2)*sqrt(pi)*a*c**(S(3)/2)*log(f)**(S(3)/2)*erfi(sqrt(c)*sqrt(log(f))/(a + b*x))/b**S(4) - S(2)*a*c*f**(c/(a + b*x)**S(2))*(a + b*x)*log(f)/b**S(4) - a*f**(c/(a + b*x)**S(2))*(a + b*x)**S(3)/b**S(4) - c**S(2)*log(f)**S(2)*Ei(c*log(f)/(a + b*x)**S(2))/(S(4)*b**S(4)) + c*f**(c/(a + b*x)**S(2))*(a + b*x)**S(2)*log(f)/(S(4)*b**S(4)) + f**(c/(a + b*x)**S(2))*(a + b*x)**S(4)/(S(4)*b**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(2))*x**S(2), x), x, -sqrt(pi)*a**S(2)*sqrt(c)*sqrt(log(f))*erfi(sqrt(c)*sqrt(log(f))/(a + b*x))/b**S(3) + a**S(2)*f**(c/(a + b*x)**S(2))*(a + b*x)/b**S(3) + a*c*log(f)*Ei(c*log(f)/(a + b*x)**S(2))/b**S(3) - a*f**(c/(a + b*x)**S(2))*(a + b*x)**S(2)/b**S(3) - S(2)*sqrt(pi)*c**(S(3)/2)*log(f)**(S(3)/2)*erfi(sqrt(c)*sqrt(log(f))/(a + b*x))/(S(3)*b**S(3)) + S(2)*c*f**(c/(a + b*x)**S(2))*(a + b*x)*log(f)/(S(3)*b**S(3)) + f**(c/(a + b*x)**S(2))*(a + b*x)**S(3)/(S(3)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(2))*x, x), x, sqrt(pi)*a*sqrt(c)*sqrt(log(f))*erfi(sqrt(c)*sqrt(log(f))/(a + b*x))/b**S(2) - a*f**(c/(a + b*x)**S(2))*(a + b*x)/b**S(2) - c*log(f)*Ei(c*log(f)/(a + b*x)**S(2))/(S(2)*b**S(2)) + f**(c/(a + b*x)**S(2))*(a + b*x)**S(2)/(S(2)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(2)), x), x, -sqrt(pi)*sqrt(c)*sqrt(log(f))*erfi(sqrt(c)*sqrt(log(f))/(a + b*x))/b + f**(c/(a + b*x)**S(2))*(a + b*x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(2))/x, x), x, Integral(f**(c/(a + b*x)**S(2))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(2))/x**S(2), x), x, Integral(f**(c/(a + b*x)**S(2))/x**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(2))/x**S(3), x), x, Integral(f**(c/(a + b*x)**S(2))/x**S(3), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(3))*x**m, x), x, Integral(f**(c/(a + b*x)**S(3))*x**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(3))*x**S(4), x), x, a**S(4)*(-c*log(f)/(a + b*x)**S(3))**(S(1)/3)*(a + b*x)*Gamma(S(-1)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(5)) - S(4)*a**S(3)*(-c*log(f)/(a + b*x)**S(3))**(S(2)/3)*(a + b*x)**S(2)*Gamma(S(-2)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(5)) - S(2)*a**S(2)*c*log(f)*Ei(c*log(f)/(a + b*x)**S(3))/b**S(5) + S(2)*a**S(2)*f**(c/(a + b*x)**S(3))*(a + b*x)**S(3)/b**S(5) - S(4)*a*(-c*log(f)/(a + b*x)**S(3))**(S(4)/3)*(a + b*x)**S(4)*Gamma(S(-4)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(5)) + (-c*log(f)/(a + b*x)**S(3))**(S(5)/3)*(a + b*x)**S(5)*Gamma(S(-5)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(3))*x**S(3), x), x, -a**S(3)*(-c*log(f)/(a + b*x)**S(3))**(S(1)/3)*(a + b*x)*Gamma(S(-1)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(4)) + a**S(2)*(-c*log(f)/(a + b*x)**S(3))**(S(2)/3)*(a + b*x)**S(2)*Gamma(S(-2)/3, -c*log(f)/(a + b*x)**S(3))/b**S(4) + a*c*log(f)*Ei(c*log(f)/(a + b*x)**S(3))/b**S(4) - a*f**(c/(a + b*x)**S(3))*(a + b*x)**S(3)/b**S(4) + (-c*log(f)/(a + b*x)**S(3))**(S(4)/3)*(a + b*x)**S(4)*Gamma(S(-4)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(3))*x**S(2), x), x, a**S(2)*(-c*log(f)/(a + b*x)**S(3))**(S(1)/3)*(a + b*x)*Gamma(S(-1)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(3)) - S(2)*a*(-c*log(f)/(a + b*x)**S(3))**(S(2)/3)*(a + b*x)**S(2)*Gamma(S(-2)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(3)) - c*log(f)*Ei(c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(3)) + f**(c/(a + b*x)**S(3))*(a + b*x)**S(3)/(S(3)*b**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(3))*x, x), x, -a*(-c*log(f)/(a + b*x)**S(3))**(S(1)/3)*(a + b*x)*Gamma(S(-1)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(2)) + (-c*log(f)/(a + b*x)**S(3))**(S(2)/3)*(a + b*x)**S(2)*Gamma(S(-2)/3, -c*log(f)/(a + b*x)**S(3))/(S(3)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(3)), x), x, (-c*log(f)/(a + b*x)**S(3))**(S(1)/3)*(a/S(3) + b*x/S(3))*Gamma(S(-1)/3, -c*log(f)/(a + b*x)**S(3))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(3))/x, x), x, Integral(f**(c/(a + b*x)**S(3))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(3))/x**S(2), x), x, Integral(f**(c/(a + b*x)**S(3))/x**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c/(a + b*x)**S(3))/x**S(3), x), x, Integral(f**(c/(a + b*x)**S(3))/x**S(3), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**n)*x**m, x), x, Integral(f**(c*(a + b*x)**n)*x**m, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**n)*x**S(3), x), x, a**S(3)*(-c*(a + b*x)**n*log(f))**(-S(1)/n)*(a + b*x)*Gamma(S(1)/n, -c*(a + b*x)**n*log(f))/(b**S(4)*n) - S(3)*a**S(2)*(-c*(a + b*x)**n*log(f))**(-S(2)/n)*(a + b*x)**S(2)*Gamma(S(2)/n, -c*(a + b*x)**n*log(f))/(b**S(4)*n) + S(3)*a*(-c*(a + b*x)**n*log(f))**(-S(3)/n)*(a + b*x)**S(3)*Gamma(S(3)/n, -c*(a + b*x)**n*log(f))/(b**S(4)*n) - (-c*(a + b*x)**n*log(f))**(-S(4)/n)*(a + b*x)**S(4)*Gamma(S(4)/n, -c*(a + b*x)**n*log(f))/(b**S(4)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**n)*x**S(2), x), x, -a**S(2)*(-c*(a + b*x)**n*log(f))**(-S(1)/n)*(a + b*x)*Gamma(S(1)/n, -c*(a + b*x)**n*log(f))/(b**S(3)*n) + S(2)*a*(-c*(a + b*x)**n*log(f))**(-S(2)/n)*(a + b*x)**S(2)*Gamma(S(2)/n, -c*(a + b*x)**n*log(f))/(b**S(3)*n) - (-c*(a + b*x)**n*log(f))**(-S(3)/n)*(a + b*x)**S(3)*Gamma(S(3)/n, -c*(a + b*x)**n*log(f))/(b**S(3)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**n)*x, x), x, a*(-c*(a + b*x)**n*log(f))**(-S(1)/n)*(a + b*x)*Gamma(S(1)/n, -c*(a + b*x)**n*log(f))/(b**S(2)*n) - (-c*(a + b*x)**n*log(f))**(-S(2)/n)*(a + b*x)**S(2)*Gamma(S(2)/n, -c*(a + b*x)**n*log(f))/(b**S(2)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**n), x), x, (-c*(a + b*x)**n*log(f))**(-S(1)/n)*(-a - b*x)*Gamma(S(1)/n, -c*(a + b*x)**n*log(f))/(b*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**n)/x, x), x, Integral(f**(c*(a + b*x)**n)/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**n)/x**S(2), x), x, Integral(f**(c*(a + b*x)**n)/x**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(c*(a + b*x)**n)/x**S(3), x), x, Integral(f**(c*(a + b*x)**n)/x**S(3), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**m, x), x, -F**a*(-b*(c + d*x)**S(2)*log(F))**(-m/S(2) + S(-1)/2)*(c + d*x)**(m + S(1))*Gamma(m/S(2) + S(1)/2, -b*(c + d*x)**S(2)*log(F))/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(11), x), x, -F**a*Gamma(S(6), -b*(c + d*x)**S(2)*log(F))/(S(2)*b**S(6)*d*log(F)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(9), x), x, F**a*Gamma(S(5), -b*(c + d*x)**S(2)*log(F))/(S(2)*b**S(5)*d*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(7), x), x, F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(6)/(S(2)*b*d*log(F)) - S(3)*F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(4)/(S(2)*b**S(2)*d*log(F)**S(2)) + S(3)*F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(2)/(b**S(3)*d*log(F)**S(3)) - S(3)*F**(a + b*(c + d*x)**S(2))/(b**S(4)*d*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(5), x), x, F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(4)/(S(2)*b*d*log(F)) - F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(2)/(b**S(2)*d*log(F)**S(2)) + F**(a + b*(c + d*x)**S(2))/(b**S(3)*d*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(3), x), x, F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(2)/(S(2)*b*d*log(F)) - F**(a + b*(c + d*x)**S(2))/(S(2)*b**S(2)*d*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x), x), x, F**(a + b*(c + d*x)**S(2))/(S(2)*b*d*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x), x), x, F**a*Ei(b*(c + d*x)**S(2)*log(F))/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(3), x), x, F**a*b*log(F)*Ei(b*(c + d*x)**S(2)*log(F))/(S(2)*d) - F**(a + b*(c + d*x)**S(2))/(S(2)*d*(c + d*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(5), x), x, F**a*b**S(2)*log(F)**S(2)*Ei(b*(c + d*x)**S(2)*log(F))/(S(4)*d) - F**(a + b*(c + d*x)**S(2))*b*log(F)/(S(4)*d*(c + d*x)**S(2)) - F**(a + b*(c + d*x)**S(2))/(S(4)*d*(c + d*x)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(7), x), x, F**a*b**S(3)*log(F)**S(3)*Ei(b*(c + d*x)**S(2)*log(F))/(S(12)*d) - F**(a + b*(c + d*x)**S(2))*b**S(2)*log(F)**S(2)/(S(12)*d*(c + d*x)**S(2)) - F**(a + b*(c + d*x)**S(2))*b*log(F)/(S(12)*d*(c + d*x)**S(4)) - F**(a + b*(c + d*x)**S(2))/(S(6)*d*(c + d*x)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(9), x), x, -F**a*b**S(4)*Gamma(S(-4), -b*(c + d*x)**S(2)*log(F))*log(F)**S(4)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(11), x), x, F**a*b**S(5)*Gamma(S(-5), -b*(c + d*x)**S(2)*log(F))*log(F)**S(5)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(12), x), x, -F**a*(c + d*x)**S(13)*Gamma(S(13)/2, -b*(c + d*x)**S(2)*log(F))/(S(2)*d*(-b*(c + d*x)**S(2)*log(F))**(S(13)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(10), x), x, -F**a*(c + d*x)**S(11)*Gamma(S(11)/2, -b*(c + d*x)**S(2)*log(F))/(S(2)*d*(-b*(c + d*x)**S(2)*log(F))**(S(11)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(8), x), x, S(105)*sqrt(pi)*F**a*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(32)*b**(S(9)/2)*d*log(F)**(S(9)/2)) + F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(7)/(S(2)*b*d*log(F)) - S(7)*F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(5)/(S(4)*b**S(2)*d*log(F)**S(2)) + S(35)*F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(3)/(S(8)*b**S(3)*d*log(F)**S(3)) - S(105)*F**(a + b*(c + d*x)**S(2))*(c + d*x)/(S(16)*b**S(4)*d*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(6), x), x, -S(15)*sqrt(pi)*F**a*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(16)*b**(S(7)/2)*d*log(F)**(S(7)/2)) + F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(5)/(S(2)*b*d*log(F)) - S(5)*F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(3)/(S(4)*b**S(2)*d*log(F)**S(2)) + S(15)*F**(a + b*(c + d*x)**S(2))*(c + d*x)/(S(8)*b**S(3)*d*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(4), x), x, S(3)*sqrt(pi)*F**a*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(8)*b**(S(5)/2)*d*log(F)**(S(5)/2)) + F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(3)/(S(2)*b*d*log(F)) - S(3)*F**(a + b*(c + d*x)**S(2))*(c + d*x)/(S(4)*b**S(2)*d*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(c + d*x)**S(2), x), x, -sqrt(pi)*F**a*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(4)*b**(S(3)/2)*d*log(F)**(S(3)/2)) + F**(a + b*(c + d*x)**S(2))*(c + d*x)/(S(2)*b*d*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2)), x), x, sqrt(pi)*F**a*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(2)*sqrt(b)*d*sqrt(log(F))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(2), x), x, sqrt(pi)*F**a*sqrt(b)*sqrt(log(F))*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/d - F**(a + b*(c + d*x)**S(2))/(d*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(4), x), x, S(2)*sqrt(pi)*F**a*b**(S(3)/2)*log(F)**(S(3)/2)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(3)*d) - S(2)*F**(a + b*(c + d*x)**S(2))*b*log(F)/(S(3)*d*(c + d*x)) - F**(a + b*(c + d*x)**S(2))/(S(3)*d*(c + d*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(6), x), x, S(4)*sqrt(pi)*F**a*b**(S(5)/2)*log(F)**(S(5)/2)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(15)*d) - S(4)*F**(a + b*(c + d*x)**S(2))*b**S(2)*log(F)**S(2)/(S(15)*d*(c + d*x)) - S(2)*F**(a + b*(c + d*x)**S(2))*b*log(F)/(S(15)*d*(c + d*x)**S(3)) - F**(a + b*(c + d*x)**S(2))/(S(5)*d*(c + d*x)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(8), x), x, S(8)*sqrt(pi)*F**a*b**(S(7)/2)*log(F)**(S(7)/2)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(105)*d) - S(8)*F**(a + b*(c + d*x)**S(2))*b**S(3)*log(F)**S(3)/(S(105)*d*(c + d*x)) - S(4)*F**(a + b*(c + d*x)**S(2))*b**S(2)*log(F)**S(2)/(S(105)*d*(c + d*x)**S(3)) - S(2)*F**(a + b*(c + d*x)**S(2))*b*log(F)/(S(35)*d*(c + d*x)**S(5)) - F**(a + b*(c + d*x)**S(2))/(S(7)*d*(c + d*x)**S(7)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(10), x), x, -F**a*(-b*(c + d*x)**S(2)*log(F))**(S(9)/2)*Gamma(S(-9)/2, -b*(c + d*x)**S(2)*log(F))/(S(2)*d*(c + d*x)**S(9)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(c + d*x)**S(12), x), x, -F**a*(-b*(c + d*x)**S(2)*log(F))**(S(11)/2)*Gamma(S(-11)/2, -b*(c + d*x)**S(2)*log(F))/(S(2)*d*(c + d*x)**S(11)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))*(c + d*x)**m, x), x, -F**a*(-b*(c + d*x)**S(3)*log(F))**(-m/S(3) + S(-1)/3)*(c + d*x)**(m + S(1))*Gamma(m/S(3) + S(1)/3, -b*(c + d*x)**S(3)*log(F))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(17), x), x, -F**a*Gamma(S(6), -b*(c + d*x)**S(3)*log(F))/(S(3)*b**S(6)*d*log(F)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(14), x), x, F**a*Gamma(S(5), -b*(c + d*x)**S(3)*log(F))/(S(3)*b**S(5)*d*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(11), x), x, F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(9)/(S(3)*b*d*log(F)) - F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(6)/(b**S(2)*d*log(F)**S(2)) + S(2)*F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(3)/(b**S(3)*d*log(F)**S(3)) - S(2)*F**(a + b*(c + d*x)**S(3))/(b**S(4)*d*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(8), x), x, F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(6)/(S(3)*b*d*log(F)) - S(2)*F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(3)/(S(3)*b**S(2)*d*log(F)**S(2)) + S(2)*F**(a + b*(c + d*x)**S(3))/(S(3)*b**S(3)*d*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(5), x), x, F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(3)/(S(3)*b*d*log(F)) - F**(a + b*(c + d*x)**S(3))/(S(3)*b**S(2)*d*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(2), x), x, F**(a + b*(c + d*x)**S(3))/(S(3)*b*d*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))/(c + d*x), x), x, F**a*Ei(b*(c + d*x)**S(3)*log(F))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))/(c + d*x)**S(4), x), x, F**a*b*log(F)*Ei(b*(c + d*x)**S(3)*log(F))/(S(3)*d) - F**(a + b*(c + d*x)**S(3))/(S(3)*d*(c + d*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))/(c + d*x)**S(7), x), x, F**a*b**S(2)*log(F)**S(2)*Ei(b*(c + d*x)**S(3)*log(F))/(S(6)*d) - F**(a + b*(c + d*x)**S(3))*b*log(F)/(S(6)*d*(c + d*x)**S(3)) - F**(a + b*(c + d*x)**S(3))/(S(6)*d*(c + d*x)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))/(c + d*x)**S(10), x), x, F**a*b**S(3)*log(F)**S(3)*Ei(b*(c + d*x)**S(3)*log(F))/(S(18)*d) - F**(a + b*(c + d*x)**S(3))*b**S(2)*log(F)**S(2)/(S(18)*d*(c + d*x)**S(3)) - F**(a + b*(c + d*x)**S(3))*b*log(F)/(S(18)*d*(c + d*x)**S(6)) - F**(a + b*(c + d*x)**S(3))/(S(9)*d*(c + d*x)**S(9)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))/(c + d*x)**S(13), x), x, -F**a*b**S(4)*Gamma(S(-4), -b*(c + d*x)**S(3)*log(F))*log(F)**S(4)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))/(c + d*x)**S(16), x), x, F**a*b**S(5)*Gamma(S(-5), -b*(c + d*x)**S(3)*log(F))*log(F)**S(5)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))*(c + d*x)**S(3), x), x, -F**a*(c + d*x)**S(4)*Gamma(S(4)/3, -b*(c + d*x)**S(3)*log(F))/(S(3)*d*(-b*(c + d*x)**S(3)*log(F))**(S(4)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))*(c + d*x), x), x, -F**a*(c + d*x)**S(2)*Gamma(S(2)/3, -b*(c + d*x)**S(3)*log(F))/(S(3)*d*(-b*(c + d*x)**S(3)*log(F))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3)), x), x, -F**a*(c + d*x)*Gamma(S(1)/3, -b*(c + d*x)**S(3)*log(F))/(S(3)*d*(-b*(c + d*x)**S(3)*log(F))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))/(c + d*x)**S(2), x), x, -F**a*(-b*(c + d*x)**S(3)*log(F))**(S(1)/3)*Gamma(S(-1)/3, -b*(c + d*x)**S(3)*log(F))/(S(3)*d*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))/(c + d*x)**S(3), x), x, -F**a*(-b*(c + d*x)**S(3)*log(F))**(S(2)/3)*Gamma(S(-2)/3, -b*(c + d*x)**S(3)*log(F))/(S(3)*d*(c + d*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(3))/(c + d*x)**S(5), x), x, -F**a*(-b*(c + d*x)**S(3)*log(F))**(S(4)/3)*Gamma(S(-4)/3, -b*(c + d*x)**S(3)*log(F))/(S(3)*d*(c + d*x)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*sqrt(c + d*x)), x), x, S(2)*f**(a + b*sqrt(c + d*x))*sqrt(c + d*x)/(b*d*log(f)) - S(2)*f**(a + b*sqrt(c + d*x))/(b**S(2)*d*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*(c + d*x)**(S(1)/3)), x), x, S(3)*f**(a + b*(c + d*x)**(S(1)/3))*(c + d*x)**(S(2)/3)/(b*d*log(f)) - S(6)*f**(a + b*(c + d*x)**(S(1)/3))*(c + d*x)**(S(1)/3)/(b**S(2)*d*log(f)**S(2)) + S(6)*f**(a + b*(c + d*x)**(S(1)/3))/(b**S(3)*d*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))*(c + d*x)**m, x), x, F**a*(-b*log(F)/(c + d*x))**(m + S(1))*(c + d*x)**(m + S(1))*Gamma(-m + S(-1), -b*log(F)/(c + d*x))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))*(c + d*x)**S(4), x), x, -F**a*b**S(5)*Gamma(S(-5), -b*log(F)/(c + d*x))*log(F)**S(5)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))*(c + d*x)**S(3), x), x, F**a*b**S(4)*Gamma(S(-4), -b*log(F)/(c + d*x))*log(F)**S(4)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))*(c + d*x)**S(2), x), x, -F**a*b**S(3)*log(F)**S(3)*Ei(b*log(F)/(c + d*x))/(S(6)*d) + F**(a + b/(c + d*x))*b**S(2)*(c + d*x)*log(F)**S(2)/(S(6)*d) + F**(a + b/(c + d*x))*b*(c + d*x)**S(2)*log(F)/(S(6)*d) + F**(a + b/(c + d*x))*(c + d*x)**S(3)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))*(c + d*x), x), x, -F**a*b**S(2)*log(F)**S(2)*Ei(b*log(F)/(c + d*x))/(S(2)*d) + F**(a + b/(c + d*x))*b*(c + d*x)*log(F)/(S(2)*d) + F**(a + b/(c + d*x))*(c + d*x)**S(2)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)), x), x, -F**a*b*log(F)*Ei(b*log(F)/(c + d*x))/d + F**(a + b/(c + d*x))*(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(c + d*x), x), x, -F**a*Ei(b*log(F)/(c + d*x))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(c + d*x)**S(2), x), x, -F**(a + b/(c + d*x))/(b*d*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(c + d*x)**S(3), x), x, -F**(a + b/(c + d*x))/(b*d*(c + d*x)*log(F)) + F**(a + b/(c + d*x))/(b**S(2)*d*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(c + d*x)**S(4), x), x, -F**(a + b/(c + d*x))/(b*d*(c + d*x)**S(2)*log(F)) + S(2)*F**(a + b/(c + d*x))/(b**S(2)*d*(c + d*x)*log(F)**S(2)) - S(2)*F**(a + b/(c + d*x))/(b**S(3)*d*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(c + d*x)**S(5), x), x, -F**(a + b/(c + d*x))/(b*d*(c + d*x)**S(3)*log(F)) + S(3)*F**(a + b/(c + d*x))/(b**S(2)*d*(c + d*x)**S(2)*log(F)**S(2)) - S(6)*F**(a + b/(c + d*x))/(b**S(3)*d*(c + d*x)*log(F)**S(3)) + S(6)*F**(a + b/(c + d*x))/(b**S(4)*d*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(c + d*x)**S(6), x), x, -F**a*Gamma(S(5), -b*log(F)/(c + d*x))/(b**S(5)*d*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(c + d*x)**S(7), x), x, F**a*Gamma(S(6), -b*log(F)/(c + d*x))/(b**S(6)*d*log(F)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**m, x), x, F**a*(-b*log(F)/(c + d*x)**S(2))**(m/S(2) + S(1)/2)*(c + d*x)**(m + S(1))*Gamma(-m/S(2) + S(-1)/2, -b*log(F)/(c + d*x)**S(2))/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(9), x), x, -F**a*b**S(5)*Gamma(S(-5), -b*log(F)/(c + d*x)**S(2))*log(F)**S(5)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(7), x), x, F**a*b**S(4)*Gamma(S(-4), -b*log(F)/(c + d*x)**S(2))*log(F)**S(4)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(5), x), x, -F**a*b**S(3)*log(F)**S(3)*Ei(b*log(F)/(c + d*x)**S(2))/(S(12)*d) + F**(a + b/(c + d*x)**S(2))*b**S(2)*(c + d*x)**S(2)*log(F)**S(2)/(S(12)*d) + F**(a + b/(c + d*x)**S(2))*b*(c + d*x)**S(4)*log(F)/(S(12)*d) + F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(6)/(S(6)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(3), x), x, -F**a*b**S(2)*log(F)**S(2)*Ei(b*log(F)/(c + d*x)**S(2))/(S(4)*d) + F**(a + b/(c + d*x)**S(2))*b*(c + d*x)**S(2)*log(F)/(S(4)*d) + F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(4)/(S(4)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x), x), x, -F**a*b*log(F)*Ei(b*log(F)/(c + d*x)**S(2))/(S(2)*d) + F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(2)/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x), x), x, -F**a*Ei(b*log(F)/(c + d*x)**S(2))/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(3), x), x, -F**(a + b/(c + d*x)**S(2))/(S(2)*b*d*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(5), x), x, -F**(a + b/(c + d*x)**S(2))/(S(2)*b*d*(c + d*x)**S(2)*log(F)) + F**(a + b/(c + d*x)**S(2))/(S(2)*b**S(2)*d*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(7), x), x, -F**(a + b/(c + d*x)**S(2))/(S(2)*b*d*(c + d*x)**S(4)*log(F)) + F**(a + b/(c + d*x)**S(2))/(b**S(2)*d*(c + d*x)**S(2)*log(F)**S(2)) - F**(a + b/(c + d*x)**S(2))/(b**S(3)*d*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(9), x), x, -F**(a + b/(c + d*x)**S(2))/(S(2)*b*d*(c + d*x)**S(6)*log(F)) + S(3)*F**(a + b/(c + d*x)**S(2))/(S(2)*b**S(2)*d*(c + d*x)**S(4)*log(F)**S(2)) - S(3)*F**(a + b/(c + d*x)**S(2))/(b**S(3)*d*(c + d*x)**S(2)*log(F)**S(3)) + S(3)*F**(a + b/(c + d*x)**S(2))/(b**S(4)*d*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(11), x), x, -F**a*Gamma(S(5), -b*log(F)/(c + d*x)**S(2))/(S(2)*b**S(5)*d*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(13), x), x, F**a*Gamma(S(6), -b*log(F)/(c + d*x)**S(2))/(S(2)*b**S(6)*d*log(F)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(10), x), x, F**a*(-b*log(F)/(c + d*x)**S(2))**(S(11)/2)*(c + d*x)**S(11)*Gamma(S(-11)/2, -b*log(F)/(c + d*x)**S(2))/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(8), x), x, F**a*(-b*log(F)/(c + d*x)**S(2))**(S(9)/2)*(c + d*x)**S(9)*Gamma(S(-9)/2, -b*log(F)/(c + d*x)**S(2))/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(6), x), x, -S(8)*sqrt(pi)*F**a*b**(S(7)/2)*log(F)**(S(7)/2)*erfi(sqrt(b)*sqrt(log(F))/(c + d*x))/(S(105)*d) + S(8)*F**(a + b/(c + d*x)**S(2))*b**S(3)*(c + d*x)*log(F)**S(3)/(S(105)*d) + S(4)*F**(a + b/(c + d*x)**S(2))*b**S(2)*(c + d*x)**S(3)*log(F)**S(2)/(S(105)*d) + S(2)*F**(a + b/(c + d*x)**S(2))*b*(c + d*x)**S(5)*log(F)/(S(35)*d) + F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(7)/(S(7)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(4), x), x, -S(4)*sqrt(pi)*F**a*b**(S(5)/2)*log(F)**(S(5)/2)*erfi(sqrt(b)*sqrt(log(F))/(c + d*x))/(S(15)*d) + S(4)*F**(a + b/(c + d*x)**S(2))*b**S(2)*(c + d*x)*log(F)**S(2)/(S(15)*d) + S(2)*F**(a + b/(c + d*x)**S(2))*b*(c + d*x)**S(3)*log(F)/(S(15)*d) + F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(5)/(S(5)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(2), x), x, -S(2)*sqrt(pi)*F**a*b**(S(3)/2)*log(F)**(S(3)/2)*erfi(sqrt(b)*sqrt(log(F))/(c + d*x))/(S(3)*d) + S(2)*F**(a + b/(c + d*x)**S(2))*b*(c + d*x)*log(F)/(S(3)*d) + F**(a + b/(c + d*x)**S(2))*(c + d*x)**S(3)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2)), x), x, -sqrt(pi)*F**a*sqrt(b)*sqrt(log(F))*erfi(sqrt(b)*sqrt(log(F))/(c + d*x))/d + F**(a + b/(c + d*x)**S(2))*(c + d*x)/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(2), x), x, -sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(log(F))/(c + d*x))/(S(2)*sqrt(b)*d*sqrt(log(F))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(4), x), x, sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(log(F))/(c + d*x))/(S(4)*b**(S(3)/2)*d*log(F)**(S(3)/2)) - F**(a + b/(c + d*x)**S(2))/(S(2)*b*d*(c + d*x)*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(6), x), x, -S(3)*sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(log(F))/(c + d*x))/(S(8)*b**(S(5)/2)*d*log(F)**(S(5)/2)) - F**(a + b/(c + d*x)**S(2))/(S(2)*b*d*(c + d*x)**S(3)*log(F)) + S(3)*F**(a + b/(c + d*x)**S(2))/(S(4)*b**S(2)*d*(c + d*x)*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(8), x), x, S(15)*sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(log(F))/(c + d*x))/(S(16)*b**(S(7)/2)*d*log(F)**(S(7)/2)) - F**(a + b/(c + d*x)**S(2))/(S(2)*b*d*(c + d*x)**S(5)*log(F)) + S(5)*F**(a + b/(c + d*x)**S(2))/(S(4)*b**S(2)*d*(c + d*x)**S(3)*log(F)**S(2)) - S(15)*F**(a + b/(c + d*x)**S(2))/(S(8)*b**S(3)*d*(c + d*x)*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(10), x), x, -S(105)*sqrt(pi)*F**a*erfi(sqrt(b)*sqrt(log(F))/(c + d*x))/(S(32)*b**(S(9)/2)*d*log(F)**(S(9)/2)) - F**(a + b/(c + d*x)**S(2))/(S(2)*b*d*(c + d*x)**S(7)*log(F)) + S(7)*F**(a + b/(c + d*x)**S(2))/(S(4)*b**S(2)*d*(c + d*x)**S(5)*log(F)**S(2)) - S(35)*F**(a + b/(c + d*x)**S(2))/(S(8)*b**S(3)*d*(c + d*x)**S(3)*log(F)**S(3)) + S(105)*F**(a + b/(c + d*x)**S(2))/(S(16)*b**S(4)*d*(c + d*x)*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(12), x), x, F**a*Gamma(S(11)/2, -b*log(F)/(c + d*x)**S(2))/(S(2)*d*(-b*log(F)/(c + d*x)**S(2))**(S(11)/2)*(c + d*x)**S(11)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(2))/(c + d*x)**S(14), x), x, F**a*Gamma(S(13)/2, -b*log(F)/(c + d*x)**S(2))/(S(2)*d*(-b*log(F)/(c + d*x)**S(2))**(S(13)/2)*(c + d*x)**S(13)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))*(c + d*x)**m, x), x, F**a*(-b*log(F)/(c + d*x)**S(3))**(m/S(3) + S(1)/3)*(c + d*x)**(m + S(1))*Gamma(-m/S(3) + S(-1)/3, -b*log(F)/(c + d*x)**S(3))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))*(c + d*x)**S(14), x), x, -F**a*b**S(5)*Gamma(S(-5), -b*log(F)/(c + d*x)**S(3))*log(F)**S(5)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))*(c + d*x)**S(11), x), x, F**a*b**S(4)*Gamma(S(-4), -b*log(F)/(c + d*x)**S(3))*log(F)**S(4)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))*(c + d*x)**S(8), x), x, -F**a*b**S(3)*log(F)**S(3)*Ei(b*log(F)/(c + d*x)**S(3))/(S(18)*d) + F**(a + b/(c + d*x)**S(3))*b**S(2)*(c + d*x)**S(3)*log(F)**S(2)/(S(18)*d) + F**(a + b/(c + d*x)**S(3))*b*(c + d*x)**S(6)*log(F)/(S(18)*d) + F**(a + b/(c + d*x)**S(3))*(c + d*x)**S(9)/(S(9)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))*(c + d*x)**S(5), x), x, -F**a*b**S(2)*log(F)**S(2)*Ei(b*log(F)/(c + d*x)**S(3))/(S(6)*d) + F**(a + b/(c + d*x)**S(3))*b*(c + d*x)**S(3)*log(F)/(S(6)*d) + F**(a + b/(c + d*x)**S(3))*(c + d*x)**S(6)/(S(6)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))*(c + d*x)**S(2), x), x, -F**a*b*log(F)*Ei(b*log(F)/(c + d*x)**S(3))/(S(3)*d) + F**(a + b/(c + d*x)**S(3))*(c + d*x)**S(3)/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x), x), x, -F**a*Ei(b*log(F)/(c + d*x)**S(3))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x)**S(4), x), x, -F**(a + b/(c + d*x)**S(3))/(S(3)*b*d*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x)**S(7), x), x, -F**(a + b/(c + d*x)**S(3))/(S(3)*b*d*(c + d*x)**S(3)*log(F)) + F**(a + b/(c + d*x)**S(3))/(S(3)*b**S(2)*d*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x)**S(10), x), x, -F**(a + b/(c + d*x)**S(3))/(S(3)*b*d*(c + d*x)**S(6)*log(F)) + S(2)*F**(a + b/(c + d*x)**S(3))/(S(3)*b**S(2)*d*(c + d*x)**S(3)*log(F)**S(2)) - S(2)*F**(a + b/(c + d*x)**S(3))/(S(3)*b**S(3)*d*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x)**S(13), x), x, -F**(a + b/(c + d*x)**S(3))/(S(3)*b*d*(c + d*x)**S(9)*log(F)) + F**(a + b/(c + d*x)**S(3))/(b**S(2)*d*(c + d*x)**S(6)*log(F)**S(2)) - S(2)*F**(a + b/(c + d*x)**S(3))/(b**S(3)*d*(c + d*x)**S(3)*log(F)**S(3)) + S(2)*F**(a + b/(c + d*x)**S(3))/(b**S(4)*d*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x)**S(16), x), x, -F**a*Gamma(S(5), -b*log(F)/(c + d*x)**S(3))/(S(3)*b**S(5)*d*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x)**S(19), x), x, F**a*Gamma(S(6), -b*log(F)/(c + d*x)**S(3))/(S(3)*b**S(6)*d*log(F)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))*(c + d*x)**S(3), x), x, F**a*(-b*log(F)/(c + d*x)**S(3))**(S(4)/3)*(c + d*x)**S(4)*Gamma(S(-4)/3, -b*log(F)/(c + d*x)**S(3))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))*(c + d*x), x), x, F**a*(-b*log(F)/(c + d*x)**S(3))**(S(2)/3)*(c + d*x)**S(2)*Gamma(S(-2)/3, -b*log(F)/(c + d*x)**S(3))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3)), x), x, F**a*(-b*log(F)/(c + d*x)**S(3))**(S(1)/3)*(c + d*x)*Gamma(S(-1)/3, -b*log(F)/(c + d*x)**S(3))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x)**S(2), x), x, F**a*Gamma(S(1)/3, -b*log(F)/(c + d*x)**S(3))/(S(3)*d*(-b*log(F)/(c + d*x)**S(3))**(S(1)/3)*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x)**S(3), x), x, F**a*Gamma(S(2)/3, -b*log(F)/(c + d*x)**S(3))/(S(3)*d*(-b*log(F)/(c + d*x)**S(3))**(S(2)/3)*(c + d*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x)**S(3))/(c + d*x)**S(5), x), x, F**a*Gamma(S(4)/3, -b*log(F)/(c + d*x)**S(3))/(S(3)*d*(-b*log(F)/(c + d*x)**S(3))**(S(4)/3)*(c + d*x)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**m, x), x, -F**a*(-b*(c + d*x)**n*log(F))**(-(m + S(1))/n)*(c + d*x)**(m + S(1))*Gamma((m + S(1))/n, -b*(c + d*x)**n*log(F))/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**S(3), x), x, -F**a*(-b*(c + d*x)**n*log(F))**(-S(4)/n)*(c + d*x)**S(4)*Gamma(S(4)/n, -b*(c + d*x)**n*log(F))/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**S(2), x), x, -F**a*(-b*(c + d*x)**n*log(F))**(-S(3)/n)*(c + d*x)**S(3)*Gamma(S(3)/n, -b*(c + d*x)**n*log(F))/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x), x), x, -F**a*(-b*(c + d*x)**n*log(F))**(-S(2)/n)*(c + d*x)**S(2)*Gamma(S(2)/n, -b*(c + d*x)**n*log(F))/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n), x), x, -F**a*(-b*(c + d*x)**n*log(F))**(-S(1)/n)*(c + d*x)*Gamma(S(1)/n, -b*(c + d*x)**n*log(F))/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)/(c + d*x), x), x, F**a*Ei(b*(c + d*x)**n*log(F))/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)/(c + d*x)**S(2), x), x, -F**a*(-b*(c + d*x)**n*log(F))**(S(1)/n)*Gamma(-S(1)/n, -b*(c + d*x)**n*log(F))/(d*n*(c + d*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)/(c + d*x)**S(3), x), x, -F**a*(-b*(c + d*x)**n*log(F))**(S(2)/n)*Gamma(-S(2)/n, -b*(c + d*x)**n*log(F))/(d*n*(c + d*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)/(c + d*x)**S(4), x), x, -F**a*(-b*(c + d*x)**n*log(F))**(S(3)/n)*Gamma(-S(3)/n, -b*(c + d*x)**n*log(F))/(d*n*(c + d*x)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(S(6)*n + S(-1)), x), x, -F**a*Gamma(S(6), -b*(c + d*x)**n*log(F))/(b**S(6)*d*n*log(F)**S(6)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(S(5)*n + S(-1)), x), x, F**a*Gamma(S(5), -b*(c + d*x)**n*log(F))/(b**S(5)*d*n*log(F)**S(5)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(S(4)*n + S(-1)), x), x, F**(a + b*(c + d*x)**n)*(c + d*x)**(S(3)*n)/(b*d*n*log(F)) - S(3)*F**(a + b*(c + d*x)**n)*(c + d*x)**(S(2)*n)/(b**S(2)*d*n*log(F)**S(2)) + S(6)*F**(a + b*(c + d*x)**n)*(c + d*x)**n/(b**S(3)*d*n*log(F)**S(3)) - S(6)*F**(a + b*(c + d*x)**n)/(b**S(4)*d*n*log(F)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(S(3)*n + S(-1)), x), x, F**(a + b*(c + d*x)**n)*(c + d*x)**(S(2)*n)/(b*d*n*log(F)) - S(2)*F**(a + b*(c + d*x)**n)*(c + d*x)**n/(b**S(2)*d*n*log(F)**S(2)) + S(2)*F**(a + b*(c + d*x)**n)/(b**S(3)*d*n*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(S(2)*n + S(-1)), x), x, F**(a + b*(c + d*x)**n)*(c + d*x)**n/(b*d*n*log(F)) - F**(a + b*(c + d*x)**n)/(b**S(2)*d*n*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(n + S(-1)), x), x, F**(a + b*(c + d*x)**n)/(b*d*n*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)/(c + d*x), x), x, F**a*Ei(b*(c + d*x)**n*log(F))/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(-n + S(-1)), x), x, F**a*b*log(F)*Ei(b*(c + d*x)**n*log(F))/(d*n) - F**(a + b*(c + d*x)**n)*(c + d*x)**(-n)/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(-S(2)*n + S(-1)), x), x, F**a*b**S(2)*log(F)**S(2)*Ei(b*(c + d*x)**n*log(F))/(S(2)*d*n) - F**(a + b*(c + d*x)**n)*b*(c + d*x)**(-n)*log(F)/(S(2)*d*n) - F**(a + b*(c + d*x)**n)*(c + d*x)**(-S(2)*n)/(S(2)*d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(-S(3)*n + S(-1)), x), x, F**a*b**S(3)*log(F)**S(3)*Ei(b*(c + d*x)**n*log(F))/(S(6)*d*n) - F**(a + b*(c + d*x)**n)*b**S(2)*(c + d*x)**(-n)*log(F)**S(2)/(S(6)*d*n) - F**(a + b*(c + d*x)**n)*b*(c + d*x)**(-S(2)*n)*log(F)/(S(6)*d*n) - F**(a + b*(c + d*x)**n)*(c + d*x)**(-S(3)*n)/(S(3)*d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(-S(4)*n + S(-1)), x), x, -F**a*b**S(4)*Gamma(S(-4), -b*(c + d*x)**n*log(F))*log(F)**S(4)/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**n)*(c + d*x)**(-S(5)*n + S(-1)), x), x, F**a*b**S(5)*Gamma(S(-5), -b*(c + d*x)**n*log(F))*log(F)**S(5)/(d*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(c*(a + b*x)**n)*(a + b*x)**(n/S(2) + S(-1)), x), x, sqrt(pi)*erfi(sqrt(c)*(a + b*x)**(n/S(2))*sqrt(log(F)))/(b*sqrt(c)*n*sqrt(log(F))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(-c*(a + b*x)**n)*(a + b*x)**(n/S(2) + S(-1)), x), x, sqrt(pi)*erf(sqrt(c)*(a + b*x)**(n/S(2))*sqrt(log(F)))/(b*sqrt(c)*n*sqrt(log(F))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(e + f*x)**S(5), x), x, sqrt(pi)*F**a*(-c*f + d*e)**S(5)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(2)*sqrt(b)*d**S(6)*sqrt(log(F))) - S(5)*sqrt(pi)*F**a*f**S(2)*(-c*f + d*e)**S(3)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(2)*b**(S(3)/2)*d**S(6)*log(F)**(S(3)/2)) + S(15)*sqrt(pi)*F**a*f**S(4)*(-c*f + d*e)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(8)*b**(S(5)/2)*d**S(6)*log(F)**(S(5)/2)) + F**(a + b*(c + d*x)**S(2))*f**S(5)*(c + d*x)**S(4)/(S(2)*b*d**S(6)*log(F)) + S(5)*F**(a + b*(c + d*x)**S(2))*f**S(4)*(c + d*x)**S(3)*(-c*f + d*e)/(S(2)*b*d**S(6)*log(F)) + S(5)*F**(a + b*(c + d*x)**S(2))*f**S(3)*(c + d*x)**S(2)*(-c*f + d*e)**S(2)/(b*d**S(6)*log(F)) + S(5)*F**(a + b*(c + d*x)**S(2))*f**S(2)*(c + d*x)*(-c*f + d*e)**S(3)/(b*d**S(6)*log(F)) + S(5)*F**(a + b*(c + d*x)**S(2))*f*(-c*f + d*e)**S(4)/(S(2)*b*d**S(6)*log(F)) - F**(a + b*(c + d*x)**S(2))*f**S(5)*(c + d*x)**S(2)/(b**S(2)*d**S(6)*log(F)**S(2)) - S(15)*F**(a + b*(c + d*x)**S(2))*f**S(4)*(c + d*x)*(-c*f + d*e)/(S(4)*b**S(2)*d**S(6)*log(F)**S(2)) - S(5)*F**(a + b*(c + d*x)**S(2))*f**S(3)*(-c*f + d*e)**S(2)/(b**S(2)*d**S(6)*log(F)**S(2)) + F**(a + b*(c + d*x)**S(2))*f**S(5)/(b**S(3)*d**S(6)*log(F)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(e + f*x)**S(4), x), x, sqrt(pi)*F**a*(-c*f + d*e)**S(4)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(2)*sqrt(b)*d**S(5)*sqrt(log(F))) - S(3)*sqrt(pi)*F**a*f**S(2)*(-c*f + d*e)**S(2)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(2)*b**(S(3)/2)*d**S(5)*log(F)**(S(3)/2)) + S(3)*sqrt(pi)*F**a*f**S(4)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(8)*b**(S(5)/2)*d**S(5)*log(F)**(S(5)/2)) + F**(a + b*(c + d*x)**S(2))*f**S(4)*(c + d*x)**S(3)/(S(2)*b*d**S(5)*log(F)) + S(2)*F**(a + b*(c + d*x)**S(2))*f**S(3)*(c + d*x)**S(2)*(-c*f + d*e)/(b*d**S(5)*log(F)) + S(3)*F**(a + b*(c + d*x)**S(2))*f**S(2)*(c + d*x)*(-c*f + d*e)**S(2)/(b*d**S(5)*log(F)) + S(2)*F**(a + b*(c + d*x)**S(2))*f*(-c*f + d*e)**S(3)/(b*d**S(5)*log(F)) - S(3)*F**(a + b*(c + d*x)**S(2))*f**S(4)*(c + d*x)/(S(4)*b**S(2)*d**S(5)*log(F)**S(2)) - S(2)*F**(a + b*(c + d*x)**S(2))*f**S(3)*(-c*f + d*e)/(b**S(2)*d**S(5)*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(e + f*x)**S(3), x), x, sqrt(pi)*F**a*(-c*f + d*e)**S(3)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(2)*sqrt(b)*d**S(4)*sqrt(log(F))) - S(3)*sqrt(pi)*F**a*f**S(2)*(-c*f + d*e)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(4)*b**(S(3)/2)*d**S(4)*log(F)**(S(3)/2)) + F**(a + b*(c + d*x)**S(2))*f**S(3)*(c + d*x)**S(2)/(S(2)*b*d**S(4)*log(F)) + S(3)*F**(a + b*(c + d*x)**S(2))*f**S(2)*(c + d*x)*(-c*f + d*e)/(S(2)*b*d**S(4)*log(F)) + S(3)*F**(a + b*(c + d*x)**S(2))*f*(-c*f + d*e)**S(2)/(S(2)*b*d**S(4)*log(F)) - F**(a + b*(c + d*x)**S(2))*f**S(3)/(S(2)*b**S(2)*d**S(4)*log(F)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(e + f*x)**S(2), x), x, sqrt(pi)*F**a*(-c*f + d*e)**S(2)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(2)*sqrt(b)*d**S(3)*sqrt(log(F))) - sqrt(pi)*F**a*f**S(2)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(4)*b**(S(3)/2)*d**S(3)*log(F)**(S(3)/2)) + F**(a + b*(c + d*x)**S(2))*f**S(2)*(c + d*x)/(S(2)*b*d**S(3)*log(F)) + F**(a + b*(c + d*x)**S(2))*f*(-c*f + d*e)/(b*d**S(3)*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))*(e + f*x), x), x, sqrt(pi)*F**a*(-c*f/S(2) + d*e/S(2))*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(sqrt(b)*d**S(2)*sqrt(log(F))) + F**(a + b*(c + d*x)**S(2))*f/(S(2)*b*d**S(2)*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2)), x), x, sqrt(pi)*F**a*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/(S(2)*sqrt(b)*d*sqrt(log(F))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(e + f*x), x), x, Integral(F**(a + b*(c + d*x)**S(2))/(e + f*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(e + f*x)**S(2), x), x, sqrt(pi)*F**a*sqrt(b)*d*sqrt(log(F))*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/f**S(2) - F**(a + b*(c + d*x)**S(2))/(f*(e + f*x)) - S(2)*b*d*(-c*f + d*e)*log(F)*Integral(F**(a + b*(c + d*x)**S(2))/(e + f*x), x)/f**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*(c + d*x)**S(2))/(e + f*x)**S(3), x), x, -sqrt(pi)*F**a*b**(S(3)/2)*d**S(2)*(-c*f + d*e)*log(F)**(S(3)/2)*erfi(sqrt(b)*(c + d*x)*sqrt(log(F)))/f**S(4) + F**(a + b*(c + d*x)**S(2))*b*d*(-c*f + d*e)*log(F)/(f**S(3)*(e + f*x)) - F**(a + b*(c + d*x)**S(2))/(S(2)*f*(e + f*x)**S(2)) + S(2)*b**S(2)*d**S(2)*(-c*f + d*e)**S(2)*log(F)**S(2)*Integral(F**(a + b*(c + d*x)**S(2))/(e + f*x), x)/f**S(4) + b*d**S(2)*log(F)*Integral(F**(a + b*(c + d*x)**S(2))/(e + f*x), x)/f**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*exp(e*(c + d*x)**S(3)), x), x, -b**S(3)*(c + d*x)**S(4)*Gamma(S(4)/3, -e*(c + d*x)**S(3))/(S(3)*d**S(4)*(-e*(c + d*x)**S(3))**(S(4)/3)) - b**S(2)*(-a*d + b*c)*exp(e*(c + d*x)**S(3))/(d**S(4)*e) - b*(c + d*x)**S(2)*(-a*d + b*c)**S(2)*Gamma(S(2)/3, -e*(c + d*x)**S(3))/(d**S(4)*(-e*(c + d*x)**S(3))**(S(2)/3)) + (c + d*x)*(-a*d + b*c)**S(3)*Gamma(S(1)/3, -e*(c + d*x)**S(3))/(S(3)*d**S(4)*(-e*(c + d*x)**S(3))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*exp(e*(c + d*x)**S(3)), x), x, b**S(2)*exp(e*(c + d*x)**S(3))/(S(3)*d**S(3)*e) + S(2)*b*(c + d*x)**S(2)*(-a*d + b*c)*Gamma(S(2)/3, -e*(c + d*x)**S(3))/(S(3)*d**S(3)*(-e*(c + d*x)**S(3))**(S(2)/3)) - (c + d*x)*(-a*d + b*c)**S(2)*Gamma(S(1)/3, -e*(c + d*x)**S(3))/(S(3)*d**S(3)*(-e*(c + d*x)**S(3))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*exp(e*(c + d*x)**S(3)), x), x, -b*(c + d*x)**S(2)*Gamma(S(2)/3, -e*(c + d*x)**S(3))/(S(3)*d**S(2)*(-e*(c + d*x)**S(3))**(S(2)/3)) + (c + d*x)*(-a*d/S(3) + b*c/S(3))*Gamma(S(1)/3, -e*(c + d*x)**S(3))/(d**S(2)*(-e*(c + d*x)**S(3))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e*(c + d*x)**S(3)), x), x, (-c/S(3) - d*x/S(3))*Gamma(S(1)/3, -e*(c + d*x)**S(3))/(d*(-e*(c + d*x)**S(3))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e*(c + d*x)**S(3))/(a + b*x), x), x, Integral(exp(e*(c + d*x)**S(3))/(a + b*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e*(c + d*x)**S(3))/(a + b*x)**S(2), x), x, -exp(e*(c + d*x)**S(3))/(b*(a + b*x)) - d*e*(c + d*x)**S(2)*Gamma(S(2)/3, -e*(c + d*x)**S(3))/(b**S(2)*(-e*(c + d*x)**S(3))**(S(2)/3)) + S(3)*d*e*(-a*d + b*c)**S(2)*Integral(exp(e*(c + d*x)**S(3))/(a + b*x), x)/b**S(3) - d*e*(c + d*x)*(-a*d + b*c)*Gamma(S(1)/3, -e*(c + d*x)**S(3))/(b**S(3)*(-e*(c + d*x)**S(3))**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(e + f*x), x), x, -F**a*Ei(b*log(F)/(c + d*x))/f + F**(a - b*f/(-c*f + d*e))*Ei(b*d*(e + f*x)*log(F)/((c + d*x)*(-c*f + d*e)))/f, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(e + f*x)**S(2), x), x, F**(a + b/(c + d*x))*d/(f*(-c*f + d*e)) - F**(a + b/(c + d*x))/(f*(e + f*x)) - F**(a - b*f/(-c*f + d*e))*b*d*log(F)*Ei(b*d*(e + f*x)*log(F)/((c + d*x)*(-c*f + d*e)))/(-c*f + d*e)**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(e + f*x)**S(3), x), x, -F**(a + b/(c + d*x))*b*d**S(2)*log(F)/(S(2)*(-c*f + d*e)**S(3)) + F**(a + b/(c + d*x))*b*d*log(F)/(S(2)*(e + f*x)*(-c*f + d*e)**S(2)) + F**(a + b/(c + d*x))*d**S(2)/(S(2)*f*(-c*f + d*e)**S(2)) - F**(a + b/(c + d*x))/(S(2)*f*(e + f*x)**S(2)) + F**(a - b*f/(-c*f + d*e))*b**S(2)*d**S(2)*f*log(F)**S(2)*Ei(b*d*(e + f*x)*log(F)/((c + d*x)*(-c*f + d*e)))/(S(2)*(-c*f + d*e)**S(4)) - F**(a - b*f/(-c*f + d*e))*b*d**S(2)*log(F)*Ei(b*d*(e + f*x)*log(F)/((c + d*x)*(-c*f + d*e)))/(-c*f + d*e)**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b/(c + d*x))/(e + f*x)**S(4), x), x, F**(a + b/(c + d*x))*b**S(2)*d**S(3)*f*log(F)**S(2)/(S(6)*(-c*f + d*e)**S(5)) - F**(a + b/(c + d*x))*b**S(2)*d**S(2)*f*log(F)**S(2)/(S(6)*(e + f*x)*(-c*f + d*e)**S(4)) - S(5)*F**(a + b/(c + d*x))*b*d**S(3)*log(F)/(S(6)*(-c*f + d*e)**S(4)) + S(2)*F**(a + b/(c + d*x))*b*d**S(2)*log(F)/(S(3)*(e + f*x)*(-c*f + d*e)**S(3)) + F**(a + b/(c + d*x))*b*d*log(F)/(S(6)*(e + f*x)**S(2)*(-c*f + d*e)**S(2)) + F**(a + b/(c + d*x))*d**S(3)/(S(3)*f*(-c*f + d*e)**S(3)) - F**(a + b/(c + d*x))/(S(3)*f*(e + f*x)**S(3)) - F**(a - b*f/(-c*f + d*e))*b**S(3)*d**S(3)*f**S(2)*log(F)**S(3)*Ei(b*d*(e + f*x)*log(F)/((c + d*x)*(-c*f + d*e)))/(S(6)*(-c*f + d*e)**S(6)) + F**(a - b*f/(-c*f + d*e))*b**S(2)*d**S(3)*f*log(F)**S(2)*Ei(b*d*(e + f*x)*log(F)/((c + d*x)*(-c*f + d*e)))/(-c*f + d*e)**S(5) - F**(a - b*f/(-c*f + d*e))*b*d**S(3)*log(F)*Ei(b*d*(e + f*x)*log(F)/((c + d*x)*(-c*f + d*e)))/(-c*f + d*e)**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(4)*exp(e/(c + d*x)), x), x, -b**S(4)*e**S(5)*Gamma(S(-5), -e/(c + d*x))/d**S(5) - S(4)*b**S(3)*e**S(4)*(-a*d + b*c)*Gamma(S(-4), -e/(c + d*x))/d**S(5) - b**S(2)*e**S(3)*(-a*d + b*c)**S(2)*Ei(e/(c + d*x))/d**S(5) + b**S(2)*e**S(2)*(c + d*x)*(-a*d + b*c)**S(2)*exp(e/(c + d*x))/d**S(5) + b**S(2)*e*(c + d*x)**S(2)*(-a*d + b*c)**S(2)*exp(e/(c + d*x))/d**S(5) + S(2)*b**S(2)*(c + d*x)**S(3)*(-a*d + b*c)**S(2)*exp(e/(c + d*x))/d**S(5) + S(2)*b*e**S(2)*(-a*d + b*c)**S(3)*Ei(e/(c + d*x))/d**S(5) - S(2)*b*e*(c + d*x)*(-a*d + b*c)**S(3)*exp(e/(c + d*x))/d**S(5) - S(2)*b*(c + d*x)**S(2)*(-a*d + b*c)**S(3)*exp(e/(c + d*x))/d**S(5) - e*(-a*d + b*c)**S(4)*Ei(e/(c + d*x))/d**S(5) + (c + d*x)*(-a*d + b*c)**S(4)*exp(e/(c + d*x))/d**S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*exp(e/(c + d*x)), x), x, b**S(3)*e**S(4)*Gamma(S(-4), -e/(c + d*x))/d**S(4) + b**S(2)*e**S(3)*(-a*d + b*c)*Ei(e/(c + d*x))/(S(2)*d**S(4)) - b**S(2)*e**S(2)*(c + d*x)*(-a*d + b*c)*exp(e/(c + d*x))/(S(2)*d**S(4)) - b**S(2)*e*(c + d*x)**S(2)*(-a*d + b*c)*exp(e/(c + d*x))/(S(2)*d**S(4)) - b**S(2)*(c + d*x)**S(3)*(-a*d + b*c)*exp(e/(c + d*x))/d**S(4) - S(3)*b*e**S(2)*(-a*d + b*c)**S(2)*Ei(e/(c + d*x))/(S(2)*d**S(4)) + S(3)*b*e*(c + d*x)*(-a*d + b*c)**S(2)*exp(e/(c + d*x))/(S(2)*d**S(4)) + S(3)*b*(c + d*x)**S(2)*(-a*d + b*c)**S(2)*exp(e/(c + d*x))/(S(2)*d**S(4)) + e*(-a*d + b*c)**S(3)*Ei(e/(c + d*x))/d**S(4) - (c + d*x)*(-a*d + b*c)**S(3)*exp(e/(c + d*x))/d**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*exp(e/(c + d*x)), x), x, -b**S(2)*e**S(3)*Ei(e/(c + d*x))/(S(6)*d**S(3)) + b**S(2)*e**S(2)*(c + d*x)*exp(e/(c + d*x))/(S(6)*d**S(3)) + b**S(2)*e*(c + d*x)**S(2)*exp(e/(c + d*x))/(S(6)*d**S(3)) + b**S(2)*(c + d*x)**S(3)*exp(e/(c + d*x))/(S(3)*d**S(3)) + b*e**S(2)*(-a*d + b*c)*Ei(e/(c + d*x))/d**S(3) - b*e*(c + d*x)*(-a*d + b*c)*exp(e/(c + d*x))/d**S(3) - b*(c + d*x)**S(2)*(-a*d + b*c)*exp(e/(c + d*x))/d**S(3) - e*(-a*d + b*c)**S(2)*Ei(e/(c + d*x))/d**S(3) + (c + d*x)*(-a*d + b*c)**S(2)*exp(e/(c + d*x))/d**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*exp(e/(c + d*x)), x), x, -b*e**S(2)*Ei(e/(c + d*x))/(S(2)*d**S(2)) + b*e*(c + d*x)*exp(e/(c + d*x))/(S(2)*d**S(2)) + b*(c + d*x)**S(2)*exp(e/(c + d*x))/(S(2)*d**S(2)) + e*(-a*d + b*c)*Ei(e/(c + d*x))/d**S(2) + (c + d*x)*(a*d - b*c)*exp(e/(c + d*x))/d**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x)), x), x, -e*Ei(e/(c + d*x))/d + (c + d*x)*exp(e/(c + d*x))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x))/(a + b*x), x), x, exp(b*e/(-a*d + b*c))*Ei(-d*e*(a + b*x)/((c + d*x)*(-a*d + b*c)))/b - Ei(e/(c + d*x))/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x))/(a + b*x)**S(2), x), x, -d*e*exp(b*e/(-a*d + b*c))*Ei(-d*e*(a + b*x)/((c + d*x)*(-a*d + b*c)))/(-a*d + b*c)**S(2) - d*exp(e/(c + d*x))/(b*(-a*d + b*c)) - exp(e/(c + d*x))/(b*(a + b*x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x))/(a + b*x)**S(3), x), x, b*d**S(2)*e**S(2)*exp(b*e/(-a*d + b*c))*Ei(-d*e*(a + b*x)/((c + d*x)*(-a*d + b*c)))/(S(2)*(-a*d + b*c)**S(4)) + d**S(2)*e*exp(e/(c + d*x))/(S(2)*(-a*d + b*c)**S(3)) + d**S(2)*e*exp(b*e/(-a*d + b*c))*Ei(-d*e*(a + b*x)/((c + d*x)*(-a*d + b*c)))/(-a*d + b*c)**S(3) + d*e*exp(e/(c + d*x))/(S(2)*(a + b*x)*(-a*d + b*c)**S(2)) + d**S(2)*exp(e/(c + d*x))/(S(2)*b*(-a*d + b*c)**S(2)) - exp(e/(c + d*x))/(S(2)*b*(a + b*x)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*exp(e/(c + d*x)**S(2)), x), x, -b**S(3)*e**S(2)*Ei(e/(c + d*x)**S(2))/(S(4)*d**S(4)) + b**S(3)*e*(c + d*x)**S(2)*exp(e/(c + d*x)**S(2))/(S(4)*d**S(4)) + b**S(3)*(c + d*x)**S(4)*exp(e/(c + d*x)**S(2))/(S(4)*d**S(4)) + S(2)*sqrt(pi)*b**S(2)*e**(S(3)/2)*(-a*d + b*c)*erfi(sqrt(e)/(c + d*x))/d**S(4) - S(2)*b**S(2)*e*(c + d*x)*(-a*d + b*c)*exp(e/(c + d*x)**S(2))/d**S(4) - b**S(2)*(c + d*x)**S(3)*(-a*d + b*c)*exp(e/(c + d*x)**S(2))/d**S(4) - S(3)*b*e*(-a*d + b*c)**S(2)*Ei(e/(c + d*x)**S(2))/(S(2)*d**S(4)) + S(3)*b*(c + d*x)**S(2)*(-a*d + b*c)**S(2)*exp(e/(c + d*x)**S(2))/(S(2)*d**S(4)) + sqrt(pi)*sqrt(e)*(-a*d + b*c)**S(3)*erfi(sqrt(e)/(c + d*x))/d**S(4) - (c + d*x)*(-a*d + b*c)**S(3)*exp(e/(c + d*x)**S(2))/d**S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*exp(e/(c + d*x)**S(2)), x), x, -S(2)*sqrt(pi)*b**S(2)*e**(S(3)/2)*erfi(sqrt(e)/(c + d*x))/(S(3)*d**S(3)) + S(2)*b**S(2)*e*(c + d*x)*exp(e/(c + d*x)**S(2))/(S(3)*d**S(3)) + b**S(2)*(c + d*x)**S(3)*exp(e/(c + d*x)**S(2))/(S(3)*d**S(3)) + b*e*(-a*d + b*c)*Ei(e/(c + d*x)**S(2))/d**S(3) - b*(c + d*x)**S(2)*(-a*d + b*c)*exp(e/(c + d*x)**S(2))/d**S(3) - sqrt(pi)*sqrt(e)*(-a*d + b*c)**S(2)*erfi(sqrt(e)/(c + d*x))/d**S(3) + (c + d*x)*(-a*d + b*c)**S(2)*exp(e/(c + d*x)**S(2))/d**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*exp(e/(c + d*x)**S(2)), x), x, -b*e*Ei(e/(c + d*x)**S(2))/(S(2)*d**S(2)) + b*(c + d*x)**S(2)*exp(e/(c + d*x)**S(2))/(S(2)*d**S(2)) + sqrt(pi)*sqrt(e)*(-a*d + b*c)*erfi(sqrt(e)/(c + d*x))/d**S(2) + (c + d*x)*(a*d - b*c)*exp(e/(c + d*x)**S(2))/d**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x)**S(2)), x), x, -sqrt(pi)*sqrt(e)*erfi(sqrt(e)/(c + d*x))/d + (c + d*x)*exp(e/(c + d*x)**S(2))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x)**S(2))/(a + b*x), x), x, Integral(exp(e/(c + d*x)**S(2))/(a + b*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x)**S(2))/(a + b*x)**S(2), x), x, Integral(exp(e/(c + d*x)**S(2))/(a + b*x)**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x)**S(2))/(a + b*x)**S(3), x), x, Integral(exp(e/(c + d*x)**S(2))/(a + b*x)**S(3), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(3)*exp(e/(c + d*x)**S(3)), x), x, b**S(3)*(-e/(c + d*x)**S(3))**(S(4)/3)*(c + d*x)**S(4)*Gamma(S(-4)/3, -e/(c + d*x)**S(3))/(S(3)*d**S(4)) + b**S(2)*e*(-a*d + b*c)*Ei(e/(c + d*x)**S(3))/d**S(4) - b**S(2)*(c + d*x)**S(3)*(-a*d + b*c)*exp(e/(c + d*x)**S(3))/d**S(4) + b*(-e/(c + d*x)**S(3))**(S(2)/3)*(c + d*x)**S(2)*(-a*d + b*c)**S(2)*Gamma(S(-2)/3, -e/(c + d*x)**S(3))/d**S(4) - (-e/(c + d*x)**S(3))**(S(1)/3)*(c + d*x)*(-a*d + b*c)**S(3)*Gamma(S(-1)/3, -e/(c + d*x)**S(3))/(S(3)*d**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*exp(e/(c + d*x)**S(3)), x), x, -b**S(2)*e*Ei(e/(c + d*x)**S(3))/(S(3)*d**S(3)) + b**S(2)*(c + d*x)**S(3)*exp(e/(c + d*x)**S(3))/(S(3)*d**S(3)) - S(2)*b*(-e/(c + d*x)**S(3))**(S(2)/3)*(c + d*x)**S(2)*(-a*d + b*c)*Gamma(S(-2)/3, -e/(c + d*x)**S(3))/(S(3)*d**S(3)) + (-e/(c + d*x)**S(3))**(S(1)/3)*(c + d*x)*(-a*d + b*c)**S(2)*Gamma(S(-1)/3, -e/(c + d*x)**S(3))/(S(3)*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)*exp(e/(c + d*x)**S(3)), x), x, b*(-e/(c + d*x)**S(3))**(S(2)/3)*(c + d*x)**S(2)*Gamma(S(-2)/3, -e/(c + d*x)**S(3))/(S(3)*d**S(2)) - (-e/(c + d*x)**S(3))**(S(1)/3)*(c + d*x)*(-a*d/S(3) + b*c/S(3))*Gamma(S(-1)/3, -e/(c + d*x)**S(3))/d**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x)**S(3)), x), x, (-e/(c + d*x)**S(3))**(S(1)/3)*(c + d*x)*Gamma(S(-1)/3, -e/(c + d*x)**S(3))/(S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x)**S(3))/(a + b*x), x), x, Integral(exp(e/(c + d*x)**S(3))/(a + b*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(e/(c + d*x)**S(3))/(a + b*x)**S(2), x), x, Integral(exp(e/(c + d*x)**S(3))/(a + b*x)**S(2), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(e + f*(a + b*x)/(c + d*x))/(g + h*x), x), x, F**(e + f*(-a*h + b*g)/(-c*h + d*g))*Ei(f*(g + h*x)*(a*d - b*c)*log(F)/((c + d*x)*(-c*h + d*g)))/h - F**(b*f/d + e)*Ei(f*(a*d - b*c)*log(F)/(d*(c + d*x)))/h, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(e + f*(a + b*x)/(c + d*x))/(g + h*x)**S(2), x), x, -F**(e + f*(a + b*x)/(c + d*x))/(h*(g + h*x)) + F**(e + f*(-a*h + b*g)/(-c*h + d*g))*f*(-a*d + b*c)*log(F)*Ei(f*(g + h*x)*(a*d - b*c)*log(F)/((c + d*x)*(-c*h + d*g)))/(-c*h + d*g)**S(2) + F**(b*f/d + e - f*(-a*d + b*c)/(d*(c + d*x)))*d/(h*(-c*h + d*g)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(e + f*(a + b*x)/(c + d*x))/(g + h*x)**S(3), x), x, -F**(e + f*(a + b*x)/(c + d*x))*f*(-a*d/S(2) + b*c/S(2))*log(F)/((g + h*x)*(-c*h + d*g)**S(2)) - F**(e + f*(a + b*x)/(c + d*x))/(S(2)*h*(g + h*x)**S(2)) + F**(e + f*(-a*h + b*g)/(-c*h + d*g))*d*f*(-a*d + b*c)*log(F)*Ei(f*(g + h*x)*(a*d - b*c)*log(F)/((c + d*x)*(-c*h + d*g)))/(-c*h + d*g)**S(3) + F**(e + f*(-a*h + b*g)/(-c*h + d*g))*f**S(2)*h*(-a*d + b*c)**S(2)*log(F)**S(2)*Ei(f*(g + h*x)*(a*d - b*c)*log(F)/((c + d*x)*(-c*h + d*g)))/(S(2)*(-c*h + d*g)**S(4)) + F**(b*f/d + e - f*(-a*d + b*c)/(d*(c + d*x)))*d**S(2)/(S(2)*h*(-c*h + d*g)**S(2)) + F**(b*f/d + e - f*(-a*d + b*c)/(d*(c + d*x)))*d*f*(-a*d + b*c)*log(F)/(S(2)*(-c*h + d*g)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(e + f*(a + b*x)/(c + d*x))/(g + h*x)**S(4), x), x, -S(2)*F**(e + f*(a + b*x)/(c + d*x))*d*f*(-a*d + b*c)*log(F)/(S(3)*(g + h*x)*(-c*h + d*g)**S(3)) - F**(e + f*(a + b*x)/(c + d*x))*f**S(2)*h*(-a*d + b*c)**S(2)*log(F)**S(2)/(S(6)*(g + h*x)*(-c*h + d*g)**S(4)) - F**(e + f*(a + b*x)/(c + d*x))*f*(-a*d/S(6) + b*c/S(6))*log(F)/((g + h*x)**S(2)*(-c*h + d*g)**S(2)) - F**(e + f*(a + b*x)/(c + d*x))/(S(3)*h*(g + h*x)**S(3)) + F**(e + f*(-a*h + b*g)/(-c*h + d*g))*d**S(2)*f*(-a*d + b*c)*log(F)*Ei(f*(g + h*x)*(a*d - b*c)*log(F)/((c + d*x)*(-c*h + d*g)))/(-c*h + d*g)**S(4) + F**(e + f*(-a*h + b*g)/(-c*h + d*g))*d*f**S(2)*h*(-a*d + b*c)**S(2)*log(F)**S(2)*Ei(f*(g + h*x)*(a*d - b*c)*log(F)/((c + d*x)*(-c*h + d*g)))/(-c*h + d*g)**S(5) + F**(e + f*(-a*h + b*g)/(-c*h + d*g))*f**S(3)*h**S(2)*(-a*d + b*c)**S(3)*log(F)**S(3)*Ei(f*(g + h*x)*(a*d - b*c)*log(F)/((c + d*x)*(-c*h + d*g)))/(S(6)*(-c*h + d*g)**S(6)) + F**(b*f/d + e - f*(-a*d + b*c)/(d*(c + d*x)))*d**S(3)/(S(3)*h*(-c*h + d*g)**S(3)) + S(5)*F**(b*f/d + e - f*(-a*d + b*c)/(d*(c + d*x)))*d**S(2)*f*(-a*d + b*c)*log(F)/(S(6)*(-c*h + d*g)**S(4)) + F**(b*f/d + e - f*(-a*d + b*c)/(d*(c + d*x)))*d*f**S(2)*h*(-a*d + b*c)**S(2)*log(F)**S(2)/(S(6)*(-c*h + d*g)**S(5)), expand=True, _diff=True, _numerical=True)

    # fails 1940 and 1939 recursion assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*x**S(3), x), x, -sqrt(pi)*b**S(3)*f**(a - b**S(2)/(S(4)*c))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(16)*c**(S(7)/2)*sqrt(log(f))) + b**S(2)*f**(a + b*x + c*x**S(2))/(S(8)*c**S(3)*log(f)) - b*f**(a + b*x + c*x**S(2))*x/(S(4)*c**S(2)*log(f)) + S(3)*sqrt(pi)*b*f**(a - b**S(2)/(S(4)*c))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(8)*c**(S(5)/2)*log(f)**(S(3)/2)) + f**(a + b*x + c*x**S(2))*x**S(2)/(S(2)*c*log(f)) - f**(a + b*x + c*x**S(2))/(S(2)*c**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*x**S(2), x), x, sqrt(pi)*b**S(2)*f**(a - b**S(2)/(S(4)*c))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(8)*c**(S(5)/2)*sqrt(log(f))) - b*f**(a + b*x + c*x**S(2))/(S(4)*c**S(2)*log(f)) + f**(a + b*x + c*x**S(2))*x/(S(2)*c*log(f)) - sqrt(pi)*f**(a - b**S(2)/(S(4)*c))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(4)*c**(S(3)/2)*log(f)**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*x, x), x, -sqrt(pi)*b*f**(a - b**S(2)/(S(4)*c))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(4)*c**(S(3)/2)*sqrt(log(f))) + f**(a + b*x + c*x**S(2))/(S(2)*c*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2)), x), x, sqrt(pi)*f**(a - b**S(2)/(S(4)*c))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(2)*sqrt(c)*sqrt(log(f))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))/x, x), x, Integral(f**(a + b*x + c*x**S(2))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))/x**S(2), x), x, b*log(f)*Integral(f**(a + b*x + c*x**S(2))/x, x) + sqrt(pi)*sqrt(c)*f**(a - b**S(2)/(S(4)*c))*sqrt(log(f))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c)) - f**(a + b*x + c*x**S(2))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*exp(a + b*x - c*x**S(2)), x), x, -sqrt(pi)*b**S(3)*exp(a + b**S(2)/(S(4)*c))*erf((b/S(2) - c*x)/sqrt(c))/(S(16)*c**(S(7)/2)) - b**S(2)*exp(a + b*x - c*x**S(2))/(S(8)*c**S(3)) - b*x*exp(a + b*x - c*x**S(2))/(S(4)*c**S(2)) - S(3)*sqrt(pi)*b*exp(a + b**S(2)/(S(4)*c))*erf((b/S(2) - c*x)/sqrt(c))/(S(8)*c**(S(5)/2)) - x**S(2)*exp(a + b*x - c*x**S(2))/(S(2)*c) - exp(a + b*x - c*x**S(2))/(S(2)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(a + b*x - c*x**S(2)), x), x, -sqrt(pi)*b**S(2)*exp(a + b**S(2)/(S(4)*c))*erf((b/S(2) - c*x)/sqrt(c))/(S(8)*c**(S(5)/2)) - b*exp(a + b*x - c*x**S(2))/(S(4)*c**S(2)) - x*exp(a + b*x - c*x**S(2))/(S(2)*c) - sqrt(pi)*exp(a + b**S(2)/(S(4)*c))*erf((b/S(2) - c*x)/sqrt(c))/(S(4)*c**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(a + b*x - c*x**S(2)), x), x, -sqrt(pi)*b*exp(a + b**S(2)/(S(4)*c))*erf((b/S(2) - c*x)/sqrt(c))/(S(4)*c**(S(3)/2)) - exp(a + b*x - c*x**S(2))/(S(2)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a + b*x - c*x**S(2)), x), x, -sqrt(pi)*exp(a + b**S(2)/(S(4)*c))*erf((b/S(2) - c*x)/sqrt(c))/(S(2)*sqrt(c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a + b*x - c*x**S(2))/x, x), x, Integral(exp(a + b*x - c*x**S(2))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a + b*x - c*x**S(2))/x**S(2), x), x, b*Integral(exp(a + b*x - c*x**S(2))/x, x) + sqrt(pi)*sqrt(c)*exp(a + b**S(2)/(S(4)*c))*erf((b/S(2) - c*x)/sqrt(c)) - exp(a + b*x - c*x**S(2))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*exp((a + b*x)*(c + d*x)), x), x, x**S(2)*exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/(S(2)*b*d) - x*(a*d/S(4) + b*c/S(4))*exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/(b**S(2)*d**S(2)) - exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/(S(2)*b**S(2)*d**S(2)) + (a*d + b*c)**S(2)*exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/(S(8)*b**S(3)*d**S(3)) + sqrt(pi)*(S(3)*a*d/S(8) + S(3)*b*c/S(8))*exp(-(-a*d + b*c)**S(2)/(S(4)*b*d))*erfi((a*d/S(2) + b*c/S(2) + b*d*x)/(sqrt(b)*sqrt(d)))/(b**(S(5)/2)*d**(S(5)/2)) - sqrt(pi)*(a*d + b*c)**S(3)*exp(-(-a*d + b*c)**S(2)/(S(4)*b*d))*erfi((a*d/S(2) + b*c/S(2) + b*d*x)/(sqrt(b)*sqrt(d)))/(S(16)*b**(S(7)/2)*d**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp((a + b*x)*(c + d*x)), x), x, x*exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/(S(2)*b*d) + (-a*d/S(4) - b*c/S(4))*exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/(b**S(2)*d**S(2)) - sqrt(pi)*exp(-(-a*d + b*c)**S(2)/(S(4)*b*d))*erfi((a*d/S(2) + b*c/S(2) + b*d*x)/(sqrt(b)*sqrt(d)))/(S(4)*b**(S(3)/2)*d**(S(3)/2)) + sqrt(pi)*(a*d + b*c)**S(2)*exp(-(-a*d + b*c)**S(2)/(S(4)*b*d))*erfi((a*d/S(2) + b*c/S(2) + b*d*x)/(sqrt(b)*sqrt(d)))/(S(8)*b**(S(5)/2)*d**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp((a + b*x)*(c + d*x)), x), x, exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/(S(2)*b*d) - sqrt(pi)*(a*d/S(4) + b*c/S(4))*exp(-(-a*d + b*c)**S(2)/(S(4)*b*d))*erfi((a*d/S(2) + b*c/S(2) + b*d*x)/(sqrt(b)*sqrt(d)))/(b**(S(3)/2)*d**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp((a + b*x)*(c + d*x)), x), x, sqrt(pi)*exp(-(-a*d + b*c)**S(2)/(S(4)*b*d))*erfi((a*d/S(2) + b*c/S(2) + b*d*x)/(sqrt(b)*sqrt(d)))/(S(2)*sqrt(b)*sqrt(d)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp((a + b*x)*(c + d*x))/x, x), x, Integral(exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/x, x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp((a + b*x)*(c + d*x))/x**S(2), x), x, sqrt(pi)*sqrt(b)*sqrt(d)*exp(-(-a*d + b*c)**S(2)/(S(4)*b*d))*erfi((a*d/S(2) + b*c/S(2) + b*d*x)/(sqrt(b)*sqrt(d))) + (a*d + b*c)*Integral(exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/x, x) - exp(a*c + b*d*x**S(2) + x*(a*d + b*c))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*(d + e*x)**S(3), x), x, e*f**(a + b*x + c*x**S(2))*(d + e*x)**S(2)/(S(2)*c*log(f)) - e**S(3)*f**(a + b*x + c*x**S(2))/(S(2)*c**S(2)*log(f)**S(2)) + e*f**(a + b*x + c*x**S(2))*(d + e*x)*(-b*e + S(2)*c*d)/(S(4)*c**S(2)*log(f)) + e*f**(a + b*x + c*x**S(2))*(-b*e + S(2)*c*d)**S(2)/(S(8)*c**S(3)*log(f)) - S(3)*sqrt(pi)*e**S(2)*f**(a - b**S(2)/(S(4)*c))*(-b*e + S(2)*c*d)*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(8)*c**(S(5)/2)*log(f)**(S(3)/2)) + sqrt(pi)*f**(a - b**S(2)/(S(4)*c))*(-b*e + S(2)*c*d)**S(3)*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(16)*c**(S(7)/2)*sqrt(log(f))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*(d + e*x)**S(2), x), x, e*f**(a + b*x + c*x**S(2))*(d + e*x)/(S(2)*c*log(f)) + e*f**(a + b*x + c*x**S(2))*(-b*e + S(2)*c*d)/(S(4)*c**S(2)*log(f)) - sqrt(pi)*e**S(2)*f**(a - b**S(2)/(S(4)*c))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(4)*c**(S(3)/2)*log(f)**(S(3)/2)) + sqrt(pi)*f**(a - b**S(2)/(S(4)*c))*(-b*e + S(2)*c*d)**S(2)*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(8)*c**(S(5)/2)*sqrt(log(f))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*(d + e*x), x), x, e*f**(a + b*x + c*x**S(2))/(S(2)*c*log(f)) + sqrt(pi)*f**(a - b**S(2)/(S(4)*c))*(-b*e/S(4) + c*d/S(2))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(c**(S(3)/2)*sqrt(log(f))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))/(d + e*x), x), x, Integral(f**(a + b*x + c*x**S(2))/(d + e*x), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))/(d + e*x)**S(2), x), x, sqrt(pi)*sqrt(c)*f**(a - b**S(2)/(S(4)*c))*sqrt(log(f))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/e**S(2) - f**(a + b*x + c*x**S(2))/(e*(d + e*x)) - (-b*e + S(2)*c*d)*log(f)*Integral(f**(a + b*x + c*x**S(2))/(d + e*x), x)/e**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))/(d + e*x)**S(3), x), x, -sqrt(pi)*sqrt(c)*f**(a - b**S(2)/(S(4)*c))*(-b*e/S(2) + c*d)*log(f)**(S(3)/2)*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/e**S(4) + c*log(f)*Integral(f**(a + b*x + c*x**S(2))/(d + e*x), x)/e**S(2) - f**(a + b*x + c*x**S(2))/(S(2)*e*(d + e*x)**S(2)) + f**(a + b*x + c*x**S(2))*(-b*e/S(2) + c*d)*log(f)/(e**S(3)*(d + e*x)) + (-b*e + S(2)*c*d)**S(2)*log(f)**S(2)*Integral(f**(a + b*x + c*x**S(2))/(d + e*x), x)/(S(2)*e**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*(b + S(2)*c*x)**S(3), x), x, -S(4)*c*f**(a + b*x + c*x**S(2))/log(f)**S(2) + f**(a + b*x + c*x**S(2))*(b + S(2)*c*x)**S(2)/log(f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*(b + S(2)*c*x)**S(2), x), x, -sqrt(pi)*sqrt(c)*f**(a - b**S(2)/(S(4)*c))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/log(f)**(S(3)/2) + f**(a + b*x + c*x**S(2))*(b + S(2)*c*x)/log(f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*(b + S(2)*c*x), x), x, f**(a + b*x + c*x**S(2))/log(f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))/(b + S(2)*c*x), x), x, f**(a - b**S(2)/(S(4)*c))*Ei((b + S(2)*c*x)**S(2)*log(f)/(S(4)*c))/(S(4)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))/(b + S(2)*c*x)**S(2), x), x, -f**(a + b*x + c*x**S(2))/(S(2)*c*(b + S(2)*c*x)) + sqrt(pi)*f**(a - b**S(2)/(S(4)*c))*sqrt(log(f))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(4)*c**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))/(b + S(2)*c*x)**S(3), x), x, -f**(a + b*x + c*x**S(2))/(S(4)*c*(b + S(2)*c*x)**S(2)) + f**(a - b**S(2)/(S(4)*c))*log(f)*Ei((b + S(2)*c*x)**S(2)*log(f)/(S(4)*c))/(S(16)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(b*x + c*x**S(2))*(b + S(2)*c*x)**S(3), x), x, -S(4)*c*f**(b*x + c*x**S(2))/log(f)**S(2) + f**(b*x + c*x**S(2))*(b + S(2)*c*x)**S(2)/log(f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(b*x + c*x**S(2))*(b + S(2)*c*x)**S(2), x), x, -sqrt(pi)*sqrt(c)*f**(-b**S(2)/(S(4)*c))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/log(f)**(S(3)/2) + f**(b*x + c*x**S(2))*(b + S(2)*c*x)/log(f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(b*x + c*x**S(2))*(b + S(2)*c*x), x), x, f**(b*x + c*x**S(2))/log(f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(b*x + c*x**S(2))/(b + S(2)*c*x), x), x, f**(-b**S(2)/(S(4)*c))*Ei((b + S(2)*c*x)**S(2)*log(f)/(S(4)*c))/(S(4)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(b*x + c*x**S(2))/(b + S(2)*c*x)**S(2), x), x, -f**(b*x + c*x**S(2))/(S(2)*c*(b + S(2)*c*x)) + sqrt(pi)*f**(-b**S(2)/(S(4)*c))*sqrt(log(f))*erfi((b/S(2) + c*x)*sqrt(log(f))/sqrt(c))/(S(4)*c**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(b*x + c*x**S(2))/(b + S(2)*c*x)**S(3), x), x, -f**(b*x + c*x**S(2))/(S(4)*c*(b + S(2)*c*x)**S(2)) + f**(-b**S(2)/(S(4)*c))*log(f)*Ei((b + S(2)*c*x)**S(2)*log(f)/(S(4)*c))/(S(16)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(a + b*exp(c + d*x))), x), x, Integral(S(1)/(x*(a + b*exp(c + d*x))), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*exp(c + d*x)), x), x, x/a - log(a + b*exp(c + d*x))/(a*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*exp(c + d*x)), x), x, -x*log(a*exp(-c - d*x)/b + S(1))/(a*d) + polylog(S(2), -a*exp(-c - d*x)/b)/(a*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*exp(c + d*x)), x), x, -x**S(2)*log(a*exp(-c - d*x)/b + S(1))/(a*d) + S(2)*x*polylog(S(2), -a*exp(-c - d*x)/b)/(a*d**S(2)) + S(2)*polylog(S(3), -a*exp(-c - d*x)/b)/(a*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/(a + b*exp(c + d*x)), x), x, -x**S(3)*log(a*exp(-c - d*x)/b + S(1))/(a*d) + S(3)*x**S(2)*polylog(S(2), -a*exp(-c - d*x)/b)/(a*d**S(2)) + S(6)*x*polylog(S(3), -a*exp(-c - d*x)/b)/(a*d**S(3)) + S(6)*polylog(S(4), -a*exp(-c - d*x)/b)/(a*d**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*exp(c - d*x)), x), x, x/a + log(a + b*exp(c - d*x))/(a*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*exp(-c - d*x)), x), x, x/a + log(a + b*exp(-c - d*x))/(a*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(a + b*exp(c + d*x))**S(2)), x), x, Integral(S(1)/(x*(a + b*exp(c + d*x))**S(2)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(c + d*x))**(S(-2)), x), x, S(1)/(a*d*(a + b*exp(c + d*x))) + x/a**S(2) - log(a + b*exp(c + d*x))/(a**S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*exp(c + d*x))**S(2), x), x, x/(a*d*(a + b*exp(c + d*x))) + x**S(2)/(S(2)*a**S(2)) - x*log(S(1) + b*exp(c + d*x)/a)/(a**S(2)*d) - x/(a**S(2)*d) + log(a + b*exp(c + d*x))/(a**S(2)*d**S(2)) - polylog(S(2), -b*exp(c + d*x)/a)/(a**S(2)*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*exp(c + d*x))**S(2), x), x, x**S(2)/(a*d*(a + b*exp(c + d*x))) + x**S(3)/(S(3)*a**S(2)) - x**S(2)*log(S(1) + b*exp(c + d*x)/a)/(a**S(2)*d) + S(2)*x*log(a*exp(-c - d*x)/b + S(1))/(a**S(2)*d**S(2)) - S(2)*x*polylog(S(2), -b*exp(c + d*x)/a)/(a**S(2)*d**S(2)) - S(2)*polylog(S(2), -a*exp(-c - d*x)/b)/(a**S(2)*d**S(3)) + S(2)*polylog(S(3), -b*exp(c + d*x)/a)/(a**S(2)*d**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/(a + b*exp(c + d*x))**S(2), x), x, x**S(3)/(a*d*(a + b*exp(c + d*x))) + x**S(4)/(S(4)*a**S(2)) - x**S(3)*log(S(1) + b*exp(c + d*x)/a)/(a**S(2)*d) + S(3)*x**S(2)*log(a*exp(-c - d*x)/b + S(1))/(a**S(2)*d**S(2)) - S(3)*x**S(2)*polylog(S(2), -b*exp(c + d*x)/a)/(a**S(2)*d**S(2)) - S(6)*x*polylog(S(2), -a*exp(-c - d*x)/b)/(a**S(2)*d**S(3)) + S(6)*x*polylog(S(3), -b*exp(c + d*x)/a)/(a**S(2)*d**S(3)) - S(6)*polylog(S(3), -a*exp(-c - d*x)/b)/(a**S(2)*d**S(4)) - S(6)*polylog(S(4), -b*exp(c + d*x)/a)/(a**S(2)*d**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(c - d*x))**(S(-2)), x), x, -S(1)/(a*d*(a + b*exp(c - d*x))) + x/a**S(2) + log(a + b*exp(c - d*x))/(a**S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(-c - d*x))**(S(-2)), x), x, -S(1)/(a*d*(a + b*exp(-c - d*x))) + x/a**S(2) + log(a + b*exp(-c - d*x))/(a**S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(x*(a + b*exp(c + d*x))**S(3)), x), x, Integral(S(1)/(x*(a + b*exp(c + d*x))**S(3)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(c + d*x))**(S(-3)), x), x, S(1)/(S(2)*a*d*(a + b*exp(c + d*x))**S(2)) + S(1)/(a**S(2)*d*(a + b*exp(c + d*x))) + x/a**S(3) - log(a + b*exp(c + d*x))/(a**S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*exp(c + d*x))**S(3), x), x, x/(S(2)*a*d*(a + b*exp(c + d*x))**S(2)) + x/(a**S(2)*d*(a + b*exp(c + d*x))) - S(1)/(S(2)*a**S(2)*d**S(2)*(a + b*exp(c + d*x))) + x**S(2)/(S(2)*a**S(3)) - x*log(S(1) + b*exp(c + d*x)/a)/(a**S(3)*d) - S(3)*x/(S(2)*a**S(3)*d) + S(3)*log(a + b*exp(c + d*x))/(S(2)*a**S(3)*d**S(2)) - polylog(S(2), -b*exp(c + d*x)/a)/(a**S(3)*d**S(2)), expand=True, _diff=True, _numerical=True)

    # recursion assert rubi_test(rubi_integrate(x**S(2)/(a + b*exp(c + d*x))**S(3), x), x, x**S(2)/(S(2)*a*d*(a + b*exp(c + d*x))**S(2)) + x**S(2)/(a**S(2)*d*(a + b*exp(c + d*x))) - x/(a**S(2)*d**S(2)*(a + b*exp(c + d*x))) + x**S(3)/(S(3)*a**S(3)) - x**S(2)*log(S(1) + b*exp(c + d*x)/a)/(a**S(3)*d) - S(3)*x**S(2)/(S(2)*a**S(3)*d) + S(3)*x*log(S(1) + b*exp(c + d*x)/a)/(a**S(3)*d**S(2)) - S(2)*x*polylog(S(2), -b*exp(c + d*x)/a)/(a**S(3)*d**S(2)) + x/(a**S(3)*d**S(2)) - log(a + b*exp(c + d*x))/(a**S(3)*d**S(3)) + S(3)*polylog(S(2), -b*exp(c + d*x)/a)/(a**S(3)*d**S(3)) + S(2)*polylog(S(3), -b*exp(c + d*x)/a)/(a**S(3)*d**S(3)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate(x**S(2)/(a + b*exp(c + d*x))**S(3), x), x, x**S(2)/(S(2)*a*d*(a + b*exp(c + d*x))**S(2)) + x**S(2)/(a**S(2)*d*(a + b*exp(c + d*x))) - x/(a**S(2)*d**S(2)*(a + b*exp(c + d*x))) + x**S(3)/(S(3)*a**S(3)) - x**S(2)*log(S(1) + b*exp(c + d*x)/a)/(a**S(3)*d) - x**S(2)/(S(2)*a**S(3)*d) + x*log(S(1) + b*exp(c + d*x)/a)/(a**S(3)*d**S(2)) + S(2)*x*log(a*exp(-c - d*x)/b + S(1))/(a**S(3)*d**S(2)) - S(2)*x*polylog(S(2), -b*exp(c + d*x)/a)/(a**S(3)*d**S(2)) + x/(a**S(3)*d**S(2)) - log(a + b*exp(c + d*x))/(a**S(3)*d**S(3)) + polylog(S(2), -b*exp(c + d*x)/a)/(a**S(3)*d**S(3)) - S(2)*polylog(S(2), -a*exp(-c - d*x)/b)/(a**S(3)*d**S(3)) + S(2)*polylog(S(3), -b*exp(c + d*x)/a)/(a**S(3)*d**S(3)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate((a + b*exp(c - d*x))**(S(-3)), x), x, -S(1)/(S(2)*a*d*(a + b*exp(c - d*x))**S(2)) - S(1)/(a**S(2)*d*(a + b*exp(c - d*x))) + x/a**S(3) + log(a + b*exp(c - d*x))/(a**S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(-c - d*x))**(S(-3)), x), x, -S(1)/(S(2)*a*d*(a + b*exp(-c - d*x))**S(2)) - S(1)/(a**S(2)*d*(a + b*exp(-c - d*x))) + x/a**S(3) + log(a + b*exp(-c - d*x))/(a**S(3)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a + b*x)/(x**S(2)*(c + d*x**S(2))), x), x, b*exp(a)*Ei(b*x)/c - sqrt(d)*exp(a - b*sqrt(-c)/sqrt(d))*Ei(b*(sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*(-c)**(S(3)/2)) + sqrt(d)*exp(a + b*sqrt(-c)/sqrt(d))*Ei(-b*(-sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*(-c)**(S(3)/2)) - exp(a + b*x)/(c*x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a + b*x)/(x*(c + d*x**S(2))), x), x, exp(a)*Ei(b*x)/c - exp(a - b*sqrt(-c)/sqrt(d))*Ei(b*(sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*c) - exp(a + b*sqrt(-c)/sqrt(d))*Ei(-b*(-sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a + b*x)/(c + d*x**S(2)), x), x, -exp(a - b*sqrt(-c)/sqrt(d))*Ei(b*(sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*sqrt(d)*sqrt(-c)) + exp(a + b*sqrt(-c)/sqrt(d))*Ei(-b*(-sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*sqrt(d)*sqrt(-c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(a + b*x)/(c + d*x**S(2)), x), x, exp(a - b*sqrt(-c)/sqrt(d))*Ei(b*(sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*d) + exp(a + b*sqrt(-c)/sqrt(d))*Ei(-b*(-sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(a + b*x)/(c + d*x**S(2)), x), x, -sqrt(-c)*exp(a - b*sqrt(-c)/sqrt(d))*Ei(b*(sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*d**(S(3)/2)) + sqrt(-c)*exp(a + b*sqrt(-c)/sqrt(d))*Ei(-b*(-sqrt(d)*x + sqrt(-c))/sqrt(d))/(S(2)*d**(S(3)/2)) + exp(a + b*x)/(b*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(d + e*x)/(x**S(2)*(a + b*x + c*x**S(2))), x), x, e*exp(d)*Ei(e*x)/a - exp(d + e*x)/(a*x) - b*exp(d)*Ei(e*x)/a**S(2) + (b + (-S(2)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*exp(d - e*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*a**S(2)) + (b + (S(2)*a*c - b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*exp(d - e*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*a**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(d + e*x)/(x*(a + b*x + c*x**S(2))), x), x, -(-b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*exp(d - e*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*a) - (b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*exp(d - e*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*a) + exp(d)*Ei(e*x)/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(d + e*x)/(a + b*x + c*x**S(2)), x), x, exp(d - e*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/sqrt(-S(4)*a*c + b**S(2)) - exp(d - e*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/sqrt(-S(4)*a*c + b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(d + e*x)/(a + b*x + c*x**S(2)), x), x, (-b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*exp(d - e*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*c) + (b/sqrt(-S(4)*a*c + b**S(2)) + S(1))*exp(d - e*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(d + e*x)/(a + b*x + c*x**S(2)), x), x, exp(d + e*x)/(c*e) - (b + (-S(2)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*exp(d - e*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*c**S(2)) - (b + (S(2)*a*c - b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*exp(d - e*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*c**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*exp(d + e*x)/(a + b*x + c*x**S(2)), x), x, -b*exp(d + e*x)/(c**S(2)*e) + x*exp(d + e*x)/(c*e) - exp(d + e*x)/(c*e**S(2)) + (-a*c + b**S(2) - b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*exp(d - e*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*c**S(3)) + (-a*c + b**S(2) + b*(-S(3)*a*c + b**S(2))/sqrt(-S(4)*a*c + b**S(2)))*exp(d - e*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))*Ei(e*(b + S(2)*c*x + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(S(2)*c**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(4)**x/(S(2)**x*b + a), x), x, S(2)**x/(b*log(S(2))) - a*log(S(2)**x*b + a)/(b**S(2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(S(2)*x)/(S(2)**x*b + a), x), x, S(2)**x/(b*log(S(2))) - a*log(S(2)**x*b + a)/(b**S(2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(4)**x/(-S(2)**x*b + a), x), x, -S(2)**x/(b*log(S(2))) - a*log(-S(2)**x*b + a)/(b**S(2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(S(2)*x)/(-S(2)**x*b + a), x), x, -S(2)**x/(b*log(S(2))) - a*log(-S(2)**x*b + a)/(b**S(2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(4)**x/(a + S(2)**(-x)*b), x), x, -S(2)**x*b/(a**S(2)*log(S(2))) + S(2)**(S(2)*x + S(-1))/(a*log(S(2))) + b**S(2)*x/a**S(3) + b**S(2)*log(a + S(2)**(-x)*b)/(a**S(3)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(S(2)*x)/(a + S(2)**(-x)*b), x), x, -S(2)**x*b/(a**S(2)*log(S(2))) + S(2)**(S(2)*x + S(-1))/(a*log(S(2))) + b**S(2)*x/a**S(3) + b**S(2)*log(a + S(2)**(-x)*b)/(a**S(3)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(4)**x/(a - S(2)**(-x)*b), x), x, S(2)**x*b/(a**S(2)*log(S(2))) + S(2)**(S(2)*x + S(-1))/(a*log(S(2))) + b**S(2)*x/a**S(3) + b**S(2)*log(a - S(2)**(-x)*b)/(a**S(3)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(S(2)*x)/(a - S(2)**(-x)*b), x), x, S(2)**x*b/(a**S(2)*log(S(2))) + S(2)**(S(2)*x + S(-1))/(a*log(S(2))) + b**S(2)*x/a**S(3) + b**S(2)*log(a - S(2)**(-x)*b)/(a**S(3)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/(S(4)**x*b + a), x), x, atan(S(2)**x*sqrt(b)/sqrt(a))/(sqrt(a)*sqrt(b)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/(S(2)**(S(2)*x)*b + a), x), x, atan(S(2)**x*sqrt(b)/sqrt(a))/(sqrt(a)*sqrt(b)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/(-S(4)**x*b + a), x), x, atanh(S(2)**x*sqrt(b)/sqrt(a))/(sqrt(a)*sqrt(b)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/(-S(2)**(S(2)*x)*b + a), x), x, atanh(S(2)**x*sqrt(b)/sqrt(a))/(sqrt(a)*sqrt(b)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/(a + S(4)**(-x)*b), x), x, S(2)**x/(a*log(S(2))) - sqrt(b)*atan(S(2)**x*sqrt(a)/sqrt(b))/(a**(S(3)/2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/(a + S(2)**(-S(2)*x)*b), x), x, S(2)**x/(a*log(S(2))) - sqrt(b)*atan(S(2)**x*sqrt(a)/sqrt(b))/(a**(S(3)/2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/(a - S(4)**(-x)*b), x), x, S(2)**x/(a*log(S(2))) - sqrt(b)*atanh(S(2)**x*sqrt(a)/sqrt(b))/(a**(S(3)/2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/(a - S(2)**(-S(2)*x)*b), x), x, S(2)**x/(a*log(S(2))) - sqrt(b)*atanh(S(2)**x*sqrt(a)/sqrt(b))/(a**(S(3)/2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/sqrt(S(4)**x*b + a), x), x, atanh(S(2)**x*sqrt(b)/sqrt(S(2)**(S(2)*x)*b + a))/(sqrt(b)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/sqrt(S(2)**(S(2)*x)*b + a), x), x, atanh(S(2)**x*sqrt(b)/sqrt(S(2)**(S(2)*x)*b + a))/(sqrt(b)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/sqrt(-S(4)**x*b + a), x), x, atan(S(2)**x*sqrt(b)/sqrt(-S(2)**(S(2)*x)*b + a))/(sqrt(b)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/sqrt(-S(2)**(S(2)*x)*b + a), x), x, atan(S(2)**x*sqrt(b)/sqrt(-S(2)**(S(2)*x)*b + a))/(sqrt(b)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/sqrt(a + S(4)**(-x)*b), x), x, S(2)**x*sqrt(a + S(2)**(-S(2)*x)*b)/(a*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/sqrt(a + S(2)**(-S(2)*x)*b), x), x, S(2)**x*sqrt(a + S(2)**(-S(2)*x)*b)/(a*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/sqrt(a - S(4)**(-x)*b), x), x, S(2)**x*sqrt(a - S(2)**(-S(2)*x)*b)/(a*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x/sqrt(a - S(2)**(-S(2)*x)*b), x), x, S(2)**x*sqrt(a - S(2)**(-S(2)*x)*b)/(a*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(4)**x/sqrt(S(2)**x*b + a), x), x, -S(2)*a*sqrt(S(2)**x*b + a)/(b**S(2)*log(S(2))) + S(2)*(S(2)**x*b + a)**(S(3)/2)/(S(3)*b**S(2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(S(2)*x)/sqrt(S(2)**x*b + a), x), x, -S(2)*a*sqrt(S(2)**x*b + a)/(b**S(2)*log(S(2))) + S(2)*(S(2)**x*b + a)**(S(3)/2)/(S(3)*b**S(2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(4)**x/sqrt(-S(2)**x*b + a), x), x, -S(2)*a*sqrt(-S(2)**x*b + a)/(b**S(2)*log(S(2))) + S(2)*(-S(2)**x*b + a)**(S(3)/2)/(S(3)*b**S(2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(S(2)*x)/sqrt(-S(2)**x*b + a), x), x, -S(2)*a*sqrt(-S(2)**x*b + a)/(b**S(2)*log(S(2))) + S(2)*(-S(2)**x*b + a)**(S(3)/2)/(S(3)*b**S(2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(4)**x/sqrt(a + S(2)**(-x)*b), x), x, -S(3)*S(2)**(x + S(-2))*b*sqrt(a + S(2)**(-x)*b)/(a**S(2)*log(S(2))) + S(2)**(S(2)*x + S(-1))*sqrt(a + S(2)**(-x)*b)/(a*log(S(2))) + S(3)*b**S(2)*atanh(sqrt(a + S(2)**(-x)*b)/sqrt(a))/(S(4)*a**(S(5)/2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(S(2)*x)/sqrt(a + S(2)**(-x)*b), x), x, -S(3)*S(2)**(x + S(-2))*b*sqrt(a + S(2)**(-x)*b)/(a**S(2)*log(S(2))) + S(2)**(S(2)*x + S(-1))*sqrt(a + S(2)**(-x)*b)/(a*log(S(2))) + S(3)*b**S(2)*atanh(sqrt(a + S(2)**(-x)*b)/sqrt(a))/(S(4)*a**(S(5)/2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(4)**x/sqrt(a - S(2)**(-x)*b), x), x, S(3)*S(2)**(x + S(-2))*b*sqrt(a - S(2)**(-x)*b)/(a**S(2)*log(S(2))) + S(2)**(S(2)*x + S(-1))*sqrt(a - S(2)**(-x)*b)/(a*log(S(2))) + S(3)*b**S(2)*atanh(sqrt(a - S(2)**(-x)*b)/sqrt(a))/(S(4)*a**(S(5)/2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(S(2)*x)/sqrt(a - S(2)**(-x)*b), x), x, S(3)*S(2)**(x + S(-2))*b*sqrt(a - S(2)**(-x)*b)/(a**S(2)*log(S(2))) + S(2)**(S(2)*x + S(-1))*sqrt(a - S(2)**(-x)*b)/(a*log(S(2))) + S(3)*b**S(2)*atanh(sqrt(a - S(2)**(-x)*b)/sqrt(a))/(S(4)*a**(S(5)/2)*log(S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(exp(S(2)*x) + S(2)*exp(x) + S(1)), x), x, x - log(exp(x) + S(1)) + S(1)/(exp(x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(exp(S(2)*x) + S(3)*exp(x) + S(2)), x), x, x/S(2) - log(exp(x) + S(1)) + log(exp(x) + S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(exp(S(2)*x) + exp(x) + S(-1)), x), x, -x + (-sqrt(S(5)) + S(5))*log(S(2)*exp(x) + S(1) + sqrt(S(5)))/S(10) + (sqrt(S(5)) + S(5))*log(S(2)*exp(x) - sqrt(S(5)) + S(1))/S(10), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(exp(S(2)*x) + S(3)*exp(x) + S(3)), x), x, x/S(3) - log(exp(S(2)*x) + S(3)*exp(x) + S(3))/S(6) - sqrt(S(3))*atan(sqrt(S(3))*(S(2)*exp(x) + S(3))/S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*exp(x) + c*exp(S(2)*x)), x), x, b*atanh((b + S(2)*c*exp(x))/sqrt(-S(4)*a*c + b**S(2)))/(a*sqrt(-S(4)*a*c + b**S(2))) + x/a - log(a + b*exp(x) + c*exp(S(2)*x))/(S(2)*a), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(exp(S(2)*x) + S(2)*exp(x) + S(1)), x), x, x**S(2)/S(2) - x*log(exp(x) + S(1)) - x + x/(exp(x) + S(1)) + log(exp(x) + S(1)) - polylog(S(2), -exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(exp(S(2)*x) + S(3)*exp(x) + S(2)), x), x, -x*log(S(1) + exp(-x)) + x*log(S(1) + S(2)*exp(-x))/S(2) - polylog(S(2), -S(2)*exp(-x))/S(2) + polylog(S(2), -exp(-x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(exp(S(2)*x) + exp(x) + S(-1)), x), x, S(2)*sqrt(S(5))*x*log(S(1) + (S(1)/2 + sqrt(S(5))/S(2))*exp(-x))/(S(5)*(S(1) + sqrt(S(5)))) - S(2)*sqrt(S(5))*x*log(S(1) + (-sqrt(S(5))/S(2) + S(1)/2)*exp(-x))/(S(5)*(-sqrt(S(5)) + S(1))) + S(2)*sqrt(S(5))*polylog(S(2), (S(-1)/2 + sqrt(S(5))/S(2))*exp(-x))/(S(5)*(-sqrt(S(5)) + S(1))) - S(2)*sqrt(S(5))*polylog(S(2), (-sqrt(S(5))/S(2) + S(-1)/2)*exp(-x))/(S(5)*(S(1) + sqrt(S(5)))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(exp(S(2)*x) + S(3)*exp(x) + S(3)), x), x, -S(2)*sqrt(S(3))*x*log(S(1) + (S(3)/2 - sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(sqrt(S(3)) + S(3)*I)) + S(2)*sqrt(S(3))*x*log(S(1) + (S(3)/2 + sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(-sqrt(S(3)) + S(3)*I)) - S(2)*sqrt(S(3))*polylog(S(2), (S(-3)/2 - sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(-sqrt(S(3)) + S(3)*I)) + S(2)*sqrt(S(3))*polylog(S(2), (S(-3)/2 + sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(sqrt(S(3)) + S(3)*I)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*exp(x) + c*exp(S(2)*x)), x), x, S(2)*c*x*log(S(1) + (b/S(2) + sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) + b*sqrt(-S(4)*a*c + b**S(2))) + S(2)*c*x*log(S(1) + (b/S(2) - sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) - b*sqrt(-S(4)*a*c + b**S(2))) - S(2)*c*polylog(S(2), (-b/S(2) - sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) + b*sqrt(-S(4)*a*c + b**S(2))) - S(2)*c*polylog(S(2), (-b/S(2) + sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) - b*sqrt(-S(4)*a*c + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(exp(S(2)*x) + S(2)*exp(x) + S(1)), x), x, x**S(3)/S(3) - x**S(2)*log(exp(x) + S(1)) + x**S(2)/(exp(x) + S(1)) + S(2)*x*log(S(1) + exp(-x)) - S(2)*x*polylog(S(2), -exp(x)) - S(2)*polylog(S(2), -exp(-x)) + S(2)*polylog(S(3), -exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(exp(S(2)*x) + S(3)*exp(x) + S(2)), x), x, -x**S(2)*log(S(1) + exp(-x)) + x**S(2)*log(S(1) + S(2)*exp(-x))/S(2) - x*polylog(S(2), -S(2)*exp(-x)) + S(2)*x*polylog(S(2), -exp(-x)) - polylog(S(3), -S(2)*exp(-x)) + S(2)*polylog(S(3), -exp(-x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(exp(S(2)*x) + exp(x) + S(-1)), x), x, S(2)*sqrt(S(5))*x**S(2)*log(S(1) + (S(1)/2 + sqrt(S(5))/S(2))*exp(-x))/(S(5)*(S(1) + sqrt(S(5)))) - S(2)*sqrt(S(5))*x**S(2)*log(S(1) + (-sqrt(S(5))/S(2) + S(1)/2)*exp(-x))/(S(5)*(-sqrt(S(5)) + S(1))) + S(4)*sqrt(S(5))*x*polylog(S(2), (S(-1)/2 + sqrt(S(5))/S(2))*exp(-x))/(S(5)*(-sqrt(S(5)) + S(1))) - S(4)*sqrt(S(5))*x*polylog(S(2), (-sqrt(S(5))/S(2) + S(-1)/2)*exp(-x))/(S(5)*(S(1) + sqrt(S(5)))) + S(4)*sqrt(S(5))*polylog(S(3), (S(-1)/2 + sqrt(S(5))/S(2))*exp(-x))/(S(5)*(-sqrt(S(5)) + S(1))) - S(4)*sqrt(S(5))*polylog(S(3), (-sqrt(S(5))/S(2) + S(-1)/2)*exp(-x))/(S(5)*(S(1) + sqrt(S(5)))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(exp(S(2)*x) + S(3)*exp(x) + S(3)), x), x, -S(2)*sqrt(S(3))*x**S(2)*log(S(1) + (S(3)/2 - sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(sqrt(S(3)) + S(3)*I)) + S(2)*sqrt(S(3))*x**S(2)*log(S(1) + (S(3)/2 + sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(-sqrt(S(3)) + S(3)*I)) - S(4)*sqrt(S(3))*x*polylog(S(2), (S(-3)/2 - sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(-sqrt(S(3)) + S(3)*I)) + S(4)*sqrt(S(3))*x*polylog(S(2), (S(-3)/2 + sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(sqrt(S(3)) + S(3)*I)) - S(4)*sqrt(S(3))*polylog(S(3), (S(-3)/2 - sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(-sqrt(S(3)) + S(3)*I)) + S(4)*sqrt(S(3))*polylog(S(3), (S(-3)/2 + sqrt(S(3))*I/S(2))*exp(-x))/(S(3)*(sqrt(S(3)) + S(3)*I)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*exp(x) + c*exp(S(2)*x)), x), x, S(2)*c*x**S(2)*log(S(1) + (b/S(2) + sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) + b*sqrt(-S(4)*a*c + b**S(2))) + S(2)*c*x**S(2)*log(S(1) + (b/S(2) - sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) - b*sqrt(-S(4)*a*c + b**S(2))) - S(4)*c*x*polylog(S(2), (-b/S(2) - sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) + b*sqrt(-S(4)*a*c + b**S(2))) - S(4)*c*x*polylog(S(2), (-b/S(2) + sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) - b*sqrt(-S(4)*a*c + b**S(2))) - S(4)*c*polylog(S(3), (-b/S(2) - sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) + b*sqrt(-S(4)*a*c + b**S(2))) - S(4)*c*polylog(S(3), (-b/S(2) + sqrt(-S(4)*a*c + b**S(2))/S(2))*exp(-x)/c)/(-S(4)*a*c + b**S(2) - b*sqrt(-S(4)*a*c + b**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(S(2)*f**(c + d*x) + f**(S(2)*c + S(2)*d*x) + S(1)), x), x, x - log(f**(c + d*x) + S(1))/(d*log(f)) + S(1)/(d*(f**(c + d*x) + S(1))*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*f**(c + d*x) + c*f**(S(2)*c + S(2)*d*x)), x), x, b*atanh((b + S(2)*c*f**(c + d*x))/sqrt(-S(4)*a*c + b**S(2)))/(a*d*sqrt(-S(4)*a*c + b**S(2))*log(f)) + x/a - log(a + b*f**(c + d*x) + c*f**(S(2)*c + S(2)*d*x))/(S(2)*a*d*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*f**(g + h*x) + c*f**(S(2)*g + S(2)*h*x)), x), x, b*atanh((b + S(2)*c*f**(g + h*x))/sqrt(-S(4)*a*c + b**S(2)))/(a*h*sqrt(-S(4)*a*c + b**S(2))*log(f)) + x/a - log(a + b*f**(g + h*x) + c*f**(S(2)*g + S(2)*h*x))/(S(2)*a*h*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(S(2)*f**(c + d*x) + f**(S(2)*c + S(2)*d*x) + S(1)), x), x, x**S(2)/S(2) - x*log(f**(c + d*x) + S(1))/(d*log(f)) - x/(d*log(f)) + x/(d*(f**(c + d*x) + S(1))*log(f)) + log(f**(c + d*x) + S(1))/(d**S(2)*log(f)**S(2)) - polylog(S(2), -f**(c + d*x))/(d**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*f**(c + d*x) + c*f**(S(2)*c + S(2)*d*x)), x), x, S(2)*c*x*log(S(1) + f**(-c - d*x)*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d*(b + sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)) - S(2)*c*x*log(S(1) + f**(-c - d*x)*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d*(b - sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)) - S(2)*c*polylog(S(2), -f**(-c - d*x)*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d**S(2)*(b + sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)**S(2)) + S(2)*c*polylog(S(2), -f**(-c - d*x)*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d**S(2)*(b - sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(S(2)*f**(c + d*x) + f**(S(2)*c + S(2)*d*x) + S(1)), x), x, x**S(3)/S(3) - x**S(2)*log(f**(c + d*x) + S(1))/(d*log(f)) + x**S(2)/(d*(f**(c + d*x) + S(1))*log(f)) + S(2)*x*log(f**(-c - d*x) + S(1))/(d**S(2)*log(f)**S(2)) - S(2)*x*polylog(S(2), -f**(c + d*x))/(d**S(2)*log(f)**S(2)) - S(2)*polylog(S(2), -f**(-c - d*x))/(d**S(3)*log(f)**S(3)) + S(2)*polylog(S(3), -f**(c + d*x))/(d**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*f**(c + d*x) + c*f**(S(2)*c + S(2)*d*x)), x), x, S(2)*c*x**S(2)*log(S(1) + f**(-c - d*x)*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d*(b + sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)) - S(2)*c*x**S(2)*log(S(1) + f**(-c - d*x)*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d*(b - sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)) - S(4)*c*x*polylog(S(2), -f**(-c - d*x)*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d**S(2)*(b + sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)**S(2)) + S(4)*c*x*polylog(S(2), -f**(-c - d*x)*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d**S(2)*(b - sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)**S(2)) - S(4)*c*polylog(S(3), -f**(-c - d*x)*(b + sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d**S(3)*(b + sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)**S(3)) + S(4)*c*polylog(S(3), -f**(-c - d*x)*(b - sqrt(-S(4)*a*c + b**S(2)))/(S(2)*c))/(d**S(3)*(b - sqrt(-S(4)*a*c + b**S(2)))*sqrt(-S(4)*a*c + b**S(2))*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*f**(g + h*x))/(a + b*f**(g + h*x) + c*f**(S(2)*g + S(2)*h*x)), x), x, d*x/a - d*log(a + b*f**(g + h*x) + c*f**(S(2)*g + S(2)*h*x))/(S(2)*a*h*log(f)) + (-S(2)*a*e + b*d)*atanh((b + S(2)*c*f**(g + h*x))/sqrt(-S(4)*a*c + b**S(2)))/(a*h*sqrt(-S(4)*a*c + b**S(2))*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*f**(g + h*x))/(a + b*f**(g + h*x) + c*f**(S(2)*g + S(2)*h*x)), x), x, d*x/a - d*log(a + b*f**(g + h*x) + c*f**(S(2)*g + S(2)*h*x))/(S(2)*a*h*log(f)) + (-S(2)*a*e + b*d)*atanh((b + S(2)*c*f**(g + h*x))/sqrt(-S(4)*a*c + b**S(2)))/(a*h*sqrt(-S(4)*a*c + b**S(2))*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(exp(x) + S(2) + exp(-x)), x), x, -S(1)/(exp(x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(exp(x) + S(2) + exp(-x)), x), x, x - x/(exp(x) + S(1)) - log(exp(x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(exp(x) + S(2) + exp(-x)), x), x, -x**S(2)/(exp(x) + S(1)) - S(2)*x*log(S(1) + exp(-x)) + S(2)*polylog(S(2), -exp(-x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(f**(-c - d*x) + f**(c + d*x) + S(2)), x), x, -S(1)/(d*(f**(c + d*x) + S(1))*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(f**(-c - d*x) + f**(c + d*x) + S(2)), x), x, x/(d*log(f)) - x/(d*(f**(c + d*x) + S(1))*log(f)) - log(f**(c + d*x) + S(1))/(d**S(2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(f**(-c - d*x) + f**(c + d*x) + S(2)), x), x, -x**S(2)/(d*(f**(c + d*x) + S(1))*log(f)) - S(2)*x*log(f**(-c - d*x) + S(1))/(d**S(2)*log(f)**S(2)) + S(2)*polylog(S(2), -f**(-c - d*x))/(d**S(3)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(S(3)**x + S(2) + S(3)**(-x)), x), x, -S(1)/((S(3)**x + S(1))*log(S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(S(2)*exp(x) + S(1) - exp(-x)), x), x, log(-S(2)*exp(x) + S(1))/S(3) - log(exp(x) + S(1))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*exp(-x) + c*exp(x)), x), x, -S(2)*atanh((a + S(2)*c*exp(x))/sqrt(a**S(2) - S(4)*b*c))/sqrt(a**S(2) - S(4)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*exp(-x) + c*exp(x)), x), x, x*log(S(2)*c*exp(x)/(a - sqrt(a**S(2) - S(4)*b*c)) + S(1))/sqrt(a**S(2) - S(4)*b*c) - x*log(S(2)*c*exp(x)/(a + sqrt(a**S(2) - S(4)*b*c)) + S(1))/sqrt(a**S(2) - S(4)*b*c) + polylog(S(2), -S(2)*c*exp(x)/(a - sqrt(a**S(2) - S(4)*b*c)))/sqrt(a**S(2) - S(4)*b*c) - polylog(S(2), -S(2)*c*exp(x)/(a + sqrt(a**S(2) - S(4)*b*c)))/sqrt(a**S(2) - S(4)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*exp(-x) + c*exp(x)), x), x, x**S(2)*log(S(2)*c*exp(x)/(a - sqrt(a**S(2) - S(4)*b*c)) + S(1))/sqrt(a**S(2) - S(4)*b*c) - x**S(2)*log(S(2)*c*exp(x)/(a + sqrt(a**S(2) - S(4)*b*c)) + S(1))/sqrt(a**S(2) - S(4)*b*c) + S(2)*x*polylog(S(2), -S(2)*c*exp(x)/(a - sqrt(a**S(2) - S(4)*b*c)))/sqrt(a**S(2) - S(4)*b*c) - S(2)*x*polylog(S(2), -S(2)*c*exp(x)/(a + sqrt(a**S(2) - S(4)*b*c)))/sqrt(a**S(2) - S(4)*b*c) - S(2)*polylog(S(3), -S(2)*c*exp(x)/(a - sqrt(a**S(2) - S(4)*b*c)))/sqrt(a**S(2) - S(4)*b*c) + S(2)*polylog(S(3), -S(2)*c*exp(x)/(a + sqrt(a**S(2) - S(4)*b*c)))/sqrt(a**S(2) - S(4)*b*c), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a + b*f**(-c - d*x) + c*f**(c + d*x)), x), x, -S(2)*atanh((a + S(2)*c*f**(c + d*x))/sqrt(a**S(2) - S(4)*b*c))/(d*sqrt(a**S(2) - S(4)*b*c)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a + b*f**(-c - d*x) + c*f**(c + d*x)), x), x, x*log(S(2)*c*f**(c + d*x)/(a - sqrt(a**S(2) - S(4)*b*c)) + S(1))/(d*sqrt(a**S(2) - S(4)*b*c)*log(f)) - x*log(S(2)*c*f**(c + d*x)/(a + sqrt(a**S(2) - S(4)*b*c)) + S(1))/(d*sqrt(a**S(2) - S(4)*b*c)*log(f)) + polylog(S(2), -S(2)*c*f**(c + d*x)/(a - sqrt(a**S(2) - S(4)*b*c)))/(d**S(2)*sqrt(a**S(2) - S(4)*b*c)*log(f)**S(2)) - polylog(S(2), -S(2)*c*f**(c + d*x)/(a + sqrt(a**S(2) - S(4)*b*c)))/(d**S(2)*sqrt(a**S(2) - S(4)*b*c)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a + b*f**(-c - d*x) + c*f**(c + d*x)), x), x, x**S(2)*log(S(2)*c*f**(c + d*x)/(a - sqrt(a**S(2) - S(4)*b*c)) + S(1))/(d*sqrt(a**S(2) - S(4)*b*c)*log(f)) - x**S(2)*log(S(2)*c*f**(c + d*x)/(a + sqrt(a**S(2) - S(4)*b*c)) + S(1))/(d*sqrt(a**S(2) - S(4)*b*c)*log(f)) + S(2)*x*polylog(S(2), -S(2)*c*f**(c + d*x)/(a - sqrt(a**S(2) - S(4)*b*c)))/(d**S(2)*sqrt(a**S(2) - S(4)*b*c)*log(f)**S(2)) - S(2)*x*polylog(S(2), -S(2)*c*f**(c + d*x)/(a + sqrt(a**S(2) - S(4)*b*c)))/(d**S(2)*sqrt(a**S(2) - S(4)*b*c)*log(f)**S(2)) - S(2)*polylog(S(3), -S(2)*c*f**(c + d*x)/(a - sqrt(a**S(2) - S(4)*b*c)))/(d**S(3)*sqrt(a**S(2) - S(4)*b*c)*log(f)**S(3)) + S(2)*polylog(S(3), -S(2)*c*f**(c + d*x)/(a + sqrt(a**S(2) - S(4)*b*c)))/(d**S(3)*sqrt(a**S(2) - S(4)*b*c)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((F**(sqrt(-a*x + S(1))/sqrt(a*x + S(1))))**n/(-a**S(2)*x**S(2) + S(1)), x), x, -F**(-n*sqrt(-a*x + S(1))/sqrt(a*x + S(1)))*(F**(sqrt(-a*x + S(1))/sqrt(a*x + S(1))))**n*Ei(n*sqrt(-a*x + S(1))*log(F)/sqrt(a*x + S(1)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(S(3)*sqrt(-a*x + S(1))/sqrt(a*x + S(1)))/(-a**S(2)*x**S(2) + S(1)), x), x, -Ei(S(3)*sqrt(-a*x + S(1))*log(F)/sqrt(a*x + S(1)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(S(2)*sqrt(-a*x + S(1))/sqrt(a*x + S(1)))/(-a**S(2)*x**S(2) + S(1)), x), x, -Ei(S(2)*sqrt(-a*x + S(1))*log(F)/sqrt(a*x + S(1)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(sqrt(-a*x + S(1))/sqrt(a*x + S(1)))/(-a**S(2)*x**S(2) + S(1)), x), x, -Ei(sqrt(-a*x + S(1))*log(F)/sqrt(a*x + S(1)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(-sqrt(-a*x + S(1))/sqrt(a*x + S(1)))/(-a**S(2)*x**S(2) + S(1)), x), x, -Ei(-sqrt(-a*x + S(1))*log(F)/sqrt(a*x + S(1)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(-S(2)*sqrt(-a*x + S(1))/sqrt(a*x + S(1)))/(-a**S(2)*x**S(2) + S(1)), x), x, -Ei(-S(2)*sqrt(-a*x + S(1))*log(F)/sqrt(a*x + S(1)))/a, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((F**(sqrt(-c*x + S(1))/sqrt(c*x + S(1)))*b + a)**n/(-c**S(2)*x**S(2) + S(1)), x), x, -Integral((F**x*b + a)**n/x, (x, sqrt(-c*x + S(1))/sqrt(c*x + S(1))))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((F**(sqrt(-c*x + S(1))/sqrt(c*x + S(1)))*b + a)**S(3)/(-c**S(2)*x**S(2) + S(1)), x), x, -a**S(3)*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1)))/c - S(3)*a**S(2)*b*Ei(sqrt(-c*x + S(1))*log(F)/sqrt(c*x + S(1)))/c - S(3)*a*b**S(2)*Ei(S(2)*sqrt(-c*x + S(1))*log(F)/sqrt(c*x + S(1)))/c - b**S(3)*Ei(S(3)*sqrt(-c*x + S(1))*log(F)/sqrt(c*x + S(1)))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((F**(sqrt(-c*x + S(1))/sqrt(c*x + S(1)))*b + a)**S(2)/(-c**S(2)*x**S(2) + S(1)), x), x, -a**S(2)*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1)))/c - S(2)*a*b*Ei(sqrt(-c*x + S(1))*log(F)/sqrt(c*x + S(1)))/c - b**S(2)*Ei(S(2)*sqrt(-c*x + S(1))*log(F)/sqrt(c*x + S(1)))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((F**(sqrt(-c*x + S(1))/sqrt(c*x + S(1)))*b + a)/(-c**S(2)*x**S(2) + S(1)), x), x, -a*log(sqrt(-c*x + S(1))/sqrt(c*x + S(1)))/c - b*Ei(sqrt(-c*x + S(1))*log(F)/sqrt(c*x + S(1)))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((F**(sqrt(-c*x + S(1))/sqrt(c*x + S(1)))*b + a)*(-c**S(2)*x**S(2) + S(1))), x), x, -Integral(S(1)/(x*(F**x*b + a)), (x, sqrt(-c*x + S(1))/sqrt(c*x + S(1))))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/((F**(sqrt(-c*x + S(1))/sqrt(c*x + S(1)))*b + a)**S(2)*(-c**S(2)*x**S(2) + S(1))), x), x, -Integral(S(1)/(x*(F**x*b + a)**S(2)), (x, sqrt(-c*x + S(1))/sqrt(c*x + S(1))))/c, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a**x*b**x*x**S(2), x), x, a**x*b**x*x**S(2)/(log(a) + log(b)) - S(2)*a**x*b**x*x/(log(a) + log(b))**S(2) + S(2)*a**x*b**x/(log(a) + log(b))**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a**x*b**x*x, x), x, a**x*b**x*x/(log(a) + log(b)) - a**x*b**x/(log(a) + log(b))**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a**x*b**x, x), x, a**x*b**x/(log(a) + log(b)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a**x*b**x/x, x), x, Ei(x*(log(a) + log(b))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a**x*b**x/x**S(2), x), x, -a**x*b**x/x + (log(a) + log(b))*Ei(x*(log(a) + log(b))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a**x*b**x/x**S(3), x), x, -a**x*b**x*(log(a) + log(b))/(S(2)*x) - a**x*b**x/(S(2)*x**S(2)) + (log(a) + log(b))**S(2)*Ei(x*(log(a) + log(b)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a**x*b**x*c**x, x), x, a**x*b**x*c**x/(log(a) + log(b) + log(c)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a**x*b**(-x), x), x, a**x*b**(-x)/(log(a) - log(b)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(a**x*b**(-x)*x**S(2), x), x, a**x*b**(-x)*x**S(2)/(log(a) - log(b)) - S(2)*a**x*b**(-x)*x/(log(a) - log(b))**S(2) + S(2)*a**x*b**(-x)/(log(a) - log(b))**S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(2)*x)/(a + b*exp(x)), x), x, -a*log(a + b*exp(x))/b**S(2) + exp(x)/b, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(2)*x)/(a + b*exp(x))**S(2), x), x, a/(b**S(2)*(a + b*exp(x))) + log(a + b*exp(x))/b**S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(2)*x)/(a + b*exp(x))**S(3), x), x, exp(S(2)*x)/(S(2)*a*(a + b*exp(x))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(2)*x)/(a + b*exp(x))**S(4), x), x, a/(S(3)*b**S(2)*(a + b*exp(x))**S(3)) - S(1)/(S(2)*b**S(2)*(a + b*exp(x))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(4)*x)/(a + b*exp(S(2)*x)), x), x, -a*log(a + b*exp(S(2)*x))/(S(2)*b**S(2)) + exp(S(2)*x)/(S(2)*b), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(4)*x)/(a + b*exp(S(2)*x))**S(2), x), x, a/(S(2)*b**S(2)*(a + b*exp(S(2)*x))) + log(a + b*exp(S(2)*x))/(S(2)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(4)*x)/(a + b*exp(S(2)*x))**S(3), x), x, exp(S(4)*x)/(S(4)*a*(a + b*exp(S(2)*x))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(4)*x)/(a + b*exp(S(2)*x))**S(4), x), x, a/(S(6)*b**S(2)*(a + b*exp(S(2)*x))**S(3)) - S(1)/(S(4)*b**S(2)*(a + b*exp(S(2)*x))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(4)*x)/(a + b*exp(S(2)*x))**(S(2)/3), x), x, -S(3)*a*(a + b*exp(S(2)*x))**(S(1)/3)/(S(2)*b**S(2)) + S(3)*(a + b*exp(S(2)*x))**(S(4)/3)/(S(8)*b**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(n*x))*exp(-n*x), x), x, -a*exp(-n*x)/n + b*x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(n*x))**S(2)*exp(-n*x), x), x, -a**S(2)*exp(-n*x)/n + S(2)*a*b*x + b**S(2)*exp(n*x)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(n*x))**S(3)*exp(-n*x), x), x, -a**S(3)*exp(-n*x)/n + S(3)*a**S(2)*b*x + S(3)*a*b**S(2)*exp(n*x)/n + b**S(3)*exp(S(2)*n*x)/(S(2)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(-n*x)/(a + b*exp(n*x)), x), x, -exp(-n*x)/(a*n) - b*x/a**S(2) + b*log(a + b*exp(n*x))/(a**S(2)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(-n*x)/(a + b*exp(n*x))**S(2), x), x, -b/(a**S(2)*n*(a + b*exp(n*x))) - exp(-n*x)/(a**S(2)*n) - S(2)*b*x/a**S(3) + S(2)*b*log(a + b*exp(n*x))/(a**S(3)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(-n*x)/(a + b*exp(n*x))**S(3), x), x, -b/(S(2)*a**S(2)*n*(a + b*exp(n*x))**S(2)) - S(2)*b/(a**S(3)*n*(a + b*exp(n*x))) - exp(-n*x)/(a**S(3)*n) - S(3)*b*x/a**S(4) + S(3)*b*log(a + b*exp(n*x))/(a**S(4)*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x)/(c + d*f**(S(2)*b*x + e)), x), x, f**(a - e/S(2))*atan(sqrt(d)*f**(b*x + e/S(2))/sqrt(c))/(b*sqrt(c)*sqrt(d)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + S(2)*b*x)/(c + d*f**(S(2)*b*x + e)), x), x, f**(a - e)*log(c + d*f**(S(2)*b*x + e))/(S(2)*b*d*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + S(3)*b*x)/(c + d*f**(S(2)*b*x + e)), x), x, -sqrt(c)*f**(a - S(3)*e/S(2))*atan(sqrt(d)*f**(b*x + e/S(2))/sqrt(c))/(b*d**(S(3)/2)*log(f)) + f**(a + b*x - e)/(b*d*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + S(4)*b*x)/(c + d*f**(S(2)*b*x + e)), x), x, -c*f**(a - S(2)*e)*log(c + d*f**(S(2)*b*x + e))/(S(2)*b*d**S(2)*log(f)) + f**(a + S(2)*b*x - e)/(S(2)*b*d*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + S(5)*b*x)/(c + d*f**(S(2)*b*x + e)), x), x, c**(S(3)/2)*f**(a - S(5)*e/S(2))*atan(sqrt(d)*f**(b*x + e/S(2))/sqrt(c))/(b*d**(S(5)/2)*log(f)) - c*f**(a + b*x - S(2)*e)/(b*d**S(2)*log(f)) + f**(a + S(3)*b*x - e)/(S(3)*b*d*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(exp(S(2)*x) + S(1)), x), x, atan(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(-exp(S(2)*x) + S(1)), x), x, atanh(exp(x)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(x*exp(x)/(-exp(S(2)*x) + S(1)), x), x, x*atanh(exp(x)) + polylog(S(2), -exp(x))/S(2) - polylog(S(2), exp(x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(x)/(-exp(S(2)*x) + S(1)), x), x, x**S(2)*atanh(exp(x)) + x*polylog(S(2), -exp(x)) - x*polylog(S(2), exp(x)) - polylog(S(3), -exp(x)) + polylog(S(3), exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*exp(x)/(-exp(S(2)*x) + S(1)), x), x, x**S(3)*atanh(exp(x)) + S(3)*x**S(2)*polylog(S(2), -exp(x))/S(2) - S(3)*x**S(2)*polylog(S(2), exp(x))/S(2) - S(3)*x*polylog(S(3), -exp(x)) + S(3)*x*polylog(S(3), exp(x)) + S(3)*polylog(S(4), -exp(x)) - S(3)*polylog(S(4), exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x/(a + b*f**(S(2)*x)), x), x, atan(sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x*x/(a + b*f**(S(2)*x)), x), x, x*atan(sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)) - I*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)*log(f)**S(2)) + I*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x*x**S(2)/(a + b*f**(S(2)*x)), x), x, x**S(2)*atan(sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)) - I*x*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)**S(2)) + I*x*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)**S(2)) + I*polylog(S(3), -I*sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)**S(3)) - I*polylog(S(3), I*sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x*x**S(3)/(a + b*f**(S(2)*x)), x), x, x**S(3)*atan(sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)) - S(3)*I*x**S(2)*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)*log(f)**S(2)) + S(3)*I*x**S(2)*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(2)*sqrt(a)*sqrt(b)*log(f)**S(2)) + S(3)*I*x*polylog(S(3), -I*sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)**S(3)) - S(3)*I*x*polylog(S(3), I*sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)**S(3)) - S(3)*I*polylog(S(4), -I*sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)**S(4)) + S(3)*I*polylog(S(4), I*sqrt(b)*f**x/sqrt(a))/(sqrt(a)*sqrt(b)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x/(a + b*f**(S(2)*x))**S(2), x), x, f**x/(S(2)*a*(a + b*f**(S(2)*x))*log(f)) + atan(sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x*x/(a + b*f**(S(2)*x))**S(2), x), x, f**x*x/(S(2)*a*(a + b*f**(S(2)*x))*log(f)) + x*atan(sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)) - atan(sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(2)) - I*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(4)*a**(S(3)/2)*sqrt(b)*log(f)**S(2)) + I*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(4)*a**(S(3)/2)*sqrt(b)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x*x**S(2)/(a + b*f**(S(2)*x))**S(2), x), x, f**x*x**S(2)/(S(2)*a*(a + b*f**(S(2)*x))*log(f)) + x**S(2)*atan(sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)) - x*atan(sqrt(b)*f**x/sqrt(a))/(a**(S(3)/2)*sqrt(b)*log(f)**S(2)) - I*x*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(2)) + I*x*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(2)) + I*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(3)) - I*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(3)) + I*polylog(S(3), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(3)) - I*polylog(S(3), I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x*x**S(3)/(a + b*f**(S(2)*x))**S(2), x), x, f**x*x**S(3)/(S(2)*a*(a + b*f**(S(2)*x))*log(f)) + x**S(3)*atan(sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)) - S(3)*x**S(2)*atan(sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(2)) - S(3)*I*x**S(2)*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(4)*a**(S(3)/2)*sqrt(b)*log(f)**S(2)) + S(3)*I*x**S(2)*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(4)*a**(S(3)/2)*sqrt(b)*log(f)**S(2)) + S(3)*I*x*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(3)) - S(3)*I*x*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(3)) + S(3)*I*x*polylog(S(3), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(3)) - S(3)*I*x*polylog(S(3), I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(3)) - S(3)*I*polylog(S(3), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(4)) + S(3)*I*polylog(S(3), I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(4)) - S(3)*I*polylog(S(4), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(4)) + S(3)*I*polylog(S(4), I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(3)/2)*sqrt(b)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x*x/(a + b*f**(S(2)*x))**S(3), x), x, f**x*x/(S(4)*a*(a + b*f**(S(2)*x))**S(2)*log(f)) + S(3)*f**x*x/(S(8)*a**S(2)*(a + b*f**(S(2)*x))*log(f)) - f**x/(S(8)*a**S(2)*(a + b*f**(S(2)*x))*log(f)**S(2)) + S(3)*x*atan(sqrt(b)*f**x/sqrt(a))/(S(8)*a**(S(5)/2)*sqrt(b)*log(f)) - atan(sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(5)/2)*sqrt(b)*log(f)**S(2)) - S(3)*I*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(16)*a**(S(5)/2)*sqrt(b)*log(f)**S(2)) + S(3)*I*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(16)*a**(S(5)/2)*sqrt(b)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**x*x**S(2)/(a + b*f**(S(2)*x))**S(3), x), x, f**x*x**S(2)/(S(4)*a*(a + b*f**(S(2)*x))**S(2)*log(f)) + S(3)*f**x*x**S(2)/(S(8)*a**S(2)*(a + b*f**(S(2)*x))*log(f)) - f**x*x/(S(4)*a**S(2)*(a + b*f**(S(2)*x))*log(f)**S(2)) + S(3)*x**S(2)*atan(sqrt(b)*f**x/sqrt(a))/(S(8)*a**(S(5)/2)*sqrt(b)*log(f)) - x*atan(sqrt(b)*f**x/sqrt(a))/(a**(S(5)/2)*sqrt(b)*log(f)**S(2)) - S(3)*I*x*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(8)*a**(S(5)/2)*sqrt(b)*log(f)**S(2)) + S(3)*I*x*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(8)*a**(S(5)/2)*sqrt(b)*log(f)**S(2)) + atan(sqrt(b)*f**x/sqrt(a))/(S(4)*a**(S(5)/2)*sqrt(b)*log(f)**S(3)) + I*polylog(S(2), -I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(5)/2)*sqrt(b)*log(f)**S(3)) - I*polylog(S(2), I*sqrt(b)*f**x/sqrt(a))/(S(2)*a**(S(5)/2)*sqrt(b)*log(f)**S(3)) + S(3)*I*polylog(S(3), -I*sqrt(b)*f**x/sqrt(a))/(S(8)*a**(S(5)/2)*sqrt(b)*log(f)**S(3)) - S(3)*I*polylog(S(3), I*sqrt(b)*f**x/sqrt(a))/(S(8)*a**(S(5)/2)*sqrt(b)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a*f**x + b*f**(-x)), x), x, x*atan(sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)) - I*polylog(S(2), -I*sqrt(a)*f**x/sqrt(b))/(S(2)*sqrt(a)*sqrt(b)*log(f)**S(2)) + I*polylog(S(2), I*sqrt(a)*f**x/sqrt(b))/(S(2)*sqrt(a)*sqrt(b)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a*f**x + b*f**(-x)), x), x, x**S(2)*atan(sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)) - I*x*polylog(S(2), -I*sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)**S(2)) + I*x*polylog(S(2), I*sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)**S(2)) + I*polylog(S(3), -I*sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)**S(3)) - I*polylog(S(3), I*sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/(a*f**x + b*f**(-x)), x), x, x**S(3)*atan(sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)) - S(3)*I*x**S(2)*polylog(S(2), -I*sqrt(a)*f**x/sqrt(b))/(S(2)*sqrt(a)*sqrt(b)*log(f)**S(2)) + S(3)*I*x**S(2)*polylog(S(2), I*sqrt(a)*f**x/sqrt(b))/(S(2)*sqrt(a)*sqrt(b)*log(f)**S(2)) + S(3)*I*x*polylog(S(3), -I*sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)**S(3)) - S(3)*I*x*polylog(S(3), I*sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)**S(3)) - S(3)*I*polylog(S(4), -I*sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)**S(4)) + S(3)*I*polylog(S(4), I*sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)**S(4)), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(f**x/(a + b*f**(S(2)*x))**S(3), x), x, f**x/(S(4)*a*(a + b*f**(S(2)*x))**S(2)*log(f)) + S(3)*f**x/(S(8)*a**S(2)*(a + b*f**(S(2)*x))*log(f)) + S(3)*atan(sqrt(b)*f**x/sqrt(a))/(S(8)*a**(S(5)/2)*sqrt(b)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(a*f**x + b*f**(-x)), x), x, atan(sqrt(a)*f**x/sqrt(b))/(sqrt(a)*sqrt(b)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*f**x + b*f**(-x))**(S(-2)), x), x, -S(1)/(S(2)*a*(a*f**(S(2)*x) + b)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a*f**x + b*f**(-x))**S(2), x), x, -x/(S(2)*a*(a*f**(S(2)*x) + b)*log(f)) + x/(S(2)*a*b*log(f)) - log(a*f**(S(2)*x) + b)/(S(4)*a*b*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a*f**x + b*f**(-x))**S(2), x), x, -x**S(2)/(S(2)*a*(a*f**(S(2)*x) + b)*log(f)) - x*log(S(1) + b*f**(-S(2)*x)/a)/(S(2)*a*b*log(f)**S(2)) + polylog(S(2), -b*f**(-S(2)*x)/a)/(S(4)*a*b*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)/(a*f**x + b*f**(-x))**S(2), x), x, -x**S(3)/(S(2)*a*(a*f**(S(2)*x) + b)*log(f)) - S(3)*x**S(2)*log(S(1) + b*f**(-S(2)*x)/a)/(S(4)*a*b*log(f)**S(2)) + S(3)*x*polylog(S(2), -b*f**(-S(2)*x)/a)/(S(4)*a*b*log(f)**S(3)) + S(3)*polylog(S(3), -b*f**(-S(2)*x)/a)/(S(8)*a*b*log(f)**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a*f**x + b*f**(-x))**(S(-3)), x), x, -f**x/(S(4)*a*(a*f**(S(2)*x) + b)**S(2)*log(f)) + f**x/(S(8)*a*b*(a*f**(S(2)*x) + b)*log(f)) + atan(sqrt(a)*f**x/sqrt(b))/(S(8)*a**(S(3)/2)*b**(S(3)/2)*log(f)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(a*f**x + b*f**(-x))**S(3), x), x, -f**x*x/(S(4)*a*(a*f**(S(2)*x) + b)**S(2)*log(f)) + f**x*x/(S(8)*a*b*(a*f**(S(2)*x) + b)*log(f)) + f**x/(S(8)*a*b*(a*f**(S(2)*x) + b)*log(f)**S(2)) + x*atan(sqrt(a)*f**x/sqrt(b))/(S(8)*a**(S(3)/2)*b**(S(3)/2)*log(f)) - I*polylog(S(2), -I*sqrt(a)*f**x/sqrt(b))/(S(16)*a**(S(3)/2)*b**(S(3)/2)*log(f)**S(2)) + I*polylog(S(2), I*sqrt(a)*f**x/sqrt(b))/(S(16)*a**(S(3)/2)*b**(S(3)/2)*log(f)**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/(a*f**x + b*f**(-x))**S(3), x), x, -f**x*x**S(2)/(S(4)*a*(a*f**(S(2)*x) + b)**S(2)*log(f)) + f**x*x**S(2)/(S(8)*a*b*(a*f**(S(2)*x) + b)*log(f)) + f**x*x/(S(4)*a*b*(a*f**(S(2)*x) + b)*log(f)**S(2)) + x**S(2)*atan(sqrt(a)*f**x/sqrt(b))/(S(8)*a**(S(3)/2)*b**(S(3)/2)*log(f)) - I*x*polylog(S(2), -I*sqrt(a)*f**x/sqrt(b))/(S(8)*a**(S(3)/2)*b**(S(3)/2)*log(f)**S(2)) + I*x*polylog(S(2), I*sqrt(a)*f**x/sqrt(b))/(S(8)*a**(S(3)/2)*b**(S(3)/2)*log(f)**S(2)) - atan(sqrt(a)*f**x/sqrt(b))/(S(4)*a**(S(3)/2)*b**(S(3)/2)*log(f)**S(3)) + I*polylog(S(3), -I*sqrt(a)*f**x/sqrt(b))/(S(8)*a**(S(3)/2)*b**(S(3)/2)*log(f)**S(3)) - I*polylog(S(3), I*sqrt(a)*f**x/sqrt(b))/(S(8)*a**(S(3)/2)*b**(S(3)/2)*log(f)**S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(a + b*x + c*x**S(2))*g**(d + e*x + f*x**S(2)), x), x, sqrt(pi)*f**a*g**d*exp(-(b*log(f) + e*log(g))**S(2)/(S(4)*(c*log(f) + f*log(g))))*erfi((b*log(f)/S(2) + e*log(g)/S(2) + x*(c*log(f) + f*log(g)))/sqrt(c*log(f) + f*log(g)))/(S(2)*sqrt(c*log(f) + f*log(g))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(e*(c + d*x))*(G**(h*(f + g*x))*b + a)**n, x), x, F**(e*(c + d*x))*(G**(h*(f + g*x))*b + a)**(n + S(1))*hyper((S(1), d*e*log(F)/(g*h*log(G)) + n + S(1)), (d*e*log(F)/(g*h*log(G)) + S(1),), -G**(h*(f + g*x))*b/a)/(a*d*e*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(e*(c + d*x))*H**(t*(r + s*x))/(F**(e*(c + d*x))*b + a), x), x, H**(t*(r + s*x))*hyper((S(1), -s*t*log(H)/(d*e*log(F))), (S(1) - s*t*log(H)/(d*e*log(F)),), -F**(-e*(c + d*x))*a/b)/(b*s*t*log(H)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(e*(d*x + f))*H**(t*(r + s*x))/(F**(e*(c + d*x))*b + a), x), x, F**(-e*(c - f))*H**(t*(r + s*x))*hyper((S(1), -s*t*log(H)/(d*e*log(F))), (S(1) - s*t*log(H)/(d*e*log(F)),), -F**(-e*(c + d*x))*a/b)/(b*s*t*log(H)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*exp(h + i*x))*(f + g*x)**S(3)/(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x)), x), x, S(6)*g**S(3)*(e + (b*e - S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(4), -(b + sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(4)*(b + sqrt(-S(4)*a*c + b**S(2)))) + S(6)*g**S(3)*(e + (-b*e + S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(4), -(b - sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(4)*(b - sqrt(-S(4)*a*c + b**S(2)))) + S(6)*g**S(2)*(e + (b*e - S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)*polylog(S(3), -(b + sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(3)*(b + sqrt(-S(4)*a*c + b**S(2)))) + S(6)*g**S(2)*(e + (-b*e + S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)*polylog(S(3), -(b - sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(3)*(b - sqrt(-S(4)*a*c + b**S(2)))) + S(3)*g*(e + (b*e - S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)**S(2)*polylog(S(2), -(b + sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(2)*(b + sqrt(-S(4)*a*c + b**S(2)))) + S(3)*g*(e + (-b*e + S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)**S(2)*polylog(S(2), -(b - sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(2)*(b - sqrt(-S(4)*a*c + b**S(2)))) - (e + (b*e - S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)**S(3)*log(S(1) + (b + sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i*(b + sqrt(-S(4)*a*c + b**S(2)))) - (e + (-b*e + S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)**S(3)*log(S(1) + (b - sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i*(b - sqrt(-S(4)*a*c + b**S(2)))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*exp(h + i*x))*(f + g*x)**S(2)/(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x)), x), x, S(2)*g**S(2)*(e + (b*e - S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(3), -(b + sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(3)*(b + sqrt(-S(4)*a*c + b**S(2)))) + S(2)*g**S(2)*(e + (-b*e + S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(3), -(b - sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(3)*(b - sqrt(-S(4)*a*c + b**S(2)))) + S(2)*g*(e + (b*e - S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)*polylog(S(2), -(b + sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(2)*(b + sqrt(-S(4)*a*c + b**S(2)))) + S(2)*g*(e + (-b*e + S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)*polylog(S(2), -(b - sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(2)*(b - sqrt(-S(4)*a*c + b**S(2)))) - (e + (b*e - S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)**S(2)*log(S(1) + (b + sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i*(b + sqrt(-S(4)*a*c + b**S(2)))) - (e + (-b*e + S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)**S(2)*log(S(1) + (b - sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i*(b - sqrt(-S(4)*a*c + b**S(2)))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*exp(h + i*x))*(f + g*x)/(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x)), x), x, g*(e + (b*e - S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -(b + sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(2)*(b + sqrt(-S(4)*a*c + b**S(2)))) + g*(e + (-b*e + S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*polylog(S(2), -(b - sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i**S(2)*(b - sqrt(-S(4)*a*c + b**S(2)))) - (e + (b*e - S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(f + g*x)*log(S(1) + (b + sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i*(b + sqrt(-S(4)*a*c + b**S(2)))) + (e + (-b*e + S(2)*c*d)/sqrt(-S(4)*a*c + b**S(2)))*(-f - g*x)*log(S(1) + (b - sqrt(-S(4)*a*c + b**S(2)))*exp(-h - i*x)/(S(2)*c))/(i*(b - sqrt(-S(4)*a*c + b**S(2)))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((d + e*exp(h + i*x))/(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x)), x), x, d*x/a - d*log(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x))/(S(2)*a*i) + (-S(2)*a*e + b*d)*atanh((b + S(2)*c*exp(h + i*x))/sqrt(-S(4)*a*c + b**S(2)))/(a*i*sqrt(-S(4)*a*c + b**S(2))), expand=True, _diff=True, _numerical=True)

    # long time assert rubi_test(rubi_integrate((d + e*exp(h + i*x))/((f + g*x)*(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x))), x), x, d*Integral(S(1)/((f + g*x)*(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x))), x) + e*Integral(exp(h + i*x)/((f + g*x)*(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x))), x), expand=True, _diff=True, _numerical=True)
    # long time assert rubi_test(rubi_integrate((d + e*exp(h + i*x))/((f + g*x)**S(2)*(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x))), x), x, d*Integral(S(1)/((f + g*x)**S(2)*(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x))), x) + e*Integral(exp(h + i*x)/((f + g*x)**S(2)*(a + b*exp(h + i*x) + c*exp(S(2)*h + S(2)*i*x))), x), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(x*(-a*e*exp(c + d*x) + b*e)/(-S(2)*a*e*exp(c + d*x) - b*e*exp(S(2)*c + S(2)*d*x) + b*e), x), x, -x*log(S(1) + (a - sqrt(a**S(2) + b**S(2)))*exp(-c - d*x)/b)/(S(2)*d) - x*log(S(1) + (a + sqrt(a**S(2) + b**S(2)))*exp(-c - d*x)/b)/(S(2)*d) + polylog(S(2), -(a - sqrt(a**S(2) + b**S(2)))*exp(-c - d*x)/b)/(S(2)*d**S(2)) + polylog(S(2), -(a + sqrt(a**S(2) + b**S(2)))*exp(-c - d*x)/b)/(S(2)*d**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(a + b*x + c*x**S(3))*(b + S(3)*c*x**S(2)), x), x, F**(a + b*x + c*x**S(3))/log(F), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(F**(S(1)/(a + b*x + c*x**S(2)))*(b + S(2)*c*x)/(a + b*x + c*x**S(2))**S(2), x), x, -F**(S(1)/(a + b*x + c*x**S(2)))/log(F), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*(a + b*x + c*x**S(2))**m*exp(a + b*x + c*x**S(2)), x), x, (-a - b*x - c*x**S(2))**(-m)*(a + b*x + c*x**S(2))**m*Gamma(m + S(1), -a - b*x - c*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*(a + b*x + c*x**S(2))**S(3)*exp(a + b*x + c*x**S(2)), x), x, (a + b*x + c*x**S(2))**S(3)*exp(a + b*x + c*x**S(2)) - S(3)*(a + b*x + c*x**S(2))**S(2)*exp(a + b*x + c*x**S(2)) + S(6)*(a + b*x + c*x**S(2))*exp(a + b*x + c*x**S(2)) - S(6)*exp(a + b*x + c*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*(a + b*x + c*x**S(2))**S(2)*exp(a + b*x + c*x**S(2)), x), x, (a + b*x + c*x**S(2))**S(2)*exp(a + b*x + c*x**S(2)) - S(2)*(a + b*x + c*x**S(2))*exp(a + b*x + c*x**S(2)) + S(2)*exp(a + b*x + c*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*(a + b*x + c*x**S(2))*exp(a + b*x + c*x**S(2)), x), x, (a + b*x + c*x**S(2))*exp(a + b*x + c*x**S(2)) - exp(a + b*x + c*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*exp(a + b*x + c*x**S(2)), x), x, exp(a + b*x + c*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*exp(a + b*x + c*x**S(2))/(a + b*x + c*x**S(2)), x), x, Ei(a + b*x + c*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*exp(a + b*x + c*x**S(2))/(a + b*x + c*x**S(2))**S(2), x), x, Ei(a + b*x + c*x**S(2)) - exp(a + b*x + c*x**S(2))/(a + b*x + c*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*exp(a + b*x + c*x**S(2))/(a + b*x + c*x**S(2))**S(3), x), x, Ei(a + b*x + c*x**S(2))/S(2) - exp(a + b*x + c*x**S(2))/(S(2)*(a + b*x + c*x**S(2))) - exp(a + b*x + c*x**S(2))/(S(2)*(a + b*x + c*x**S(2))**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*(a + b*x + c*x**S(2))**(S(7)/2)*exp(a + b*x + c*x**S(2)), x), x, (a + b*x + c*x**S(2))**(S(7)/2)*exp(a + b*x + c*x**S(2)) - S(7)*(a + b*x + c*x**S(2))**(S(5)/2)*exp(a + b*x + c*x**S(2))/S(2) + S(35)*(a + b*x + c*x**S(2))**(S(3)/2)*exp(a + b*x + c*x**S(2))/S(4) - S(105)*sqrt(a + b*x + c*x**S(2))*exp(a + b*x + c*x**S(2))/S(8) + S(105)*sqrt(pi)*erfi(sqrt(a + b*x + c*x**S(2)))/S(16), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*(a + b*x + c*x**S(2))**(S(5)/2)*exp(a + b*x + c*x**S(2)), x), x, (a + b*x + c*x**S(2))**(S(5)/2)*exp(a + b*x + c*x**S(2)) - S(5)*(a + b*x + c*x**S(2))**(S(3)/2)*exp(a + b*x + c*x**S(2))/S(2) + S(15)*sqrt(a + b*x + c*x**S(2))*exp(a + b*x + c*x**S(2))/S(4) - S(15)*sqrt(pi)*erfi(sqrt(a + b*x + c*x**S(2)))/S(8), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*(a + b*x + c*x**S(2))**(S(3)/2)*exp(a + b*x + c*x**S(2)), x), x, (a + b*x + c*x**S(2))**(S(3)/2)*exp(a + b*x + c*x**S(2)) - S(3)*sqrt(a + b*x + c*x**S(2))*exp(a + b*x + c*x**S(2))/S(2) + S(3)*sqrt(pi)*erfi(sqrt(a + b*x + c*x**S(2)))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*sqrt(a + b*x + c*x**S(2))*exp(a + b*x + c*x**S(2)), x), x, sqrt(a + b*x + c*x**S(2))*exp(a + b*x + c*x**S(2)) - sqrt(pi)*erfi(sqrt(a + b*x + c*x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*exp(a + b*x + c*x**S(2))/sqrt(a + b*x + c*x**S(2)), x), x, sqrt(pi)*erfi(sqrt(a + b*x + c*x**S(2))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*exp(a + b*x + c*x**S(2))/(a + b*x + c*x**S(2))**(S(3)/2), x), x, S(2)*sqrt(pi)*erfi(sqrt(a + b*x + c*x**S(2))) - S(2)*exp(a + b*x + c*x**S(2))/sqrt(a + b*x + c*x**S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*exp(a + b*x + c*x**S(2))/(a + b*x + c*x**S(2))**(S(5)/2), x), x, S(4)*sqrt(pi)*erfi(sqrt(a + b*x + c*x**S(2)))/S(3) - S(4)*exp(a + b*x + c*x**S(2))/(S(3)*sqrt(a + b*x + c*x**S(2))) - S(2)*exp(a + b*x + c*x**S(2))/(S(3)*(a + b*x + c*x**S(2))**(S(3)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*exp(a + b*x + c*x**S(2))/(a + b*x + c*x**S(2))**(S(7)/2), x), x, S(8)*sqrt(pi)*erfi(sqrt(a + b*x + c*x**S(2)))/S(15) - S(8)*exp(a + b*x + c*x**S(2))/(S(15)*sqrt(a + b*x + c*x**S(2))) - S(4)*exp(a + b*x + c*x**S(2))/(S(15)*(a + b*x + c*x**S(2))**(S(3)/2)) - S(2)*exp(a + b*x + c*x**S(2))/(S(5)*(a + b*x + c*x**S(2))**(S(5)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((b + S(2)*c*x)*exp(a + b*x + c*x**S(2))/(a + b*x + c*x**S(2))**(S(9)/2), x), x, S(16)*sqrt(pi)*erfi(sqrt(a + b*x + c*x**S(2)))/S(105) - S(16)*exp(a + b*x + c*x**S(2))/(S(105)*sqrt(a + b*x + c*x**S(2))) - S(8)*exp(a + b*x + c*x**S(2))/(S(105)*(a + b*x + c*x**S(2))**(S(3)/2)) - S(4)*exp(a + b*x + c*x**S(2))/(S(35)*(a + b*x + c*x**S(2))**(S(5)/2)) - S(2)*exp(a + b*x + c*x**S(2))/(S(7)*(a + b*x + c*x**S(2))**(S(7)/2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(-x)/sqrt(S(1) - exp(-S(2)*x)), x), x, -asin(exp(-x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(exp(S(2)*x) + S(4)), x), x, atan(exp(x)/S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(-exp(S(2)*x) + S(1)), x), x, atanh(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(-S(4)*exp(S(2)*x) + S(3)), x), x, sqrt(S(3))*atanh(S(2)*sqrt(S(3))*exp(x)/S(3))/S(6), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(-S(4)*exp(S(2)*x) + S(3))*exp(x), x), x, sqrt(-S(4)*exp(S(2)*x) + S(3))*exp(x)/S(2) + S(3)*asin(S(2)*sqrt(S(3))*exp(x)/S(3))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*exp(x**S(2)), x), x, x**S(2)*exp(x**S(2))/S(2) - exp(x**S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(-exp(S(2)*x) + S(1))*exp(x), x), x, sqrt(-exp(S(2)*x) + S(1))*exp(x)/S(2) + asin(exp(x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/sqrt(exp(S(2)*x) + exp(x) + S(1)), x), x, asinh(sqrt(S(3))*(S(2)*exp(x) + S(1))/S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(exp(S(2)*x) + S(-4)), x), x, -atanh(exp(x)/S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(-x**S(2) + S(2)), x), x, -exp(-x**S(2) + S(2))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(-x**E + exp(x), x), x, -x**(E + S(1))/(E + S(1)) + exp(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(S(2)*x) + S(-1))/(exp(S(2)*x) + S(3)), x), x, -x/S(3) + S(2)*log(exp(S(2)*x) + S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/sqrt(-exp(S(2)*x) + S(1)), x), x, asin(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(2)*x)/(exp(S(4)*x) + S(1)), x), x, atan(exp(S(2)*x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(exp(S(2)*x) - S(3)*exp(x)), x), x, -x/S(9) + log(-exp(x) + S(3))/S(9) + exp(-x)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) + S(-2))*exp(x)/(exp(x) + S(1)), x), x, exp(x) - S(3)*log(exp(x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(exp(S(2)*x) + S(-1)), x), x, -atanh(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(exp(S(2)*x) + S(1)), x), x, atan(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) + exp(-x))/(exp(x) - exp(-x)), x), x, log(-exp(x) + exp(-x)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((exp(x) + exp(-x))/(exp(x) - exp(-x)), x), x, -x + log(-exp(S(2)*x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) - exp(-x))/(exp(x) + exp(-x)), x), x, log(exp(x) + exp(-x)), expand=True, _diff=True, _numerical=True) or rubi_test(rubi_integrate((exp(x) - exp(-x))/(exp(x) + exp(-x)), x), x, -x + log(exp(S(2)*x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(S(2)*x) + exp(-S(2)*x))/(exp(S(2)*x) - exp(-S(2)*x)), x), x, -x + log(-exp(S(4)*x) + S(1))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/sqrt(exp(S(2)*x) + S(1)), x), x, asinh(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(sqrt(x + S(4)))/sqrt(x + S(4)), x), x, S(2)*exp(sqrt(x + S(4))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/sqrt(exp(S(2)*x**S(2)) + S(-1)), x), x, atan(sqrt(exp(S(2)*x**S(2)) + S(-1)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(exp(S(2)*x) + S(9))*exp(x), x), x, sqrt(exp(S(2)*x) + S(9))*exp(x)/S(2) + S(9)*asinh(exp(x)/S(3))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(exp(S(2)*x) + S(1))*exp(x), x), x, sqrt(exp(S(2)*x) + S(1))*exp(x)/S(2) + asinh(exp(x))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(x**S(2))/(exp(S(2)*x**S(2)) + S(1)), x), x, atan(exp(x**S(2)))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(x**(S(3)/2)), x), x, S(2)*x**(S(3)/2)*exp(x**(S(3)/2))/S(3) - S(2)*exp(x**(S(3)/2))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/sqrt(exp(S(2)*x) + S(-3)), x), x, atanh(exp(x)/sqrt(exp(S(2)*x) + S(-3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(-exp(S(2)*x) + S(16)), x), x, atanh(exp(x)/S(4))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(5)*x)/(exp(S(10)*x) + S(1)), x), x, atan(exp(S(5)*x))/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(4)*x)/sqrt(exp(S(8)*x) + S(16)), x), x, asinh(exp(S(4)*x)/S(4))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(S(4)*x**S(3))*cos(S(7)*x**S(3)), x), x, S(7)*exp(S(4)*x**S(3))*sin(S(7)*x**S(3))/S(195) + S(4)*exp(S(4)*x**S(3))*cos(S(7)*x**S(3))/S(195), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(x**S(2) + S(1)), x), x, exp(x**S(2) + S(1))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(x**S(3) + S(1)), x), x, exp(x**S(3) + S(1))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(sqrt(x))/sqrt(x), x), x, S(2)*exp(sqrt(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x**(S(1)/3))/x**(S(2)/3), x), x, S(3)*exp(x**(S(1)/3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((x**S(5) + S(2)*x**S(3) + S(-8))*exp(S(3)*x), x), x, x**S(5)*exp(S(3)*x)/S(3) - S(5)*x**S(4)*exp(S(3)*x)/S(9) + S(38)*x**S(3)*exp(S(3)*x)/S(27) - S(38)*x**S(2)*exp(S(3)*x)/S(27) + S(76)*x*exp(S(3)*x)/S(81) - S(724)*exp(S(3)*x)/S(243), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((x + exp(x))**S(2), x), x, x**S(3)/S(3) + S(2)*x*exp(x) + exp(S(2)*x)/S(2) - S(2)*exp(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(S(3)*x) + exp(S(2)*x) + exp(x))*exp(-S(4)*x), x), x, -exp(-x) - exp(-S(2)*x)/S(2) - exp(-S(3)*x)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(exp(S(2)*x) + S(2)*exp(x) + S(1)), x), x, -S(1)/(exp(x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(-x)*cos(S(3)*x), x), x, S(3)*exp(-x)*sin(S(3)*x)/S(10) - exp(-x)*cos(S(3)*x)/S(10), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(2)*x)/(exp(S(2)*x) + S(3)*exp(x) + S(2)), x), x, -log(exp(x) + S(1)) + S(2)*log(exp(x) + S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(2)*x)/(exp(x) + S(1)), x), x, exp(x) - log(exp(x) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(3)*x)*cos(S(5)*x), x), x, S(5)*exp(S(3)*x)*sin(S(5)*x)/S(34) + S(3)*exp(S(3)*x)*cos(S(5)*x)/S(34), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(exp(x)*sech(exp(x)), x), x, atan(sinh(exp(x))), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(exp(-x)/(S(2)*exp(x) + S(1)), x), x, -S(2)*x + S(2)*log(S(2)*exp(x) + S(1)) - exp(-x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)*cos(S(3)*x + S(4)), x), x, S(3)*exp(x)*sin(S(3)*x + S(4))/S(10) + exp(x)*cos(S(3)*x + S(4))/S(10), expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(x*(exp(x) + exp(-x)), x), x, x*exp(x) - x*exp(-x) - exp(x) - exp(-x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(exp(S(2)*x) + S(3)*exp(x) + S(2)), x), x, -S(2)*atanh(S(2)*exp(x) + S(3)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(2)*x)/(exp(x) + S(1))**(S(1)/3), x), x, S(3)*(exp(x) + S(1))**(S(5)/3)/S(5) - S(3)*(exp(x) + S(1))**(S(2)/3)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(2)*x)/(exp(x) + S(1))**(S(1)/4), x), x, S(4)*(exp(x) + S(1))**(S(7)/4)/S(7) - S(4)*(exp(x) + S(1))**(S(3)/4)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((S(2)*exp(S(2)*x) - exp(x))/sqrt(S(3)*exp(S(2)*x) - S(6)*exp(x) + S(-1)), x), x, S(2)*sqrt(S(3)*exp(S(2)*x) - S(6)*exp(x) + S(-1))/S(3) - sqrt(S(3))*atanh(sqrt(S(3))*(-exp(x) + S(1))/sqrt(S(3)*exp(S(2)*x) - S(6)*exp(x) + S(-1)))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((x**S(2) - S(5)*x)*exp(x), x), x, x**S(2)*exp(x) - S(7)*x*exp(x) + S(7)*exp(x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((x**S(2) - x)*exp(S(3)*x), x), x, x**S(2)*exp(S(3)*x)/S(3) - S(5)*x*exp(S(3)*x)/S(9) + S(5)*exp(S(3)*x)/S(27), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(S(2)*x)*(log(x) + S(1))*exp(x**x), x), x, (x**x + S(-1))*exp(x**x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(S(7)*x) + exp(S(5)*x))/(exp(x) + exp(-x)), x), x, exp(S(6)*x)/S(6), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**(S(-2) - S(1)/x)*(-log(x) + S(1)), x), x, -x**(-S(1)/x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(x))**S(2), x), x, a**S(2)*x + S(2)*a*b*exp(x) + b**S(2)*exp(S(2)*x)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(x))**S(3), x), x, a**S(3)*x + S(3)*a**S(2)*b*exp(x) + S(3)*a*b**S(2)*exp(S(2)*x)/S(2) + b**S(3)*exp(S(3)*x)/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*exp(x))**S(4), x), x, a**S(4)*x + S(4)*a**S(3)*b*exp(x) + S(3)*a**S(2)*b**S(2)*exp(S(2)*x) + S(4)*a*b**S(3)*exp(S(3)*x)/S(3) + b**S(4)*exp(S(4)*x)/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(a + b*exp(c + d*x)), x), x, -S(2)*atanh(sqrt(a + b*exp(c + d*x))/sqrt(a))/(sqrt(a)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/sqrt(-a + b*exp(c + d*x)), x), x, S(2)*atan(sqrt(-a + b*exp(c + d*x))/sqrt(a))/(sqrt(a)*d), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(a + b*exp(c + d*x)), x), x, -S(2)*sqrt(a)*atanh(sqrt(a + b*exp(c + d*x))/sqrt(a))/d + S(2)*sqrt(a + b*exp(c + d*x))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(-a + b*exp(c + d*x)), x), x, -S(2)*sqrt(a)*atan(sqrt(-a + b*exp(c + d*x))/sqrt(a))/d + S(2)*sqrt(-a + b*exp(c + d*x))/d, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(6)*x)*sin(S(3)*x), x), x, S(2)*exp(S(6)*x)*sin(S(3)*x)/S(15) - exp(S(6)*x)*cos(S(3)*x)/S(15), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(3)*x)/(exp(S(2)*x) + S(1)), x), x, exp(x) - atan(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(3)*x)/(exp(S(2)*x) + S(-1)), x), x, exp(x) - atanh(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(-x)/sqrt(exp(S(2)*x) + S(1)), x), x, -sqrt(exp(S(2)*x) + S(1))*exp(-x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(exp(S(2)*x) - S(8)*exp(x) + S(-1)), x), x, sqrt(S(17))*atanh(sqrt(S(17))*(-exp(x) + S(4))/S(17))/S(17), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*exp(S(7)*x), x), x, x**S(3)*exp(S(7)*x)/S(7) - S(3)*x**S(2)*exp(S(7)*x)/S(49) + S(6)*x*exp(S(7)*x)/S(343) - S(6)*exp(S(7)*x)/S(2401), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*exp(-S(2)*x + S(8)), x), x, -x**S(3)*exp(-S(2)*x + S(8))/S(2) - S(3)*x**S(2)*exp(-S(2)*x + S(8))/S(4) - S(3)*x*exp(-S(2)*x + S(8))/S(4) - S(3)*exp(-S(2)*x + S(8))/S(8), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(-exp(S(2)*x) + S(9))*exp(x), x), x, sqrt(-exp(S(2)*x) + S(9))*exp(x)/S(2) + S(9)*asin(exp(x)/S(3))/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(sqrt(-exp(S(2)*x) + S(9))*exp(S(6)*x), x), x, -(-exp(S(2)*x) + S(9))**(S(7)/2)/S(7) + S(18)*(-exp(S(2)*x) + S(9))**(S(5)/2)/S(5) - S(27)*(-exp(S(2)*x) + S(9))**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(6)*x)/(-exp(x) + S(9))**(S(5)/2), x), x, S(2)*(-exp(x) + S(9))**(S(7)/2)/S(7) - S(18)*(-exp(x) + S(9))**(S(5)/2) + S(540)*(-exp(x) + S(9))**(S(3)/2) - S(14580)*sqrt(-exp(x) + S(9)) - S(65610)/sqrt(-exp(x) + S(9)) + S(39366)/(-exp(x) + S(9))**(S(3)/2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*(-S(7)*exp(x**S(4)) + S(2))**S(5), x), x, S(8)*x**S(4) - S(16807)*exp(S(5)*x**S(4))/S(20) + S(12005)*exp(S(4)*x**S(4))/S(8) - S(3430)*exp(S(3)*x**S(4))/S(3) + S(490)*exp(S(2)*x**S(4)) - S(140)*exp(x**S(4)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*sqrt(-exp(S(2)*x**S(2)) + S(1))*exp(x**S(2)), x), x, sqrt(-exp(S(2)*x**S(2)) + S(1))*exp(x**S(2))/S(4) + asin(exp(x**S(2)))/S(4), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(-exp(S(4)*x**S(3)) + S(1))**S(2)*exp(x**S(3)), x), x, exp(S(9)*x**S(3))/S(27) - S(2)*exp(S(5)*x**S(3))/S(15) + exp(x**S(3))/S(3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x + exp(x)), x), x, exp(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x + exp(x) + exp(exp(x))), x), x, exp(exp(exp(x))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) + exp(-x))**S(2), x), x, S(2)*x + exp(S(2)*x)/S(2) - exp(-S(2)*x)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(exp(x) + exp(-x)), x), x, atan(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) + exp(-x))**(S(-2)), x), x, -S(1)/(S(2)*(exp(S(2)*x) + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(exp(x) - exp(-x)), x), x, -atanh(exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) - exp(-x))**(S(-2)), x), x, S(1)/(S(2)*(-exp(S(2)*x) + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) - exp(-x))**S(2)*exp(x), x), x, exp(S(3)*x)/S(3) - S(2)*exp(x) - exp(-x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) - exp(-x))**S(3)*exp(x), x), x, S(3)*x + exp(S(4)*x)/S(4) - S(3)*exp(S(2)*x)/S(2) + exp(-S(2)*x)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((S(4)**x + S(1))/(S(2)**x + S(1)), x), x, S(2)**x/log(S(2)) + x - S(2)*log(S(2)**x + S(1))/log(S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((S(4)**x + S(1))/(S(1) + S(2)**(-x)), x), x, -S(2)**x/log(S(2)) + S(2)**(S(2)*x + S(-1))/log(S(2)) + S(2)*log(S(2)**x + S(1))/log(S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(-S(2)*a*exp((a + x)**S(2))/x + exp((a + x)**S(2))/x**S(2), x), x, sqrt(pi)*erfi(a + x) - exp((a + x)**S(2))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((x**S(8) + x**S(6) + x**S(4))*exp(-x**S(2)), x), x, -x**S(7)*exp(-x**S(2))/S(2) - S(9)*x**S(5)*exp(-x**S(2))/S(4) - S(49)*x**S(3)*exp(-x**S(2))/S(8) - S(147)*x*exp(-x**S(2))/S(16) + S(147)*sqrt(pi)*erf(x)/S(32), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(1)/(exp(S(3)*x) - exp(x)), x), x, -atanh(exp(x)) + exp(-x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((x**S(2) + x + S(-5))*exp(x)/(x + S(-1))**S(2), x), x, exp(x) - S(3)*exp(x)/(-x + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(3)*exp(x**S(2))/(x**S(2) + S(1))**S(2), x), x, exp(x**S(2))/(S(2)*(x**S(2) + S(1))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(S(3)*x)/sqrt(S(16)*exp(S(2)*x) + S(25)), x), x, sqrt(S(16)*exp(S(2)*x) + S(25))*exp(x)/S(32) - S(25)*asinh(S(4)*exp(x)/S(5))/S(128), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) + S(1))/sqrt(x + exp(x)), x), x, S(2)*sqrt(x + exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) + S(1))/(x + exp(x)), x), x, log(x + exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x**S(2))/x**S(2), x), x, sqrt(pi)*erfi(x) - exp(x**S(2))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((S(4)*x**S(4) + S(1))*exp(x**S(2))/x**S(2), x), x, S(2)*x*exp(x**S(2)) - exp(x**S(2))/x, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**S(2)*sqrt(f**x), x), x, S(16)*b**S(2)*sqrt(f**x)/log(f)**S(3) - S(8)*b*(a + b*x)*sqrt(f**x)/log(f)**S(2) + S(2)*(a + b*x)**S(2)*sqrt(f**x)/log(f), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(3)**(x**S(2) + S(1))*x, x), x, S(3)**(x**S(2) + S(1))/(S(2)*log(S(3))), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(sqrt(x))/sqrt(x), x), x, S(2)**(sqrt(x) + S(1))/log(S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**(S(1)/x)/x**S(2), x), x, -S(2)**(S(1)/x)/log(S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)**x + S(2)**(-x), x), x, S(2)**x/log(S(2)) - S(2)**(-x)/log(S(2)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((x**S(2) - S(3)*x + S(2))*exp(-S(4)*x), x), x, -x**S(2)*exp(-S(4)*x)/S(4) + S(5)*x*exp(-S(4)*x)/S(8) - S(11)*exp(-S(4)*x)/S(32), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(k**(x/S(2)) + x**(sqrt(k)), x), x, S(2)*k**(x/S(2))/log(k) + x**(sqrt(k) + S(1))/(sqrt(k) + S(1)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(10)**(sqrt(x))/sqrt(x), x), x, S(2)**(sqrt(x) + S(1))*S(5)**(sqrt(x))/log(S(10)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/sqrt(x + exp(x)) + S(1)/sqrt(x + exp(x)), x), x, S(2)*sqrt(x + exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(exp(x) + S(1))/sqrt(x + exp(x)) + S(2)*sqrt(x + exp(x)), x), x, S(2)*x*sqrt(x + exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(x)/sqrt(x + exp(x)) + x/sqrt(x + exp(x)) + S(2)*sqrt(x + exp(x)), x), x, S(2)*x*sqrt(x + exp(x)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*(exp(x) + S(1))/sqrt(x + exp(x)), x), x, S(2)*x*sqrt(x + exp(x)) - S(2)*Integral(sqrt(x + exp(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(x)/sqrt(x + exp(x)) + x/sqrt(x + exp(x)), x), x, S(2)*x*sqrt(x + exp(x)) - S(2)*Integral(sqrt(x + exp(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp(x)/sqrt(x + exp(x)), x), x, S(2)*x*sqrt(x + exp(x)) + S(2)*sqrt(x + exp(x)) - Integral(S(1)/sqrt(x + exp(x)), x) - S(3)*Integral(sqrt(x + exp(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*(S(3)*x**S(2) + S(5)*exp(x))/(S(5)*sqrt(x**S(3) + S(5)*exp(x))) + S(4)*x*sqrt(x**S(3) + S(5)*exp(x))/S(5), x), x, S(2)*x**S(2)*sqrt(x**S(3) + S(5)*exp(x))/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)*exp(x)/sqrt(x**S(3) + S(5)*exp(x)), x), x, S(2)*x**S(2)*sqrt(x**S(3) + S(5)*exp(x))/S(5) - S(4)*Integral(x*sqrt(x**S(3) + S(5)*exp(x)), x)/S(5) - S(3)*Integral(x**S(4)/sqrt(x**S(3) + S(5)*exp(x)), x)/S(5), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((-exp(x) + S(-1))/(x + exp(x))**(S(1)/3), x), x, -S(3)*(x + exp(x))**(S(2)/3)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(x + exp(x))**(S(1)/3) - (x + exp(x))**(S(2)/3) - S(1)/(x + exp(x))**(S(1)/3), x), x, -S(3)*(x + exp(x))**(S(2)/3)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(x + exp(x))**(S(1)/3), x), x, -S(3)*(x + exp(x))**(S(2)/3)/S(2) + Integral((x + exp(x))**(S(-1)/3), x) + Integral((x + exp(x))**(S(2)/3), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((S(5)*x + (S(2)*x + S(3))*exp(x))/(x + exp(x))**(S(1)/3), x), x, S(3)*x*(x + exp(x))**(S(2)/3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(S(2)*x*exp(x)/(x + exp(x))**(S(1)/3) + S(2)*x/(x + exp(x))**(S(1)/3) + S(3)*(x + exp(x))**(S(2)/3), x), x, S(3)*x*(x + exp(x))**(S(2)/3), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((exp(x) - exp(-x))*(exp(x) + exp(-x))**S(2)*exp(x), x), x, -x + exp(S(4)*x)/S(4) + exp(S(2)*x)/S(2) + exp(-S(2)*x)/S(2), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x/(x + exp(x)), x), x, Integral(x/(x + exp(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x**S(2)/sqrt(x + exp(x)), x), x, Integral(x**S(2)/sqrt(x + exp(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(x + exp(x)), x), x, Integral(exp(x)/(x + exp(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(x)/(x**S(2) + exp(x)), x), x, Integral(exp(x)/(x**S(2) + exp(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f(x)/(x + f(x)), x), x, x - Integral(x/(x + f(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f(x)/(x**S(2) + f(x)), x), x, x - Integral(x**S(2)/(x**S(2) + f(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f(x)/(x + f(x))**S(2), x), x, -Integral(x/(x + f(x))**S(2), x) + Integral(S(1)/(x + f(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f(x)/(x**S(2) + f(x))**S(2), x), x, -Integral(x**S(2)/(x**S(2) + f(x))**S(2), x) + Integral(S(1)/(x**S(2) + f(x)), x), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((F**(c + d*x)*a)**m*(F**(e + f*x)*b)**n, x), x, (F**(c + d*x)*a)**m*(F**(e + f*x)*b)**n/((d*m + f*n)*log(F)), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(exp(a + b*x**n + c + d*x**n), x), x, -x*(x**n*(-b - d))**(-S(1)/n)*Gamma(S(1)/n, x**n*(-b - d))*exp(a + c)/n, expand=True, _diff=True, _numerical=True)

    # (difference in simplify `exp(a*log(f) + c*log(g))` converts to `f**a*g**c` in mathematica)
    # failing assert rubi_test(rubi_integrate(f**(a + b*x**n)*g**(c + d*x**n), x), x, -f**a*g**c*x*(-x**n*(b*log(f) + d*log(g)))**(-S(1)/n)*Gamma(S(1)/n, -x**n*(b*log(f) + d*log(g)))/n, expand=True, _diff=True, _numerical=True)

    assert rubi_test(rubi_integrate(x**m*exp(x**n), x), x, -x**(m + S(1))*(-x**n)**(-(m + S(1))/n)*Gamma((m + S(1))/n, -x**n)/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**(x**n)*x**m, x), x, -x**(m + S(1))*(-x**n*log(f))**(-(m + S(1))/n)*Gamma((m + S(1))/n, -x**n*log(f))/n, expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate((a + b*x)**m*exp((a + b*x)**n), x), x, -(-(a + b*x)**n)**(-(m + S(1))/n)*(a + b*x)**(m + S(1))*Gamma((m + S(1))/n, -(a + b*x)**n)/(b*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(f**((a + b*x)**n)*(a + b*x)**m, x), x, -(-(a + b*x)**n*log(f))**(-(m + S(1))/n)*(a + b*x)**(m + S(1))*Gamma((m + S(1))/n, -(a + b*x)**n*log(f))/(b*n), expand=True, _diff=True, _numerical=True)
    assert rubi_test(rubi_integrate(x*exp((a + b*x)**S(3)), x), x, a*(a + b*x)*Gamma(S(1)/3, -(a + b*x)**S(3))/(S(3)*b**S(2)*(-(a + b*x)**S(3))**(S(1)/3)) - (a + b*x)**S(2)*Gamma(S(2)/3, -(a + b*x)**S(3))/(S(3)*b**S(2)*(-(a + b*x)**S(3))**(S(2)/3)), expand=True, _diff=True, _numerical=True)
