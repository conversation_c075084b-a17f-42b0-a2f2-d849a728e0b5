["test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_1d_input_nodes", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_2d_coordinates", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_constant_function_distribution", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_empty_nodes_array", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_invalid_input_dimensions", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_list_input_conversion", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_missing_parameter_in_distribution", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_multiple_nodes_multiple_params", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_multiple_nodes_single_param", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_parameter_order_consistency", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_single_node_multiple_params", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_single_node_single_param", "test/test_calc_params_on_nodes.py::TestCalcParamsOnNodes::test_single_point_as_1d_array", "test/test_distribution.py::TestDistribution::test_2d_input_evaluation", "test/test_distribution.py::TestDistribution::test_array_output", "test/test_distribution.py::TestDistribution::test_domain_transformation", "test/test_distribution.py::TestDistribution::test_empty_input", "test/test_distribution.py::TestDistribution::test_invalid_input", "test/test_distribution.py::TestDistribution::test_multiple_functions", "test/test_distribution.py::TestDistribution::test_multiple_points_evaluation", "test/test_distribution.py::TestDistribution::test_scalar_function_output", "test/test_distribution.py::TestDistribution::test_single_point_evaluation", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_complex_mesh_multiple_cell_blocks", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_empty_distributions", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_empty_mesh", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_empty_region", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_multiple_distributions", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_nonexistent_region", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_overlapping_regions", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_parameter_name_consistency", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_return_type_and_structure", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_single_distribution_multi_param", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_single_distribution_single_param", "test/test_eval_distributions_on_region_nodes.py::TestEvalDistributionsOnRegionNodes::test_single_node_element", "test/test_get_element_list.py::TestGetElementList::test_different_cell_data_types", "test/test_get_element_list.py::TestGetElementList::test_docstring_example", "test/test_get_element_list.py::TestGetElementList::test_element_tag_sequential_positioning", "test/test_get_element_list.py::TestGetElementList::test_empty_cell_block", "test/test_get_element_list.py::TestGetElementList::test_empty_input", "test/test_get_element_list.py::TestGetElementList::test_large_node_indices", "test/test_get_element_list.py::TestGetElementList::test_mismatched_lengths_error", "test/test_get_element_list.py::TestGetElementList::test_multiple_cell_blocks", "test/test_get_element_list.py::TestGetElementList::test_node_id_preservation", "test/test_get_element_list.py::TestGetElementList::test_return_type_and_structure", "test/test_get_element_list.py::TestGetElementList::test_single_cell_block_lines", "test/test_get_element_list.py::TestGetElementList::test_single_cell_block_triangles", "test/test_get_element_list.py::TestGetElementList::test_single_element_per_block", "test/test_get_selected_elements.py::TestGetSelectedElements::test_all_elements_selected", "test/test_get_selected_elements.py::TestGetSelectedElements::test_different_element_types", "test/test_get_selected_elements.py::TestGetSelectedElements::test_docstring_example", "test/test_get_selected_elements.py::TestGetSelectedElements::test_duplicate_selected_etags", "test/test_get_selected_elements.py::TestGetSelectedElements::test_empty_element_list", "test/test_get_selected_elements.py::TestGetSelectedElements::test_empty_selected_etags", "test/test_get_selected_elements.py::TestGetSelectedElements::test_large_node_indices", "test/test_get_selected_elements.py::TestGetSelectedElements::test_multiple_selected_elements", "test/test_get_selected_elements.py::TestGetSelectedElements::test_nonexistent_etag_raises_error", "test/test_get_selected_elements.py::TestGetSelectedElements::test_return_type_and_structure", "test/test_get_selected_elements.py::TestGetSelectedElements::test_selected_etags_order_preserved", "test/test_get_selected_elements.py::TestGetSelectedElements::test_single_element_list", "test/test_get_selected_elements.py::TestGetSelectedElements::test_single_selected_element", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_all_elements_selected", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_docstring_example", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_duplicate_nodes_in_element", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_empty_elements", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_empty_mask_all_false", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_large_node_indices", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_line_elements", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_mismatched_lengths_error", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_multiple_elements_selected", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_non_sequential_node_ids", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_overlapping_nodes", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_quad_elements", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_return_type_and_structure", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_single_element_array", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_single_element_selected", "test/test_get_unique_ntags_of_selected_elements.py::TestGetUniqueNtagsOfSelectedElements::test_zero_node_ids", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_different_cell_data_types", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_docstring_example", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_duplicate_selected_etags", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_empty_cell_block", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_empty_input", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_empty_selected_etags", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_large_element_tags", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_multiple_cell_blocks", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_order_independence_of_selected_etags", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_return_type_and_structure", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_single_cell_block_all_selected", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_single_cell_block_none_selected", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_single_cell_block_partial_selection", "test/test_mask_selected_elements.py::TestMaskSelectedElements::test_single_element_per_block", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_1d_nodes", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_2d_nodes", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_all_nodes_selected", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_different_input_types", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_docstring_example", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_duplicate_selected_node_ids", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_empty_nodes", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_empty_selected_node_ids", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_large_node_indices", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_mixed_valid_invalid_indices", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_multiple_nodes_selected", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_negative_node_ids_ignored", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_no_nodes_selected", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_non_sequential_node_ids", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_order_independence_of_selected_node_ids", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_return_type_and_structure", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_single_node_array", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_single_node_selected", "test/test_mask_selected_nodes.py::TestMaskSelectedNodes::test_zero_dimensional_edge_case"]