../../Scripts/isympy.exe,sha256=IAVzrgWEbFACcgFIvtzY5NsCauVSGYMItR3SejkhDWw,40968
..\..\share\man\man1\isympy.1,sha256=9DZdSOIQLikrATHlbkdDZ04LBQigZDUE0_oCXBDvdBs,6659
isympy.py,sha256=_CEhK_UH8FpCTGhf_8yZMrNzzHIFbldYfjcozNbX7AQ,11199
sympy-1.11.1.dist-info/AUTHORS,sha256=-ftL4cJQG6TwV3LW5WFdugV13wXd6SUX_9Tko4BZR68,46763
sympy-1.11.1.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
sympy-1.11.1.dist-info/LICENSE,sha256=DXU9_7vLdBK8GtJeXFGTyf9_jGozMfO2B2H429dEt0I,7885
sympy-1.11.1.dist-info/METADATA,sha256=5a-bE9bN7-9LczC1dShmJGfVO2e0EMgaYWFCvWrxoY8,12701
sympy-1.11.1.dist-info/RECORD,,
sympy-1.11.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy-1.11.1.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
sympy-1.11.1.dist-info/entry_points.txt,sha256=Sp-vLJom4PRlhGfY6RpUre7SjYm33JNq9NCwCGeW-fQ,39
sympy-1.11.1.dist-info/top_level.txt,sha256=elXb5xfjLdjgSSoQFk4_2Qu3lp2CIaglF9MQtfIoH7o,13
sympy/__init__.py,sha256=XO9XKqsIFs7kyag4kXmBqdPU0LhKmtwJ1rGwtJlLkos,28621
sympy/abc.py,sha256=P1iQKfXl7Iut6Z5Y97QmGr_UqiAZ6qR-eoRMtYacGfA,3748
sympy/algebras/__init__.py,sha256=7PRGOW30nlMOTeUPR7iy8l5xGoE2yCBEfRbjqDKWOgU,62
sympy/algebras/quaternion.py,sha256=IadLKa3tbiRGIDFHTfrdslw90Z4zvuSvmu-mgC_OIrY,32023
sympy/algebras/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/algebras/tests/test_quaternion.py,sha256=tuyd67sfMwaePgtoyIH5-kWxAuNb6hJultjBT5MOGi8,12432
sympy/assumptions/__init__.py,sha256=PFS8djTqiNbGVMjg7PaPjEfwmjyZVfioXiRVzqqA3E0,550
sympy/assumptions/ask.py,sha256=MJKqqoE9uaRUn1gcYR_16W7hvGYRcoR-ujXjhkR6LXM,18865
sympy/assumptions/ask_generated.py,sha256=DSsSGSwjV0K3ASMvWvatFEXviYKXR-1xPwySPsLL-c4,17083
sympy/assumptions/assume.py,sha256=f65Q_PDlThZMipujusMW_HMAujOT_AGdZSapt8ix-vQ,14670
sympy/assumptions/cnf.py,sha256=15zd5niV4daLQbFV0r23-Z2kLJRc91xTxCFlChXuXqI,12722
sympy/assumptions/facts.py,sha256=q0SDVbzmU46_8mf63Uao5pYE4MgyrhR9vn94QJqQSv8,7609
sympy/assumptions/handlers/__init__.py,sha256=lvjAfPdz0MDjTxjuzbBSGBco2OmpZRiGixSG0oaiZi0,330
sympy/assumptions/handlers/calculus.py,sha256=ul36wLjxrU_LUxEWX63dWklWHgHWw5xVT0d7BkZCdFE,7198
sympy/assumptions/handlers/common.py,sha256=sW_viw2xdO9Klqf31x3YlYcGlhgRj52HV1JFmwrgtb4,4064
sympy/assumptions/handlers/matrices.py,sha256=Gdauk2xk1hKPRr4i6RpvOMHtDnyVD34x1OyhL-Oh8Hc,22321
sympy/assumptions/handlers/ntheory.py,sha256=2i-EhgO9q1LfDLzN3BZVzHNfaXSsce131XtBr5TEh2I,7213
sympy/assumptions/handlers/order.py,sha256=Y6Txiykbj4gkibX0mrcUUlhtRWE27p-4lpG4WACX3Ik,12222
sympy/assumptions/handlers/sets.py,sha256=2Jh2G6Ce1qz9Imzv5et_v-sMxY62j3rFdnp1UZ_PGB8,23818
sympy/assumptions/predicates/__init__.py,sha256=q1C7iWpvdDymEUZNyzJvZLsLtgwSkYtCixME-fYyIDw,110
sympy/assumptions/predicates/calculus.py,sha256=vFnlYVYZVd6D9OwA7-3bDK_Q0jf2iCZCZiMlWenw0Vg,1889
sympy/assumptions/predicates/common.py,sha256=zpByACpa_tF0nVNB0J_rJehnXkHtkxhchn1DvkVVS-s,2279
sympy/assumptions/predicates/matrices.py,sha256=8txCKP55plGkCrO9xhO1NMAyw05uFg9bnPs4KpDvcqw,12139
sympy/assumptions/predicates/ntheory.py,sha256=wvFNFSf0S4egbY7REw0V0ANC03CuiRU9PLmdi16VfHo,2546
sympy/assumptions/predicates/order.py,sha256=ZI4u_WfusMPAEsMFawkSN9QvaMwI3-Jt3-U_xIcGl_8,9508
sympy/assumptions/predicates/sets.py,sha256=0cTuD90CE_XSSf55f_Y-00TnxnXc6UxMVNInxSgpT4g,8935
sympy/assumptions/refine.py,sha256=TmCB0rxh38ATNBmbUY_2dN6tWB7CXcftjhy9NcH8CqI,11939
sympy/assumptions/relation/__init__.py,sha256=t2tZNEIK7w-xXshRQIRL8tIyiNe1W5fMhN7QNRPnQFo,261
sympy/assumptions/relation/binrel.py,sha256=EYXBp7kh2LZq6VI9A-f-bNWxQ3PesDp6aKUx22hI7aI,6314
sympy/assumptions/relation/equality.py,sha256=RbwztgBBVlnfc9-M-IYKonybITSr8WdqWQqwlp2j3V8,7160
sympy/assumptions/satask.py,sha256=TXDXIsri1tdxsRi_X64NpA3vidt47PSo91i17AYZD34,11279
sympy/assumptions/sathandlers.py,sha256=Uu_ur8XtxUH5uaAlfGQHEyx2S1-3Q00EFmezDYaGxT0,9428
sympy/assumptions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/assumptions/tests/test_assumptions_2.py,sha256=oNgIDOoW-GpBbXxbtw05SWnE8I7sGislYmB3MDogwB4,1070
sympy/assumptions/tests/test_context.py,sha256=I5gES7AY9_vz1-CEaCchy4MXABtX85ncNkvoRuLskG8,1153
sympy/assumptions/tests/test_matrices.py,sha256=nzSofuawc18hNe9Nj0dN_lTeDwa2KbPjt4K2rvb3xmw,12258
sympy/assumptions/tests/test_query.py,sha256=teHsXTfPw_q4197tXcz2Ov-scVxDHP-T_LpcELmOMnI,97999
sympy/assumptions/tests/test_refine.py,sha256=7Za4kTvEZOvvYHogD3YYnIx3u78_w5aCPRzd7Yp4jZ4,8515
sympy/assumptions/tests/test_satask.py,sha256=IIqqIxzkLfANpTNBKEsCGCp3Bm8zmDnYd23woqKh9EE,15741
sympy/assumptions/tests/test_sathandlers.py,sha256=jMCZQb3G6pVQ5MHaSTWV_0eULHaCF8Mowu12Ll72rgs,1842
sympy/assumptions/tests/test_wrapper.py,sha256=KB7VAtaQbUSdwh9_Bp0S63RQwn1en2Gtc5Hfs50Iwlg,949
sympy/assumptions/wrapper.py,sha256=dhzunhkLtLfRAhrCpcwRAbGUPYChJ1WqfPgrpn6RHt0,4245
sympy/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/benchmarks/bench_discrete_log.py,sha256=CNchIJ5HFMPpNlVZh2vOU0GgQ3bse6hqyqDovpDHlKE,2473
sympy/benchmarks/bench_meijerint.py,sha256=dSNdZhoc8a4h50wRtbOxLwpmgUiuMFpe6ytTLURcplY,11610
sympy/benchmarks/bench_symbench.py,sha256=UMD3eYf_Poht0qxjdH2_axGwwON6cZo1Sp700Ci1M1M,2997
sympy/calculus/__init__.py,sha256=IWDc6qPbEcWyTm9QM6V8vSAs-5OtGNijimykoWz3Clc,828
sympy/calculus/accumulationbounds.py,sha256=QUtDtgO3s6oxkYGsXnJtOPtUYXk_uqkQiNWfCmp9Gc4,28670
sympy/calculus/euler.py,sha256=0QrHD9TYKlSZuO8drnU3bUFJrSu8v5SncqtkRSWLjGM,3436
sympy/calculus/finite_diff.py,sha256=X7qZJ5GmHlHKokUUMFoaQqrqX2jLRq4b7W2G5aWntzM,17053
sympy/calculus/singularities.py,sha256=ctVHpnE4Z7iE6tNAssMWmdXu9qWXOXzVJasLxC-cToQ,11757
sympy/calculus/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/calculus/tests/test_accumulationbounds.py,sha256=a_Ry2nKX5WbhSe1Bk2k0W6-VWOpVTg0FnA9u8rNSIV4,11195
sympy/calculus/tests/test_euler.py,sha256=YWpts4pWSiYEwRsi5DLQ16JgC9109-9NKZIL_IO6_Aw,2683
sympy/calculus/tests/test_finite_diff.py,sha256=V52uNDNvarcK_FXnWrPZjifFMRWTy_2H4lt3FmvA4W4,7760
sympy/calculus/tests/test_singularities.py,sha256=SN1TrOJGLWgPwwy9TXk02HcHphaDWCEFrHepV-PlmnY,4213
sympy/calculus/tests/test_util.py,sha256=S5_YEGW0z7xzzthShrSsg2wAmzE9mR4u4Ndzuzw_Gx8,15034
sympy/calculus/util.py,sha256=G-_2fKjS8gAjSUQ-Q2Xs3UR7RCQH2Fz2UKb29LArHm4,26125
sympy/categories/__init__.py,sha256=XiKBVC6pbDED-OVtNlSH-fGB8dB_jWLqwCEO7wBTAyA,984
sympy/categories/baseclasses.py,sha256=G3wCiNCgNiTLLFZxGLd2ZFmnsbiRxhapSfZWlWSC508,31411
sympy/categories/diagram_drawing.py,sha256=1LuZfytsNeVTQ-Ui9aqgcCmnnake3a-Owbx5VwUXW4Q,95498
sympy/categories/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/categories/tests/test_baseclasses.py,sha256=SwD6QsfSlrEdpD2dbkcN62CPVIRP5SadjCplLrMAoa8,5767
sympy/categories/tests/test_drawing.py,sha256=IELPpadmnQyQ2x5a5qHC8ioq5kfT1UnAl4h1vO3gbqg,27848
sympy/codegen/__init__.py,sha256=sQcJsyLyoRh9ccOPhv2eZ-wHjQrArByOON9ndj-MYgQ,974
sympy/codegen/abstract_nodes.py,sha256=TY4ecftqnym5viYInnb59zGPPFXdeSGQwi--xTz6Pvo,490
sympy/codegen/algorithms.py,sha256=_isSQBzQzn1xKkYhYEF7nVK1sCa7n78Qo5AoCeNs8eU,5056
sympy/codegen/approximations.py,sha256=UnVbikz2vjJo8DtE02ipa6ZEsCe5lXOT_r16F5ByW4Q,6447
sympy/codegen/ast.py,sha256=ZBAYPTMGMqHi4jOBtIk7YtO9Iki8AFx58_cEwLXoec0,56345
sympy/codegen/cfunctions.py,sha256=VprdBbowbm8dR7mB1F9J908X25Bo0CaIOXA7jVvrif8,11804
sympy/codegen/cnodes.py,sha256=ZFBxHsRBUcQ14EJRURZXh9EjTsSSJGwmWubfmpE0-p4,2823
sympy/codegen/cutils.py,sha256=vlzMs8OkC5Bu4sIP-AF2mYf_tIo7Uo4r2DAI_LNhZzM,383
sympy/codegen/cxxnodes.py,sha256=Om-EBfYduFF97tgXOF68rr8zYbngem9kBRm9SJiKLSM,342
sympy/codegen/fnodes.py,sha256=x0qCVeUsHlocTFgrqxziU9ZtfrWxipFXo9k0CbYpCjY,18932
sympy/codegen/futils.py,sha256=k-mxMJKr_Q_afTy6NrKNl_N2XQLBmSdZAssO5hBonNY,1792
sympy/codegen/matrix_nodes.py,sha256=Hhip0cbBj27i-4JwVinkEt4PHRbAIe5ERxwyywoSJm8,2089
sympy/codegen/numpy_nodes.py,sha256=iIWZeppvzy1tX1VfEfsgkWHmZ8pZtCLFir-0Qy5_ltw,3147
sympy/codegen/pynodes.py,sha256=Neo1gFQ9kC31T-gH8TeeCaDDNaDe5deIP97MRZFgMHk,243
sympy/codegen/pyutils.py,sha256=HfF6SP710Y7yExZcSesI0usVaDiWdEPEmMtyMD3JtOY,838
sympy/codegen/rewriting.py,sha256=6s1by6X9i6YNTuCgeO-VKbugvu6wPaoyh36SY7ASLNw,11532
sympy/codegen/scipy_nodes.py,sha256=DaSs4xrRv1pS6YH6mPVF4WJzwrPOwwM2ajJsO6r0MwM,1176
sympy/codegen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/codegen/tests/test_abstract_nodes.py,sha256=a_GKf3FpeNN8zfMc-V8AaSrQtEI1oiLfJOco2VKiSKI,451
sympy/codegen/tests/test_algorithms.py,sha256=uvlQLfGAddk6Le7unfFDTAZop0rP4rlaxFtAHoZjDlk,4702
sympy/codegen/tests/test_applications.py,sha256=QPzfg8O8TMHVweVBpFBkF5VV9tAA_ijcagf_3CSl7rk,2190
sympy/codegen/tests/test_approximations.py,sha256=SZpOUzahb_bJOceD0DLdmeiw-jN37OPmf5TRp1dyRgM,2035
sympy/codegen/tests/test_ast.py,sha256=CxiZ3OBpLbOW-TDN0BeWaLTaz0IPGDnYBlzkGaguBBI,21680
sympy/codegen/tests/test_cfunctions.py,sha256=EuRwj9U00iLc2--qtY2YD7TpICndQ0gVsCXTYHrIFhQ,4613
sympy/codegen/tests/test_cnodes.py,sha256=FlI5XP39K3kC1QWKQ-QKkzNQw8TROjj5mKXJhK1UU2c,3039
sympy/codegen/tests/test_cxxnodes.py,sha256=5OwN8D_ZtKN9z5uNeUwbUkyAGzNLrTgIKUlcRWmOSpE,366
sympy/codegen/tests/test_fnodes.py,sha256=r206n8YM0D1vFP0vdjUaAR7QRpmUWw8VmqSMFxh8FU8,6643
sympy/codegen/tests/test_numpy_nodes.py,sha256=VcG7eGVlzx9sSKRp1n9zfK0NjigxY5WOW6F_nQnnnSs,1658
sympy/codegen/tests/test_pynodes.py,sha256=Gso18KKzSwA-1AHC55SgHPAfH1GrGUCGaN6QR7iuEO0,432
sympy/codegen/tests/test_pyutils.py,sha256=jr5QGvUP0M1Rr2_7vHTazlMaJOoMHztqFTxT6EkBcb4,285
sympy/codegen/tests/test_rewriting.py,sha256=veyEnqeVftIsgKlTG_xMmCWcWPLDeSV_OpAEekhfvpQ,13475
sympy/codegen/tests/test_scipy_nodes.py,sha256=xicOg2JT60xN3fQEsYKP6UYFNWiTiEJOAyXrgnPF6cU,650
sympy/combinatorics/__init__.py,sha256=Dx9xakpHuTIgy4G8zVjAY6pTu8J9_K3d_jKPizRMdVo,1500
sympy/combinatorics/coset_table.py,sha256=A3O5l1tkFmF1mEqiab08eBcR6lAdiqKJ2uPao3Ucvlk,42935
sympy/combinatorics/fp_groups.py,sha256=QjeCEGBfTBbMZd-WpCOY5iEUyt8O7eJXa3RDLfMC7wk,47800
sympy/combinatorics/free_groups.py,sha256=DC1WJj-OpkYWBhG8xEIRzobYqWjzahXuCbJ1tzb1R8U,39562
sympy/combinatorics/generators.py,sha256=3jkz9kmKIZJIrmSYfFUGGOXozyAlltYaaJiKz4BFfHA,7492
sympy/combinatorics/graycode.py,sha256=fbm_5OScw4MhGv3nrEDuxZPLY43Z-ZR1DtKke_ZcGKc,11164
sympy/combinatorics/group_constructs.py,sha256=IKx12_yWJqEQ7g-oBuAWd5VRLbCOWyL0LG4PQu43BS8,2021
sympy/combinatorics/group_numbers.py,sha256=QuB-EvXmTulg5MuI4aLE3GlmFNTGKulAP-DQW9TBXU4,3073
sympy/combinatorics/homomorphisms.py,sha256=bllijpNSeBSztcL93NehL_Cgq-Agv7-v7uewdy828fY,19247
sympy/combinatorics/named_groups.py,sha256=gCZ0XpGu16ngbBRh5mgB6FhkhFnjswENb5H1kLdix4E,7900
sympy/combinatorics/partitions.py,sha256=yFaDPFGe5YQkuPTlh1QG44hdgo9hK_tUDtNLaltZoXE,20845
sympy/combinatorics/pc_groups.py,sha256=TlkasW4N9xm57E9wvPrDSY34A84dTeeR_WAiGf3R79Q,21359
sympy/combinatorics/perm_groups.py,sha256=hqbH_8zi71VTEa-HyLfqiyfkOiTrhSSDUyyOJFfRONQ,182040
sympy/combinatorics/permutations.py,sha256=PfrWdZPb98RN8eAta__horGaMo-nsrSlZS_WhfGqAxw,87589
sympy/combinatorics/polyhedron.py,sha256=GPzV68BkDrX09RO65nSrldbIjsaPwWIfoeI9Fi1vSWE,35960
sympy/combinatorics/prufer.py,sha256=vwaSBKXmE9Mp6LTKSDVwkZP22W9YXDGzQS0pUMeuGqQ,12097
sympy/combinatorics/rewritingsystem.py,sha256=NmdOxFV4LZ1WDDAHEjiKtA9mfMW8bnojGb68sAAdmiw,17104
sympy/combinatorics/rewritingsystem_fsm.py,sha256=CKGhLqyvxY0mlmy8_Hb4WzkSdWYPUaU2yZYhz-0iZ5w,2433
sympy/combinatorics/schur_number.py,sha256=anXMobUrT3g189J4SiwYVwXq3zc2bIDip1YBMd98mqc,4436
sympy/combinatorics/subsets.py,sha256=8sDzJ5Hi29_lT0ModrUdJXFeAQRxqaMNbdG7R6wMuCE,16051
sympy/combinatorics/tensor_can.py,sha256=F8XrUC3Cf8tkMcsdLHxO94ShbuFZd4xsUOa0Tfs6iZc,40783
sympy/combinatorics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/combinatorics/tests/test_coset_table.py,sha256=cEUF0OH6SNhN_kh069wMsq6h4eSVqbDLghrg2r9Ht48,28474
sympy/combinatorics/tests/test_fp_groups.py,sha256=7ATMwzPvAoWiH7Cex-D63nmlOa20h70zO5TWGVisFwM,9969
sympy/combinatorics/tests/test_free_groups.py,sha256=h3tPyjMA79M9QMc0rOlgVXU31lZ0s_xoY_YIVsVz0Fg,6161
sympy/combinatorics/tests/test_generators.py,sha256=6YpOp0i5PRGtySPNZseQ8mjSXbwpfGfz0hDB4kfk40Q,3567
sympy/combinatorics/tests/test_graycode.py,sha256=pI4e7Y615d5Bmmxui6fdEeyca6j6KSD0YmeychV6ORk,2800
sympy/combinatorics/tests/test_group_constructs.py,sha256=jJLwMdhuUalKv4Aql9SzV2utK8Ex-IYdMecggr95pi8,450
sympy/combinatorics/tests/test_group_numbers.py,sha256=nRxK4R8Cdq4Ni9e_6n4fRjir3VBOmXMzAIXnlRNQD3Y,989
sympy/combinatorics/tests/test_homomorphisms.py,sha256=VupppDAg9ODaGCDU-Z0dM2syhsUQjlXFfJLi6qUpbLg,3628
sympy/combinatorics/tests/test_named_groups.py,sha256=tsuDVGv4iHGEZ0BVR87_ENhyAfZvFIl0M6Dv_HX1VoY,1931
sympy/combinatorics/tests/test_partitions.py,sha256=oppszKJLLSpcEzHgespIveSmEC3fDZ0qkus1k7MBt4E,4097
sympy/combinatorics/tests/test_pc_groups.py,sha256=Gt-8AG3TEV97vE3bXryg0HLssrYfAtP5vDsQhiBHw8o,2739
sympy/combinatorics/tests/test_perm_groups.py,sha256=HQoCotM_g-cFdyQuQnzufqwD4uPlUlI1UKrWJnDmFyo,39537
sympy/combinatorics/tests/test_permutations.py,sha256=rTvGdYRw2ZwzmBReBYa-_dSpkGAGvIyD-59D6UDBAWk,20156
sympy/combinatorics/tests/test_polyhedron.py,sha256=3SWkFQKeF-p1QWP4Iu9NIA1oTxAFo1BLRrrLerBFAhw,4180
sympy/combinatorics/tests/test_prufer.py,sha256=OTJp0NxjiVswWkOuCIlnGFU2Gw4noRsrPpUJtp2XhEs,2649
sympy/combinatorics/tests/test_rewriting.py,sha256=3COHq74k6knt2rqE7hfd4ZP_6whf0Kg14tYxFmTtYrI,1787
sympy/combinatorics/tests/test_schur_number.py,sha256=wuSqtoMnCsLj2zvcJ716PcUcQM38LE1L1mL7zm7zE5w,1727
sympy/combinatorics/tests/test_subsets.py,sha256=6pyhLYV5HuXvx63r-gGVHr8LSrGRXcpDudhFn9fBqX8,2635
sympy/combinatorics/tests/test_tensor_can.py,sha256=olH5D5wwTBOkZXjtqvLO6RKbvCG9KoMVK4__wDe95N4,24676
sympy/combinatorics/tests/test_testutil.py,sha256=uJlO09XgD-tImCWu1qkajiC07rK3GoN91v3_OqT5-qo,1729
sympy/combinatorics/tests/test_util.py,sha256=sOYMWHxlbM0mqalqA7jNrYMm8DKcf_GwL5YBjs96_C4,4499
sympy/combinatorics/testutil.py,sha256=KA0m6Obnuz7-djU20AIV9NEImJ8JdVij2I_mWQXcAro,11152
sympy/combinatorics/util.py,sha256=FqSxatFnOKsyBvn50172ASTgiZs7GABdKlc27rURb8Q,16313
sympy/concrete/__init__.py,sha256=2HDmg3VyLgM_ZPw3XsGpkOClGiQnyTlUNHSwVTtizA0,144
sympy/concrete/delta.py,sha256=wk6oCjdLKQmlPrtSX_XmjCk4pW8Rt1zk-UtNYBG0uCI,9957
sympy/concrete/expr_with_intlimits.py,sha256=vj4PjttB9xE5aUYu37R1A4_KtGgxcPa65jzjv8-krsc,11352
sympy/concrete/expr_with_limits.py,sha256=eT7NWxl6peHmZF7VpZvH1gtRgYaLbwO7_o5iXn7W2Zs,21725
sympy/concrete/gosper.py,sha256=3q8gkZz_oAeBOBUfObMvwArBkBKYReHR0prVXMIqrNE,5557
sympy/concrete/guess.py,sha256=bZd0pcdD_1FI4HcMWuAb_ZHxxby4sL60z_q9aT0opQ8,17482
sympy/concrete/products.py,sha256=IO-aujgFg1SxtbBP-dMjFcBbxo4XRRRZwAtaOm4P7ms,18608
sympy/concrete/summations.py,sha256=cSApGBNpk2O0nLhjcPwm590O5to28Vcl3fVyI_-7-4s,55265
sympy/concrete/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/concrete/tests/test_delta.py,sha256=CHd9JzjmM91qb1DAgvL6rFvS_B2XyP6Q3XXZJAUrgYg,23694
sympy/concrete/tests/test_gosper.py,sha256=9pa-1V5BdSnQSRyEIlxKhWu0NKt68WpK3V7UahON6F4,7909
sympy/concrete/tests/test_guess.py,sha256=TPW6Hy11Po6VLZG_dx95x3sMBYl5kcQH8wjJ6TOtu-k,3370
sympy/concrete/tests/test_products.py,sha256=kVGc4h5bm_EQaD_OA2MvugRWiAhT_Ibgty3TH3x0s7k,14132
sympy/concrete/tests/test_sums_products.py,sha256=rHehnGlegQI_eD3DvX2ztuSN9udbdQr3fCYUNHioZNA,63242
sympy/conftest.py,sha256=3vg-GlDw8Y8MGoa324FoRJR3HaRaJhZpiXdTTVoNAoI,2245
sympy/core/__init__.py,sha256=LQBkB1S-CYmQ3P24ei_kHcsMwtbDobn3BqzJQ-rJ1Hs,3050
sympy/core/_print_helpers.py,sha256=GQo9dI_BvAJtYHVFFfmroNr0L8d71UeI-tU7SGJgctk,2388
sympy/core/add.py,sha256=G27eGRHo10Kr7fLMEsfVF3wRgLiCQ45QEOHDPkEuUzM,42911
sympy/core/alphabets.py,sha256=vWBs2atOvfRK6Xfg6hc5IKiB7s_0sZIiVJpcCUJL0N4,266
sympy/core/assumptions.py,sha256=2pbPxGOtMU20oSS3Bh_V1w34y_AL_FOPlqQEhkegY1Q,21562
sympy/core/backend.py,sha256=AUgGtYmz0mIoVmjKVMAa5ZzlC1p5anxk-N4Sy7pePNo,3842
sympy/core/basic.py,sha256=I37pjl8DIg514L9JbK9ui-4NyPNdmJo8KIF02lxGVtc,69743
sympy/core/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/core/benchmarks/bench_arit.py,sha256=gfrnvKSXLCaUoFFxMgJhnLUp7rG9Pa_YT7OKgOrPP8E,412
sympy/core/benchmarks/bench_assumptions.py,sha256=evfZzTgOUUvvvlK0DRdDZQRqxIlGLfJYzKu8QDMxSks,177
sympy/core/benchmarks/bench_basic.py,sha256=YF0tTJ_AN_Wz11qidzM4bIhlwEhEqVc-IGVGrUx6SaA,210
sympy/core/benchmarks/bench_expand.py,sha256=xgQYQMwqgXJtKajM4JVhuL-7AW8TLY-vdBpO6uyMDoQ,427
sympy/core/benchmarks/bench_numbers.py,sha256=fvcbOkslXdADqiX_amiL-BEUtrXBfdiTZeOtbiI2auI,1105
sympy/core/benchmarks/bench_sympify.py,sha256=G5iGInhhbkkxSY2pS08BNG945m9m4eZlNT1aJutGt5M,138
sympy/core/cache.py,sha256=1PZYlw85pqJpG1_u951Xwmtqe-wCcBHdFn5Ri5UeG9o,4642
sympy/core/compatibility.py,sha256=XQH7ezmRi6l3R23qMHN2wfA-YMRWbh2YYjPY7LRo3lo,1145
sympy/core/containers.py,sha256=NkUyoz8RCfeART7YAbltMNl8O2TAVmhbFY6CDYypb_4,11335
sympy/core/core.py,sha256=MvjFtRHHjwu9fzrg2PMFu399WYxgy8MuPY9haqHyuYQ,2870
sympy/core/coreerrors.py,sha256=OKpJwk_yE3ZMext49R-QwtTudZaXZbmTspaq1ZMMpAU,272
sympy/core/decorators.py,sha256=de6eYm3D_YdEW1rEKOIES_aEyvbjqRM98I67l8QGGVU,8217
sympy/core/evalf.py,sha256=9iIcQ-lX2Sf_sBoFriAgiJCDJeB3tOgKHrzcH6LE0E0,62211
sympy/core/expr.py,sha256=-nhguhXPY8XQ37Ul60o1fUSwAU30e5XAKzBmSZKd3IM,141732
sympy/core/exprtools.py,sha256=Jx63dtYEW4WhvW8_N6GK5LPDUm_t8rkFbrFvcxIEPtU,51679
sympy/core/facts.py,sha256=gHql_vo0UJD_w31qvPjkXZfTtqhR1Ih-KguKq3yxnz8,15729
sympy/core/function.py,sha256=hIwiAQcRMzU-k5LCWyNUfsTwK8hr1Kdr23zA6vc3hXY,115082
sympy/core/kind.py,sha256=5QLjiFzOKv6wbga6Q3uP_8DWO-_YAPohFBFAni0pshw,11542
sympy/core/logic.py,sha256=k5nUtUpdJ-HMilSymqyiiOOnrK2yKSvHYbjJyQY_3Lo,10872
sympy/core/mod.py,sha256=t4hf6YUmi1F0MgDa9K9DM2-zRkGctZkYv7_9x9CHUsc,7343
sympy/core/mul.py,sha256=LyfiDFf73DWKk6cL-rnzQyfqVOjXvdFbytD_QfqB_Ho,75384
sympy/core/multidimensional.py,sha256=NWX1okybO_nZCl9IhIOE8QYalY1WoC0zlzsvBg_E1eE,4233
sympy/core/numbers.py,sha256=tXyCAl4KXfCnOLKxfnSvTo0nd_LdXmHIWmqF8Ulz-10,136031
sympy/core/operations.py,sha256=XMHgqtZNnrLjjNaLUDXKPwwRq-Zy5jcZPQjshmJ56cU,25187
sympy/core/parameters.py,sha256=09LVewtoOyKABQvYeMaJuc-HG7TjJusyT_WMw5NQDDs,3733
sympy/core/power.py,sha256=EQgG8F_EsEZB7WzSVXvU4WiG-6v55SEGS4xVsEjv69Y,75175
sympy/core/random.py,sha256=OUPZQTL4iNRMISBs0LlyzQJg3GF6OFFY5VqbY8n4W2w,6425
sympy/core/relational.py,sha256=I89vjxcOtWDTbw8GpRvJlMPnZ5GBtF3xRTEWWZNTl_w,51109
sympy/core/rules.py,sha256=AJuZztmYKZ_yUITLZB6rhZjDy6ROBCtajcYqPa50sjc,1496
sympy/core/singleton.py,sha256=IMAmPbetFdA2FAr1WJwDhD_MpStQkCPTVg3qBL3fpJo,6923
sympy/core/sorting.py,sha256=FtvutjgHPtSaM7L7U-oLjWg5AUHXMUkC1jjp5SYS9Is,10670
sympy/core/symbol.py,sha256=-VtyKr_m_yBwCl5Da_Him04f5JRGg1PX063x80UpSMg,27905
sympy/core/sympify.py,sha256=nwfG_mabfHrkqOCORghBcedJ-gsrI2TO7yU_uqy_Wuw,20410
sympy/core/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/core/tests/test_args.py,sha256=PL19fhM30GaAapC7RF1r0qvuU3eA_zA1rpFT1jJ_RLM,179053
sympy/core/tests/test_arit.py,sha256=217KWy947FLSzZjjwxV88NFjdrlovkAYul8yYh7AZPA,77348
sympy/core/tests/test_assumptions.py,sha256=m6_46XFOnRuF2IjmpCTpcoWfiSLYEy3iNBKkf0Ocv8o,40124
sympy/core/tests/test_basic.py,sha256=g0MfMUwzTg6WeZ3jDuFH5DFQlKXjwuV3QTfbU-DtWVY,8833
sympy/core/tests/test_cache.py,sha256=_NdbnRlRubPO-lfA8Yrp3klHeaozHgqHf0KZnPE00Tg,1565
sympy/core/tests/test_compatibility.py,sha256=7pvNUEGIcRrfWl3doqHlm3AdNkGlcChO69gos3Fk09A,240
sympy/core/tests/test_complex.py,sha256=koNGFMt6UMmzahJADSja_eD24gr-GG5gGCtyDgCRtPI,21906
sympy/core/tests/test_constructor_postprocessor.py,sha256=0d7vbVuKi3GCm3PKLtiNqv_Au7v6RYt1rzRdHiD08tM,2441
sympy/core/tests/test_containers.py,sha256=34N_ucrkH3VISar7es5KH0--gHyT-9rpLVARKzUQwXs,7453
sympy/core/tests/test_count_ops.py,sha256=eIA2WvCuWKXVBJEGfWoJrn6WfUshX_NXttrrfyLbNnI,5665
sympy/core/tests/test_diff.py,sha256=6j4Vk9UCNRv8Oyx_4iv1ePjocwBg7_-3ftrSJ8u0cPo,5421
sympy/core/tests/test_equal.py,sha256=RoOJuu4kMe4Rkk7eNyVOJov5S1770YHiVAiziNIKd2o,1678
sympy/core/tests/test_eval.py,sha256=o0kZn3oaMidVYdNjeZYtx4uUKBoE3A2tWn2NS4hu72Q,2366
sympy/core/tests/test_evalf.py,sha256=bo6BTXsZe3Vbg7ezbV2CV_X7Cw11InL5HIwvaiyZdtQ,28274
sympy/core/tests/test_expand.py,sha256=gaJtLHlbEiZKtRKZ4BpDp7A5LRpVqG1yS2WlpJNoSxM,12164
sympy/core/tests/test_expr.py,sha256=XT69bgp9DsmmtcG9Gij2gw2r5Hx0_4iMxNUObIanjlg,75267
sympy/core/tests/test_exprtools.py,sha256=kIBhTITCJqNCU_tchwkX8Cy8-n-_TqMhSC2szrC86ZY,18963
sympy/core/tests/test_facts.py,sha256=YEZMZ-116VFnFqJ48h9bQsF2flhiB65trnZvJsRSh_o,11579
sympy/core/tests/test_function.py,sha256=FpDvZIVZfpNgcF0CaAURx2V4j09Dv8YrMPmH3VGz3QU,51821
sympy/core/tests/test_kind.py,sha256=NLJbwCpugzlNbaSyUlbb6NHoT_9dHuoXj023EDQMrNI,2048
sympy/core/tests/test_logic.py,sha256=_YKSIod6Q0oIz9lDs78UQQrv9LU-uKaztd7w8LWwuwY,5634
sympy/core/tests/test_match.py,sha256=2ewD4Ao9cYNvbt2TAId8oZCU0GCNWsSDx4qO5-_Xhwc,22716
sympy/core/tests/test_multidimensional.py,sha256=Fr-lagme3lwLrBpdaWP7O7oPezhIatn5X8fYYs-8bN8,848
sympy/core/tests/test_noncommutative.py,sha256=IkGPcvLO4ACVj5LMT2IUgyj68F1RBvMKbm01iqTOK04,4436
sympy/core/tests/test_numbers.py,sha256=WIAKXnf3bQM3gOQCGaxC2bf551eivYuuNt-gQg8piTw,73764
sympy/core/tests/test_operations.py,sha256=mRxftKlrxxrn3zS3UPwqkF6Nr15l5Cv6j3c2RJX46s4,2859
sympy/core/tests/test_parameters.py,sha256=lRZSShirTW7GRfYgU3A3LRlW79xEPqi62XtoJeaMuDs,2799
sympy/core/tests/test_power.py,sha256=YoXtqbNbiJhLreg9DDZ1xKxwatOJ7znTmlRpWuYlk20,23803
sympy/core/tests/test_priority.py,sha256=g9dGW-qT647yL4uk1D_v3M2S8rgV1Wi4JBUFyTSwUt4,3190
sympy/core/tests/test_random.py,sha256=oUGj3wyTlj9oZw76i-Ao3nmyj8EBfdH-Iod-cN82h1I,712
sympy/core/tests/test_relational.py,sha256=cE-sDyOhciiOSFQGaFDPuPuDIZuePBRoP-10a4rEIAU,43065
sympy/core/tests/test_rules.py,sha256=iwmMX7hxC_73CuX9BizeAci-cO4JDq-y1sicKBXEGA4,349
sympy/core/tests/test_singleton.py,sha256=xLJJgXwmkbKhsot_qTs-o4dniMjHUh3_va0xsA5h-KA,3036
sympy/core/tests/test_sorting.py,sha256=6BZKYqUedAR-jeHcIgsJelJHFWuougml2c1NNilxGZg,902
sympy/core/tests/test_subs.py,sha256=PlUerlijKTxYmM3GARBp1QMLkzVlz0tMomS6TmZwNyg,30057
sympy/core/tests/test_symbol.py,sha256=PsEhrt7SgxbvLmxSGhfRcbKP6RHF_cDSe4oqgyrZq_Y,12315
sympy/core/tests/test_sympify.py,sha256=pc_0fcysBaj93c5iLW9_pIZLp6AteVduqCrhRiuyBSw,27112
sympy/core/tests/test_traversal.py,sha256=cmgvMW8G-LZ20ZXy-wg5Vz5ogI_oq2p2bJSwMy9IMF0,4311
sympy/core/tests/test_truediv.py,sha256=RYfJX39-mNhekRE3sj5TGFZXKra4ML9vGvObsRYuD3k,854
sympy/core/tests/test_var.py,sha256=hexP-0q2nN9h_dyhKLCuvqFXgLC9e_Hroni8Ldb16Ko,1594
sympy/core/trace.py,sha256=9WC8p3OpBL6TdHmZWMDK9jaCG-16f4uZV2VptduVH98,348
sympy/core/traversal.py,sha256=BzXPBm_l8B-ZWgZnDOqfgOMfmjpn3CsILBHChvrUaNc,8963
sympy/crypto/__init__.py,sha256=i8GcbScXhIPbMEe7uuMgXqh_cU2mZm2f6hspIgmW5uM,2158
sympy/crypto/crypto.py,sha256=r1NDAF8GLAb23kDbYMbSGoTe_PNt6UFSqnapMevqQz8,89440
sympy/crypto/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/crypto/tests/test_crypto.py,sha256=-GJYezqcuQ3KUq_IqCEJAWa-zWAPWFku2WdLj7Aonrc,19763
sympy/diffgeom/__init__.py,sha256=cWj4N7AfNgrYcGIBexX-UrWxfd1bP9DTNqUmLWUJ9nA,991
sympy/diffgeom/diffgeom.py,sha256=5hZLA6kjcy5xAeYmJ5bIJetPueGg01mld0R6X9bOA7E,72320
sympy/diffgeom/rn.py,sha256=g7LQRsVXuSrbrhiWmd7aCipSqSN8vXUCBpd_ubUEzSE,6336
sympy/diffgeom/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/diffgeom/tests/test_class_structure.py,sha256=LbRyxhhp-NnnfJ2gTn1SdlgCBQn2rhyB7xApOgcd_rM,1048
sympy/diffgeom/tests/test_diffgeom.py,sha256=3BepCr6ned-4C_3me4zScu06HXG9Qx_dBBxIpiXAvy4,14145
sympy/diffgeom/tests/test_function_diffgeom_book.py,sha256=0YU63iHyY6O-4LR9lRS5kLZMpcMpuNxEsgqtXALV7ic,5258
sympy/diffgeom/tests/test_hyperbolic_space.py,sha256=c4xQJ_bBS4xrMj3pfx1Ms3oC2_LwuJuNYXNZxs-cVG8,2598
sympy/discrete/__init__.py,sha256=A_Seud0IRr2gPYlz6JMQZa3sBhRL3O7gVqhIvMRRvE0,772
sympy/discrete/convolutions.py,sha256=xeXCLxPSpBNfrKNlPGGpuU3D9Azf0uR01OpDGCOAALg,14505
sympy/discrete/recurrences.py,sha256=FqU5QG4qNNLSVBqcpL7HtKa7rQOlmHMXDQRzHZ_P_s0,5124
sympy/discrete/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/discrete/tests/test_convolutions.py,sha256=wNCjQFKN4EQaeu2F0Qy0g_IeXaY72gXlqDt_xfIVJLM,16627
sympy/discrete/tests/test_recurrences.py,sha256=s5ZEZQ262gcnBLpCjJVmeKlTKQByRTQBrc-N9p_4W8c,3019
sympy/discrete/tests/test_transforms.py,sha256=vEORFaPvxmPSsw0f4Z2hLEN1wD0FdyQOYHDEY9aVm5A,5546
sympy/discrete/transforms.py,sha256=5dDgQ9gSfj_dk93YVygoehJiTO9jyS9HDl_ewMXctaM,11679
sympy/external/__init__.py,sha256=C6s4654Elc_X-D9UgI2cUQWiQyGDt9LG3IKUc8qqzuo,578
sympy/external/gmpy.py,sha256=V3Z0HQyg7SOgviwOvBik8dUtSxO6yiNqFqjARnjTO3I,2982
sympy/external/importtools.py,sha256=yvmhbUMeJkEIm0M5hAldnYi2MPHVP8cqxfao6nFojrc,7676
sympy/external/pythonmpq.py,sha256=WOMTvHxYLXNp_vQ1F3jE_haeRlnGicbRlCTOp4ZNuo8,11243
sympy/external/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/external/tests/test_autowrap.py,sha256=tRDOkHdndNTmsa9sGjlZ1lFIh1rL2Awck4ec1iolb7c,9755
sympy/external/tests/test_codegen.py,sha256=zOgdevzcR5pK73FnXe3Su_2D6cuvrkP2FMqsro83G-c,12676
sympy/external/tests/test_importtools.py,sha256=KrfontKYv11UvpazQ0vS1qyhxIvgZrCOXh1JFeACjeo,1394
sympy/external/tests/test_numpy.py,sha256=7-YWZ--nbVX0h_rzah18AEjiz7JyvEzjHtklhwaAGhI,10123
sympy/external/tests/test_pythonmpq.py,sha256=L_FdZmmk5N-VEivE_O_qZa98BZhT1WSxRfdmG817bA0,5797
sympy/external/tests/test_scipy.py,sha256=CVaw7D0-6DORgg78Q6b35SNKn05PlKwWJuqXOuU-qdY,1172
sympy/functions/__init__.py,sha256=B8tPQ6yn2uCBMAewy1SGRlCm-88S0v1NHHh-dZFchCQ,5183
sympy/functions/combinatorial/__init__.py,sha256=WqXI3qU_TTJ7nJA8m3Z-7ZAYKoApT8f9Xs0u2bTwy_c,53
sympy/functions/combinatorial/factorials.py,sha256=zIJMJtwZJx9S8qHCcEtTCuJFDuTFtvlWWUYeRZjDiCg,37538
sympy/functions/combinatorial/numbers.py,sha256=KWA3jG0646v7l42viW9bhiEaxWzOM4hBD5ui2t7-uYg,73198
sympy/functions/combinatorial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/combinatorial/tests/test_comb_factorials.py,sha256=J46LvRxTP_XYg1OvEov2LLjqtjgjsSp8zcV_AGtoZdQ,26177
sympy/functions/combinatorial/tests/test_comb_numbers.py,sha256=cbC3fzU_Rsj3hNcflokCaIn_4K3rCH3VuATGC3QRzUM,28945
sympy/functions/elementary/__init__.py,sha256=Fj8p5qE-Rr1lqAyHI0aSgC3RYX56O-gWwo6wu-eUQYA,50
sympy/functions/elementary/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/elementary/benchmarks/bench_exp.py,sha256=PFBYa9eMovH5XOFN5XTxWr1VDj1EBoKwn4mAtj-_DdM,185
sympy/functions/elementary/complexes.py,sha256=4c8X_BttCo9E8iZik9yFZ_9WDYGile1RuzUMKXa8TmE,43541
sympy/functions/elementary/exponential.py,sha256=gH1it7wEqfORMNeV6w3Kwe7Az_3BcnPLseXlwS0qfOY,42857
sympy/functions/elementary/hyperbolic.py,sha256=uxLaP4QhsPWKpuUa0DFYstzoPWvX-W0I6vZw8E4mnfc,55863
sympy/functions/elementary/integers.py,sha256=6WxmPmffjD0Yxe_Ath5PYtZoeRLt_TvY2WLjd3YPkgU,18174
sympy/functions/elementary/miscellaneous.py,sha256=BHM8FdyAXu77_q8dGZi9R9JLLA5sJFkntP5CJNgQyqQ,27928
sympy/functions/elementary/piecewise.py,sha256=SeoRgMTSkYsf84V5CIXj_D1BxkyC21qPDTqPzXRcGKU,57075
sympy/functions/elementary/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/elementary/tests/test_complexes.py,sha256=Qe54tC3EGAwz40kdPn8Tw7ZZkD7K1RMwGSWHGp4vChc,33520
sympy/functions/elementary/tests/test_exponential.py,sha256=xp5vWNnhvZiL7FvNtT347LxG0Pc0Nf3i7qBJ39s_XnE,27747
sympy/functions/elementary/tests/test_hyperbolic.py,sha256=sgmUpmE354G-Ldqh5OXNT1alucD6BdpC8AR-ooiuMR8,38507
sympy/functions/elementary/tests/test_integers.py,sha256=7bWIiLJl1vOqemuYCeOp1_zVAquO6lWItkd47Cev8F8,20219
sympy/functions/elementary/tests/test_interface.py,sha256=dBHnagyfDEXsQWlxVzWpqgCBdiJM0oUIv2QONbEYo9s,2054
sympy/functions/elementary/tests/test_miscellaneous.py,sha256=pd7jhSBY2L1UtecRE-TeodGaRBZSF04PF4xamfy0xBA,17136
sympy/functions/elementary/tests/test_piecewise.py,sha256=d50huioGzyxhFjMW6Up6U1WnMUoBi0KJeQjcG-Q2r4s,58555
sympy/functions/elementary/tests/test_trigonometric.py,sha256=tPeDRAM3e8z5D7xFsqdLt-xmmk_8tFVMva2ztHlJGzg,85639
sympy/functions/elementary/trigonometric.py,sha256=E4CuKTuHuYQ3mvmySBX47cQLjFSsLAWhFhA4eOn7BIE,113688
sympy/functions/special/__init__.py,sha256=5pjIq_RVCMsuCe1b-FlwIty30KxoUowZYKLmpIT9KHQ,59
sympy/functions/special/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/special/benchmarks/bench_special.py,sha256=wzAoKTccuEaG4xrEYTlYfIJuLi3kUTMTEJ9iA113Wog,164
sympy/functions/special/bessel.py,sha256=DSxpciJ4ZkBIbSweL9Mwhvvyxt-AXw1PhR22dbXGQ08,56898
sympy/functions/special/beta_functions.py,sha256=v_QwwDlab7SvEXFj8YeEVCFykhD4Ualedn4OZx5kD0M,11724
sympy/functions/special/bsplines.py,sha256=t-b0yrmp14QGK2vC3kHgNRr2d5sAknMOgLs6QEaFFGM,10164
sympy/functions/special/delta_functions.py,sha256=8Sgl9gg9l8ajhIq24J1GA2AfhfMQSgtvGjYokr0b8gI,19864
sympy/functions/special/elliptic_integrals.py,sha256=6ZDt-X-gByFR54TpSXs2Xz9_e6oXwkaUhyHRkgVhoIU,14688
sympy/functions/special/error_functions.py,sha256=RX4IBqDurh7w8qHacDE1ibkjfveZawWFsXpq_EhxAbY,77068
sympy/functions/special/gamma_functions.py,sha256=J0SMOXKFrzzdE8-2gaQ3IbjulrEUZ0yzbEQiHfMMss0,42292
sympy/functions/special/hyper.py,sha256=EE-c0nGP314qLxt8wUdrQj5g9WqzXIg5plUCzaAb4Ic,37261
sympy/functions/special/mathieu_functions.py,sha256=nF-Gak8vN-RZb-Ps028GyF4ErmMXUwXIkAI_UBQYLaY,6578
sympy/functions/special/polynomials.py,sha256=V33T9ugiGxUYiYPzd_UwuCPdWCFX11SwR279t3O1LKw,40920
sympy/functions/special/singularity_functions.py,sha256=nmyC49NvV8HPcbzBs1IO3E0wKz1q-r5WDB0K_o3QgzI,7996
sympy/functions/special/spherical_harmonics.py,sha256=H2yWXXC_-_d77cneP0Jn9Nip_zmVYMv3AT9m9ZCOnI0,10990
sympy/functions/special/tensor_functions.py,sha256=ZzMc93n_4Y4L-WVd9nmMh0nZQPYMB7uKqcnaFdupEXE,12277
sympy/functions/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/special/tests/test_bessel.py,sha256=4JopY-vZ3OqMlQNxgOfIQEzXj5Cw-8YGPNObRQK0jdE,30597
sympy/functions/special/tests/test_beta_functions.py,sha256=55_-XmZsMG1-8OCJBn3OtX-VJ9kVJNsrWBep9rvVZ7k,2777
sympy/functions/special/tests/test_bsplines.py,sha256=6UYg7IqXTi8fcSOut8TEzNVkxIA4ff-CyG22qJnbIYA,7145
sympy/functions/special/tests/test_delta_functions.py,sha256=8xhSWG4SLL86z1QKFfLk_3b--bCrxjvCaxHlODBVToE,7138
sympy/functions/special/tests/test_elliptic_integrals.py,sha256=AazZYMow9szbvC_WfK10c5j-LQRAzno6V1WJCbtp4MU,6860
sympy/functions/special/tests/test_error_functions.py,sha256=5BspuRg_y6q44viE7PhCCwdm6Bv83R_1z48D55IQLM0,31157
sympy/functions/special/tests/test_gamma_functions.py,sha256=QRKl4FjXEGjDN5iBWmFUpzubvHQ68QH64jxDs9WXWB8,29153
sympy/functions/special/tests/test_hyper.py,sha256=HRK9_RHFT0zQiZUHuiOP210Cwyxy9nRuFVLlGDhKl9E,16111
sympy/functions/special/tests/test_mathieu.py,sha256=pqoFbnC84NDL6EQkigFtx5OQ1RFYppckTjzsm9XT0PY,1282
sympy/functions/special/tests/test_singularity_functions.py,sha256=tqMJQIOOsBrveXctXPkPFIYdThG-wwKsjfdRHshEpfw,5467
sympy/functions/special/tests/test_spec_polynomials.py,sha256=0-Qu7846gjV1BuVpXp_fWahZrKe7nQHw5P7_S4umM9s,16280
sympy/functions/special/tests/test_spherical_harmonics.py,sha256=pUFtFpNPBnJTdnqou0jniSchijyh1rdzKv8H24RT9FU,3850
sympy/functions/special/tests/test_tensor_functions.py,sha256=0D2SIzRT2_KkuOg0SZyo1_SQ72LfEdlOXkaxieFUOm8,5542
sympy/functions/special/tests/test_zeta_functions.py,sha256=fqJyF9Uhey31VD7Gc731fyXvkA8sCW0oWPzzJ-46sqk,9599
sympy/functions/special/zeta_functions.py,sha256=OElxOIVBOoLBJLRJBZGmEM0ylE5O3nYe0Qy4WGTCvJY,21346
sympy/galgebra.py,sha256=yEosUPSnhLp9a1NWXvpCLoU20J6TQ58XNIvw07POkVk,123
sympy/geometry/__init__.py,sha256=BU2MiKm8qJyZJ_hz1qC-3nFJTPEcuvx4hYd02jHjqSM,1240
sympy/geometry/curve.py,sha256=F7b6XrlhUZ0QWLDoZJVojWfC5LeyOU-69OTFnYAREg8,10170
sympy/geometry/ellipse.py,sha256=Sh-hzEqSotEETUExAkI_TCaoyv23UHhx20L-Aj7FfkA,50949
sympy/geometry/entity.py,sha256=FVYE7rPrBFw_CqSlt68wg52p0-kLHDWLXUPB51d6lYo,20659
sympy/geometry/exceptions.py,sha256=XtUMA44UTdrBWt771jegFC-TXsobhDiI-10TDH_WNFM,131
sympy/geometry/line.py,sha256=lYSmpkzQq-5E1YiVQQzX6n54p-EfxQXFu5lfyiKPLyA,79299
sympy/geometry/parabola.py,sha256=PsR1Iyi6JUzzhlkLALPoTatSoCdrA7855fuvbHZT4u0,10602
sympy/geometry/plane.py,sha256=TgVCe9CF75SK9JHc9ZjVgUPfugZCt3BRlBF2BOBIIVM,27086
sympy/geometry/point.py,sha256=4M2eTrAZjNX_6RcE7Bmhv-HJietXboV6k_bnXlb1mSE,36636
sympy/geometry/polygon.py,sha256=Tef-Rw6dg5ZkzTATnt6V4qOlgtd003Vfpy_BADVPQKA,81739
sympy/geometry/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/geometry/tests/test_curve.py,sha256=xL4uRWAal4mXZxuQhcs9QOhs6MheCbFNyH1asq_a2IQ,4479
sympy/geometry/tests/test_ellipse.py,sha256=oe9Bvye-kLjdhP3bwJPB0N1-wDL3cmVwYLhEhrGAPHk,25735
sympy/geometry/tests/test_entity.py,sha256=0pBKdmRIETq0pJYjxRj34B0j-o56f4iqzJy9J4buU7U,3897
sympy/geometry/tests/test_geometrysets.py,sha256=vvOWrFrJuNAFgbrVh1wPY94o-H-85FWlnIyyo2Kst9c,1911
sympy/geometry/tests/test_line.py,sha256=cSx_yVtFowSVRB2KuGyeNfsZ6csEkHmLkZN2WmQcQ00,37612
sympy/geometry/tests/test_parabola.py,sha256=BFO8QIoYx7Ih5rMjVxnar_HuwGau-WnRjkeKKCZYnf4,6177
sympy/geometry/tests/test_plane.py,sha256=QRcfoDsJtCtcvjFb18hBEHupycLgAT2OohF6GpNShyQ,12525
sympy/geometry/tests/test_point.py,sha256=adspxQ9EdoCQTwp5BFY9YS-phuaENpAEIwf-9iw2YVU,16406
sympy/geometry/tests/test_polygon.py,sha256=79iBkQjpX-CdO1mtMaX3lGvVfkopBiFhLC3QfWCreWA,27138
sympy/geometry/tests/test_util.py,sha256=uuKzLDMs84jCP9h_i0A_usyCEcD-fZXqYWpD0xGagqo,6205
sympy/geometry/util.py,sha256=yV9-1ejXPFyIgolf6nLIS_fwT7_GdnJPqLP4u3ek_To,20114
sympy/holonomic/__init__.py,sha256=BgHIokaSOo3nwJlGO_caJHz37n6yoA8GeM9Xjn4zMpc,784
sympy/holonomic/holonomic.py,sha256=H4WlV09VqqKRDHWMopSfmKaNtUDGchHNSBQUbdkWy4o,94837
sympy/holonomic/holonomicerrors.py,sha256=qDyUoGbrRjPtVax4SeEEf_o6-264mASEZO_rZETXH5o,1193
sympy/holonomic/numerical.py,sha256=m35A7jO54xMNgA4w5Edn1i_SHbXWBlpQTRLMR8GgbZE,2730
sympy/holonomic/recurrence.py,sha256=JFgSOT3hu6d7Mh9sdqvSxC3RxlVlH_cygsXpsX97YMY,10987
sympy/holonomic/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/holonomic/tests/test_holonomic.py,sha256=EYbqojAtYQdBaGahWEvGe_yJghrrc-ZiTjxGrjdHzyc,34760
sympy/holonomic/tests/test_recurrence.py,sha256=HEbA3yCnIw4IDFV1rb3GjmM4SCDDZL7aYRlD7PWuQFg,1056
sympy/integrals/__init__.py,sha256=aZr2Qn6i-gvFGH_5Hl_SRn2-Bd9Sf4zQdwo9VGLSeNY,1844
sympy/integrals/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/integrals/benchmarks/bench_integrate.py,sha256=vk6wAO1bqzFT9oW4qsW7nKGfc_gP0XaB5PMYKx5339Q,396
sympy/integrals/benchmarks/bench_trigintegrate.py,sha256=8XU3uB3mcavigvzHQZA7H1sHI32zgT-9RkSnLa-Y3Vc,305
sympy/integrals/deltafunctions.py,sha256=ysIQLdRBcG_YR-bVDoxt-sxEVU8TG77oSgM-J0gI0mE,7435
sympy/integrals/heurisch.py,sha256=EQls-v2FvYXM-paUgac05td0H55vF1l_j15G3tVyeyA,26308
sympy/integrals/integrals.py,sha256=iKMW5iy_H7L_aXLzlaQ9Z_cjo0SFYrxmO5yMIAevZcE,64564
sympy/integrals/intpoly.py,sha256=Sakvdw3meHdGuWyL33lcXZoU-XnnBdE-XfkVHzeNW4w,43321
sympy/integrals/manualintegrate.py,sha256=PMCoagGEJE2gXCdB1-pL7DflHAo6llb35SZxio90nfc,71629
sympy/integrals/meijerint.py,sha256=LYTKrbxFk6T0PzFdiRZZ-7cxWbe81TPF6aMnTlDSKxE,80572
sympy/integrals/meijerint_doc.py,sha256=ArEsD4oZNOCSVboymdkynK9juEBNUSjhuda38PHlHm4,1151
sympy/integrals/prde.py,sha256=yjMh6EBl_p9cUHJRv-qbiXB06UwwAwys_44JKthtpKE,52084
sympy/integrals/quadrature.py,sha256=NXRbi-iUmFn8k9GEgLxEvZiY-dlTXX2Kutjll43n9dc,17012
sympy/integrals/rationaltools.py,sha256=1OMhRhMBQ7igw2_YX5WR4q69QB_H0zMtGFtUkcbVD3Q,10922
sympy/integrals/rde.py,sha256=AuiPDqP2awC4UlWJrsfNCn1l3OAQuZl64WI-lE2M5Ds,27392
sympy/integrals/risch.py,sha256=E-Grfz10fZmrtRTdLDIW4_mVzJqJmNitTUi70_bpkiY,67699
sympy/integrals/rubi/__init__.py,sha256=wDGynce2HThL_hNOR3Oo8Wgiv6ArhB7QJjGzU6Kpc9E,3464
sympy/integrals/rubi/constraints.py,sha256=di_0Asuc3tjrKsQZnAeCwyoGYlvcUYTHEe8-IefNxfs,295555
sympy/integrals/rubi/parsetools/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/integrals/rubi/parsetools/generate_rules.py,sha256=uVqy3vvEGKtky0GPpfnIGCFs0UHyJlsBx5SQGQw2mnA,2841
sympy/integrals/rubi/parsetools/generate_tests.py,sha256=AhppixLog6VaVIANRRKCx-nEZN42lMGEntKIhNPJDTo,2720
sympy/integrals/rubi/parsetools/header.py.txt,sha256=3N5AnTBW0Zf-1iQqGexKXR-AmgYO28ys-Nn6c25lwJw,9374
sympy/integrals/rubi/parsetools/parse.py,sha256=2DFI2tgRMsjHbw2tQYRBiAL_wvfM3IgGX9zxObsvk0M,27653
sympy/integrals/rubi/parsetools/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/integrals/rubi/parsetools/tests/test_parse.py,sha256=cGofVflUY-ECSlHWBJX7p5avZigmrlkcOc8IGxeM6rI,8283
sympy/integrals/rubi/rubi_tests/__init__.py,sha256=o2xtAwgjagJBj5dgmNMP-eKUBEy9foXNKFrRM2AwHN8,293
sympy/integrals/rubi/rubi_tests/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/integrals/rubi/rubi_tests/tests/test_1_2.py,sha256=yjQq-zJ6OJgN2om9BRer1VJKDQWKh-B9IhFoJSoKAkE,30615
sympy/integrals/rubi/rubi_tests/tests/test_1_3.py,sha256=j_7RKuPZYElO7jcszeWgu-5BUKVQ68qh17Pa4eYoAh8,61374
sympy/integrals/rubi/rubi_tests/tests/test_1_4.py,sha256=B18zKhZx5wF4EJLKdIAXvZ30kZ-Z_GbkIsE0TadcEyk,10652
sympy/integrals/rubi/rubi_tests/tests/test_exponential.py,sha256=335HbTdpavQgwctr3-_VE4V7t2DLZw76L6AcTFEhUSE,251429
sympy/integrals/rubi/rubi_tests/tests/test_hyperbolic_sine.py,sha256=y8T56BADEj8TEmvkdjm7efYJBipgSkaOFJkZcx86JGg,79915
sympy/integrals/rubi/rubi_tests/tests/test_inverse_hyperbolic_sine.py,sha256=fsYpbuZa3DDWv2cgTEfKBbLegY2pK-bfCbdYXWPYdos,65535
sympy/integrals/rubi/rubi_tests/tests/test_inverse_sine.py,sha256=2gYv8HOmkCRasHsKJfO-g7lIafm0b5jrsSfMzEX7hUI,84569
sympy/integrals/rubi/rubi_tests/tests/test_logarithms.py,sha256=F39mubjaATeAm4q2Bumi3cJnLUDjdsvKTvK93j8HmIQ,442550
sympy/integrals/rubi/rubi_tests/tests/test_miscellaneous_algebra.py,sha256=GeSPi_E63_jHuL5gvA2hGESfG71w1z055jscUdGecjA,526474
sympy/integrals/rubi/rubi_tests/tests/test_secant.py,sha256=zdaDTjTWHol_1A5l9v3cgYAdOJOKi2eIe5RCOSfczVQ,93770
sympy/integrals/rubi/rubi_tests/tests/test_sine.py,sha256=9eNr_8jTKX0eal5kCIp9ljiTQSS1mqEjWFWC7KTHgxA,164746
sympy/integrals/rubi/rubi_tests/tests/test_special_functions.py,sha256=lW1X-MuBCvB2xlZ4oCjZGfuncPoT7o9LRnsVuNdCyXk,48832
sympy/integrals/rubi/rubi_tests/tests/test_tangent.py,sha256=TCOqAWH39bEeTH4ELDBuXj8ZiHHepq9nE_BQ30TSsP0,132747
sympy/integrals/rubi/rubi_tests/tests/test_trinomials.py,sha256=oEM4VOTH_CvCVdWDlvl83fIDUk557OPBCnYkQARdfrk,1508732
sympy/integrals/rubi/rubimain.py,sha256=qI9UofDmxiA6jwKx8obr8D70JwydvWGiceGR4t2Dylc,8292
sympy/integrals/rubi/rules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/integrals/rubi/rules/binomial_products.py,sha256=OQrapCeelbyn3ew6np9b4tt6oghijK_LxNSuZQ6PnfQ,199603
sympy/integrals/rubi/rules/exponential.py,sha256=ld9xYsN4wvUk510sYgxZTKkVgfvlLclK9quri-cRxzI,63309
sympy/integrals/rubi/rules/hyperbolic.py,sha256=vjB6QhP-UpsI6oVlwAc4uobMW0-E9imKBBmO9WVTi9Y,218437
sympy/integrals/rubi/rules/integrand_simplification.py,sha256=HS9wgP68XMaxYspVhmKa6hQFabN5q5wbg1VsM3uK-xg,23614
sympy/integrals/rubi/rules/inverse_hyperbolic.py,sha256=6NO9Wi9DzqPn4mejjiWF6Lrz6DYlSxzTDgDD6HhVO0o,351326
sympy/integrals/rubi/rules/inverse_trig.py,sha256=QfTOGmqE8L77XMqcPeBZZQPyMCNThC7xc2m7QnCXAjE,317618
sympy/integrals/rubi/rules/linear_products.py,sha256=P_toOUAUydqtwPObymY3jKeGuXTyY7t5vtrSoUolcwM,91985
sympy/integrals/rubi/rules/logarithms.py,sha256=V3e-Wq8bKw-pxOhi2-K9Ds5A1cTkZJGA4C9KBXjKKfY,98271
sympy/integrals/rubi/rules/miscellaneous_algebraic.py,sha256=nTFPQYnSpfWbP-qaEr7rz3BoX9uM8io38J9NL5Z_rAU,233058
sympy/integrals/rubi/rules/miscellaneous_integration.py,sha256=IZBvGacNMuC3Ay-g7Hu1r8YfV7uE1Mpt_H_k2fb_7PA,51000
sympy/integrals/rubi/rules/miscellaneous_trig.py,sha256=NdD5W6QC-bFXV1D530qEacpLi3YMyHziw9LgdBdgHrg,189698
sympy/integrals/rubi/rules/piecewise_linear.py,sha256=bOlJqsoOzYDk4Ldoz0SfFAojTSE0ISsM9eBravhyzqo,20331
sympy/integrals/rubi/rules/quadratic_products.py,sha256=jHnyHmvkDce9iAmphUk_VhSKoBhniZELZ7rt-zUYMbE,317647
sympy/integrals/rubi/rules/secant.py,sha256=Mb-6iG3ar2NGL2x_8RHMJ8Ionqffh1hWWwtcVHAmk0A,451035
sympy/integrals/rubi/rules/sine.py,sha256=dPrZKqDaSQo_wmuSdgrghwMfCs_WeU0bSGel_CzQz4w,733613
sympy/integrals/rubi/rules/special_functions.py,sha256=oB1YAnUMTugnSlc42jJa44Z9LNb3RbH5shUWrbLs2oE,89851
sympy/integrals/rubi/rules/tangent.py,sha256=ptTTZStjYzjhkjGR-ZyBla42MnWxQvVVpWx_XzccHXI,314785
sympy/integrals/rubi/rules/trinomial_products.py,sha256=CQCvaMU2fRBbOLX4Qms3toWaqF10qo5tQ0pcay2pmC4,243013
sympy/integrals/rubi/symbol.py,sha256=rSvRks0_l36oFOs8MVq7jg3dnrO2MTSKrBdtZe5joTg,1494
sympy/integrals/rubi/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/integrals/rubi/tests/test_rubi_integrate.py,sha256=XK4JseOlYHdm9Rnxb5vSgqmsSo7ZDu4z5PWgf8_tejw,3037
sympy/integrals/rubi/tests/test_utility_function.py,sha256=_zitkJvXy12RTQD6KIlXErmTpjLU0IUUfc365sZsxGw,82158
sympy/integrals/rubi/utility_function.py,sha256=7INKsiK2owgd2X8JbcVMxWIipq1R8UHwlyr4pLQFdsg,269967
sympy/integrals/singularityfunctions.py,sha256=BegUcpUW96FY9f8Yn0jHjK0LjCkM28NnCVg5S9cTWwU,2227
sympy/integrals/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/integrals/tests/test_deltafunctions.py,sha256=ivFjS-WlLQ4aMqjVS7ZzMChP2Mmw_JUPnwI9otiLnvs,3709
sympy/integrals/tests/test_failing_integrals.py,sha256=YAbF5YnyHVikufn6zF9pSpECQL_PIRVXCli9tju6_LY,7111
sympy/integrals/tests/test_heurisch.py,sha256=duI8sQqt-54GAO3JothGsk171eGjsUBCya0Ynnq2-0M,12496
sympy/integrals/tests/test_integrals.py,sha256=ed_Fszc3imOvxVHRzj7_IeBs4G4_jtZV2ENtVgjXaoQ,76162
sympy/integrals/tests/test_intpoly.py,sha256=Ey3fzMtxY5epDjVTJgGwljx1zb7ds8OySy2KUcI3Zgw,37340
sympy/integrals/tests/test_lineintegrals.py,sha256=zcPJ2n7DYt9KsgAe38t0gq3ARApUlb-kBahLThuRcq8,450
sympy/integrals/tests/test_manual.py,sha256=0ES-2fgVrIfs1FDl-bk7tHHZ8hbxQi-oVp2AHvSren8,31967
sympy/integrals/tests/test_meijerint.py,sha256=rSZRGqlQcwKNkJvwhzbiokuCN1cYglNTIV35N8NsS6o,32254
sympy/integrals/tests/test_prde.py,sha256=2BZmEDasdx_3l64-9hioArysDj6Nl520GpQN2xnEE_A,16360
sympy/integrals/tests/test_quadrature.py,sha256=iFMdqck36gkL-yksLflawIOYmw-0PzO2tFj_qdK6Hjg,19919
sympy/integrals/tests/test_rationaltools.py,sha256=6sNOkkZmOvCAPTwXrdU6hehDFleXYyakheX2KQaUHWY,5299
sympy/integrals/tests/test_rde.py,sha256=4d3vJupa-hRN4yNDISY8IC3rSI_cZW5BbtxoZm14y-Y,9571
sympy/integrals/tests/test_risch.py,sha256=Cfe6iu_EQyXLbqYieBDuV3pOfYBckbQ1YU4w058Yqf8,37620
sympy/integrals/tests/test_singularityfunctions.py,sha256=CSrHie59_NjNZ9B2GaHzKPNsMzxm5Kh6GuxlYk8zTuI,1266
sympy/integrals/tests/test_transforms.py,sha256=aOwV2CzDKiCKy981tmYwWbm7HRnv5JH1gotruYwabLs,50223
sympy/integrals/tests/test_trigonometry.py,sha256=moMYr_Prc7gaYPjBK0McLjRpTEes2veUlN0vGv9UyEA,3869
sympy/integrals/transforms.py,sha256=4Zn924_vHzagkFvMLKHd5mn7RNc8hBA7XZc3I7R1xM8,91464
sympy/integrals/trigonometry.py,sha256=WQ6AV0EuV9oquvSZzisz2zekw7T7EScVbhOXAdb4ntM,11082
sympy/interactive/__init__.py,sha256=yokwEO2HF3eN2Xu65JSpUUsN4iYmPvvU4m_64f3Q33o,251
sympy/interactive/printing.py,sha256=iQBl2edo_bTKMdC-7ohm2zNu01t3dLOFwa1V0JGSHgY,22698
sympy/interactive/session.py,sha256=bWUurVAigtiL3jN9VNwiJkyXTodpThKmqa_u-2GaJnY,15319
sympy/interactive/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/interactive/tests/test_interactive.py,sha256=Pbopy9lODrd_P46_xxlWxLwqPfG6_4J3CWWC4IqfDL4,485
sympy/interactive/tests/test_ipython.py,sha256=iYNmuETjveHBVpOywyv_jStQWkFwf1GuEBjoZUVhxK4,11799
sympy/interactive/traversal.py,sha256=XbccdO6msNAvrG6FFJl2n4XmIiRISnvda4QflfEPg7U,3189
sympy/liealgebras/__init__.py,sha256=K8tw7JqG33_y6mYl1LTr8ZNtKH5L21BqkjCHfLhP4aA,79
sympy/liealgebras/cartan_matrix.py,sha256=yr2LoZi_Gxmu-EMKgFuPOPNMYPOsxucLAS6oRpSYi2U,524
sympy/liealgebras/cartan_type.py,sha256=xLklg8Y5s40je6sXwmLmG9iyYi9YEk9KoxTSFz1GtdI,1790
sympy/liealgebras/dynkin_diagram.py,sha256=ZzGuBGNOJ3lPDdJDs4n8hvGbz6wLhC5mwb8zFkDmyPw,535
sympy/liealgebras/root_system.py,sha256=GwWc4iploE7ogS9LTOkkjsij1mbPMQxbV2_pvNriYbE,6727
sympy/liealgebras/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/liealgebras/tests/test_cartan_matrix.py,sha256=KCsakn0fHKHRbIUcrUkHBIKkudl3_ISUdHrfJy-UOd4,303
sympy/liealgebras/tests/test_cartan_type.py,sha256=t5PvYYDXbNIFL3CV59Je7SBIAeLLf-W3mOINPUoHK6E,339
sympy/liealgebras/tests/test_dynkin_diagram.py,sha256=DSixbnt_yd0zrhKzXW_XqkXWXYe1Dk2MmXN-Rjb1dGg,260
sympy/liealgebras/tests/test_root_system.py,sha256=YmGBdUeJ4PkLSfAfRgTF7GW62RCEd5nH27FSX9UaG5Q,927
sympy/liealgebras/tests/test_type_A.py,sha256=x7QmpjxsGmXol-IYVtN1lmIOmM3HLYwpX1tSG5h6FMM,657
sympy/liealgebras/tests/test_type_B.py,sha256=Gw0GP24wP2rPn38Wwla9W7BwWH4JtCGpaprZb5W6JVY,642
sympy/liealgebras/tests/test_type_C.py,sha256=ysSy-vzE9lNwzAunrmvnFkLBoJwF7W2On7QpqS6RI1s,927
sympy/liealgebras/tests/test_type_D.py,sha256=qrO4oCjrjkp1uDvrNtbgANVyaOExqOLNtIpIxD1uH0U,764
sympy/liealgebras/tests/test_type_E.py,sha256=suG6DaZ2R74ovnJrY6GGyiu9A6FjUkouRNUFPnEczqk,775
sympy/liealgebras/tests/test_type_F.py,sha256=yUQJ7LzTemv4Cd1XW_dr3x7KEI07BahsWAyJfXLS1eA,1378
sympy/liealgebras/tests/test_type_G.py,sha256=wVa6qcAHbdrc9dA63samexHL35cWWJS606pom-6mH2Q,548
sympy/liealgebras/tests/test_weyl_group.py,sha256=HrzojRECbhNUsdLFQAXYnJEt8LfktOSJZuqVE45aRnc,1501
sympy/liealgebras/type_a.py,sha256=l5SUJknj1xLgwRVMuOsVmwbcxY2V6PU59jBtssylKH4,4314
sympy/liealgebras/type_b.py,sha256=50xdcrec1nFFtyUWOmP2Qm9ZW1zpbrgwbz_YPKp55Go,4563
sympy/liealgebras/type_c.py,sha256=bXGqPiLN3x4NAsM-ZHKJPxFO6RY7lDZUckCarIODEi0,4439
sympy/liealgebras/type_d.py,sha256=Rgh7KpI5FQnDai6KVfoz_TREYaKxqvINDXu6Zdu-7EQ,4694
sympy/liealgebras/type_e.py,sha256=Uf-QzI-6bRJeI91stGHsiesknwBEVYIjZaiNP-2bIiY,9780
sympy/liealgebras/type_f.py,sha256=boKDhOxRcAWDBHsEYk4j14vUvT0mO3UkRq6QzqoPOes,4417
sympy/liealgebras/type_g.py,sha256=Ife98dGPtarGd-ii8hJbXdB0SMsct4okDkSX2wLN8XI,2965
sympy/liealgebras/weyl_group.py,sha256=5YFA8qC4GWDM0WLNR_6VgpuNFZDfyDA7fBFjBcZaLgA,14557
sympy/logic/__init__.py,sha256=RfoXrq9MESnXdL7PkwpYEfWeaxH6wBPHiE4zCgLKvk0,456
sympy/logic/algorithms/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/logic/algorithms/dpll.py,sha256=zqiZDm1oD5sNxFqm_0Hen6NjfILIDp5uRgEOad1vYXI,9188
sympy/logic/algorithms/dpll2.py,sha256=UbBxJjiUaqBbQPaivtrv3ZhNNuHHdUsJ5Us2vy8QmxA,20317
sympy/logic/algorithms/minisat22_wrapper.py,sha256=uINcvkIHGWYJb8u-Q0OgnSgaHfVUd9tYYFbBAVNiASo,1317
sympy/logic/algorithms/pycosat_wrapper.py,sha256=0vNFTbu9-YhSfjwYTsZsP_Z4HM8WpL11-xujLBS1kYg,1207
sympy/logic/boolalg.py,sha256=oQ8jNha1e6TTcEXULmZw6ggsbhyfl-CRWVTbqaQvT38,112491
sympy/logic/inference.py,sha256=18eETh6ObPCteJJgrrtrkCK031ymDQdvQbveaUymCcM,8542
sympy/logic/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/logic/tests/test_boolalg.py,sha256=C30FYh6W0gRdqcxXZwx61xqt6OH4a-vGAGJd02eXXgQ,48597
sympy/logic/tests/test_dimacs.py,sha256=EK_mA_k9zBLcQLTOKTZVrGhnGuQNza5mwXDQD_f-X1c,3886
sympy/logic/tests/test_inference.py,sha256=DOlgb4clEULjMBp0cG3ZdCrXN8vFdxJZmSDf-13bWSA,13246
sympy/logic/utilities/__init__.py,sha256=WTn2vBgHcmhONRWI79PdMYNk8UxYDzsxRlZWuc-wtNI,55
sympy/logic/utilities/dimacs.py,sha256=aaHdXUOD8kZHWbTzuZc6c5xMM8O1oHbRxyOxPpVMMdQ,1663
sympy/matrices/__init__.py,sha256=crzdP2u6Kq5kdj5S7UICLfCCkmI4aQngkCWIlJrpEyA,2481
sympy/matrices/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/matrices/benchmarks/bench_matrix.py,sha256=vGMlg-2il2cFeAWrf0NJ6pzPX3Yd3ZQMxFgQ4q5ILQE,306
sympy/matrices/common.py,sha256=5P8XTWqIgIgyrDiEcDngblspEKQA-Pkf6shMH1cbosU,96368
sympy/matrices/decompositions.py,sha256=VkCj3xHTFuqpBvaIpPMr7cz6BLqmPuNHXkKUKB5JG6w,48081
sympy/matrices/dense.py,sha256=mZsVLxVapDAkwf_SVzFppxzajBwl5NbngSBrWs2VVMc,20211
sympy/matrices/determinant.py,sha256=hqU4T_XjV5iIr-yo7IQawN0g1T9MoMOdD4bToOMGJ48,30117
sympy/matrices/eigen.py,sha256=OZcjJz-YKPLji7zs9VOyFQUN20cKhZ5m-dqCHdM3mYg,40663
sympy/matrices/expressions/__init__.py,sha256=IMqXCSsPh0Vp_MC9HZTudA5DGM4WBq_yB-Bst0azyM8,1692
sympy/matrices/expressions/adjoint.py,sha256=CbkYP2Hi9JVb7WO5HiCE14fwOn16fT3Le5HfV30cpCQ,1572
sympy/matrices/expressions/applyfunc.py,sha256=wFgcMOp6uakZ6wkkF7mB7GwM35GS5SGzXz1LCeJbemE,6749
sympy/matrices/expressions/blockmatrix.py,sha256=eKQ4GlVm4_6i2bah7T95qtJdXWLJJ28yry27ajGGfIo,31809
sympy/matrices/expressions/companion.py,sha256=lXUJRbjQR6e1mdHQdJwNIJXMW80XmKbOVqNvUXjB57U,1705
sympy/matrices/expressions/determinant.py,sha256=sf1ReOl5PUu8bbsbivupuUtr0yYMAtvDrMKFEo8vEUM,3168
sympy/matrices/expressions/diagonal.py,sha256=NtIFAfpoI_jhElfkJ6WCxc4r9iWN8VBOR3LLxKEzJsE,6326
sympy/matrices/expressions/dotproduct.py,sha256=sKdUhwVKTB3LEvd8xMwCDexNoQ1Dz43DCYsmm3UwFWw,1911
sympy/matrices/expressions/factorizations.py,sha256=zFNjMBsJqhsIcDD8Me4W8-Q-TV89WptfG3Dd9yK_tPE,1456
sympy/matrices/expressions/fourier.py,sha256=dvaftgB9jgkR_8ETyhzyVLtf1ZJu_wQC-ZbpTYMXZGE,2094
sympy/matrices/expressions/funcmatrix.py,sha256=q6R75wLn0UdV4xJdVJUrNaofV1k1egXLLQdBeZcPtiY,3520
sympy/matrices/expressions/hadamard.py,sha256=cRehbYeMEqq0c8S80rucBHaeoaJ59zw9NqlMj3kVHCQ,14430
sympy/matrices/expressions/inverse.py,sha256=kzPcDQeM2xpuwjXuoUrC9uKocac9_vOOUrAphtS5QcE,2727
sympy/matrices/expressions/kronecker.py,sha256=SfRY6o6TDJXNTOfRacgBikk9Ym3qhCpMyJ8I3RVGuCI,13422
sympy/matrices/expressions/matadd.py,sha256=lpu4lDhWOzvPxIxS134cSHzWETDK64j0nouzju99W4w,5302
sympy/matrices/expressions/matexpr.py,sha256=KDnnctOcScQoj2r9CMwF68JyF4EtATb0HqfwVQOSlgo,27337
sympy/matrices/expressions/matmul.py,sha256=1fD8b3lH2n3MGfP3DPQY_szVG1seeum_1Y3KmG_OW0I,16014
sympy/matrices/expressions/matpow.py,sha256=rO2GcFEIb9AzDx5lXkorSByqTwWFdwpJAKoOdXBzW0Y,4911
sympy/matrices/expressions/permutation.py,sha256=gGIht-JI1zWyZz7VPvm5S1Ae2i-P0WUAJl3euLRXWtM,8046
sympy/matrices/expressions/sets.py,sha256=KxGHZ-4p4nALQBj2f1clG43lB4qYu6M2P0zpubiH-ik,2001
sympy/matrices/expressions/slice.py,sha256=aNdY1Ey4VJR-UCvoORX2kh2DmA6QjOp-waENvWg8WVE,3355
sympy/matrices/expressions/special.py,sha256=5-Q1YO8IQRiuVaypgVWE4gI5aXtSBlVmDj3uJHs_PYw,7435
sympy/matrices/expressions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/matrices/expressions/tests/test_adjoint.py,sha256=cxOc334yNSI9MazhG9HT8s1OCXjkDWr3Zj2JnyHS3Z4,1065
sympy/matrices/expressions/tests/test_applyfunc.py,sha256=khe_ZfbAI_JGlZpscTHq2Hty1Yw1TOb7H4TfXJN10Ww,3746
sympy/matrices/expressions/tests/test_blockmatrix.py,sha256=EHJWm2dniNmf1CfODQSPm_HCCV77Ia0FbeNigsYJXZY,15695
sympy/matrices/expressions/tests/test_companion.py,sha256=Lam6r-cSOokjhSlJws55Kq-gL5_pHfeV_Xuvmn5PkRU,1657
sympy/matrices/expressions/tests/test_derivatives.py,sha256=i8elRGLbBffWEtU4tbnT-7DFADha3y_cS50-LdcXc2g,15898
sympy/matrices/expressions/tests/test_determinant.py,sha256=QutUKtr35GCZ4iS2H1WTzMwa0jAvL0prcS82Untgr5k,1989
sympy/matrices/expressions/tests/test_diagonal.py,sha256=3L6Vs_Yr36a8dgIqAeIcNEf0xcVyeyGhANNu0dlIpwI,4516
sympy/matrices/expressions/tests/test_dotproduct.py,sha256=Zkv2N6oRPm0-sN4PFwsVFrM5Y_qv4x2gWqQQQD86hBY,1171
sympy/matrices/expressions/tests/test_factorizations.py,sha256=6UPA_UhCL5JPbaQCOatMnxhGnQ-aIHmb3lXqbwrSoIE,786
sympy/matrices/expressions/tests/test_fourier.py,sha256=0eD69faoHXBcuQ7g2Q31fqs-gyR_Xfe-gv-7DXhJh_c,1638
sympy/matrices/expressions/tests/test_funcmatrix.py,sha256=zmOEcXHCK2MziwVBJb7iq9Q-Lbl4bbCQ_RAk27c7qUU,2381
sympy/matrices/expressions/tests/test_hadamard.py,sha256=Jq-4zhhqL9msgZK6rJTY9g9NXcJrfZdHtvyx_G1nxqA,4162
sympy/matrices/expressions/tests/test_indexing.py,sha256=wwYQa7LNlzhBA5fU50gPyE8cqaJf0s3O70PUx4eNCEA,12038
sympy/matrices/expressions/tests/test_inverse.py,sha256=xYAkba6kk-mvqp0Z1OvfuaEqQ2N7zC2F0bCP7Qt8dzM,2174
sympy/matrices/expressions/tests/test_kronecker.py,sha256=e5H6av3ioOn8jkjyDBrT3NEmCkyHbN6ZEHOlyB9OYLk,5366
sympy/matrices/expressions/tests/test_matadd.py,sha256=lfE5oxRwwLeAudV63lMXm2uMviDnPpqqtWIzyQIFkrs,1604
sympy/matrices/expressions/tests/test_matexpr.py,sha256=zcXE-KZ77KwZvMg7klLajpMOB2W2Trnw9c0ubvUt9hU,17504
sympy/matrices/expressions/tests/test_matmul.py,sha256=EflwEz5g0E0YN12DfzDTeUk-QyB0fP4BY_qgzCGqIlM,5775
sympy/matrices/expressions/tests/test_matpow.py,sha256=3tRbEmZi2gZTmkBm7mAWUDbX4jwEfC8tC4kYoOuzaUg,7304
sympy/matrices/expressions/tests/test_permutation.py,sha256=93Cqjj2k3aoR3ayMJLdJUa5h1u87bRRxT3I8B4FQsvU,5607
sympy/matrices/expressions/tests/test_sets.py,sha256=x60NRXGjxS_AE37jGFAOvZdKlWW5m4X0C3OzIukftAM,1410
sympy/matrices/expressions/tests/test_slice.py,sha256=C7OGAQQTz0YZxZCa7g0m8_0Bqq8jaPRa22JHVSqK7tY,2027
sympy/matrices/expressions/tests/test_special.py,sha256=fGwnrz53Pi_JE6a3fVmXHdwwlvbqugnUeFuWJTZ8Xhk,6958
sympy/matrices/expressions/tests/test_trace.py,sha256=fRlrw9CfdO3z3SI4TQb1fCUb_zVAndbtyOErEeCTCQ0,3383
sympy/matrices/expressions/tests/test_transpose.py,sha256=P3wPPRywKnrAppX6gssgD66v0RIcolxqDkCaKGGPVcM,1987
sympy/matrices/expressions/trace.py,sha256=-wGUhCN90beTDy-Lpnxgy_qdwlRsWlE2_wfBE1OeFnE,5342
sympy/matrices/expressions/transpose.py,sha256=SnfU_CE3_dBQkbi_SkPGqsE8eDgstYuplx7XDxKJIyA,2691
sympy/matrices/graph.py,sha256=O73INKAbTpnzNdZ7y08ow9U2CmApdn7S9NEsA9LR-XQ,9076
sympy/matrices/immutable.py,sha256=3NWY8oHiTGdWQR6AfZpg2fOtjRc1KH75yxkITNzCcPg,5425
sympy/matrices/inverse.py,sha256=pGDQ3-iG9oTMEIuCwrFe0X5lxkvZSF-iMzod8zTv1OA,11409
sympy/matrices/matrices.py,sha256=XSg5C_ji5c-DyuofiV8WYLt8Ny-eHbDE5z_9nY9DnyM,78001
sympy/matrices/normalforms.py,sha256=W1HqK_8y-aCWIgwx2IbozI61EfsCHbYJ_RcV7GqbWJM,3764
sympy/matrices/reductions.py,sha256=GmXqmi3mgxi-jUiSx-B8xN0M7qLLovdDDTzjoMZvQR0,10781
sympy/matrices/repmatrix.py,sha256=a_q72tdcGNmaGmald6WNCuRNzehNuffyFemJ7WLX0JM,21965
sympy/matrices/solvers.py,sha256=s3eY8Iqat9Nvn8PZDq0pw7zIaQbvjJ-TP_GwGIKSnds,22765
sympy/matrices/sparse.py,sha256=Euy_BJsRhQXLeBjQF7bC5O8kzOQhIk7zrZWxcteEbTU,14681
sympy/matrices/sparsetools.py,sha256=tzI541P8QW_v1eVJAXgOlo_KK1Xp6u1geawX_tdlBxY,9182
sympy/matrices/subspaces.py,sha256=uLo4qnP0xvFcFo5hhf6g7pHSHiRbcQ1ATDKwGBxW7CE,3761
sympy/matrices/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/matrices/tests/test_commonmatrix.py,sha256=jlcl6iS8iZPWMrNdbNT3SccvogLAvy3h7D4L9O3aw_U,39457
sympy/matrices/tests/test_decompositions.py,sha256=My7dCcMvEdAR5fd0g_vDDGRMWgvGFT-_mk9Ra0fF8z8,14418
sympy/matrices/tests/test_determinant.py,sha256=RYmf2bLWtk8nuyIJuhRpSIFklsfVtAGa2gx2AvAi2TU,13350
sympy/matrices/tests/test_eigen.py,sha256=Hxc__jG0Pgd4k6QRRQmWwDjODZnohfMwVBlughrXZYw,21948
sympy/matrices/tests/test_graph.py,sha256=ckfGDCg2M6gluv9XFnfURga8gxd2HTL7aX281s6wy6c,3213
sympy/matrices/tests/test_immutable.py,sha256=qV1L1i8RWX3ihJx3J-M07s_thfXmuUA1wIRfQnUbqyA,4618
sympy/matrices/tests/test_interactions.py,sha256=RKQsDDiwuEZxL7-bTJR_ue7DKGbCZYl7pvjjgE7EyEY,2066
sympy/matrices/tests/test_matrices.py,sha256=HySEMf5uwWU5VRVdOlis51sBlqAqazB2_b3-F4URkGE,144491
sympy/matrices/tests/test_normalforms.py,sha256=JQvFfp53MW8cJhxEkyNvsMmhhD7FVncAkjuGMXu5Fok,3009
sympy/matrices/tests/test_reductions.py,sha256=xbB-_vbF9IYIzvkaOjsVeFfJHRk3buFRNdxKGZvuZXE,13951
sympy/matrices/tests/test_solvers.py,sha256=hsbvtRyBhLzTxX62AYqDTn7bltGanT1NwYUecUPEViE,20386
sympy/matrices/tests/test_sparse.py,sha256=GvXN6kBVldjqoR8WN8I_PjblKhRmyRWvVuLUgZEgugY,23281
sympy/matrices/tests/test_sparsetools.py,sha256=pjQR6UaEMR92NolB_IGZ9Umk6FPZjvI0vk1Fd4H_C5I,4877
sympy/matrices/tests/test_subspaces.py,sha256=vnuIyKbViZMa-AHCZ3PI9HbCL_t-LNI70gwbZvzRtzw,3839
sympy/matrices/utilities.py,sha256=mMnNsDTxGKqiG0JATsM4W9b5jglhacy-vmRw2aZojgY,2117
sympy/multipledispatch/__init__.py,sha256=aV2NC2cO_KmD6QFiwy4oC1D8fm3pFuPbaiTMeWmNWak,259
sympy/multipledispatch/conflict.py,sha256=rR6tKn58MfhMMKZ4ZrhVduylXd9f5PjT2TpzM9LMB6o,2117
sympy/multipledispatch/core.py,sha256=CYf-PuNGWRZtjNwQw1klpwSqc9CBY_JiFJ281KVMkdc,2250
sympy/multipledispatch/dispatcher.py,sha256=wErJy5aMtQq9W-YtXR_GHSCplj2KZYcw9QyfeISIHl8,12230
sympy/multipledispatch/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/multipledispatch/tests/test_conflict.py,sha256=msNVSiikuPOqsEm_MMGmjsNbA2CAR0F1FZaHskzzo04,1786
sympy/multipledispatch/tests/test_core.py,sha256=E5Nl3zlh8qRBlwyz02553PIEg371jB4BsWns2eyIck8,4053
sympy/multipledispatch/tests/test_dispatcher.py,sha256=saJPpGXLpLOuRfw-ekzZGzY-Rys0NsS5ke0n33i9j0U,6228
sympy/multipledispatch/utils.py,sha256=JTc39g5ee9yUHq3NXYPV1Ah4s31vVFy8F5lWQsv9jGk,3102
sympy/ntheory/__init__.py,sha256=MBs5Tdw5xAgNMlCdN8fSLiIswQudZibIbHjI9L5BEds,2746
sympy/ntheory/bbp_pi.py,sha256=3Ff-ihyEiquIqDVUeUE7GyAD3vcbZrmgd_DPuzfvTeU,5209
sympy/ntheory/continued_fraction.py,sha256=-GA1fzvgK7h8Bad_1NN0majRhwIQEg2zZDPuKSHAVYA,10109
sympy/ntheory/digits.py,sha256=xFzoMyAC36fLR5OvtTetoXUSvhNTbP3HKY_co8RUEr4,3688
sympy/ntheory/ecm.py,sha256=pK474hyuB70f9c3ez_xz0bnMYlLbin7IjJicQAybcGw,10200
sympy/ntheory/egyptian_fraction.py,sha256=pxJl2r2rrWx0q04OFBvEs_AxYkKrWa6AzWsiDmVXGvU,6879
sympy/ntheory/elliptic_curve.py,sha256=UnvHSR6Ttk4TkzMJaaHSpSMYGJ6IcyJHmxRArx49rYQ,11515
sympy/ntheory/factor_.py,sha256=Ed3mtp8yeAVUtr-yLIgdXWtgtOKx-0vViu95VZ2BaY8,75771
sympy/ntheory/generate.py,sha256=MuLNt_9k8HaJlrfsMBCgPvw4p_8gDuh3LzP7Jq6ITnw,29410
sympy/ntheory/modular.py,sha256=fA3_ovJcPqrwT2bPjmd4cSGPDyVG6HSM9oP07HP1R_s,7650
sympy/ntheory/multinomial.py,sha256=rbm3STjgfRbNVbcPeH69qtWktthSCk0sC373NuDM6fU,5073
sympy/ntheory/partitions_.py,sha256=fu6SFxj2AFFKGUVuutm80OXAfU4bbDDhh3x7LidIStk,5973
sympy/ntheory/primetest.py,sha256=D0muM9oUloweDHciaLhBXKLVuULR7rUat6HKsIMFvvg,20949
sympy/ntheory/qs.py,sha256=K5kzeX7xLnx0r20de0UR9qKTcBeJwQgnX7mczRzQ-wQ,18470
sympy/ntheory/residue_ntheory.py,sha256=ZhZzydW-tj8j8ZXmMW5_nNianSXz7btT_CavW_buxwU,39488
sympy/ntheory/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/ntheory/tests/test_bbp_pi.py,sha256=-RXXkqMUfVCYeO9HonldOOISDKDaUYCCe5CUgK18L3o,9433
sympy/ntheory/tests/test_continued_fraction.py,sha256=gfQfLuLVFn-bmEPBcgnU-f0VibJiY8hAEl0FO4V3iVU,3052
sympy/ntheory/tests/test_digits.py,sha256=jC8GCQVJelFcHMApf5TZU1KXP2oBp48lkkD0bM2TLCo,1182
sympy/ntheory/tests/test_ecm.py,sha256=Hy9pYRZPuFm7yrGVRs2ob_w3YY3bMEENH_hkDh947UE,2303
sympy/ntheory/tests/test_egyptian_fraction.py,sha256=tpHcwteuuQAahcPqvgBm4Mwq-efzcHOn8mldijynjlE,2378
sympy/ntheory/tests/test_elliptic_curve.py,sha256=wc0EOsGo-qGpdevRq1o64htwTOT_YSUzUfyhJC-JVbg,624
sympy/ntheory/tests/test_factor_.py,sha256=rp-wJ-UM6kK9HNc9mk5P7u08-8oR_J0K4Jt7Jlk9PGA,25040
sympy/ntheory/tests/test_generate.py,sha256=ALKzLAcCPIMTr3JC6RJHuOYd6z0aFVaF5-e481icYe8,8069
sympy/ntheory/tests/test_modular.py,sha256=g73sUXtYNxzbDcq5UnMWT8NodAU8unwRj_E-PpvJqDs,1425
sympy/ntheory/tests/test_multinomial.py,sha256=8uuj6XlatNyIILOpjJap13CMZmDwrCyGKn9LiIUiLV0,2344
sympy/ntheory/tests/test_partitions.py,sha256=AkmDpR0IFxo0ret91tRPYUqrgQfQ367okTt2Ee2Vm60,507
sympy/ntheory/tests/test_primetest.py,sha256=1Pkoi-TNxvB0oT1J5_YXryabyiGgPeXigS_vo_4x_v8,7062
sympy/ntheory/tests/test_qs.py,sha256=2YyVoX3x4sOSPxxe2yyoVmhq--k8T-5KkPLupPNIxE4,4136
sympy/ntheory/tests/test_residue.py,sha256=t3-yaWmZvfkQpjUDqOzgwnTFO0je7BkEU2QKpA-pttU,12884
sympy/parsing/__init__.py,sha256=KHuyDeHY1ifpVxT4aTOhomazCBYVIrKWd28jqp6YNJ8,125
sympy/parsing/ast_parser.py,sha256=PWuAoNPZ6-C8HCYYGCG9tMCgwuMzi_ebyIqFSJCqk6k,2724
sympy/parsing/autolev/Autolev.g4,sha256=980mo25mLWrQFmhRIg-aqIalUuwktYYaBGTXZ5_XZwA,4195
sympy/parsing/autolev/__init__.py,sha256=sp5hzv5siVW3xUmhkp0S0iaA0Cz-PVB0HO1zC04pxYs,3611
sympy/parsing/autolev/_antlr/__init__.py,sha256=MQ4ZacpTuP-NmruFXKdWLQatoeVJQ8SaBQ2DnYvtyE8,203
sympy/parsing/autolev/_antlr/autolevlexer.py,sha256=-CcZ9BbABD3GAWqtpLtFfqxg_rBhprg3avKckPyBjg4,13607
sympy/parsing/autolev/_antlr/autolevlistener.py,sha256=EDb3XkH9Y7CLzxGM-tY-nGqxMGfBHVkqKdVCPxABgRE,12821
sympy/parsing/autolev/_antlr/autolevparser.py,sha256=pkzuxJsWPQ2sF0Hq2nq2suR28-WKWwRr6prG0MN7XGA,108725
sympy/parsing/autolev/_build_autolev_antlr.py,sha256=XOR44PCPo234I_Z1QnneSArY8aPpp4xP4-dycMalQQw,2590
sympy/parsing/autolev/_listener_autolev_antlr.py,sha256=P5XTo2UjkyDyx4d9kpmWIm6BoCXyOiED9s8Tr3w3Am4,104758
sympy/parsing/autolev/_parse_autolev_antlr.py,sha256=rSJc6PiDg2Rby6jV4F_uzwrQXmOSjjPQI3OOZD8bmMo,1723
sympy/parsing/autolev/test-examples/README.txt,sha256=0C4m_nLROeV5J8nMfm3RYEfYgQJqmlHZaCpVD24boQY,528
sympy/parsing/autolev/test-examples/pydy-example-repo/chaos_pendulum.al,sha256=HpTcX2wXzLqmgpp8fcSqNweKjxljk43iYK0wQmBbCDI,690
sympy/parsing/autolev/test-examples/pydy-example-repo/chaos_pendulum.py,sha256=FSu4TP2BDTQjzYhMkcpRhXbb3kAD27XCyO_EoL55Ack,2274
sympy/parsing/autolev/test-examples/pydy-example-repo/double_pendulum.al,sha256=wjeeRdCS3Es6ldX9Ug5Du1uaijUTyoXpfTqmhL0uYfk,427
sympy/parsing/autolev/test-examples/pydy-example-repo/double_pendulum.py,sha256=uU9azTUGrY15BSDtw5T_V-7gmjyhHbXslzkmwBvFjGk,1583
sympy/parsing/autolev/test-examples/pydy-example-repo/mass_spring_damper.al,sha256=Gf7OhgRlwqUEXq7rkfbf89yWA23u4uIUJ-buXTyOuXM,505
sympy/parsing/autolev/test-examples/pydy-example-repo/mass_spring_damper.py,sha256=9ReCAqcUH5HYBgHmop9h5Zx54mfScWZN5L5F6rCHk4w,1366
sympy/parsing/autolev/test-examples/pydy-example-repo/non_min_pendulum.al,sha256=p5v40h1nVFrWNqnB0K7GiNQT0b-MqwayYjZxXOY4M8M,362
sympy/parsing/autolev/test-examples/pydy-example-repo/non_min_pendulum.py,sha256=DdxcWrm3HMQuyyY3Pk6sKHb4RXhQEM_EKY3HYZCP8ec,1503
sympy/parsing/autolev/test-examples/ruletest1.al,sha256=mDJ02Q1Qm-ShVmGoyjzSfgDJHUOuDrsUg3YMnkpKdUw,176
sympy/parsing/autolev/test-examples/ruletest1.py,sha256=eIKEFzEwkCFhPF0GTmf6SLuxXT384GqdCJnhiL2U0BQ,555
sympy/parsing/autolev/test-examples/ruletest10.al,sha256=jKpV8BgX91iQsQDLFOJyaS396AyE5YQlUMxih5o9RK0,781
sympy/parsing/autolev/test-examples/ruletest10.py,sha256=I1tsQcSAW6wqIguF-7lwlj9D4YZ8kCZqPqTKPUHR9oI,2726
sympy/parsing/autolev/test-examples/ruletest11.al,sha256=j_q7giq2KIuXVRLWwNlwIlpbhNO6SqBMnLGLcxIkzwk,188
sympy/parsing/autolev/test-examples/ruletest11.py,sha256=dYTRtXvMDXHiKzXHD2Sh0fcEukob3wr_GbSeqaZrrO8,475
sympy/parsing/autolev/test-examples/ruletest12.al,sha256=drr2NLrK1ewn4FjMppXycpAUNbZEQ0IAMsdVx8nxk6I,185
sympy/parsing/autolev/test-examples/ruletest12.py,sha256=ZG36s3PnkT0aKBM9Nx6H0sdJrtoLwaebU9386YSUql8,472
sympy/parsing/autolev/test-examples/ruletest2.al,sha256=d-QjPpW0lzugaGBg8F6pDl_5sZHOR_EDJ8EvWLcz4FY,237
sympy/parsing/autolev/test-examples/ruletest2.py,sha256=jrJfb0Jk2FP4GS5pDa0UB5ph0ijEVd1X8meKeZrTVng,820
sympy/parsing/autolev/test-examples/ruletest3.al,sha256=1TAaOe8GI8-yBWJddfIxwnvScHNmOjSzSaQn0RS_v5k,308
sympy/parsing/autolev/test-examples/ruletest3.py,sha256=O3K3IQo-HCjAIOSkfz3bDlst7dVUiRwhOZ0q_3jb5LU,1574
sympy/parsing/autolev/test-examples/ruletest4.al,sha256=qPGlPbdDRrzTDUBeWydAIa7mbjs2o3uX938QAsWJ7Qk,302
sympy/parsing/autolev/test-examples/ruletest4.py,sha256=WHod5yzKF4TNbEf4Yfxmx9WnimA7NOXqtTjZXR8FsP0,682
sympy/parsing/autolev/test-examples/ruletest5.al,sha256=VuiKjiFmLK3uEdho0m3pk-n0qm4SNLoLPMRJqjMJ4GY,516
sympy/parsing/autolev/test-examples/ruletest5.py,sha256=WvUtno1D3BrmFNPYYIBKR_gOA-PaHoxLlSTNDX67dcQ,1991
sympy/parsing/autolev/test-examples/ruletest6.al,sha256=-HwgTmh_6X3wHjo3PQi7378t8YdizRJClc5Eb5DmjhE,703
sympy/parsing/autolev/test-examples/ruletest6.py,sha256=vEO0jMOD-KIevAcVexmpvac0MGjN7O_dNipOBJJNzF0,1473
sympy/parsing/autolev/test-examples/ruletest7.al,sha256=wR9S9rTzO9fyKL6Ofgwzw8XCFCV_p2hBpYotC8TvADI,773
sympy/parsing/autolev/test-examples/ruletest7.py,sha256=_XvMrMe5r9RLopTrIqMGLhaYvHL1qjteWz9CKcotCL8,1696
sympy/parsing/autolev/test-examples/ruletest8.al,sha256=P7Nu3Pq2R1mKcuFRc9dRO5jJ1_e5fwWdtqYG8NHVVds,682
sympy/parsing/autolev/test-examples/ruletest8.py,sha256=8tgbwJ-ir0wiOCsgIFCAu4uD8SieYRrLoLzEfae5YQY,2690
sympy/parsing/autolev/test-examples/ruletest9.al,sha256=txtZ5RH2p1FvAe6etwetSCH8rLktnpk5z0W72sCOdAA,755
sympy/parsing/autolev/test-examples/ruletest9.py,sha256=GtqV-Wq2GGJzfblMscAz-KXCzs0P_4XqvA3FIdlPe04,1965
sympy/parsing/c/__init__.py,sha256=J9CvkNRY-qy6CA06GZYuwTuxdnqas6oUP2g0qLztGro,65
sympy/parsing/c/c_parser.py,sha256=9xnpJ30VrF5E41kBxsiAL0pf4_53kEMSsW8vzm6jGqU,39359
sympy/parsing/fortran/__init__.py,sha256=KraiVw2qxIgYeMRTFjs1vkMi-hqqDkxUBv8Rc2gwkCI,73
sympy/parsing/fortran/fortran_parser.py,sha256=5ePsu5iZ5bJ5WJHOFQiKQN7q1ANfFb0QEpqNcwecLwk,11481
sympy/parsing/latex/LICENSE.txt,sha256=AHvDClj6QKmW53IEcSDeTq8x9REOT5w7X5P8374urKE,1075
sympy/parsing/latex/LaTeX.g4,sha256=fG0ZUQPwYQOIbcyaPDAkGvcfGs3ZwwMB8ZnKW5yHUDY,5821
sympy/parsing/latex/__init__.py,sha256=10TctFMpk3AolsniTJR5rQr19QXNqVTx-rl8ZFkHC4s,991
sympy/parsing/latex/_antlr/__init__.py,sha256=TAb79senorEsoYLCLwUa8wg8AUCHzmmZ7tLdi0XGNaE,384
sympy/parsing/latex/_antlr/latexlexer.py,sha256=M9AAt2gDo-UU1FiZvhKfxcdet9gVS8tRJ9AHkcCp4ss,30500
sympy/parsing/latex/_antlr/latexparser.py,sha256=p6yh1HwwBtbg-bGPMnCbLBPpdNPYxw3dIXCM9oQRwQw,130150
sympy/parsing/latex/_build_latex_antlr.py,sha256=id_4pbcI4nAa0tHumN0lZX0Ubb-BaJ3czGwiQR_jZPE,2777
sympy/parsing/latex/_parse_latex_antlr.py,sha256=JLMpIl9qI6LEXJRB4rGO99f73gypoq2kxuDKBn6qKXU,20384
sympy/parsing/latex/errors.py,sha256=adSpvQyWjTLsbN_2KHJ4HuXpY7_U9noeWiG0lskYLgE,45
sympy/parsing/mathematica.py,sha256=FlgUrXmsr2xNL5FnGht7mLpOXdACr5JSMHD5a1fEYNs,39186
sympy/parsing/maxima.py,sha256=DhTnXRSAceijyA1OAm86c6TyW9-aeUVoZEELGu0oZtY,1835
sympy/parsing/sym_expr.py,sha256=JIx_4oHlFcyzSk7S3yxDbUju4oL2m9SMvcbYJPPNtOw,8894
sympy/parsing/sympy_parser.py,sha256=2cEtalAkUW1IZZNtOqueUTNeInulO2tQyY9QLUz-Hks,43639
sympy/parsing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/parsing/tests/test_ast_parser.py,sha256=lcT8w7mn6UEZ8T-xfA4TqG4Mt7JxY00oHhOW7JtHQfY,803
sympy/parsing/tests/test_autolev.py,sha256=tQuUFa8YqVdsHPOcUhAwlMKB8Uk08HejDhDCda8lXs0,6647
sympy/parsing/tests/test_c_parser.py,sha256=yIYdfnaHX9Z93-Cmf6x9C7eysQ-y3_lU-6CGRXN4WL8,154665
sympy/parsing/tests/test_fortran_parser.py,sha256=SGbawrJ4a780TJAFVMONc7Y3Y8VYgVqsIHxVGaicbxE,11828
sympy/parsing/tests/test_implicit_multiplication_application.py,sha256=3U44rOoIYghlrr6hdjafBXeFn_CPL8qB0xWq2Q7mRLw,7449
sympy/parsing/tests/test_latex.py,sha256=x42KC0_C-gkjC2krysDIbJ3nbjVpAQGCL4g-MtPxuEg,11415
sympy/parsing/tests/test_latex_deps.py,sha256=oe5vm2eIKn05ZiCcXUaO8X6HCcRmN1qCuTsz6tB7Qrk,426
sympy/parsing/tests/test_mathematica.py,sha256=P9gVuhz-gEkvY4amQTASCj2X0tK5lLkmJFrzDmbcZ30,13097
sympy/parsing/tests/test_maxima.py,sha256=BJJY6eoig9ZfzZ7TzmCF3U6Y8Y6HbcfKAs1Crmk4a8c,1983
sympy/parsing/tests/test_sym_expr.py,sha256=3fu4x8weMyDLq3SCY4PXTAAdH3FM-m3MNu-r4k60Nvk,5669
sympy/parsing/tests/test_sympy_parser.py,sha256=Zzkyl9Ng92YoaocxfuEYZoypL-TBORULuuZuNAI1dEE,11822
sympy/physics/__init__.py,sha256=F_yvUMCuBq3HR-3Ai6W4oktBsXRg8KdutFLwT9FFJlY,220
sympy/physics/continuum_mechanics/__init__.py,sha256=moVrcsEw_a8db69dtuwE-aquZ1TAJc7JxHukrYnJuyM,89
sympy/physics/continuum_mechanics/beam.py,sha256=YFugwlwfVFnev1DlgbrM3CQmZ5VBfnxY6m5gev_9q-A,144981
sympy/physics/continuum_mechanics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/continuum_mechanics/tests/test_beam.py,sha256=2RaMO5Z6S6gCWWasHzFsbYt2pHm3aNNUO-fH_7OpBM4,26307
sympy/physics/continuum_mechanics/tests/test_truss.py,sha256=ZfhtTiWaKktDGtjIS4Tu3tTT_SE7tJMMGHcPY_2DBOk,2997
sympy/physics/continuum_mechanics/truss.py,sha256=pQKGgPc75n3ctvNhygwqTLeffhDGQDaS2dZg2nyaMVk,21533
sympy/physics/control/__init__.py,sha256=_EeNDNeqIyj0zHovILiaCoa3XLoSXhzpG5TBWOgKseE,974
sympy/physics/control/control_plots.py,sha256=qVGaA0dS8PobLk2-ShkLByxRaJ8cOToQnUBD_M7aqUk,32225
sympy/physics/control/lti.py,sha256=jRh38hH5f-FNAD9C9rdd6mxVCiWKluSpgbbRAHYCDxQ,111621
sympy/physics/control/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/control/tests/test_control_plots.py,sha256=EDTfKI08wacHtYFKf7HeBi43msqqAvMOhTWf-8RJu3k,15728
sympy/physics/control/tests/test_lti.py,sha256=VNHtxbQY4hVpSjvZjnIHGsPu2ean_7-DXrOdLQXTsAY,58872
sympy/physics/hep/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/hep/gamma_matrices.py,sha256=qjV_IpXg0IHPA1fRH1JwbwfRAIRjEgtyTwGX_7VzgaM,24275
sympy/physics/hep/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/hep/tests/test_gamma_matrices.py,sha256=68F36eI9DQoIaHQpmUY1CzEMfO2Rsqgo6WNOv4x7vDw,13809
sympy/physics/hydrogen.py,sha256=eVeT92wevZ0R69dviD86voWIzQOjjJyrQDEdf5CgdQ0,7593
sympy/physics/matrices.py,sha256=jHfbWkzL2myFt-39kodQo5wPubBxNZKXlljuSxZL4bE,3836
sympy/physics/mechanics/__init__.py,sha256=FSY1a2UWzhVn_sumkG3cLmXRPmM2iDk6DU8QEvLPtio,1883
sympy/physics/mechanics/body.py,sha256=D0Jv6A_egoThomHzGhM39h7KoMfJ5Lm7uql8SKAe-Dc,17568
sympy/physics/mechanics/functions.py,sha256=9ddJEcO5Nn1XIuCQatB4rkbMQHUokXBhi_FB3-Vimg8,23535
sympy/physics/mechanics/joint.py,sha256=5P1VpURTSKi-YOxAz_LO5w1QUMFl18Y4ZwIMuK25Xj0,24523
sympy/physics/mechanics/jointsmethod.py,sha256=bAE6LnxHEewsl8UpAxVlPyJO-rsLa7Evy2dSvXdXG0M,8454
sympy/physics/mechanics/kane.py,sha256=2F_XSXzKLtDQm9LkaGNf2VAGO9DvBa9FbZmABv3D1wE,28610
sympy/physics/mechanics/lagrange.py,sha256=RYrDHRYcqtWhpPsZh5Z_MmsgM1X__jiSv-8Pou6U7Kw,18319
sympy/physics/mechanics/linearize.py,sha256=sEX52OQP-pJ_pIlw8oVv01oQPeHiPf0LCm1GMuIn1Yo,15615
sympy/physics/mechanics/method.py,sha256=2vFRhA79ra4HR6AzVBHMr3oNncrcqgLLMRqdyif0DrI,660
sympy/physics/mechanics/models.py,sha256=9q1g3I2xYpuTMi-v9geswEqxJWTP3RjcOquRfzMhHzM,6463
sympy/physics/mechanics/particle.py,sha256=hPdRqjfi2s8VJ2_h0CZl_FlzoqI6g0dogtA-6woiO6A,7553
sympy/physics/mechanics/rigidbody.py,sha256=2Sj7oeJn5u_MJKARy6uw8EQSothZQIZuPObcYS9oVQs,10782
sympy/physics/mechanics/system.py,sha256=Un6ep47tygf1Vdp-8G2WS6uT-FCqOBRwrDUdonFd_vA,18671
sympy/physics/mechanics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/mechanics/tests/test_body.py,sha256=OjIIuqu_114OsuFpmaoV6uFx9DJkDT5SRVFr1ixN8lk,9316
sympy/physics/mechanics/tests/test_functions.py,sha256=sIGovtDOQA8hbAVOxN2ptEg9OqmSjg_muw8qnY3Yhtk,9101
sympy/physics/mechanics/tests/test_joint.py,sha256=_SE5fWWjnE_C9MfszOzmz56JsluYyXBfdJjhMHnd1Ww,20637
sympy/physics/mechanics/tests/test_jointsmethod.py,sha256=bQTfTlg3u5CQ6SMs_wFwMKuu7Qae67amocSGsmrv0XY,9272
sympy/physics/mechanics/tests/test_kane.py,sha256=NZln6BbwatHSC208SA5zXh5yCjt-EA1N5Pg6cv84mrY,13713
sympy/physics/mechanics/tests/test_kane2.py,sha256=3MweQ_qfbyc8WqcSvvj7iKQLRdMlki9S6uNyd8ZIDN0,19111
sympy/physics/mechanics/tests/test_kane3.py,sha256=go7p9RO-KyxzrHnDeyBFu4k4qHr8X8Js8XtGNPStCLY,14444
sympy/physics/mechanics/tests/test_kane4.py,sha256=a7CFmnz-MFbQbfop_tAhRUAHk7BJZEfa9PlcX2K8Y0Y,4722
sympy/physics/mechanics/tests/test_lagrange.py,sha256=tgdEq_2HEq6zLPDtXWlelJLUj5jFmzuwYdFu0cHRYdg,9593
sympy/physics/mechanics/tests/test_lagrange2.py,sha256=HCnDemnFD1r3DIT4oWnypcsZKvF1BA96_MMYHE7Q_xo,1413
sympy/physics/mechanics/tests/test_linearize.py,sha256=G4XdGFp6lIUwNJ6qm77X24ZPKgGcyxYBuCv61WeROXM,11826
sympy/physics/mechanics/tests/test_method.py,sha256=L7CnsvbQC-U7ijbSZdu7DEr03p88OLj4IPvFJ_3kCDo,154
sympy/physics/mechanics/tests/test_models.py,sha256=X7lrxTIWuTP7GgpYyGVmOG48zG4UDWV99FACXFO5VMA,5091
sympy/physics/mechanics/tests/test_particle.py,sha256=j66nmXM7R_TSxr2Z1xywQKD-al1z62I15ozPaywN1n0,2153
sympy/physics/mechanics/tests/test_rigidbody.py,sha256=gbCOsp2wEM5P0Z3Hpgaeug4i7Qk3pMSVtHW-W1O4feY,4344
sympy/physics/mechanics/tests/test_system.py,sha256=vRxvOH56wuWRTygmTcJJZAlB6Bw2Vlhcr9q6A526_WA,8713
sympy/physics/optics/__init__.py,sha256=0UmqIt2-u8WwNkAqsnOVt9VlkB9K0CRIJYiQaltJ73w,1647
sympy/physics/optics/gaussopt.py,sha256=xMoYUyPyh2ycyNj5gomy_0PkNKKHa9XRlE39mZUQaqI,20892
sympy/physics/optics/medium.py,sha256=cys0tWGi1VCPWMTZuKadcN_bToz_bqKsDHSEVzuV3CE,7124
sympy/physics/optics/polarization.py,sha256=0vIDOn-MXhzqWCPzVG7W1K1ieGywgj0Xx5kRhiLuKdU,21489
sympy/physics/optics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/optics/tests/test_gaussopt.py,sha256=QMXJw_6mFCC3918b-pc_4b_zgO8Hsk7_SBvMupbEi5I,4222
sympy/physics/optics/tests/test_medium.py,sha256=RxG7N3lzmCO_8hIoKyPnDKffmk8QFzA9yamu1_mr_dE,2194
sympy/physics/optics/tests/test_polarization.py,sha256=81MzyA29HZckg_Ss-88-5o0g9augDqCr_LwcJIiXuA0,2605
sympy/physics/optics/tests/test_utils.py,sha256=SjicjAptcZGwuX-ib_Lq7PlGONotvo2XJ4p3JA9iNVI,8553
sympy/physics/optics/tests/test_waves.py,sha256=PeFfrl7MBkWBHdc796sDDYDuhGepat3DQk7PmyTXVnw,3397
sympy/physics/optics/utils.py,sha256=qoSlzujMTHDxIZvBQPJ_cF2PxB-awyXVqCndriUd-PQ,22154
sympy/physics/optics/waves.py,sha256=Iw-9gGksvWhPmQ_VepmI90ekKyzHdPlq6U41wdM4ikI,10042
sympy/physics/paulialgebra.py,sha256=1r_qDBbVyl836qIXlVDdoF89Z9wedGvWIkHAbwQaK-4,6002
sympy/physics/pring.py,sha256=SCMGGIcEhVoD7dwhY7_NWL1iKwo7OfgKdmm2Ok_9Xl0,2240
sympy/physics/qho_1d.py,sha256=w1ltOxYL9GpGTYy7hymToCVG6-nvm8kltlrQ1UVeszs,2037
sympy/physics/quantum/__init__.py,sha256=RA2xbM7GhFq3dVNTna3odlTJYHqNerxjNeZ1kwigHiw,1705
sympy/physics/quantum/anticommutator.py,sha256=TH0mPF3Dk9mL5fa2heuampDpwWFxxh3HCcg4g2uNQ_E,4446
sympy/physics/quantum/boson.py,sha256=eVhs9ciXouODb_nJksGVhhFwy9tfwYhYop5d04l5MNo,6303
sympy/physics/quantum/cartesian.py,sha256=gqP-DuW2-Qc9Bj9vC6V9h3Awk2A_l2xo87GiUeoOVZo,9037
sympy/physics/quantum/cg.py,sha256=CEqIk39Yqjf0Nce4boaaTQHYuJFeWOhe0480ed0Kef8,23330
sympy/physics/quantum/circuitplot.py,sha256=dblBQ3GecaYAn4BRXS5iRmX6mAmDeE2vITf5RRT4260,11955
sympy/physics/quantum/circuitutils.py,sha256=VLqeoXPHuijup0IVMw8Mf7E_mHo9DfgoJLPrkrz94Vc,13893
sympy/physics/quantum/commutator.py,sha256=7IiNnFYxxi9EfElCFtMLEQccb6nB-jIeq4x3IlIqzKs,7521
sympy/physics/quantum/constants.py,sha256=20VRATCkSprSnGFR5ejvMEYlWwEcv1B-dE3RPqPTQ9k,1420
sympy/physics/quantum/dagger.py,sha256=KOeHXb52hvR1IbeNwlNU30KPiD9xv7S1a2dowkQqBLM,2428
sympy/physics/quantum/density.py,sha256=vCH8c4Fu5lcrT0PsuBqEK7eWnyHtCRwVx4wSh3f07ME,9743
sympy/physics/quantum/fermion.py,sha256=svczNZ861BR9KiMg3Qu8yO3LgCPkUdNsZnspaHVjn78,4501
sympy/physics/quantum/gate.py,sha256=ZL-yYxZYnuE27pEz-lNk0xQtu2BReHVzs5rdPJykljk,42018
sympy/physics/quantum/grover.py,sha256=Cu2EPTOWpfyxYMVOdGBZez8SBZ2i2QEUmHnTiPPSi-M,10454
sympy/physics/quantum/hilbert.py,sha256=qrja92vF7BUeSyHOLKVX8-XKcPGT7QaQMWrqWXjRNus,19632
sympy/physics/quantum/identitysearch.py,sha256=Gq2jadBsqlxyBOUalriTPrSTkS-SjxIEVJvNPiBIx6k,27583
sympy/physics/quantum/innerproduct.py,sha256=K4tmyWYMlgzkTTXjs82PzEC8VU4jm2J6Qic4YmAM7SQ,4279
sympy/physics/quantum/matrixcache.py,sha256=S6fPkkYmfX8ELBOc9EST-8XnQ1gtpSOBfd2KwLGKdYo,3587
sympy/physics/quantum/matrixutils.py,sha256=D5ipMBRCh2NsxIy4F6ZLQAF4Y84-2rKKC-czCVZ22Ds,8213
sympy/physics/quantum/operator.py,sha256=7dsr1WUJTbdBVnR_tuhEFv2fZdP89HcoZlsq9h2wM6Y,19171
sympy/physics/quantum/operatorordering.py,sha256=smjToA0lj6he22d9R61EL2FSNXFz9oTIF8x5UOd4RNs,11597
sympy/physics/quantum/operatorset.py,sha256=W8rYUrh167nkZcoXCTFscZ1ZvBT6WXkMfmKzRks3edE,9598
sympy/physics/quantum/pauli.py,sha256=ELCl1TbxLnNnxBzCgvTECEDY4OEHkl9bTxoOpIi_GuU,17225
sympy/physics/quantum/piab.py,sha256=Zjb2cRGniVDV6e35gjP4uEpI4w0C7YGQIEXReaq_z-E,1912
sympy/physics/quantum/qapply.py,sha256=XQE-9BFhnuYkMKMwsOlnIYd3mqE9KInPQ1rOZ3f7-18,7142
sympy/physics/quantum/qasm.py,sha256=u2p5TAGdanx3FflxSPPgby4xRwYF1WYvQPTilUXs0do,6244
sympy/physics/quantum/qexpr.py,sha256=uZT82t2bpKdGBWZ7-GtH75YrhV-asIsZ2fCOypeVh4I,14004
sympy/physics/quantum/qft.py,sha256=Iy6yd41lENuCeU5jLXY7O3E_Sc3SAHCN3X5bE0sQiiU,6352
sympy/physics/quantum/qubit.py,sha256=OyVzGFycgwyn8ZvsCNYsuDmG801JurfKwlKxVDHIBCo,26007
sympy/physics/quantum/represent.py,sha256=b_mEm3q-gZbIV5x5Vl6pzfyJytqlp_a98xpfse2AfgI,18707
sympy/physics/quantum/sho1d.py,sha256=WGK6C-M1DSGdswrhGZDIwq-i8V756ckNoOHd1mzJYk4,20823
sympy/physics/quantum/shor.py,sha256=nHT2m4msS5gyQLYPIo2X6XcF7y0pTRZYJUYxZG0YCUk,5504
sympy/physics/quantum/spin.py,sha256=3h9uGC5vJcnu3qRzXnZr-nUNyHkC4AvIOB-rBmbliJ4,72948
sympy/physics/quantum/state.py,sha256=fo9Jr0zh7KlX-GWD8U5_a99uoLCHCKJJBc0_MKAU9qg,30860
sympy/physics/quantum/tensorproduct.py,sha256=w9aj5ecPFRmSCWVjmYaXwD4bpQxUN-wyd3QT3KU4vZo,14659
sympy/physics/quantum/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/quantum/tests/test_anticommutator.py,sha256=ckWHKwQFiAMWcDaYSa_26vi_GIsvs32_0O62I5lGsr8,1304
sympy/physics/quantum/tests/test_boson.py,sha256=BZjdrZ-F1QhyhDqfK4Zc1VEFBJi1PeiPjMpfBcHekfo,1676
sympy/physics/quantum/tests/test_cartesian.py,sha256=b8eBLwmL8ize-a30TMDkoWuDym02PvBjr7ayfLwaR_I,4112
sympy/physics/quantum/tests/test_cg.py,sha256=pw14QQ6XBTkK35021E_nDqcvXdOi4bLiPlkddyE865s,8878
sympy/physics/quantum/tests/test_circuitplot.py,sha256=c3v9wUzLHUH-eBVGj6_broVhHkioNwpaaApTDAJEflU,2096
sympy/physics/quantum/tests/test_circuitutils.py,sha256=GrJAWRQVH_l8EIHrj1ve2jtxske72IriQ3lo94fqrVQ,13187
sympy/physics/quantum/tests/test_commutator.py,sha256=keBstGDpNITFRr06uVFrka_Lje56g6oFoJQEpZXmnYw,2727
sympy/physics/quantum/tests/test_constants.py,sha256=KBmYPIF49Sq34lbzbFCZRYWSyIdhnR3AK3q-VbU6grU,338
sympy/physics/quantum/tests/test_dagger.py,sha256=PR19goU60RXL3aU3hU2CJ3VyrlGeP6x_531nI9mqvm8,2009
sympy/physics/quantum/tests/test_density.py,sha256=G0KYoVmDGXLTO98kGmZBA5NFQtsRCfUEWC6LexEVDxg,9714
sympy/physics/quantum/tests/test_fermion.py,sha256=bFaOWjPHv5HNR10Jvk4i9muJ3MQIyznPWZMtDCtKrZM,1135
sympy/physics/quantum/tests/test_gate.py,sha256=7oBX1HoWnrYtHjABRoqv_wQDB9B829E99fdcJzaqawM,12496
sympy/physics/quantum/tests/test_grover.py,sha256=uze62AG6H4x2MYJJA-EY3NtkqwvrDIQ2kONuvIRQiZ4,3640
sympy/physics/quantum/tests/test_hilbert.py,sha256=IGP6rc2-b3we9dRDbpRniFAhQwp_TYtMfFzxusAprx0,2643
sympy/physics/quantum/tests/test_identitysearch.py,sha256=3YGrXCsFLhLtN5MRyT5ZF8ELrSdkvDKTv6xKM4i2ims,17745
sympy/physics/quantum/tests/test_innerproduct.py,sha256=37tT8p6MhHjAYeoay1Zyv7gCs-DeZQi4VdwUH2IffDE,1483
sympy/physics/quantum/tests/test_matrixutils.py,sha256=3wmKKRhfRuwdQWitWE2mJEHr-TUKn6ixNb_wPWs8wRw,4116
sympy/physics/quantum/tests/test_operator.py,sha256=eNnAigZpR1pS29vD8hbleOUmCU0L6aEDElWWQrC9w8k,7093
sympy/physics/quantum/tests/test_operatorordering.py,sha256=CNMvvTNGNSIXPGLaYjxAOFKk-2Tn4yp3L9w-hc1IMnE,1402
sympy/physics/quantum/tests/test_operatorset.py,sha256=DNfBeYBa_58kSG7PM5Ilo6xnzek8lSiAGX01uMFRYqI,2628
sympy/physics/quantum/tests/test_pauli.py,sha256=Bhsx_gj5cpYv4BhVJRQohxlKk_rcp4jHtSRlTP-m_xs,4940
sympy/physics/quantum/tests/test_piab.py,sha256=8ndnzyIsjF4AOu_9k6Yqap_1XUDTbiGnv7onJdrZBWA,1086
sympy/physics/quantum/tests/test_printing.py,sha256=1J-DBMJFBMcE0-8CafPQKRMI2nVntpNdszK96yX9mYk,30283
sympy/physics/quantum/tests/test_qapply.py,sha256=fWXySMhXtnnj_Gf90gLz8YpBBBFhQ7EwWOAg9mqzuU8,4592
sympy/physics/quantum/tests/test_qasm.py,sha256=ZvMjiheWBceSmIM9LHOL5fiFUl6HsUo8puqdzywrhkc,2976
sympy/physics/quantum/tests/test_qexpr.py,sha256=emcGEqQeCv-kVJxyfX66TZxahJ8pYznFLE1fyyzeZGc,1517
sympy/physics/quantum/tests/test_qft.py,sha256=CQWIKZFSpkUe5X7AF27EqVwZ4l0Zqycl3bdYgVZj3Hs,1861
sympy/physics/quantum/tests/test_qubit.py,sha256=OJxZyIzF2f6GL_5y2GRRw7fidDFFiYsQw3jeamBLsL8,8953
sympy/physics/quantum/tests/test_represent.py,sha256=rEc_cirIJvoU1xANuOTkMjJHdr6DluP4J9sWD2D8Xpc,5166
sympy/physics/quantum/tests/test_sho1d.py,sha256=nc75ZE5XXtrc88OcfB5mAGh01Wpf3d4Rbsu8vLJPTC8,4684
sympy/physics/quantum/tests/test_shor.py,sha256=3a3GCg6V5_mlJ2bltoXinGMGvlSxpq7GluapD_3SZaQ,666
sympy/physics/quantum/tests/test_spin.py,sha256=LOIPNGWalfPLL7DNAaiLCp4J_G1mZpUYmTCNx3kjqgw,344807
sympy/physics/quantum/tests/test_state.py,sha256=UjfOdwRzNXHK0AMhEaI431eMNjVUK7glqiGxOXJEC50,6741
sympy/physics/quantum/tests/test_tensorproduct.py,sha256=FoNM0NU4eju3lh2PhWjXEgcKuH5b62SizHAOBVyv3Xs,4312
sympy/physics/quantum/tests/test_trace.py,sha256=dbpTXcJArWRR_Hh5JTuy2GJIfgjVo6zS20o5mdVEGH4,3057
sympy/physics/quantum/trace.py,sha256=2ZqN9IEsz3LKHTLV8ZDwTK0sM5PfwL0p2sYet0N7Gis,6397
sympy/physics/secondquant.py,sha256=uuRtO2KVzOawq2xEKDG7Lm4I9q-Zei8RPxZH0jddl4c,90400
sympy/physics/sho.py,sha256=83QmVLG_mbClAe3IUcnJYieWsI-7RWA7xNooJmOjyhQ,2508
sympy/physics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/tests/test_clebsch_gordan.py,sha256=isS2YnInIINXzk8by3pYiL3wVj56FopQv8dKwmgq6Uc,9539
sympy/physics/tests/test_hydrogen.py,sha256=kohRIR6JojE_GWYnlzLsMMgdhoKd8whazs0mq7cCTQc,4987
sympy/physics/tests/test_paulialgebra.py,sha256=tyshEMsLNPR4iYzoAbPGZRZ-e_8t7GDP_xyjRyhepeQ,1477
sympy/physics/tests/test_physics_matrices.py,sha256=Dha8iQRhzxLcl7TKSA6QP0pnEcBoqtj_Ob6tx01SMwI,2948
sympy/physics/tests/test_pring.py,sha256=XScQQO9RhRrlqSII_ZyyOUpE-zs-7wphSFCZq2OuFnE,1261
sympy/physics/tests/test_qho_1d.py,sha256=LD9WU-Y5lW7bVM7MyCkSGW9MU2FZhVjMB5Zk848_q1M,1775
sympy/physics/tests/test_secondquant.py,sha256=I3TyKE74EccYcu2tojc3DUHyt1n5D8z7RtlH9YJh0_o,48476
sympy/physics/tests/test_sho.py,sha256=aIs1f3eo6hb4ErRU8xrr_h_yhTmRx-fQgv9n27SfsLM,693
sympy/physics/units/__init__.py,sha256=qQJ0LNhuAeYsZ9s2Nl3MCmydqehEFMZHZnvv5DbZ2eg,12210
sympy/physics/units/definitions/__init__.py,sha256=_aQ0aM1oBjTFIijDLRR3iHFz3e0LJG5xFyWMfiYNQl8,7356
sympy/physics/units/definitions/dimension_definitions.py,sha256=5r_WDnyWFX0T8bTjDA6pnr5PqRKv5XGTm0LuJrZ6ffM,1745
sympy/physics/units/definitions/unit_definitions.py,sha256=H6Yk_31fnOWMQqFt1CdsIy7VuBroBzQkVxTW-Wk-VlM,14146
sympy/physics/units/dimensions.py,sha256=D9bqnGwT4j-x4tnHn4QtfMzdNoW-hoa2MiKQWWr-ELo,20282
sympy/physics/units/prefixes.py,sha256=-H3NiC9H7ToLGWBBjSgpj4QdPa-oRt6tZU4wI1mkZCI,6222
sympy/physics/units/quantities.py,sha256=-zGlm4SEUQuNAEKGg-kkBp6FYeE8DDOcFWrN6MoJsi8,9036
sympy/physics/units/systems/__init__.py,sha256=jJuvdc15c83yl11IuvhyjijwOZ9m1JGgZOgKwKv2e2o,244
sympy/physics/units/systems/cgs.py,sha256=LrFicjQgiEP2XQVmIPwT6AhWFBDVJ7mpfP4ki9AfhUA,3649
sympy/physics/units/systems/length_weight_time.py,sha256=dhmMcT1_9gvlL--GAFdBihk7YPrZSgAZDwSYmNxus3c,6922
sympy/physics/units/systems/mks.py,sha256=Z3eX9yWK9BdvEosCROK2qRKtKFYOjtQ50Jk6vFT7AQY,1546
sympy/physics/units/systems/mksa.py,sha256=dzQze0eNqU5h_CYFmmy-JRv5pcJ6TNWD_vVNy8E5mVo,1924
sympy/physics/units/systems/natural.py,sha256=43Odvmtxdpbz8UcW_xoRE9ArJVVdF7dgdAN2ByDAXx4,909
sympy/physics/units/systems/si.py,sha256=2ziV6PwX8QgcrAcaNUL14q5yZHGYQwjK2Coz7gTMpEQ,14283
sympy/physics/units/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/units/tests/test_dimensions.py,sha256=lzkgGfEXMHxB8Izv7nRTN2uOEPh65LXPYaG8Kr5H05o,6122
sympy/physics/units/tests/test_dimensionsystem.py,sha256=s2_2RAJwOaPOTvyIiAO9SYap374ytZqWbatWkLCnbSU,2717
sympy/physics/units/tests/test_prefixes.py,sha256=wbb-oTl1v5H0sbnp9hiqIBoBiGp2Y1cQ0oBtI6p3_jw,2208
sympy/physics/units/tests/test_quantities.py,sha256=XXut9NoodeMNbWmQgbxrfHdbrbs4g4JX9cOwTb7aH6c,19641
sympy/physics/units/tests/test_unit_system_cgs_gauss.py,sha256=AHOPdXYz1qAn91U5giByBCCwyz16KBlKD4ufqEMNbmM,2703
sympy/physics/units/tests/test_unitsystem.py,sha256=1Xh78_8hbv-yP4ICWI_dUrOnk3cimlvP_VhO-EXOa7Q,3254
sympy/physics/units/tests/test_util.py,sha256=f2pOxVLArai5EwRAriPh9rQdxIyhFpZ4v7WEB0CI-SI,8465
sympy/physics/units/unitsystem.py,sha256=LHZfvlEBwyCWROO5qcyPieHHpO_fBlWx3F1hOApNA9E,7512
sympy/physics/units/util.py,sha256=zpubdYvLysF4FGPmaNPE4W_7zKtVBLR-gGm44ZoKnM4,9610
sympy/physics/vector/__init__.py,sha256=jZmrNB6ZfY7NOP8nx8GWcfI2Ixb2mv7lXuGHn63kyOw,985
sympy/physics/vector/dyadic.py,sha256=VUujyX5JTJH5pkZQu08LvfWRknlFbexJGitPrjilXbU,19545
sympy/physics/vector/fieldfunctions.py,sha256=M7mc0pa41CZHQ6Vccm1-4VR-NmtIqR7jmpFcUGgFeNU,8600
sympy/physics/vector/frame.py,sha256=DW8sjJX-DiFDLY-x9YJK1YWRB975_FXCnEq6lEkoZlA,53456
sympy/physics/vector/functions.py,sha256=Fp3Fx0donNUPj9rkZ03xFC8HhUys4UvogK69ah2Sd3o,24583
sympy/physics/vector/point.py,sha256=9hUKwsM_5npy9FuDSHe9eiOLQLfmZZE49rVxwEhPT2U,20446
sympy/physics/vector/printing.py,sha256=iQmyZQib-9Oa7_suxwHplJ9HW198LPGmptDldwqRl20,11792
sympy/physics/vector/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/vector/tests/test_dyadic.py,sha256=09VKP_uSaiJny5LxNlkSMwU_LdQhZ6yGqoD1GG4dc2U,4292
sympy/physics/vector/tests/test_fieldfunctions.py,sha256=FUjh18QzB6dXSau9iHutb36o28faSa7T9sB0icpja-M,5825
sympy/physics/vector/tests/test_frame.py,sha256=oV9zhB77m8V7_XY1gEneaVv_6p6FOO6C00PLV1lOeZg,22825
sympy/physics/vector/tests/test_functions.py,sha256=5gR01x9HlqM_DViSlu7Yf1m5NQWI2oqBe1a3dRkBcIc,20763
sympy/physics/vector/tests/test_output.py,sha256=TFqso2YUb5zw4oX6H206Wu0XTwJZFKPY92gd68ktMN4,2631
sympy/physics/vector/tests/test_point.py,sha256=B6Yk7K-ouyN-VBXycDJV4sOYrPyFf8a_Q-Ytx7vq1mo,12257
sympy/physics/vector/tests/test_printing.py,sha256=kptiX3xy_xPSyg8f4xZ2jJnorynPvfTenOBtntsYXaY,10433
sympy/physics/vector/tests/test_vector.py,sha256=Jm6DeizQxKY-CD7722--Ko073bcN4jJJ-geRoNkofs4,9458
sympy/physics/vector/vector.py,sha256=S-P6oAhneR8znoXuEMdo-sD0S25parXyakQ684gVHE4,27540
sympy/physics/wigner.py,sha256=n6gu_Q3vzSOtYpn6Gum7nwELaocBiLzzAzwQt94asLE,31950
sympy/plotting/__init__.py,sha256=hAdOjai8-laj79rLJ2HZbiW1okXlz0p1ck-CoeNU6m8,526
sympy/plotting/experimental_lambdify.py,sha256=wIvB02vdrI-nEJX3TqInsf0v8705JI5lcVgMJsJbtO0,22879
sympy/plotting/intervalmath/__init__.py,sha256=fQV7sLZ9NHpZO5XGl2ZfqX56x-mdq-sYhtWEKLngHlU,479
sympy/plotting/intervalmath/interval_arithmetic.py,sha256=OibkI5I0i6_NpFd1HEl48d_R4PRWofUoOS4HYQBkVOc,15530
sympy/plotting/intervalmath/interval_membership.py,sha256=1VpO1T7UjvPxcMySC5GhZl8-VM_DxIirSWC3ZGmxIAY,2385
sympy/plotting/intervalmath/lib_interval.py,sha256=WY1qRtyub4MDJaZizw6cXQI5NMEIXBO9UEWPEI80aW8,14809
sympy/plotting/intervalmath/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/plotting/intervalmath/tests/test_interval_functions.py,sha256=gdIo5z54tIbG8hDaGd3I8rBDP67oetMZWWdM-uvt1ec,9862
sympy/plotting/intervalmath/tests/test_interval_membership.py,sha256=D1KjcrLxAwOmDEUqA-8TCqkFWGtmeerR9KwmzS7tyjk,4216
sympy/plotting/intervalmath/tests/test_intervalmath.py,sha256=ndBMczrs6xYMN5RGnyCL9yq7pNUxrXHTSU1mdUsp5tU,9034
sympy/plotting/plot.py,sha256=krf373hfg5WiJ3JIiuRgJ8DzBKAUjKvRrGmeB9LPFLo,92166
sympy/plotting/plot_implicit.py,sha256=ArM6ThVgp4AmdGImYNWvVkTfl0-Nrf31ykfNPIfY5I4,15695
sympy/plotting/pygletplot/__init__.py,sha256=DM7GURQbdSfcddHz23MxOShatBFc26tP_sd3G8pGCQE,3732
sympy/plotting/pygletplot/color_scheme.py,sha256=IleZrsHXtdHXOAEz1RQjeZKuiYif4OK0vYuE77mqOqc,12534
sympy/plotting/pygletplot/managed_window.py,sha256=x5vfqztEq-zNr6cbidxlTJlbA-nMMa7JXuPACBKdX0c,3064
sympy/plotting/pygletplot/plot.py,sha256=7KZLsze4f-riQybqVF_USEohexRTS7n6RlCQP7haKis,13356
sympy/plotting/pygletplot/plot_axes.py,sha256=Q9YN8W0Hd1PeflHLvOvSZ-hxeLU4Kq3nUFLYDC0x0E8,8655
sympy/plotting/pygletplot/plot_camera.py,sha256=yfkGg7TF3yPhhRUDhvPMT1uJgSboTwgAOtKOJdP7d8E,4001
sympy/plotting/pygletplot/plot_controller.py,sha256=MroJJSPCbBDT8gGs_GdqpV_KHsllMNJpxx0MU3vKJV8,6941
sympy/plotting/pygletplot/plot_curve.py,sha256=UcQFGuNaVbjNQxfPY_olD5BuQI85aXKu7a89xGoc4RQ,2842
sympy/plotting/pygletplot/plot_interval.py,sha256=doqr2wxnrED4MJDlkxQ07GFvaagX36HUb77ly_vIuKQ,5431
sympy/plotting/pygletplot/plot_mode.py,sha256=Djq-ewVms_JoSriDpolDhhtttBJQdJO8BD4E0nyOWcQ,14156
sympy/plotting/pygletplot/plot_mode_base.py,sha256=Ec5GJrouqk9I9CxGLggL8QX-u1XsWPp7dcTREQZWwV0,11497
sympy/plotting/pygletplot/plot_modes.py,sha256=gKzJShz6OXa6EHKar8SuHWrELVznxg_s2d5IBQkkeYE,5352
sympy/plotting/pygletplot/plot_object.py,sha256=qGtzcKup4It1CqZ2jxA7FnorCua4S9I-B_7I3SHBjcQ,330
sympy/plotting/pygletplot/plot_rotation.py,sha256=K8MyudYRS2F-ku5blzkWg3q3goMDPUsXqzmHLDU2Uqc,1447
sympy/plotting/pygletplot/plot_surface.py,sha256=vc2fIjVUJHywFQAyUJicZCF5yTCA760IsxA2n9yKm6w,3811
sympy/plotting/pygletplot/plot_window.py,sha256=5boC2Fkmk46-gWGqWzdTkPmTMNHHOpA0CnB9q946Hwc,4643
sympy/plotting/pygletplot/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/plotting/pygletplot/tests/test_plotting.py,sha256=NisjR-yuBRJfQvjcb20skTR3yid2U3MhKHW6sy8RE10,2720
sympy/plotting/pygletplot/util.py,sha256=11jSIbJ0_-qr-Z81JVFXm54w6CjHsDWWfgBQQAupEEE,4629
sympy/plotting/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/plotting/tests/test_experimental_lambdify.py,sha256=EYshdXA5tAGWolaDX-nHAolp7xIJN4Oqb1Uc1C1IhJI,3127
sympy/plotting/tests/test_plot.py,sha256=HWledOPr2xKq3XFGr458Lc5c0wgf2e0IFa4j63bfdH0,25204
sympy/plotting/tests/test_plot_implicit.py,sha256=gXXMvVCIlp3HeN12Ej636RnhNEmV3i5WnDA48rjRPOg,5804
sympy/plotting/tests/test_region_and.png,sha256=EV0Lm4HtQPk_6eIWtPY4TPcQk-O7tkpdZIuLmFjGRaA,6864
sympy/plotting/tests/test_region_not.png,sha256=3O_9_nPW149FMULEcT5RqI2-k2H3nHELbfJADt2cO8k,7939
sympy/plotting/tests/test_region_or.png,sha256=5Bug09vyog-Cu3mky7pbtFjew5bMvbpe0ZXWsgDKfy4,8809
sympy/plotting/tests/test_region_xor.png,sha256=kucVWBA9A98OpcR4did5aLXUyoq4z0O4C3PM6dliBSw,10002
sympy/plotting/tests/test_textplot.py,sha256=VurTGeMjUfBLpLdoMqzJK9gbcShNb7f1OrAcRNyrtag,12761
sympy/plotting/textplot.py,sha256=4t7W6_e6J1FFxAA_-9JBs1_0jGnA3yyLD7p7kDm-Ji0,4929
sympy/polys/__init__.py,sha256=xTBxcXuZl8PM4WTvNaAK24EAomlMovsLQyH34RkUryI,5276
sympy/polys/agca/__init__.py,sha256=fahpWoG_0LgoqOXBnDBJS16Jj1fE1_VKG7edM3qZ2HE,130
sympy/polys/agca/extensions.py,sha256=v3VmKWXQeyPuwNGyizfR6ZFb4GkRZ97xREHawuLWqpg,9168
sympy/polys/agca/homomorphisms.py,sha256=gaMNV96pKUuYHZ8Bd7QOs27J1IbbJgkEjyWcTLe8GFI,21937
sympy/polys/agca/ideals.py,sha256=8rh6iQt26zF0qKzHlfqGXKZzKuGY6Y5t9hBNVGG9v5M,10891
sympy/polys/agca/modules.py,sha256=UZBnmvsQTHRkSVGdst6nksp9a07ZYD65eArjL91n3-Q,46946
sympy/polys/agca/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/agca/tests/test_extensions.py,sha256=i3IHQNXQByFMCvjjyd_hwwJSCiUj0z1rRwS9WFK2AFc,6455
sympy/polys/agca/tests/test_homomorphisms.py,sha256=m0hFmcTzvZ8sZbbnWeENwzKyufpE9zWwZR-WCI4kdpU,4224
sympy/polys/agca/tests/test_ideals.py,sha256=w76qXO-_HN6LQbV7l3h7gJZsM-DZ2io2X-kPWiHYRNw,3788
sympy/polys/agca/tests/test_modules.py,sha256=HdfmcxdEVucEbtfmzVq8i_1wGojT5b5DE5VIfbTMx3k,13552
sympy/polys/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/benchmarks/bench_galoispolys.py,sha256=8RtN9ZQga2oxscVPPkMGB29Dz8UbskMS2szYtqZ69u0,1502
sympy/polys/benchmarks/bench_groebnertools.py,sha256=YqGDCzewRszCye_GnneDXMRNB38ORSpVu_Jn0ELIySo,803
sympy/polys/benchmarks/bench_solvers.py,sha256=gLrZguh6pE0E4_vM2GeOS5bHnrcSUQXqD0Qz9tItfmo,446778
sympy/polys/compatibility.py,sha256=OkpZiIrD2u_1YB7dE2NJmhpt1UZoBNoX2JBY3q1Uixo,57743
sympy/polys/constructor.py,sha256=4hqADMZrcLOsnzVebcZxnn3LJ7HdPIHReq0Qalf91EY,11371
sympy/polys/densearith.py,sha256=6lkYHNpTPp2qq8qKBNiK9V-xNqLg0MYcoi_ksKaNBcg,34108
sympy/polys/densebasic.py,sha256=H9DimmE5zLuEpzyYvTWBViBJTe5bbLj-1RefaAy2XXk,35922
sympy/polys/densetools.py,sha256=q75QA1e0rH9TpVbTGIwRgeisNFt-7HiRcdPUEdHYN2E,25902
sympy/polys/dispersion.py,sha256=s6GIYnGA6U9jhGP7YXQQS8G3byG4-kPbr55BR6p-iz4,5740
sympy/polys/distributedmodules.py,sha256=O0xuZ_ihFaW9yUPNSCOI1z7JOUQvx9uhYYwI3EnG2wA,21826
sympy/polys/domainmatrix.py,sha256=FmNqklNFQR1WrQYtP2r7jypw2IQadNKGP14EaUaxUqI,310
sympy/polys/domains/__init__.py,sha256=T6qPNkU1EJ6D5BnvyJSXJv4zeJ5MUT5RLsovMkkXS9E,1872
sympy/polys/domains/algebraicfield.py,sha256=IlC1pZeLpUn-702OrlU7Rm8wkR2LthTFmA2G9JtKZ9M,20327
sympy/polys/domains/characteristiczero.py,sha256=vHYRUXPrfJzDF8wrd1KSFqG8WzwfITP_eweA-SHPVYA,382
sympy/polys/domains/complexfield.py,sha256=2GjeNMebTXxLHDkKYqbrP-hZqBXHoc_Uv7kk7xIyPcw,4620
sympy/polys/domains/compositedomain.py,sha256=wgw_yKwC5gHYWxRHEbVDeHOKQycFkZH0ZxhVES0AR04,1042
sympy/polys/domains/domain.py,sha256=r8NCFX2chu3IBNdnJeaCgdAQAf2AP0LrKNX_m6u0508,38078
sympy/polys/domains/domainelement.py,sha256=IrG-Mzv_VlCAmE-hmJVH_d77TrsfyaGGfJVmU8FFvlY,860
sympy/polys/domains/expressiondomain.py,sha256=NjZWZwDewaj4Z3agRjgPyit6Clojz3LeTUOe32kAU_I,6907
sympy/polys/domains/expressionrawdomain.py,sha256=cXarD2jXi97FGNiqNiDqQlX0g764EW2M1PEbrveImnY,1448
sympy/polys/domains/field.py,sha256=tyOjEqABaOXXkaBEL0qLqyG4g5Ktnd782B_6xTCfia8,2591
sympy/polys/domains/finitefield.py,sha256=yFU8-FvoDxGQ9Yo-mKlOqnB-91ctpz_TT0zLRmx-iQI,6025
sympy/polys/domains/fractionfield.py,sha256=pKR3dfOOXqBIwf3jvRnaqgA-t1YYWdubCuz3yNnxepU,5945
sympy/polys/domains/gaussiandomains.py,sha256=7Ho6t4IcQ4SrUedSsJR-B4ebDXI38Bp86bZqnRFdT3s,18005
sympy/polys/domains/gmpyfinitefield.py,sha256=C_Nd9GubSMBJmIe5vs_C2IuBT8YGFL4xgK4oixNCOrk,444
sympy/polys/domains/gmpyintegerring.py,sha256=U6Ph1_5Ez5bXN4JcF2Tsq1FUDEwYsGx0nUT-gZDvO5U,3017
sympy/polys/domains/gmpyrationalfield.py,sha256=dZjrfcWaUA-BHUtutzLOWPlOSNLYzBqSFeukER6L_bA,3178
sympy/polys/domains/groundtypes.py,sha256=bHPHdmpFRBWe86TNMSsE6m5grvE0bQWLWnRGRBBxMpQ,1615
sympy/polys/domains/integerring.py,sha256=TZQpq6wCc4uZVYlYi6NySaEYkwObUlRVTg7Of5JIbjY,6084
sympy/polys/domains/modularinteger.py,sha256=JVAvhlGUeF2wz8OXHNYZGCLkfb9AXlzDVCssvKFKhRM,5108
sympy/polys/domains/mpelements.py,sha256=MxymxwlGBA3Px2FFyzISEtAnkVoxeq-bJM1fk2jkEts,4616
sympy/polys/domains/old_fractionfield.py,sha256=6qVb4Zzfq8ArxDyghXwW5Vvw4SattdIt0HUx4WcnD8U,6178
sympy/polys/domains/old_polynomialring.py,sha256=_Rengtf5vN3w9GJAsDFcN3yKbWjYqkTbsPdxbtbplnE,14914
sympy/polys/domains/polynomialring.py,sha256=kStXSAtq1b5Tk3vrEze7_E8UMn8bF91Goh7hVzhtax0,6153
sympy/polys/domains/pythonfinitefield.py,sha256=RYwDRg1zVLLGtJvVXvWhwUZjC91g8pXTwAjuQoWezks,460
sympy/polys/domains/pythonintegerring.py,sha256=qUBqWBtP_faY-m2tJA07JQyCTdh27tXVBDD7vsKNUn4,2929
sympy/polys/domains/pythonrational.py,sha256=M3VUGODh3MLElePjYtjt9b02ReMThw-XXpuQTkohgNs,548
sympy/polys/domains/pythonrationalfield.py,sha256=x8BPkGKj0WPuwJzN2py5l9aAjHaY4djv65c4tzUTr3Y,2295
sympy/polys/domains/quotientring.py,sha256=LBUIIpN3y3QPS6pFYWwqpca5ShoWDyaZbZ6PwDm_SmA,5866
sympy/polys/domains/rationalfield.py,sha256=-4rLYoh3IhsURx09OtLR3A29NLDi_RO-QzWO3RGoy8Q,4869
sympy/polys/domains/realfield.py,sha256=Wt5_y7HTDe8u1qGalhNhTT7Rw3CQiVkmgduQ7jcpD9c,3782
sympy/polys/domains/ring.py,sha256=p66U2X58acSHLHxOTU6aJZ0Umdcu1qiGIUDtV8iJCD0,3236
sympy/polys/domains/simpledomain.py,sha256=_K-Zz8Opf505r3eHSrbPAlnGiGSjY_O4Cwa4OTeOSoY,369
sympy/polys/domains/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/domains/tests/test_domains.py,sha256=1PsckHIBXMQFm-sgSDMjiUor2c-000iEZhqqPV9pfR4,43846
sympy/polys/domains/tests/test_polynomialring.py,sha256=gW82jcxL2J5nKrA4iDCuk88K1bqpfAG7z32Y9191mKU,3312
sympy/polys/domains/tests/test_quotientring.py,sha256=BYoq1CqI76RDSm0xQdp1v7Dv1n5sdcmes-b_y_AfW-0,1459
sympy/polys/euclidtools.py,sha256=h8qC0ZsXf-ZKPLIMBaLV2aSCHDuXLQBczKZcU-J2BaE,41221
sympy/polys/factortools.py,sha256=AghhwHVn_wJsEBBo-THmMIKT9zr-gBJlkLTctJrT_eY,38457
sympy/polys/fglmtools.py,sha256=KYZuP4CxAN3KP6If3hM53HKM4S87rNU2HecwbYjWfOE,4302
sympy/polys/fields.py,sha256=fiDsLO8xUmhDE-yG4UHD_4w09pL8CQqO0auzcDR2scI,21235
sympy/polys/galoistools.py,sha256=cuwAArjtyoV4wfaQtX8fs4mz4ZXLuc6yKvHObyXgnw8,52133
sympy/polys/groebnertools.py,sha256=PnNfZ9VwQWVk9PvqY18kO66Ki73JoV-AMSc_MwyMv1k,23341
sympy/polys/heuristicgcd.py,sha256=rD3intgKCtAAMH3sqlgqbJL1XSq9QjfeG_MYzwCOek0,3732
sympy/polys/matrices/__init__.py,sha256=ZaPJMi8l22d3F3rudS4NqzSt0xwxbs3uwnQwlhhR91o,397
sympy/polys/matrices/ddm.py,sha256=qQGM9isjxgZjEfCAghnWsidbE6TWctu1VqgLazoHB0I,13342
sympy/polys/matrices/dense.py,sha256=vGCiiBD4vxWPmlT1MxZ6VqrBH-WWKUCpkZK15EX8MiI,8263
sympy/polys/matrices/domainmatrix.py,sha256=L_jWgFZPAlXSiSBa3GDD1IHNKgFZyVgDAwU4rVIxG_E,44674
sympy/polys/matrices/domainscalar.py,sha256=zosOQfLeKsMpAv1sm-JHPneGmMTeELvAloNxKMkZ8Uo,3643
sympy/polys/matrices/eigen.py,sha256=pvICWI8_r_usa0EFqlbz7I8ASzKMK2j2gn-65CmTSPU,2983
sympy/polys/matrices/exceptions.py,sha256=fTZo5RfUvdP_PhSfUpW1BnTmFO6lTt9jABGUNwglta8,1258
sympy/polys/matrices/linsolve.py,sha256=4GTovjVfdBbdHsukI_9pa7Qz6aJrJTN0-LB7grDWOm0,6745
sympy/polys/matrices/normalforms.py,sha256=YAUCRHG-blk1rKm3AnrkgielPAihCiGjtdZXPyOp4aI,13061
sympy/polys/matrices/sdm.py,sha256=GO_7jemIw3UXL0m2JTRtZmnXrn-q4sohjVorzCh-9p8,35229
sympy/polys/matrices/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/matrices/tests/test_ddm.py,sha256=3tFhjkA1alE827Qiw9mAPlWkSgV3Sesrqeh-NxHXsA4,16640
sympy/polys/matrices/tests/test_dense.py,sha256=Ig_SJ86pogur9AEfcetO_L01fy1WFhe-E9g9ngVTlxs,9483
sympy/polys/matrices/tests/test_domainmatrix.py,sha256=IjRa6uCfAu1hm6XrN1fUUaAA2GeVxi5IgVaf4vZc4Lk,32371
sympy/polys/matrices/tests/test_domainscalar.py,sha256=9HQL95XlxyXHNDf_UBN9t1da_9syRNZGOb7IKkmjn-U,3624
sympy/polys/matrices/tests/test_eigen.py,sha256=T1lYZeW-0NwDxDOG6ZJLr-OICfxY2wa0fVHV2V6EXSk,3200
sympy/polys/matrices/tests/test_linsolve.py,sha256=zHuoF5jwZ6Y3-1lMjS899Y1EU9ztWACxbq-SO7IkbRM,3258
sympy/polys/matrices/tests/test_normalforms.py,sha256=_4Cm3EJxHh3TEwF278uB7WQZweFWFsx3j0zc2AZFgDI,3036
sympy/polys/matrices/tests/test_sdm.py,sha256=H0oNZkNmwpP8i6UpysnkD7yave0E3YU3Z8dKGobSbOA,14000
sympy/polys/modulargcd.py,sha256=vE57ZJv1iJNKHcRbFJBgG6Jytudweq3wyDB90yxtFCc,58664
sympy/polys/monomials.py,sha256=rn8ws5KihQ3haiDgF7SnxcJnEjt6EckNS7ilCkTimK8,18870
sympy/polys/multivariate_resultants.py,sha256=G9NCKrb5MBoUshiB_QD86w6MwQAxLwOmc-_HFO_ZXdE,15265
sympy/polys/numberfields/__init__.py,sha256=oUurJxeLNwxm0b3MsQ_jAqn-5C0aIbfm6XMazCGBBf4,477
sympy/polys/numberfields/basis.py,sha256=LoUfux6wijVx7hpyOHt_oZPvmdWTMHO0_rTkwhufCXg,8424
sympy/polys/numberfields/exceptions.py,sha256=IN36PiHvWvH5YOtWmU0EHSPiKhGryPezcOawdQmesMo,1668
sympy/polys/numberfields/minpoly.py,sha256=YaqmoNcSuLvop9Q90-MtGVSkYIWIKE-85x4mLkJYIMI,27714
sympy/polys/numberfields/modules.py,sha256=pK69MtEb5BcrSWU9E9jtpVxGhEcR-5XB8_qatpskFVk,69117
sympy/polys/numberfields/primes.py,sha256=9UHrJrIDPhAcNtqrDcqXIm9Z-Ch69W_gKGOBfDKduro,23967
sympy/polys/numberfields/subfield.py,sha256=_s8u4a1y1L4HhoKEpoemSvNrXdW0Mh4YvrUOozq_lvc,16480
sympy/polys/numberfields/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/numberfields/tests/test_basis.py,sha256=MZN9YEL23jacJ_ktloiiaX2tKVEp5M4F2Mu7QHINILY,4299
sympy/polys/numberfields/tests/test_minpoly.py,sha256=IA0WH56vMXbSQpiml78jZes1M1XZSHDRARv5tM4VGTQ,22590
sympy/polys/numberfields/tests/test_modules.py,sha256=GU4166j_hMlB22uWxxIjV_ON8RsyvpaN7Ly3eK8_m8Y,22926
sympy/polys/numberfields/tests/test_numbers.py,sha256=M0vZIBnjPBHV4vFUnPBILaqiR_cgSuU50kFB-v7l1gA,5988
sympy/polys/numberfields/tests/test_primes.py,sha256=WiR7YQH26sqGqAIkKT0iQLEFVcpkx3YL8XeI3g1Afzw,9780
sympy/polys/numberfields/tests/test_subfield.py,sha256=_aCbvukrahv-QyCwNT7EpTYC1u53yUlMhfGqV5GzW3Y,12215
sympy/polys/numberfields/tests/test_utilities.py,sha256=T3YfFouXZNcBG2AfLEQ77Uqy-_TTufGTUsysmzUHNuA,3655
sympy/polys/numberfields/utilities.py,sha256=aQBm_rgKxjHOCTktOYJ-aI5Cpb59IBvWJiyZCowcM-I,13081
sympy/polys/orderings.py,sha256=UDDVD7wLgVhYLDs4CA33b7CJFlnvQz7TWj6lG6Xvng0,8516
sympy/polys/orthopolys.py,sha256=SbXTAPJVpO241Uq-DW0bQuPrAUonicbjATicamXGdnY,10115
sympy/polys/partfrac.py,sha256=KzReYNMyYfgXUM-UFj67eQU7MQk6EsbfhVuf4_Tl_u0,14665
sympy/polys/polyclasses.py,sha256=RtMjMAGOBqFYAxfxrVnNCd5B2y7N3_MRj_0eqV6eY30,54573
sympy/polys/polyconfig.py,sha256=mgfFpp9SU159tA_PM2o04WZyzMoWfOtWZugRcHnP42c,1598
sympy/polys/polyerrors.py,sha256=xByI-fqIHVYsYRm63NmHXlSSRCwSI9vZUoO-1Mf5Wlk,4744
sympy/polys/polyfuncs.py,sha256=UjK2UD5Y9q8IyKmv9ksB-dUsVWspNtOeAqmhaNEQppM,10258
sympy/polys/polymatrix.py,sha256=83_9L66dbzVv0UfbPR3OTKtxZZ6sMaeOifMBPUDBeiM,9749
sympy/polys/polyoptions.py,sha256=4VP3QNIhwzaA9MMR0rILEaniKnM7faHoIRro9iIQQ9Q,21969
sympy/polys/polyquinticconst.py,sha256=EPgPRwLXK6gSPQ1YHxjXMOHiG6nHmliFVqrZDS78L4g,96034
sympy/polys/polyroots.py,sha256=XMXZ87u7r11TjexqIQnitAqAm6e5Hq-_v3FN2pbdPoQ,36376
sympy/polys/polytools.py,sha256=vZtou-StognQriIKSS8mPGnD6gMfLtNBXVk59nLa0yQ,189675
sympy/polys/polyutils.py,sha256=gGwRUZXAFv132f96uONc6Ybfh8xyyP9pAouNY6fX-uQ,16519
sympy/polys/rationaltools.py,sha256=gkLu0YvsSJ2b04AOK7MV_rjp1m6exLkdqClOjrbBboo,2848
sympy/polys/ring_series.py,sha256=qBKirsiZpM5x0ix4V5ntm7inynnahYCfVSgHZRCpccc,57766
sympy/polys/rings.py,sha256=BCgKZWn3TxHN0THOv1fRGcZJ0UKYkU8n2FIHsilMh0E,68958
sympy/polys/rootisolation.py,sha256=LOXbJeluUfPR439ukTeKO76_V6qU4x-fXQG5d_AASdQ,64526
sympy/polys/rootoftools.py,sha256=YI1VfefMI3CEL5-PXNmgpScEes5AUePXChFU-Kh3vek,40969
sympy/polys/solvers.py,sha256=TCWE-rm_ukPWxXnmqRwP_N2c6IMh7VrkHJXW17EB5zM,13502
sympy/polys/specialpolys.py,sha256=EsA5_oMeSkjSwLtHYof_zHPjMAQrKZtfugZkR709xzs,11069
sympy/polys/sqfreetools.py,sha256=2Gdv9t9TNgdbnc-7XrpEhgYJfSvacHUyuE1aOWo9DXU,11464
sympy/polys/subresultants_qq_zz.py,sha256=zgfXwBCjcCgDLQcxIsR9yaRjCWk97kkuCnzArGRxbfs,88260
sympy/polys/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/tests/test_constructor.py,sha256=U1LBjA881oG4A8oMXqZe0sZ42pmH7YpR_VSJjBNZz-w,6378
sympy/polys/tests/test_densearith.py,sha256=1YBmEJTtPRWj4l39HMkFD6ffkU8h3pIs7lz-k_9XGYk,40428
sympy/polys/tests/test_densebasic.py,sha256=vcoTscGRB1bef9UhclHcsKnBJp9baexjQ-enXq1-pKM,21477
sympy/polys/tests/test_densetools.py,sha256=QM1Yt0hOHBnUTvdn14aFRUdfMQE9P2q1Hpzeud-n-ds,24572
sympy/polys/tests/test_dispersion.py,sha256=8JfwjSNy7X74qJODMaVp1GSLprFiRDVt6XrYc_-omgQ,3183
sympy/polys/tests/test_distributedmodules.py,sha256=dXmjhozX5Yzb7DsrtbdFTqAxi9Z1UZNJvGxj-vHM7cM,7639
sympy/polys/tests/test_euclidtools.py,sha256=vEyj48eIjm6-KRQtThNfI4ic_VDNB6l7jMouxJAF9HE,19482
sympy/polys/tests/test_factortools.py,sha256=MXOJfhjrLAu-UCyXg6YRMYAc7nkw6SAfkY66_RKG9Es,24560
sympy/polys/tests/test_fields.py,sha256=vrdg27319R3Zro_idhQVxIeomN9P6mU3jHyX7HZKeMU,10245
sympy/polys/tests/test_galoistools.py,sha256=btKRaqckjvyGOhCvIfwLtRDVG2Qiwo6CTnoPW8h4S9E,28130
sympy/polys/tests/test_groebnertools.py,sha256=ZWHBcCCOVNwDxuJWg1WPo0krTHx1m1wTPi2cOYPsAT4,18584
sympy/polys/tests/test_heuristicgcd.py,sha256=wsAKgOKuLYra14qMS8EUt_Pda_SoBfP90X2-Tv1WG7A,4031
sympy/polys/tests/test_injections.py,sha256=EONGggBUNWaVSwi817CzLBYJgkTehFq8-m-Qdqes984,1286
sympy/polys/tests/test_modulargcd.py,sha256=GE-24EnWOAQVYwgBb5PJzySX6EEJQs-q3HRFBWsXkTE,9042
sympy/polys/tests/test_monomials.py,sha256=bY057IDFyVs864jcJ46ZITLv57xMKNfBVwBC-mnzJLA,10988
sympy/polys/tests/test_multivariate_resultants.py,sha256=DJu8CcZ3xwx8njpjDeSOyhyxeqZYmhfb7dkSCU-ll7Y,9501
sympy/polys/tests/test_orderings.py,sha256=bdsIsqJTFJCVyZNRMAGVDXVk79ldw9rmAGejS_lwKP0,4254
sympy/polys/tests/test_orthopolys.py,sha256=3WDRH_ZwdlKngRNXLIvD0t8FsIYLFXrK9mp7aa108-A,5747
sympy/polys/tests/test_partfrac.py,sha256=78xlrvzvON2047j_DeQ0E8BBZg6Z1koJzksj5rQah9A,7096
sympy/polys/tests/test_polyclasses.py,sha256=uUjLcfKrfW-EBB6N9ofESJgw4_QacKWN1fLa0etn6iY,13321
sympy/polys/tests/test_polyfuncs.py,sha256=FRTHcijiqGKvZ1Q88AJXP2ZocYFgxf92k-gXE9p75ms,4530
sympy/polys/tests/test_polymatrix.py,sha256=pl2VrN_d2XGOVHvvAnaNQzkdFTdQgjt9ePgo41soBRs,7353
sympy/polys/tests/test_polyoptions.py,sha256=z9DUdt8K3lYkm4IyLH1Cv-TKe76HP-EyaRkZVsfWb6U,12416
sympy/polys/tests/test_polyroots.py,sha256=kGtZHZEgVKj1cTbGasWSq63epEhRzeE-N8mvt2KsQes,26479
sympy/polys/tests/test_polytools.py,sha256=x_xK8jkvGh8CGhySSP0codCf7b1Kt4wo0l4ov4RkpBY,124999
sympy/polys/tests/test_polyutils.py,sha256=Qs3QQl0WYmTnkYE2ovTxdLeu6DYnWO_OoUmLwNDZzSw,11547
sympy/polys/tests/test_pythonrational.py,sha256=vYMlOTuYvf-15P0nKTFm-uRrhUc-nCFEkqYFAPLxg08,4143
sympy/polys/tests/test_rationaltools.py,sha256=wkvjzNP1IH-SdubNk5JJ7OWcY-zNF6z3t32kfp9Ncs0,2397
sympy/polys/tests/test_ring_series.py,sha256=BDYMt_Roe-DNWdTWJ9nrCPR97jaSyWwGVqKtsbw1_-I,24246
sympy/polys/tests/test_rings.py,sha256=-nfzsfVSFLmXOwaIjcOV1MLEsReIg4JPVVjJKrMqaTI,43313
sympy/polys/tests/test_rootisolation.py,sha256=x-n-T-Con-8phelNa05BPszkC_UCW1C0yAOwz658I60,32724
sympy/polys/tests/test_rootoftools.py,sha256=TciUBuJjHQ_NQI-k_Cm60W6NdfwpAG7jvb6ONmBEIFQ,21508
sympy/polys/tests/test_solvers.py,sha256=LZwjEQKKpFdCr4hMaU0CoN650BqU-arsACJNOF7lOmk,13655
sympy/polys/tests/test_specialpolys.py,sha256=vBEDCC82ccGvxsETR5xr3yQ70Ho_HUqv1Q970vWf44M,4995
sympy/polys/tests/test_sqfreetools.py,sha256=QJdMLVvQOiPm8ZYr4OESV71d5Ag9QcK1dMUkYv3pY5o,4387
sympy/polys/tests/test_subresultants_qq_zz.py,sha256=ro6-F0vJrR46syl5Q0zuXfXQzEREtlkWAeRV9xJE31Y,13138
sympy/printing/__init__.py,sha256=WwWaFk8Wugov1kKhHuDgx4WE3HZLA6ypqbg4wJQb2EI,2183
sympy/printing/aesaracode.py,sha256=aKgTTGQ3Kajm-pj09DiPMJgXdtmjkBvdk-ZxiMji3YU,17942
sympy/printing/c.py,sha256=KRcWKZcZu6H0s6hJ-fqCt1BSl1v89Zuah4Kvcd9Fbd4,27030
sympy/printing/codeprinter.py,sha256=kd67UC5NYIWiipR3yyoqgRWVgEG6PlIzLCqdpTibwys,35421
sympy/printing/conventions.py,sha256=k6YRWHfvbLHJp1uKgQX-ySiOXSsXH8QJxC9fymYmcSM,2580
sympy/printing/cxx.py,sha256=wEVS6NSwRlt1f5Um12bVaqJtTttJZMqzezqWuyYYA-I,5735
sympy/printing/defaults.py,sha256=YitLfIRfFH8ltNd18Y6YtBgq5H2te0wFKlHuIO4cvo8,135
sympy/printing/dot.py,sha256=W0J798ZxBdlJercffBGnNDTp7J2tMdIYQkE_KIiyi3s,8274
sympy/printing/fortran.py,sha256=Ono-gD5Mte9bl6lx3tqzcG36GdwFJD2f0Irs0NFbdpk,28338
sympy/printing/glsl.py,sha256=3o262ERBw_9r142LZSFXRLRqQzAM14YvaGlVlDidjeI,20487
sympy/printing/gtk.py,sha256=ptnwYxJr5ox3LG4TCDbRIgxsCikaVvEzWBaqIpITUXc,466
sympy/printing/jscode.py,sha256=17Le4yF4On-MuvEBKnRmb5ynq59n9MeoHHUKOafGWhc,12052
sympy/printing/julia.py,sha256=tlDpKvRPziadjlad2_rhuIyETSHAxmXxqJ-lTDb5zrE,23472
sympy/printing/lambdarepr.py,sha256=BCx4eSdG8MQ8ZSUV1lWEd3CzbZ4IiMid-TTxPoV6FHU,8305
sympy/printing/latex.py,sha256=U0sScVDjU0Lh1SFyPeR8GpoOe3_XrX0PVMsfF3RoMWs,121584
sympy/printing/llvmjitcode.py,sha256=wa32lF5254AOPnbV9F5OvQTd1HOk0rfN-HUekcN1HmI,17164
sympy/printing/maple.py,sha256=i90ykAxJx8V4f-apYK02fBhhBWhod_ISRjsjSP6JVTc,10559
sympy/printing/mathematica.py,sha256=JGVuAv3ZYO65U6WM8cjfEaMkKPuXs44JTZ5U7KN6Ik4,12739
sympy/printing/mathml.py,sha256=cpUog6etQ_CD1WyB4PjF2wkPuDAOx1L0EvqQX6_pHQk,75275
sympy/printing/numpy.py,sha256=lrLY5YHT9NJMYhyxSc_iQ9ZBxZPeOlLZIHDspl9MaEc,19095
sympy/printing/octave.py,sha256=1ijyqSP_NdrnOqhoIKtioKKc-DSAkolGmuOMPntxaNE,25540
sympy/printing/precedence.py,sha256=dK6ueqV6OOXg0qY9L-goOgbQarqVRygIYK5FQGTBPR8,5268
sympy/printing/pretty/__init__.py,sha256=pJTe-DO4ctTlnjg1UvqyoeBY50B5znFjcGvivXRhM2U,344
sympy/printing/pretty/pretty.py,sha256=b7-twgeURKW9OP9uVD2Tlc-dU2RPsB53iJSNp43QDCo,105276
sympy/printing/pretty/pretty_symbology.py,sha256=nfBI-cLYLBP9VuZxb7DSWtFIg3vgDphNfV-uBtFDMIE,20208
sympy/printing/pretty/stringpict.py,sha256=0UUER26clPJXYHBqAcuRlXROF3jXjM8yHadmm5ROrk4,19115
sympy/printing/pretty/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/printing/pretty/tests/test_pretty.py,sha256=mgbEDalERe8aIvRzsoSIPRKPGbhV2flchcVGokuaODY,184798
sympy/printing/preview.py,sha256=B-jCAGTvH_vuE2jPW3Xm46mHFuDGfzIuV-miS1rREgw,14107
sympy/printing/printer.py,sha256=C_5cefhJfT3RJywxqel5CQviUYCkVHDGzYvu2mI4zHg,14493
sympy/printing/pycode.py,sha256=RMrqtTLMR_h5ArLs8UIcK4BXFfbfuVOzwbF7wauvnuU,24320
sympy/printing/python.py,sha256=sJcUWJYaWX41EZVkhUmZqpLA2ITcYU65Qd1UKZXMdFo,3367
sympy/printing/rcode.py,sha256=rOf4lU06UPInmLcBGbHxMLxvj7oyjkUxexRTQc_uxlM,14245
sympy/printing/repr.py,sha256=1oNVgl6riWZ8vmrc8q5nT6rbgMOSaoEGirKjqQMGGUo,11643
sympy/printing/rust.py,sha256=q19LX4Bhyzr8YYt9u8WJA0LFDdNMtfHDcEPy0bOJh7M,21313
sympy/printing/str.py,sha256=pO1fgCD_4S-5C8XQJIveDwvpE__GrmkidrQ38tJee6Y,33424
sympy/printing/tableform.py,sha256=-1d1cwmnprJKPXpViTbQxpwy3wT7K8KjPD5HCyjbDGk,11799
sympy/printing/tensorflow.py,sha256=KHdJMHMBOaJkHO8_uBfYRHeBW2VIziv_YYqIV30D-dA,7906
sympy/printing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/printing/tests/test_aesaracode.py,sha256=p14ws5U-UI9OAmAhDU54x8I3MpI_0-XJEjnUOGShC6Q,20963
sympy/printing/tests/test_c.py,sha256=0fDKwZ-F0Kt1h4-eE9J3EdMKTTdVcoHquRsu5LDGgu8,30440
sympy/printing/tests/test_codeprinter.py,sha256=Bdh1RcusYzR7lTQ8s3Sik7zw_INivUcW2AS4dA0OCtg,1410
sympy/printing/tests/test_conventions.py,sha256=yqPpU3F0WcbxImPBBAHd3YEZpkFGfcq_TLK4WN_gtP4,5257
sympy/printing/tests/test_cupy.py,sha256=-hO52M1RJSQe0qSVSl6B1LudZIgaBMme0Nkd6dQGr6g,1858
sympy/printing/tests/test_cxx.py,sha256=900VUfUpS55zfllYGQcpjdC4Wmcg4T8TV94Mr430NZc,2490
sympy/printing/tests/test_dot.py,sha256=TSAtgGIgK_JbY-RMbQgUvnAI87SJqeJOqzcLjAobhKM,4648
sympy/printing/tests/test_fortran.py,sha256=hRVbrTh3VPSU8G1TvNs5V2QfHZAj_fPcCul3HpRXmQY,34743
sympy/printing/tests/test_glsl.py,sha256=cfog9fp_EOFm_piJwqUcSvAIJ78bRwkFjecwr3ocCak,28421
sympy/printing/tests/test_gtk.py,sha256=94gp1xRlPrFiALQGuqHnmh9xKrMxR52RQVkN0MXbUdA,500
sympy/printing/tests/test_jax.py,sha256=TrwXlvy_O_W6yAlUhhgr4JvPBG_v5P7q2u-7WZw4iZc,10535
sympy/printing/tests/test_jscode.py,sha256=ObahZne9lQbBiXyJZLohjQGdHsG2CnWCFOB8KbFOAqQ,11369
sympy/printing/tests/test_julia.py,sha256=U7R9zOckGWy99f5StDFE9lMXkcEmMkGHzYj1UM1xzgc,13875
sympy/printing/tests/test_lambdarepr.py,sha256=YU_lAQpiNHKJpBjZmgXr-unzOwS6Ss-u8sS2D_u-Mq0,6947
sympy/printing/tests/test_latex.py,sha256=EwA6UBBiU8uhppZ4p5mRZwwzpHNGeJKXDXPbLgWwb6g,135220
sympy/printing/tests/test_llvmjit.py,sha256=do178JhJNq6uPuqBW_CdqkTd53LzPk4tP5kWi7Lkfi0,5348
sympy/printing/tests/test_maple.py,sha256=HXaGbKqPWgkucwVy2UVaroHlDD6Zo0pGKyIIZW4ezWE,13136
sympy/printing/tests/test_mathematica.py,sha256=vijg7xfoelywL-ZhNuXFfDjM1FgaW_4liTBx1wzpkWk,10954
sympy/printing/tests/test_mathml.py,sha256=x4IckrMxOlSzt6CxGFpHdN2l6OXl7zrcxIHwn-KxeS8,96209
sympy/printing/tests/test_numpy.py,sha256=8R1bApWTIgQtbful3lH_5tm2qhm6rjRo5ZDzuczYnRc,9916
sympy/printing/tests/test_octave.py,sha256=T2Gg1btqhQ1FZk3fSzTyHImvzDauVAkkds5DfhJdDm4,18690
sympy/printing/tests/test_precedence.py,sha256=CS4L-WbI2ZuWLgbGATtF41--h0iGkfuE6dK5DYYiC5g,2787
sympy/printing/tests/test_preview.py,sha256=dSVxiGqdNR6gbF40V4J2tGhQ-T4RDvSyGypHvYcPDYM,988
sympy/printing/tests/test_pycode.py,sha256=5wSqujnk--RUeJAFnrsKX5yHUsGJykuyqxXopZ5_Lhc,16032
sympy/printing/tests/test_python.py,sha256=wQ6sIM96LURGfh_I6nU2AQQ0esG5VT9S8Wv2LfuZZQ4,8320
sympy/printing/tests/test_rcode.py,sha256=bpv-W7eoYnIoFKMC-y0uDKBicJU4XXLr6oZZ9UFr7Qk,13781
sympy/printing/tests/test_repr.py,sha256=o7nQtkUFYH2QeN4Y_IhhTv-5EjsyiOFJ_MSowWTCuOU,12678
sympy/printing/tests/test_rust.py,sha256=eZTYJ3zN5LEt8tl5KhADg1HwcrofhSQswagP_zcxoMw,11504
sympy/printing/tests/test_str.py,sha256=2e7CvVKkA5qTpvC9vApjKmqk2ooX0H4D2L27reD6pso,41697
sympy/printing/tests/test_tableform.py,sha256=Ff5l1QL2HxN32WS_TdFhUAVqzop8YoWY3Uz1TThvVIM,5692
sympy/printing/tests/test_tensorflow.py,sha256=ee3rNzx8cs1VZKkXWS9wJX8mVNDXwC_cGJqVK85Vklc,15650
sympy/printing/tests/test_theanocode.py,sha256=dxMjRYkjTgj-l3rHuALAQAc6sddrfyeXKu1EKamOl4k,21395
sympy/printing/tests/test_tree.py,sha256=_8PGAhWMQ_A0f2DQLdDeMrpxY19889P5Ih9H41RZn8s,6080
sympy/printing/theanocode.py,sha256=k5mfsUn6KnNFb-aZuxgmSoZXNm1Ef_saspXn1m82nXw,19021
sympy/printing/tree.py,sha256=GxEF1WIflPNShlOrZc8AZch2I6GxDlbpImHqX61_P5o,3872
sympy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/release.py,sha256=2EG67lbV5_wUwU8yBg08vH3YDPwVD4FTGFaSgQC0H4U,23
sympy/sandbox/__init__.py,sha256=IaEVOYHaZ97OHEuto1UGthFuO35c0uvAZFZU23YyEaU,189
sympy/sandbox/indexed_integrals.py,sha256=svh4xDIa8nGpDeH4TeRb49gG8miMvXpCzEarbor58EE,2141
sympy/sandbox/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/sandbox/tests/test_indexed_integrals.py,sha256=UK2E2wg9EMwda4Vwpzyj3rmXs6ni33HqcbyaqAww6ww,1179
sympy/series/__init__.py,sha256=DYG9oisjzYeS55dIUpQpbAFcoDz7Q81fZJw36PRGu14,766
sympy/series/acceleration.py,sha256=9VTCOEOgIyOvcwjY5ZT_c4kWE-f_bL79iz_T3WGis94,3357
sympy/series/approximants.py,sha256=20qFt0-mm5HfOxtQKrp7s1WGuTEsWxu9wp8YBbn9duA,3208
sympy/series/aseries.py,sha256=cHVGRQaza4ayqI6ji6OHNkdQEMV7Bko4f4vug2buEQY,255
sympy/series/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/series/benchmarks/bench_limit.py,sha256=2PtdeeJtD6qyEvt9HFNvyTnMM8phFZRjscgnb4fHndU,173
sympy/series/benchmarks/bench_order.py,sha256=Hs3r6tMPDOhhU3SqVwStQxwLE8pobXCNkRx8NCcxpzk,211
sympy/series/formal.py,sha256=CtRziTUItAd8G9z__jJ9s7dRIHAOdeHajdPmNB3HRgY,51772
sympy/series/fourier.py,sha256=dzVo4VZ8OkD9YSbBEYQudpcHcEdVMG7LfnIRTMd4Lzg,22885
sympy/series/gruntz.py,sha256=_F5FVUzVYLpn4f61m3YM7wNWVBUj_gR3_UHqDIzZHxM,23879
sympy/series/kauers.py,sha256=PzD0MATMNjLjPi9GW5GQGL6Uqc2UT-uPwnzhi7TkJH8,1720
sympy/series/limits.py,sha256=cPawkAA3NcYoWdyI34fAO_yLYDsAvBQNjXfZLuKFVeg,12434
sympy/series/limitseq.py,sha256=WM1Lh3RXhSZM1gQaJrhWnUtYEgJunLujIEw1gmtVhYw,7752
sympy/series/order.py,sha256=u99m7I24vo3VO_39CuPoqRwrp_olr2btbeECCz2JxD0,19192
sympy/series/residues.py,sha256=k46s_fFfIHdJZqfst-B_-X1R-SAWs_rR9MQH7a9JLtg,2213
sympy/series/sequences.py,sha256=S2_GtHiPY9q2BpzbVgJsD4pBf_e4yWveEwluX9rSHF4,35589
sympy/series/series.py,sha256=crSkQK1wA6FQAKI1islG6rpAzvWlz1gZZPx2Awp43Qg,1861
sympy/series/series_class.py,sha256=033NJ5Re8AS4eq-chmfct3-Lz2vBqdFqXtnrbxswTx0,2918
sympy/series/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/series/tests/test_approximants.py,sha256=UXyjT0VGDBVuKsmTpqgQs2EIRcI3E44Dm0g5bwQEuR0,1033
sympy/series/tests/test_aseries.py,sha256=LblW4hBDVhigX9YvNc_HFvMm8nJMSTAT9PcUK3p-9HU,2371
sympy/series/tests/test_demidovich.py,sha256=JGYacqJMEqHS6oT2AYs9d7iutIEb32PkJs9EJqOHxcQ,4947
sympy/series/tests/test_formal.py,sha256=nFCo7uw59PfFuisFT67eqY_SkZPEUdGiVTDjq2BTK9I,22539
sympy/series/tests/test_fourier.py,sha256=Dknk64RWGNO8kXmpy2RRIbT8b-0CjL_35QcBugReW38,5891
sympy/series/tests/test_gruntz.py,sha256=nZqUjnVQ8d4ykjCRxPrGxY9vAULjHDd1Y1F4ZPnBcgE,15964
sympy/series/tests/test_kauers.py,sha256=Z85FhfXOOVki0HNGeK5BEBZOpkuB6SnKK3FqfK1-aLQ,1102
sympy/series/tests/test_limits.py,sha256=saaHmYcVuyd2bOS6JPK-g-TreeNSGUDBM4KO62HRVmE,39569
sympy/series/tests/test_limitseq.py,sha256=GlxbGk1uBPCtpCRKXGmWqRvz4zxeOo5eoJVHuP4Gy1I,5512
sympy/series/tests/test_lseries.py,sha256=Y9PwGTUxa8Y9mUOlaAbGrBGSNEn3V-Ze9Y956MeaTg8,1898
sympy/series/tests/test_nseries.py,sha256=Z_acGIOY9ZtqCn6Tjim4v7BZMYpFffSvwxKTW0jg1Zk,17160
sympy/series/tests/test_order.py,sha256=CB1Yb8yXbYV_chCdzjRVAMSBoAR016AKaDaSpq8MARs,16504
sympy/series/tests/test_residues.py,sha256=pT9xzPqtmfKGSbLLAxgDVZLTSy3TOxyfq3thTJs2VLw,3178
sympy/series/tests/test_sequences.py,sha256=Oyq32yQZnGNQDS2uJ3by3bZ-y4G9c9BFfdQTcVuW2RM,11161
sympy/series/tests/test_series.py,sha256=1FCDBWOj0IMd3pQDS2Jza4NjR89mVN3P-uhMd1zhb2A,14582
sympy/sets/__init__.py,sha256=3vjCm4v2esbpsVPY0ROwTXMETxns_66bG4FCIFZ96oM,1026
sympy/sets/conditionset.py,sha256=mBxxVHIFt9UfddAyvwfd-uVsM5fisNUSvBdNWH5QN_A,7825
sympy/sets/contains.py,sha256=-RCL7rYCowb6ZwrdvKxRcgPEHp9gHTozx_glSUIm5Hk,1242
sympy/sets/fancysets.py,sha256=kVDkGbp316dFdR5GMWLtreltBFot8G39XM_xLvG1TkU,48118
sympy/sets/handlers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/sets/handlers/add.py,sha256=_ucFvxuDv9wsmKxGkCDUERtYk3I_tQxjZjY3ZkroWs0,1863
sympy/sets/handlers/comparison.py,sha256=WfT_vLrOkvPqRg2mf7gziVs_6cLg0kOTEFv-Nb1zIvo,1601
sympy/sets/handlers/functions.py,sha256=jYSFqFNH6mXbKFPgvIAIGY8BhbLPo1dAvcNg4MxmCaI,8381
sympy/sets/handlers/intersection.py,sha256=wgv8hx9OKU6GBm4MpRyJeoOzZ9_salNyPqdulhv8rgs,16559
sympy/sets/handlers/issubset.py,sha256=azka_5eOaUro3r3v72PmET0oY8-aaoJkzVEK7kuqXCA,4739
sympy/sets/handlers/mul.py,sha256=XFbkOw4PDQumaOEUlHeQLvjhIom0f3iniSYv_Kau-xw,1842
sympy/sets/handlers/power.py,sha256=84N3dIus7r09XV7PF_RiEpFRw1y5tOGD34WKzSM9F-4,3186
sympy/sets/handlers/union.py,sha256=lrAdydqExnALUjM0dnoM-7JAZqtbgLb46Y2GGmFtQdw,4225
sympy/sets/ordinals.py,sha256=GSyaBq7BHJC3pvgoCDoUKZQ0IE2VXyHtx6_g5OS64W4,7641
sympy/sets/powerset.py,sha256=vIGnSYKngEPEt6V-6beDOXAOY9ugDLJ8fXOx5H9JJck,2913
sympy/sets/setexpr.py,sha256=jMOQigDscLTrFPXvHqo1ODVRG9BqC4yn38Ej4m6WPa0,3019
sympy/sets/sets.py,sha256=SPjQE96VoP8EtLFlAdQ4CdS8LqiF7BejKfFqYQ-NERk,78773
sympy/sets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/sets/tests/test_conditionset.py,sha256=4FdbXxobY286r5UtrCbcQPqaFIycsdlbtNO2vJmzsEI,11352
sympy/sets/tests/test_contains.py,sha256=FbrsJ_i-d5v7vcPHoGAc9TYdhyLYKG4UQJMkYFxIYEg,1614
sympy/sets/tests/test_fancysets.py,sha256=JHHoz_BHL_hc3D03HGVq4P-bk4RhtKnZEgmAI2OGTG8,51149
sympy/sets/tests/test_ordinals.py,sha256=L4DYc6ByQMDwJGFzJC3YhfSrVk5auW7pf4QYpJ5xY7w,2637
sympy/sets/tests/test_powerset.py,sha256=nfu7SrLNYAtv9Jx0NVX3v4wRc8fmciPJiHH2BNYl114,4804
sympy/sets/tests/test_setexpr.py,sha256=W-JS2fWRzxhIJAh50Hi5lnJmndkyf-_rtr7UmC9mEk8,14801
sympy/sets/tests/test_sets.py,sha256=PMsVh9KF4FUD8-9NXd_HAar-E3cHkqaP-VfXdz2UQow,66664
sympy/simplify/__init__.py,sha256=MH1vkwHq0J5tNm7ss8V6v-mjrDGUXwfOsariIwfi38c,1274
sympy/simplify/combsimp.py,sha256=XZOyP8qxowsXNbrtdUiinUFTUau4DZvivmd--Cw8Jnk,3605
sympy/simplify/cse_main.py,sha256=IsQG9FO4678sI_cMwBxNb7eR7p7_qiS9VUehHZU4V9Q,30201
sympy/simplify/cse_opts.py,sha256=ZTCaOdOrgtifWxQmFzyngrLq9uwzByBdiSS5mE-DDoE,1618
sympy/simplify/epathtools.py,sha256=YEeS5amYseT1nC4bHqyyemrjAE1qlhWz0ISXJk5I8Xo,10173
sympy/simplify/fu.py,sha256=_wOUskbhGHMqgnS36yWFUu3xbFvBdNXCH-9RTnerSAw,61852
sympy/simplify/gammasimp.py,sha256=kOweyrP9XUMVyC6tXS4mQGUqR2Kd_HEPUhTemEvm_co,18613
sympy/simplify/hyperexpand.py,sha256=DWmIvYWtE7TJJ50_E2F1K1w06RaSZUEhEtli6PW1Wso,84446
sympy/simplify/hyperexpand_doc.py,sha256=E8AD0mj8ULtelDSUkmJKJY7kYm5fVfCL4QH_DX65qEw,521
sympy/simplify/powsimp.py,sha256=BEj25EnYMqg--ZhAPr3SFp3g3s8iisxE-JvEfpT9hgs,26578
sympy/simplify/radsimp.py,sha256=TmFCwQfCkeg6RjZy85KkAPwfyZbpvwbMD8Svo-5ymmo,41314
sympy/simplify/ratsimp.py,sha256=ncVtRPGAH0hz_DAxscO5kNO4zE5Cxx1B1N-epPy-ex4,7616
sympy/simplify/simplify.py,sha256=J-aIU8a2Isg_nzD8z1HGqDKmibY55_JgdctvFyRx4oA,72635
sympy/simplify/sqrtdenest.py,sha256=ynuIPy9Fjag5da-43WFWPcKvuatdDjbYASPJc5lH1g0,21590
sympy/simplify/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/simplify/tests/test_combsimp.py,sha256=O95WSxCvo2fDQs-UlarAcSf0_8M3PuTR76lhREDoNA8,2958
sympy/simplify/tests/test_cse.py,sha256=ZhxH1v4p1-4RosFRJbXryTugVZ-j9QNSI9EM4RNKrCA,19855
sympy/simplify/tests/test_epathtools.py,sha256=ugsQlfuK6POiixdeit63QovsVAlG5JyCaPlPp0j35LE,3525
sympy/simplify/tests/test_fu.py,sha256=Xqv8OyB_z3GrDUa9YdxyY98vq_XrwiMKzwMpqKx8XFQ,18651
sympy/simplify/tests/test_function.py,sha256=gzdcSFObuDzVFJDdAgmERtZJvG38WNSmclPAdG8OaPQ,2199
sympy/simplify/tests/test_gammasimp.py,sha256=32cPRmtG-_Mz9g02lmmn-PWDD3J_Ku6sxLxIUU7WqxE,5320
sympy/simplify/tests/test_hyperexpand.py,sha256=P1OS6nfGWag9CczgOWz1c9cu5nUwUuyFGqp_ockxHEE,40863
sympy/simplify/tests/test_powsimp.py,sha256=CG5H_xSbtwZakjLzL-EEg-T9j2GOUylCU5YgLsbHm2A,14313
sympy/simplify/tests/test_radsimp.py,sha256=7GjCVKP_nyS8s36Oxwmw6TiPRY0fG3aZP9Rd3oSksTY,18789
sympy/simplify/tests/test_ratsimp.py,sha256=uRq7AGI957LeLOmYIXMqKkstQylK09xMYJRUflT8a-s,2210
sympy/simplify/tests/test_rewrite.py,sha256=LZj4V6a95GJj1o3NlKRoHMk7sWGPASFlw24nsm4z43k,1127
sympy/simplify/tests/test_simplify.py,sha256=y0PFBMEHQL90vxnaupKdfRsqn1OPtoRkDlB-wG9ViZg,40731
sympy/simplify/tests/test_sqrtdenest.py,sha256=4zRtDQVGpKRRBYSAnEF5pSM0AR_fAMumONu2Ocb3tqg,7470
sympy/simplify/tests/test_trigsimp.py,sha256=L_xTGfBVSrwr3aMmnyy7_LGREMnl-UQX7Cvyc5dtifk,19033
sympy/simplify/traversaltools.py,sha256=pn_t9Yrk_SL1X0vl-zVR6yZaxkY25D4MwTBv4ywnD1Y,409
sympy/simplify/trigsimp.py,sha256=vsSkFN5Th8yLRHrOGAi9XJWXY1PbMFjP4EaykplUGH4,45323
sympy/solvers/__init__.py,sha256=cqnpjbmL0YQNal_aQ-AFeCNkU1eHCpC17uaJ-Jo8COQ,2210
sympy/solvers/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/solvers/benchmarks/bench_solvers.py,sha256=ZVK2TIW0XjWRDBex054ymmVlSBQw-RIBhEL1wS2ZAmU,288
sympy/solvers/bivariate.py,sha256=cx0A8oMtwJyWKnP9kO2KWKxplvpVxHggPGwMJvbHwms,17868
sympy/solvers/decompogen.py,sha256=dWQla7hp7A4RqI2a0qRNQLWNPEuur68lD3dVTyktdBU,3757
sympy/solvers/deutils.py,sha256=6dCIoZqX8mFz77SpT1DOM_I5yvdwU1tUMnTbA2vjYME,10309
sympy/solvers/diophantine/__init__.py,sha256=I1p3uj3kFQv20cbsZ34K5rNCx1_pDS7JwHUCFstpBgs,128
sympy/solvers/diophantine/diophantine.py,sha256=0qzuurFivfatUFp4rPQ8WA9L8nGIPlzLvgZOCZyR1jg,119640
sympy/solvers/diophantine/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/solvers/diophantine/tests/test_diophantine.py,sha256=ax2f05mFCduTu0j3wG9ka13NKcANNF7K_RX2vXSl_ys,41778
sympy/solvers/inequalities.py,sha256=noi6TLgdBrNhmwPldBSFSQQa0Hn6nPtK3LwDbOzo86k,33104
sympy/solvers/ode/__init__.py,sha256=I7RKwCcaoerflUm5i3ZDJgBIOnkhBjb83BCHcVcFqfM,468
sympy/solvers/ode/hypergeometric.py,sha256=kizvLgjzX1VUZ1n84uT6tlOs_8NfQBW1JZVo0fJLkdM,10048
sympy/solvers/ode/lie_group.py,sha256=tGCy_KAMuKa4gb4JR084Qy0VKu9qU1BoYBgreDX5D9Q,39242
sympy/solvers/ode/nonhomogeneous.py,sha256=TcuMjyKGht1z9yeL1KQXV3vXa-xGuGX-Gk4sQwRDYB0,18230
sympy/solvers/ode/ode.py,sha256=msZlfJ_IWjxpNGO2YACbHmgjLgqmOyZfD9gueBxIqbU,145080
sympy/solvers/ode/riccati.py,sha256=1TOaQz1HMuolUjvXrnkUAxkYt1ucKaPZk1mzKX_mb8k,30746
sympy/solvers/ode/single.py,sha256=2Kb8dfZs66-hpKrF3cEz7d7V2GHhcheWcWbA06ivFKE,109559
sympy/solvers/ode/subscheck.py,sha256=CIPca_qTxL9z5oaD2e2NrgME0eVQgF9PabZndcVqHZM,16130
sympy/solvers/ode/systems.py,sha256=NowfgdTV8X9IgKsDDede_GLZzTaTcbqeN-ePHDMmeWA,71803
sympy/solvers/ode/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/solvers/ode/tests/test_lie_group.py,sha256=vg1yy_-a5x1Xm2IcVkEi5cD2uA5wE5gjqpfBwkV1vZc,5319
sympy/solvers/ode/tests/test_ode.py,sha256=WsDeiS1cxO4NCDNJa99NMAqysPsOrKTQ0c6aY_u2vjc,48311
sympy/solvers/ode/tests/test_riccati.py,sha256=8k8MBt4LgIk9RYWtzTZjbOleKwrpe-TdVyUZOU3Gf1Y,29350
sympy/solvers/ode/tests/test_single.py,sha256=lSZ66KMd-b-OsamyXaCtZl6u3r-OKMnrnKvEtM-7OCQ,99991
sympy/solvers/ode/tests/test_subscheck.py,sha256=Gzwc9h9n6zlNOhJ8Qh6fQDeB8ghaRmgv3ktBAfPJx-U,12468
sympy/solvers/ode/tests/test_systems.py,sha256=5zy3a0VMb5zOn-Lm7AM90kso7KN41n3DDqRQP6p8KKA,129728
sympy/solvers/pde.py,sha256=FRFnEbD7ZJOcy8-q1LZ5NvYRt4Fu4Avf5Xe6Xk6pWoo,35659
sympy/solvers/polysys.py,sha256=RjAK16jxrlEiHdHKGqz54cdD6X7OSfqfdy7MhshcGf0,12813
sympy/solvers/recurr.py,sha256=vu-p7vJT9xUuBXkt960RleWPquVRz-CiSng8nwP0AVg,25220
sympy/solvers/solvers.py,sha256=qklbbKqZ8lUXAB2XIanBBo-LQARL-VaLb62liaCl5dE,132536
sympy/solvers/solveset.py,sha256=ZmA8zFSKOWQbwGNS-F3zmwjXIeHA0Ang5BLURfQi3mU,139182
sympy/solvers/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/solvers/tests/test_constantsimp.py,sha256=9Feugsg9jD2BwQiG4EFpb9fORyst6JdBmZqq2GaOgH8,8707
sympy/solvers/tests/test_decompogen.py,sha256=7GUsDQQZtYbZIK0p0UxsOuNEJxEt4IHeOSsem_k-k0U,2943
sympy/solvers/tests/test_inequalities.py,sha256=MuSP5v1kFL7eH_CSqOPhl6xDd1GuwRBWcZQSCwBy6Bg,20688
sympy/solvers/tests/test_numeric.py,sha256=Ik33Yca8BaXpnA7617IIQuEYnM5QpGYT4qrD9QhcLvk,4690
sympy/solvers/tests/test_pde.py,sha256=UGP3uWjF8pKQgfPifmdfvS5URVmzSg6m2NkS7LGzmio,9257
sympy/solvers/tests/test_polysys.py,sha256=P1Jk79CAYB85L-O3KRJKpsqvwVJgqqJ_u44NigGWsaA,6873
sympy/solvers/tests/test_recurr.py,sha256=Gb_wdhHPglmaZzs485Yay_5FT-u3iXDyelwgFav0X3o,11630
sympy/solvers/tests/test_solvers.py,sha256=PKkSdxlKt2pPNZNxmV3BdbkSfxDfiNEUhVppDydxG9c,99638
sympy/solvers/tests/test_solveset.py,sha256=_kZ2w5Vuk9z8ylMjlOoFXRCV38UN-qUviyhHjpaGuBg,132936
sympy/stats/__init__.py,sha256=aNs_difmTw7e2GIfLGaPLpS-mXlttrrB3TVFPDSdGwU,8471
sympy/stats/compound_rv.py,sha256=uRI-HevjgcclwfUb0mM4qxo5k1IIYYh4vSHbAMNFD3Y,7963
sympy/stats/crv.py,sha256=VK7jvYiQH523ar6QvLzV_k67u0ghcCrrWlBgt3cMdaw,20979
sympy/stats/crv_types.py,sha256=fdCUmYQRqIxtgD_7Y77kEhAX4G9SC7I0C4DEEX0s-mU,120101
sympy/stats/drv.py,sha256=ewxYnUlCyvaF5ceMpziiz4e6FAgknzP5cC1ZVvQ_YLE,11995
sympy/stats/drv_types.py,sha256=Mts1vvlEHoMdNtCfH0-zGzSF9AKLyPgHbT82TE-Zkg8,19284
sympy/stats/error_prop.py,sha256=a-H6GZEidsiP_4-iNw7nSD99AMyN6DNHsSl0IUZGIAs,3315
sympy/stats/frv.py,sha256=C4FHAVuckxdVnXGlmT957At5xdOLVYvH76KgL44TR38,16876
sympy/stats/frv_types.py,sha256=Ilkf3w6QeuNmOp3qEm9mq6-KjcS7LhI3pNjOOQ7H-lw,23226
sympy/stats/joint_rv.py,sha256=DcixlO2Ml4gnwMmZk2VTegiHVq88DkLdQlOTQ57SQtc,15963
sympy/stats/joint_rv_types.py,sha256=fzqIDEcZj6w4W9WbueaWv-X3UrtR-pG8-ClaEVL0g5A,30601
sympy/stats/matrix_distributions.py,sha256=H5IOHv1TzQMm9iZFQo01sHWN0TcD5sWE1ca2piQ89oY,21945
sympy/stats/random_matrix.py,sha256=NmzLC5JMDWI2TvH8tY6go8lYyHmqcZ-B7sSIO7z7oAk,1028
sympy/stats/random_matrix_models.py,sha256=o7BJf0E6DFBrrSHzEwxHWG8nlj3b2K8raImdF8xVDLA,15325
sympy/stats/rv.py,sha256=-0w0O0bkLL-YpCjgH9L26OrQRegIFJ89TIj6Ca4iuJg,54493
sympy/stats/rv_interface.py,sha256=GG8ESauGq_0DoulddMqBOv8Pu0ROutqAuGgs8ywKQSQ,13936
sympy/stats/sampling/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/stats/sampling/sample_numpy.py,sha256=B4ZC7ZBrSD6ICQT468rOy-xrOgQDuecsHa0zJesAeYE,4229
sympy/stats/sampling/sample_pymc.py,sha256=9g-n04aXSFc6F7FJ5zTYtHHL6W8-26g1nrgtamJc3Hw,2995
sympy/stats/sampling/sample_scipy.py,sha256=ysqpDy8bp1RMH0g5FFgMmp2SQuXGFkcSH7JDZEpiZ8w,6329
sympy/stats/sampling/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/stats/sampling/tests/test_sample_continuous_rv.py,sha256=Gh8hFN1hFFsthEv9wP2ZdgghQfaEnE8n7HlmyXXhN1E,5708
sympy/stats/sampling/tests/test_sample_discrete_rv.py,sha256=jd2qnr4ABqpFcJrGcUpnTsN1z1d1prVvwUkG965oFeA,3319
sympy/stats/sampling/tests/test_sample_finite_rv.py,sha256=dWwrFePw8eX2rBheAXi1AVxr_gqBD63VZKfW81hNoQc,3061
sympy/stats/stochastic_process.py,sha256=pDz0rbKXTiaNmMmmz70dP3F_KWL_XhoCKFHYBNt1QeU,2312
sympy/stats/stochastic_process_types.py,sha256=4zFNmW6Ghu1vvgy-W7D9hpD3W8ou68WBxlF_lv9LiQ8,88503
sympy/stats/symbolic_multivariate_probability.py,sha256=HRFzkdHtj2C4cFUuiedRsK6w9CvpC5UlR1WJuwIzETg,10451
sympy/stats/symbolic_probability.py,sha256=y7eAj3PlW4RxGMTJ6Nlv2P8cnfI9Jij1jHdq-zplutM,23024
sympy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/stats/tests/test_compound_rv.py,sha256=2927chbHTThA34Ki-ji319QT7ajQ1ueC640Mga-18ZA,6263
sympy/stats/tests/test_continuous_rv.py,sha256=j3SFC2-4a6X2JObL3JU8znQkRXOGxz2a9XPlGPoBku0,55665
sympy/stats/tests/test_discrete_rv.py,sha256=TTPnAglTM8cXuiZSnFz9GlI9_IwEBux30kS_IY-olN8,10848
sympy/stats/tests/test_error_prop.py,sha256=xKAkw3F5XJ72xiDREI7PkyReWNVW_89CD_mjOY_diDY,1933
sympy/stats/tests/test_finite_rv.py,sha256=JHYgY4snFF5t9qcnQfKaN5zaGsO7_SuNR7Tq234W4No,20413
sympy/stats/tests/test_joint_rv.py,sha256=FieWxwbsSMY2h9JEvpK40M_YPOv6eM2qxkP2LGGgNyo,18647
sympy/stats/tests/test_matrix_distributions.py,sha256=gQ15FhfspALv6IQzFCDhicBDpEl24ofiN-AePRZwzaw,8832
sympy/stats/tests/test_mix.py,sha256=Cplnw06Ki96Y_4fx6Bu7lUXjxoIfX7tNJasm9SOz5wQ,3991
sympy/stats/tests/test_random_matrix.py,sha256=CiD1hV25MGHwTfHGaoaehGD3iJ4lqNYi-ZiwReO6CVk,5842
sympy/stats/tests/test_rv.py,sha256=zsO70B7RNjGhs8A6piRpUKz9CniYk66gLfYbB6i3X58,12955
sympy/stats/tests/test_stochastic_process.py,sha256=5CwqLe4FMg5sp2NN5q60fchwlE5Vu5ABsjDf-VbQz00,39245
sympy/stats/tests/test_symbolic_multivariate.py,sha256=eZF3W049etnjeUkxxylXONerUTZjbwCi5oVeSP18lvU,5600
sympy/stats/tests/test_symbolic_probability.py,sha256=k5trScMiwSgl9dzJt30BV-t0KuYcyD-s9HtT2-hVhQ0,9398
sympy/strategies/__init__.py,sha256=YYamNi53QzE17bQ9Y3m3HF6b5xv4TEW0f1ifaN3OeY0,1405
sympy/strategies/branch/__init__.py,sha256=nLBM56YXsigAyTXZ3cfiLJHyyAYVbfYlGDMnU_oHWI4,355
sympy/strategies/branch/core.py,sha256=xUDgpUgANrtRUDUUmD94oUOc_YJppEMJSggOCD6e1W0,2748
sympy/strategies/branch/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/strategies/branch/tests/test_core.py,sha256=MOPyYEEi_Ug5mCfROO5xwfWhR22q1KSkx6US65iZy0Y,2255
sympy/strategies/branch/tests/test_tools.py,sha256=9l077bAkY2m2EH4ZrR-SjyO1F60M8dVcP6fyPrCHuFU,930
sympy/strategies/branch/tests/test_traverse.py,sha256=KJrjKpRXMxLYtcEGGjVFoDQ7vLS3eG8OIDqo8_4YFV4,1337
sympy/strategies/branch/tools.py,sha256=DsS7JI-QROuqwn2S3kcHlCqIefAWOJRD0AhVOmsik-M,356
sympy/strategies/branch/traverse.py,sha256=omjfYf7vxg1MO4duQsOjoBmkxsjAu1m2j0mh-kmxMXg,798
sympy/strategies/core.py,sha256=Z5VTZVNYd4y1KYAkh7srqfdga8zDxoQ4rTzUNjaGIFk,2941
sympy/strategies/rl.py,sha256=zYNaBvGv2aCZBjwPOZrg820WlFhZ-ZGrLNfI-5wo-Bo,4395
sympy/strategies/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/strategies/tests/test_core.py,sha256=vWne0nUBpnE5_rrYHC-9jGNzpO7Ybzzp2as1YmUj-ZU,1881
sympy/strategies/tests/test_rl.py,sha256=RJ_azRVxCyfdQnaSezZ50f_-c8A6uFS5bsfzT7nUREY,1999
sympy/strategies/tests/test_strat.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/strategies/tests/test_tools.py,sha256=9bWXX1LJ3570XPIP5uPHzhslHQiV9Ac7Y2H-CKTw2-I,868
sympy/strategies/tests/test_traverse.py,sha256=rbYaedoUVq0xQ6k_pIgSiuvWUGPRGcLGnDftTEtFA3Q,2033
sympy/strategies/tests/test_tree.py,sha256=Pn1cvWIf0f_pyT3MvoCfZykqEiOIr0Q75iqImgKi-40,2559
sympy/strategies/tools.py,sha256=NNHLdiR0BlALHPW307IleT4fkxowPJ9m2Fgrs8axZ_s,1365
sympy/strategies/traverse.py,sha256=DhPnBJ5Rw_xzhGiBtSciTyV-H2zhlxgjYVjrNH-gLyk,1183
sympy/strategies/tree.py,sha256=VXVKPWgaTqMTRO9RO49Y4OFZbM-AbvJoq7tO17g68e8,3747
sympy/strategies/util.py,sha256=BretVuOjq2Mk_ORyS5hcNRTdLGfarsyZA7O2sFl585I,359
sympy/tensor/__init__.py,sha256=VMNXCRSayigQT6a3cvf5M_M-wdV-KSil_JbAmHcuUQc,870
sympy/tensor/array/__init__.py,sha256=IFh_TaaK0slf5Qoc6iaqD836Wr_f4ac4hWZQbbyl2m0,7299
sympy/tensor/array/array_comprehension.py,sha256=01PTIbkAGaq0CDcaI_2KsaMnYm1nxQ8sFAiHHcc__gw,12262
sympy/tensor/array/array_derivatives.py,sha256=iCScnAHPIBKbViBB7YHya8HyaEM3AQrctqAFXHNwHmk,4877
sympy/tensor/array/arrayop.py,sha256=V-wDjRcj6Yk0KdK0EVk0Vv8aQGHfY3okOHGLwSRl_mA,17405
sympy/tensor/array/dense_ndim_array.py,sha256=YsFrCfURsA-ivL1SED3MCXtYvuKihkBuhqI7VtsYmk0,6371
sympy/tensor/array/expressions/__init__.py,sha256=m4sDn6I-Ifyd5K2smZERIkZPLOzWzJj9ca2Ig_hjUOE,7045
sympy/tensor/array/expressions/array_expressions.py,sha256=HbsFcunOekF6DNNYptVVpepT50SP_zcGwckPvKKHvew,77134
sympy/tensor/array/expressions/arrayexpr_derivatives.py,sha256=Le8r_-6Xi5sP-dCknCNNoqRNNetP71VLOD_ngWe_vbM,6078
sympy/tensor/array/expressions/conv_array_to_indexed.py,sha256=79cAjkqu93q0AYa7raprDLquCXs1AZeiifF0XvKJko8,3941
sympy/tensor/array/expressions/conv_array_to_matrix.py,sha256=fudbXB8sY4n14pmU3Xzui1RDqhpjNXP2zTvGoGliOb8,41387
sympy/tensor/array/expressions/conv_indexed_to_array.py,sha256=HT89I8bsehHcUNSw8zicsa69k0iSogObZTqvABBrJNg,11194
sympy/tensor/array/expressions/conv_matrix_to_array.py,sha256=yIY1RupF9-FVV3jZLsqWxZ1ckoE1-HkQyM8cQIm4_Gs,3929
sympy/tensor/array/expressions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/tensor/array/expressions/tests/test_array_expressions.py,sha256=QUAdxQ9TvBpDEAZoJpLSWwbqjmuflPe3xBRP30lFZr0,31262
sympy/tensor/array/expressions/tests/test_arrayexpr_derivatives.py,sha256=lpC4ly6MJLDRBcVt3GcP3H6ke9bI-o3VULw0xyF5QbY,2470
sympy/tensor/array/expressions/tests/test_as_explicit.py,sha256=nOjFKXCqYNu2O7Szc1TD1x1bsUchPRAG3nGlNGEd1Yg,2568
sympy/tensor/array/expressions/tests/test_convert_array_to_indexed.py,sha256=9Zg09E7hN9YDRss3Y24HCvzCBoHJnADaEFv6qncD4wA,2500
sympy/tensor/array/expressions/tests/test_convert_array_to_matrix.py,sha256=RHkRzWDAD0L918RtFl2VoCvTGiD_goCMVbyNoM5WcLk,29311
sympy/tensor/array/expressions/tests/test_convert_index_to_array.py,sha256=qI6kUjD1H9QTptARW-fcKu3Wlexefn-pv8wsdGkXf5U,8615
sympy/tensor/array/expressions/tests/test_convert_matrix_to_array.py,sha256=8_exsSNJvkzPviEGONwe57HImt5OSp5u0N8zNEZHoGM,4595
sympy/tensor/array/expressions/utils.py,sha256=Rn58boHHUEoBZFtinDpruLWFBkNBwgkVQ4c9m7Nym1o,3939
sympy/tensor/array/mutable_ndim_array.py,sha256=M0PTt8IOIcVXqQPWe2N50sm4Eq2bodRXV4Vkd08crXk,277
sympy/tensor/array/ndim_array.py,sha256=SrA-AeQUUagBiz_qNyLTkOlgRCaq8GeOrbSkhXw4Ces,19043
sympy/tensor/array/sparse_ndim_array.py,sha256=4nD_Hg-JdC_1mYQTohmKFfL5M1Ugdq0fpnDUILkTtq8,6387
sympy/tensor/array/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/tensor/array/tests/test_array_comprehension.py,sha256=32n8ZKV4_5DeJ0F7fM_Xo0i0mx6m9w3uWUI2a6OXhzY,4750
sympy/tensor/array/tests/test_array_derivatives.py,sha256=3O2nD4_d1TFP75qcGJ8XD4DwfPblFzKhY6fAgNQ9KJ0,1609
sympy/tensor/array/tests/test_arrayop.py,sha256=JMturFC8nd6X1pm4A2bBNjb9d9DMrMmqxnLxDC30WYw,25851
sympy/tensor/array/tests/test_immutable_ndim_array.py,sha256=9ji_14szn-qoL6DQ5muzIFNaXefT7n55PFigXoFwk50,15823
sympy/tensor/array/tests/test_mutable_ndim_array.py,sha256=rFFa0o0AJYgPNnpqijl91Vb9EW2kgHGQc6cu9f1fIvY,13070
sympy/tensor/array/tests/test_ndim_array.py,sha256=KH-9LAME3ldVIu5n7Vd_Xr36dN4frCdiF9qZdBWETu0,2232
sympy/tensor/array/tests/test_ndim_array_conversions.py,sha256=CUGDCbCcslACy3Ngq-zoig9JnO4yHTw3IPcKy0FnRpw,648
sympy/tensor/functions.py,sha256=3jkzxjMvHHsWchz-0wvuOSFvkNqnoG5knknPCEsZ1bk,4166
sympy/tensor/index_methods.py,sha256=tI_0A7pTLKOxnXnPK2h0scA77k-7pKOgQiIYZBOmFiU,15433
sympy/tensor/indexed.py,sha256=2Peh4y2rFt9kbKWCjG_tw_MgkvsFdx0U29vij-eeTy4,24659
sympy/tensor/tensor.py,sha256=r5votAkt3HvK8raTRrGgaZpgTRIZk5GJz6SXK7s6sso,148427
sympy/tensor/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/tensor/tests/test_functions.py,sha256=rBBHjJIUA2oR83UgEJ_GIASDWfTZXDzOllmcO90XYDU,1552
sympy/tensor/tests/test_index_methods.py,sha256=Pu951z4yYYMOXBKcNteH63hTAxmNX8702nSQH_pciFE,7112
sympy/tensor/tests/test_indexed.py,sha256=pCvqmScU0oQxx44qm9T3MkKIXKgVFRDkSHLDhSNqOIY,16157
sympy/tensor/tests/test_tensor.py,sha256=aDGv7UqvWHXr5Anst2_V8N9sWPK4sHtL1huS5A8mVEc,72526
sympy/tensor/tests/test_tensor_element.py,sha256=1dF96FtqUGaJzethw23vJIj3H5KdxsU1Xyd4DU54EB4,908
sympy/tensor/tests/test_tensor_operators.py,sha256=bHSOa3ltyn6KGFd4jEgHdypXnwVdvmUAoGKLxXWka_0,17946
sympy/tensor/toperators.py,sha256=HH734EoQptgYPIH20VYzc0QvpcTQV_Uu2L1T10vUflI,9010
sympy/testing/__init__.py,sha256=YhdM87Kfsci8340HmKrXVmA4y0z_VeUN5QQbwAOvEbg,139
sympy/testing/benchmarking.py,sha256=p9z1krmlzzcRTFwe6_T0RMIgYHU-c2rAI__KXhlZQdk,6194
sympy/testing/matrices.py,sha256=VWBPdjIUYNHE7fdbYcmQwQTYcIWpOP9tFn9A0rGCBmE,216
sympy/testing/pytest.py,sha256=5AQOnKjlGYRAEd99g9FmVEzjozYPG6Strx6MQNtGWks,12605
sympy/testing/quality_unicode.py,sha256=-y_85he9qOIqxUXSYIz1kMNBZSa7wumhqGsgZJIX7y0,3059
sympy/testing/randtest.py,sha256=IKDFAm8b72Z1OkT7vpgnZjaW5LsSU_wf6g35sCkq9I0,562
sympy/testing/runtests.py,sha256=o5k6L614PLDETn3HllaffiOMBUD1BYTzHFTzGeTHJEk,88576
sympy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/testing/tests/diagnose_imports.py,sha256=ICRLtdU7nXZOJJoyfQlfa0uui-UAiIdTGovlkdrneAE,9701
sympy/testing/tests/test_code_quality.py,sha256=FUsBKNgCj_XXwRfvRIynEOAJ91mYSfzOEgoWd_aOgqo,19317
sympy/testing/tests/test_deprecated.py,sha256=wQZHs4wDNuK4flaKKLsJW6XRMtrVjMv_5rUP3WspgPA,183
sympy/testing/tests/test_module_imports.py,sha256=5w6F6JW6K7lgpbB4X9Tj0Vw8AcNVlfaSuvbwKXJKD6c,1459
sympy/testing/tests/test_pytest.py,sha256=iKO10Tvua1Xem6a22IWH4SDrpFfr-bM-rXx039Ua7YA,6778
sympy/testing/tmpfiles.py,sha256=bF8ktKC9lDhS65gahB9hOewsZ378UkhLgq3QHiqWYXU,1042
sympy/this.py,sha256=XfOkN5EIM2RuDxSm_q6k_R_WtkIoSy6PXWKp3aAXvoc,550
sympy/unify/__init__.py,sha256=Upa9h7SSr9W1PXo0WkNESsGsMZ85rcWkeruBtkAi3Fg,293
sympy/unify/core.py,sha256=-BCNPPMdfZuhhIWqyn9pYJoO8yFPGDX78Hn2551ABuE,7037
sympy/unify/rewrite.py,sha256=Emr8Uoum3gxKpMDqFHJIjx3xChArUIN6XIy6NPfCS8I,1798
sympy/unify/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/unify/tests/test_rewrite.py,sha256=BgA8zmdz9Nw-Xbu4-w3UABeWypqLvmy9VzL744EmYtE,2002
sympy/unify/tests/test_sympy.py,sha256=ebmD-gA8Uj0KfIaazVyDQBiwwo8LNOSg0zA_QFOFSLM,5935
sympy/unify/tests/test_unify.py,sha256=4TlgchV6NWuBekJx9RGlMjx3-UwonzgIYXDytb7sBRU,3029
sympy/unify/usympy.py,sha256=6Kxx96FXSdqXimLseVK_FkYwy2vqWhNnxMVPMRShvy4,3964
sympy/utilities/__init__.py,sha256=-Ei8G4RW-QubhMmwu9TQAPdR3wliEnJR-vM1eDkAtHU,883
sympy/utilities/_compilation/__init__.py,sha256=uYUDPbwrMTbGEMVuago32EN_ix8fsi5M0SvcLOtwMOk,751
sympy/utilities/_compilation/availability.py,sha256=ybxp3mboH5772JHTWKBN1D-cs6QxATQiaL4zJVV4RE0,2884
sympy/utilities/_compilation/compilation.py,sha256=Up8uuFlsPs1wf2Hvaimg452IdKBZcV98Lwf4_37NUdI,20595
sympy/utilities/_compilation/runners.py,sha256=e4vDrTpR_iE8XLuriRg8TNHB6wYthrhYnf4GKM1zD7Q,9059
sympy/utilities/_compilation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/utilities/_compilation/tests/test_compilation.py,sha256=AITBam5DIOl2Yn28EdnLo3SD2VS_veHrqx2M_w7XYDs,1733
sympy/utilities/_compilation/util.py,sha256=JY52yzVa4jxi7BlHwPjFkr5E-hHn_8p7ldeVEfts8rM,7928
sympy/utilities/autowrap.py,sha256=hwr1rZLZ7KU9XfkbZ5AQD_q3inXSW3-cf_PsU64Bz4E,41391
sympy/utilities/benchmarking.py,sha256=XvCouc3ewYxZuJHpZw-Prf9rwlvvLG2_qRwiaHiuTQ4,465
sympy/utilities/codegen.py,sha256=uf6VFdXptZQNCksyJ75GhaSDymeqZPlkSv3u3OeoCaw,81486
sympy/utilities/decorator.py,sha256=zRsHijmGSObfwNck8EhbAB_v_6-M6FTsawseDM_hneo,10902
sympy/utilities/enumerative.py,sha256=ut89aGhPyt8IEvZE7e-ra59NiX_Jzly9SZGWVV49qQ0,43623
sympy/utilities/exceptions.py,sha256=g9fgLCjrkuYk-ImX_V42ve2XIayK01mWmlXKOIVmW_8,10571
sympy/utilities/iterables.py,sha256=-NQhf-Rb5hHSh4qj-aVwWM4wuaYMYK0HAFJh38KB9gA,88124
sympy/utilities/lambdify.py,sha256=p8skVuY5JHVDGouN-kxZl0M4uuH7UaQKADSxoW_sTkw,50986
sympy/utilities/magic.py,sha256=ofrwi1-xwMWb4VCQOEIwe4J1QAwxOscigDq26uSn3iY,400
sympy/utilities/matchpy_connector.py,sha256=3uFNjCfDLA1H3Un0EUVjw9CMnSiYEZkXQ9_fXAQSlU8,10084
sympy/utilities/mathml/__init__.py,sha256=RKr6V6UxjzJP2opeKLhhXTHgR6MUdIiAK9mgapSOjwA,2065
sympy/utilities/mathml/data/mmlctop.xsl,sha256=h-cttlnimatJ0gWjOwRGN2LhB9ZddVsZ79D7UjP75H4,114443
sympy/utilities/mathml/data/mmltex.xsl,sha256=Il0u0L8p_TI1ZGJHmE5mrDtdlmjv8y44n_H-Es272zk,137304
sympy/utilities/mathml/data/simple_mmlctop.xsl,sha256=lhL-HXG_FfsJZhjeHbD7Ou8RnUaStI0-5VFcggsogjA,114432
sympy/utilities/memoization.py,sha256=ZGOUUmwJCNRhHVZjTF4j65WjQ6VUoCeC1E8DkjryU00,1429
sympy/utilities/misc.py,sha256=4Du5VXy4pKntbESHXLFYd0OwB2Y7QL7rOoAn_-QPNdQ,15715
sympy/utilities/pkgdata.py,sha256=jt-hKL0xhxnDJDI9C2IXtH_QgYYtfq9fX9kJ3E7iang,1788
sympy/utilities/pytest.py,sha256=F9TGNtoNvQUdlt5HYU084ITNmc7__7MBCSLLulBlM_Y,435
sympy/utilities/randtest.py,sha256=aYUX_mgmQyfRdMjEOWaHM506CZ6WUK0eFuew0vFTwRs,430
sympy/utilities/runtests.py,sha256=hYnDNiFNnDjQcXG04_3lzPFbUz6i0AUZ2rZ_RECVoDo,446
sympy/utilities/source.py,sha256=9slUxMXXz-eptGfzOd_iPDvMwczRKdTfgBttllVzLBE,1769
sympy/utilities/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/utilities/tests/test_autowrap.py,sha256=ZXKDa8QRWRWRTzWvtk2NOqIs9eRzt44URA7G2ZyuFkU,14830
sympy/utilities/tests/test_codegen.py,sha256=PLuSicBhnspClTiSeKCJgKd1NyU0qBkDRvQMrwm_gLc,55496
sympy/utilities/tests/test_codegen_julia.py,sha256=kb3soJ1L7lTfZkYJKytfY_aKoHt6fkNjWhYblebzThw,18543
sympy/utilities/tests/test_codegen_octave.py,sha256=_yd9uGKHZzwUFpderSa9E2cYqt8JMcEtBuN6U7_7bJ0,17833
sympy/utilities/tests/test_codegen_rust.py,sha256=wJh6YmDfq8haGjJDniDaVUsDIKEj3rT_OB4r6uLI77Y,12323
sympy/utilities/tests/test_decorator.py,sha256=VYUvzUrVI7I7MK0YZxLLEmEu4pV5dqaB1CLEJ8Ocav4,3705
sympy/utilities/tests/test_deprecated.py,sha256=x0L3rsg_9NEM-SbB6jlcWun8O68SZg_uH7a60ehXPBY,686
sympy/utilities/tests/test_enumerative.py,sha256=aUw6nbSzBp8h_pk35YZ_uzRncRoLYStblodeiDRFk6I,6089
sympy/utilities/tests/test_exceptions.py,sha256=OKRa2yuHMtnVcnisu-xcaedi2RKsH9QrgU9exgoOK30,716
sympy/utilities/tests/test_iterables.py,sha256=xZ-XaQi33YtMoOlexWiZaQsy3r1OKgU9p8yp8622-eE,32697
sympy/utilities/tests/test_lambdify.py,sha256=nGhgU9mmB6EQBg7QJ4TvEAkHNiuxLF8_ib6j1A3t818,52255
sympy/utilities/tests/test_matchpy_connector.py,sha256=dUfDfIdofKYufww29jV8mVQmglU1AnG2uEyREpNY7V0,4506
sympy/utilities/tests/test_misc.py,sha256=yeX5r7kwmF06BR3rEzuX5K--pHC-vxhd8FUZzV1UD6I,4669
sympy/utilities/tests/test_pickling.py,sha256=84FfeJzoQ9JbyMWTtgzlmOY19PNRc5XCnPrwTiEQNso,23488
sympy/utilities/tests/test_source.py,sha256=FvKuTiw4HFZ6UWE6066vdzAY3yOnGiHU94nxY-cUpoo,797
sympy/utilities/tests/test_timeutils.py,sha256=sCRC6BCSho1e9n4clke3QXHx4a3qYLru-bddS_sEmFA,337
sympy/utilities/tests/test_wester.py,sha256=6A340zTT8V3HSVWvSIUfv8BlAmgkJgLBtjWA7YZGR94,94476
sympy/utilities/timeutils.py,sha256=DUtQYONkJnWjU2FvAbvxuRMkGmXpLMeaiOcH7R9Os9o,1968
sympy/utilities/tmpfiles.py,sha256=yOjbs90sEtVc00YZyveyblT8zkwj4o70_RmuEKdKq_s,445
sympy/vector/__init__.py,sha256=8a4cSQ1sJ5uirdMoHnV7SWXU3zJPKt_0ojona8C-p1Y,1909
sympy/vector/basisdependent.py,sha256=qJ8lhiZ-IRIzuRWjuC-oVSRi7GUvt_VtiRlfgTtSIcc,11562
sympy/vector/coordsysrect.py,sha256=1JV4GBgG99JKIWo2snYMMgIJCdob3XcwYqq9s8d6fA8,36859
sympy/vector/deloperator.py,sha256=4BJNjmI342HkVRmeQkqauqvibKsf2HOuzknQTfQMkpg,3191
sympy/vector/dyadic.py,sha256=Y2KEJPXCFBER_2N0g4Q0JVJFZ2U3bNZoFCh8tE0Jc-8,8650
sympy/vector/functions.py,sha256=auLfE1Su2kLtkRvlB_7Wol8O0_sqei1hojun3pkDRYI,15552
sympy/vector/implicitregion.py,sha256=VVInH4DmovkE1VbSmR6IqiR39IYkQ2Vu-qrslMcD4Q8,16155
sympy/vector/integrals.py,sha256=x8DrvKXPznE05JgnZ7I3IWLWrvFl9SEghGaFmHrBaE4,6837
sympy/vector/operators.py,sha256=mI6d0eIxVcoDeH5PrhtPTzhxX_RXByX_4hjXeBTeq88,9521
sympy/vector/orienters.py,sha256=EtWNWfOvAuy_wipam9SA7_muKSrsP-43UPRCCz56sb0,11798
sympy/vector/parametricregion.py,sha256=3YyY0fkFNelR6ldi8XYRWpkFEvqY5-rFg_vT3NFute0,5932
sympy/vector/point.py,sha256=ozYlInnlsmIpKBEr5Ui331T1lnAB5zS2_pHYh9k_eMs,4516
sympy/vector/scalar.py,sha256=Z2f2wiK7BS73ctYTyNvn3gB74mXZuENpScLi_M1SpYg,1962
sympy/vector/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/vector/tests/test_coordsysrect.py,sha256=q9n9OIG_CpD4KQN20dzwRZIXoMv7VSgp8fHmVnkZfr0,19595
sympy/vector/tests/test_dyadic.py,sha256=f1R-BL_63VBbc0XgEX_LYzV_3OupYd4hp5RzRk6dAbI,4949
sympy/vector/tests/test_field_functions.py,sha256=Mwk9MiaYB71tP8ozWcmiV6unKCHsP7oPpG82B6ywz9c,14095
sympy/vector/tests/test_functions.py,sha256=Bs2sekdDJyw_wrUpG7vZQGH0y0S4C4AbxGSpeU_8C2s,8050
sympy/vector/tests/test_implicitregion.py,sha256=wVilD5H-MhHiW58QT6P5U7uT79JdKHm9D7JgZoi6BE4,4028
sympy/vector/tests/test_integrals.py,sha256=BVRhrr_JeAsCKv_E-kA2jaXB8ZXTfj7nkNgT5o-XOJc,5093
sympy/vector/tests/test_operators.py,sha256=KexUWvc_Nwp2HWrEbhxiO7MeaFxYlckrp__Tkwg-wmU,1613
sympy/vector/tests/test_parametricregion.py,sha256=OfKapF9A_g9X6JxgYc0UfxIhwXzRERzaj-EijQCJONw,4009
sympy/vector/tests/test_printing.py,sha256=3BeW55iQ4qXdfDTFqptE2ufJPJIBOzdfIYVx84n_EwA,7708
sympy/vector/tests/test_vector.py,sha256=Mo88Jgmy3CuSQz25WSH34EnZSs_JBY7E-OKPO2SjhPc,7861
sympy/vector/vector.py,sha256=HZucZdScEqL5xPlKLSo7KyTorf_TmW_fdNkxxjvPb5Y,18010
